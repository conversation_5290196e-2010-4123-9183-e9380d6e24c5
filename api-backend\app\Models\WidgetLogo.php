<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WidgetLogo extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'widget_id',
        'logo_data',
        'logo_type',
        'original_filename',
        'mime_type',
        'file_size',
        'width',
        'height',
        'display_width',
        'display_height',
        'position',
        'alt_text',
        'quality',
        'auto_optimize',
        'format_preference',
        'is_active',
        'uploaded_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'display_width' => 'integer',
        'display_height' => 'integer',
        'quality' => 'integer',
        'auto_optimize' => 'boolean',
        'is_active' => 'boolean',
        'uploaded_at' => 'datetime',
    ];

    /**
     * Get the widget that owns this logo.
     */
    public function widget()
    {
        return $this->belongsTo(Widget::class);
    }

    /**
     * Check if the logo is a URL or base64 data.
     */
    public function isUrl(): bool
    {
        return $this->logo_type === 'url';
    }

    /**
     * Check if the logo is base64 encoded data.
     */
    public function isBase64(): bool
    {
        return $this->logo_type === 'base64';
    }

    /**
     * Get the logo data for display.
     */
    public function getDisplayData(): string
    {
        return $this->logo_data;
    }

    /**
     * Get logo metadata as array.
     */
    public function getMetadata(): array
    {
        return [
            'filename' => $this->original_filename,
            'mimeType' => $this->mime_type,
            'fileSize' => $this->file_size,
            'dimensions' => [
                'width' => $this->width,
                'height' => $this->height,
            ],
            'displayDimensions' => [
                'width' => $this->display_width,
                'height' => $this->display_height,
            ],
            'position' => $this->position,
            'altText' => $this->alt_text,
            'quality' => $this->quality,
            'autoOptimize' => $this->auto_optimize,
            'formatPreference' => $this->format_preference,
            'uploadedAt' => $this->uploaded_at,
        ];
    }

    /**
     * Create or update logo from data.
     */
    public static function createFromData(int $widgetId, array $data): self
    {
        // Determine logo type
        $logoType = 'url';
        if (isset($data['logoData']) && str_starts_with($data['logoData'], 'data:image/')) {
            $logoType = 'base64';
        }

        return static::updateOrCreate(
            ['widget_id' => $widgetId],
            [
                'logo_data' => $data['logoData'] ?? $data['logoUrl'] ?? '',
                'logo_type' => $logoType,
                'original_filename' => $data['originalFilename'] ?? null,
                'mime_type' => $data['mimeType'] ?? null,
                'file_size' => $data['fileSize'] ?? null,
                'width' => $data['width'] ?? null,
                'height' => $data['height'] ?? null,
                'display_width' => $data['displayWidth'] ?? null,
                'display_height' => $data['displayHeight'] ?? null,
                'position' => $data['position'] ?? 'header',
                'alt_text' => $data['altText'] ?? null,
                'quality' => $data['quality'] ?? 92,
                'auto_optimize' => $data['autoOptimize'] ?? true,
                'format_preference' => $data['formatPreference'] ?? 'auto',
                'is_active' => $data['isActive'] ?? true,
                'uploaded_at' => $logoType === 'base64' ? now() : null,
            ]
        );
    }

    /**
     * Get logo configuration as array for API responses.
     */
    public function toConfigArray(): array
    {
        return [
            'id' => $this->id,
            'logoData' => $this->logo_data,
            'logoType' => $this->logo_type,
            'metadata' => $this->getMetadata(),
            'isActive' => $this->is_active,
        ];
    }

    /**
     * Get optimized logo data based on settings.
     */
    public function getOptimizedData(): string
    {
        // For now, return the original data
        // In the future, this could implement actual image optimization
        return $this->logo_data;
    }
}
