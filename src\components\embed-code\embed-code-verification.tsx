import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
    CheckCircle2,
    AlertCircle,
    Globe,
    Search,
    RefreshCcw,
    ExternalLink,
    AlertTriangle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface VerificationResult {
    status: "success" | "error" | "pending";
    message: string;
    details?: {
        deploymentUrl?: string;
        scriptFound?: boolean;
        correctVersion?: boolean;
        configValid?: boolean;
    };
}

interface EmbedCodeVerificationProps {
    widgetId: string;
    onSuccess?: (url: string) => void;
}

export function EmbedCodeVerification({ widgetId, onSuccess }: EmbedCodeVerificationProps) {
    const [url, setUrl] = useState<string>("");
    const [isVerifying, setIsVerifying] = useState<boolean>(false);
    const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
    const { toast } = useToast();

    const validateUrl = (input: string): boolean => {
        try {
            const urlObj = new URL(input);
            return urlObj.protocol === "http:" || urlObj.protocol === "https:";
        } catch (error) {
            return false;
        }
    };

    const verifyEmbedCode = async () => {
        if (!validateUrl(url)) {
            toast({
                title: "Invalid URL",
                description: "Please enter a valid URL starting with http:// or https://",
                variant: "destructive",
            });
            return;
        }

        setIsVerifying(true);
        setVerificationResult({
            status: "pending",
            message: "Verifying installation...",
        });

        try {
            // In a real implementation, this would make an API call to verify the installation
            // For demonstration, we'll simulate the verification process
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulated response
            const success = Math.random() > 0.3; // 70% chance of success for demo

            if (success) {
                setVerificationResult({
                    status: "success",
                    message: "Widget correctly installed!",
                    details: {
                        deploymentUrl: url,
                        scriptFound: true,
                        correctVersion: true,
                        configValid: true,
                    },
                });

                if (onSuccess) {
                    onSuccess(url);
                }

                toast({
                    title: "Verification successful",
                    description: "Your widget is correctly installed and configured",
                });
            } else {
                setVerificationResult({
                    status: "error",
                    message: "Widget installation issues detected",
                    details: {
                        deploymentUrl: url,
                        scriptFound: true,
                        correctVersion: false,
                        configValid: Math.random() > 0.5,
                    },
                });

                toast({
                    title: "Verification failed",
                    description: "There are issues with your widget installation",
                    variant: "destructive",
                });
            }
        } catch (error) {
            setVerificationResult({
                status: "error",
                message: "Verification failed. Could not connect to the provided URL.",
            });

            toast({
                title: "Verification error",
                description: "Could not complete the verification process",
                variant: "destructive",
            });
        } finally {
            setIsVerifying(false);
        }
    };

    return (
        <div className="space-y-4">
            <div>
                <Label className="text-base">Verify Installation</Label>
                <p className="text-sm text-muted-foreground mb-2">
                    Check if your widget is correctly installed on your website
                </p>
            </div>

            <div className="flex gap-2">
                <div className="relative flex-1">
                    <Globe className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="https://your-website.com"
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        className="pl-9"
                        disabled={isVerifying}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                verifyEmbedCode();
                            }
                        }}
                    />
                </div>
                <Button
                    onClick={verifyEmbedCode}
                    disabled={!url.trim() || isVerifying}
                >
                    {isVerifying ? (
                        <>
                            <RefreshCcw className="h-4 w-4 mr-1 animate-spin" />
                            Verifying...
                        </>
                    ) : (
                        <>
                            <Search className="h-4 w-4 mr-1" />
                            Verify
                        </>
                    )}
                </Button>
            </div>

            {verificationResult && (
                <Card className={
                    verificationResult.status === "success"
                        ? "border-green-200 bg-green-50"
                        : verificationResult.status === "error"
                            ? "border-red-200 bg-red-50"
                            : "border-yellow-200 bg-yellow-50"
                }>
                    <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                            {verificationResult.status === "success" ? (
                                <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
                            ) : verificationResult.status === "error" ? (
                                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                            ) : (
                                <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                            )}

                            <div className="space-y-3 flex-1">
                                <div>
                                    <h4 className="font-medium">
                                        {verificationResult.message}
                                    </h4>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        {verificationResult.status === "pending"
                                            ? "Please wait while we check your installation..."
                                            : `Widget ID: ${widgetId}`
                                        }
                                    </p>
                                </div>

                                {verificationResult.details && (
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <Badge variant={verificationResult.details.scriptFound ? "default" : "destructive"}>
                                                Script
                                            </Badge>
                                            <span className="text-sm">
                                                {verificationResult.details.scriptFound
                                                    ? "Widget script found"
                                                    : "Widget script not found"
                                                }
                                            </span>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Badge variant={verificationResult.details.correctVersion ? "default" : "destructive"}>
                                                Version
                                            </Badge>
                                            <span className="text-sm">
                                                {verificationResult.details.correctVersion
                                                    ? "Using latest version"
                                                    : "Using outdated version"
                                                }
                                            </span>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Badge variant={verificationResult.details.configValid ? "default" : "destructive"}>
                                                Config
                                            </Badge>
                                            <span className="text-sm">
                                                {verificationResult.details.configValid
                                                    ? "Configuration valid"
                                                    : "Configuration invalid"
                                                }
                                            </span>
                                        </div>
                                    </div>
                                )}

                                {verificationResult.status === "error" && (
                                    <Alert variant="destructive">
                                        <AlertDescription>
                                            Please check that you have correctly installed the embed code
                                            and that you're using the latest version.
                                        </AlertDescription>
                                    </Alert>
                                )}

                                {verificationResult.status === "success" && (
                                    <div className="flex justify-end">
                                        <Button size="sm" variant="outline" className="gap-1">
                                            <ExternalLink className="h-4 w-4" />
                                            Visit Site
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
} 