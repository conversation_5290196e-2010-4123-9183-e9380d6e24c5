<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WidgetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'ai_model_id' => 'nullable|exists:ai_models,id',
            'settings' => 'nullable|array',
            'settings.general' => 'nullable|array',
            'settings.general.name' => 'nullable|string|max:255',
            'settings.general.welcomeMessage' => 'nullable|string|max:500',
            'settings.general.botName' => 'nullable|string|max:100',
            'settings.general.placeholderText' => 'nullable|string|max:100',
            'settings.general.widgetPosition' => 'nullable|string|in:bottom-right,bottom-left,top-right,top-left',
            'settings.general.presetTheme' => 'nullable|string|max:50',

            'settings.appearance' => 'nullable|array',
            'settings.appearance.primaryColor' => 'nullable|string|max:20',
            'settings.appearance.secondaryColor' => 'nullable|string|max:20',
            'settings.appearance.headerBgColor' => 'nullable|string|max:20',
            'settings.appearance.textColor' => 'nullable|string|max:20',
            'settings.appearance.fontSize' => 'nullable|numeric|min:10|max:24',
            'settings.appearance.borderRadius' => 'nullable|numeric|min:0|max:50',
            'settings.appearance.widgetWidth' => 'nullable|numeric|min:200|max:800',
            'settings.appearance.widgetHeight' => 'nullable|numeric|min:300|max:1000',
            'settings.appearance.showLogo' => 'nullable|boolean',
            'settings.appearance.showCloseButton' => 'nullable|boolean',
            'settings.appearance.darkMode' => 'nullable|boolean',
            'settings.appearance.customCSS' => 'nullable|string|max:5000',
            'settings.appearance.animation' => 'nullable|string|in:none,fade,slide,bounce',
            'settings.appearance.shadow' => 'nullable|string|in:none,sm,md,lg,xl',
            'settings.appearance.glassMorphism' => 'nullable|boolean',

            'settings.behavior' => 'nullable|array',
            'settings.behavior.startMinimized' => 'nullable|boolean',
            'settings.behavior.autoOpen' => 'nullable|boolean',
            'settings.behavior.autoOpenDelay' => 'nullable|numeric|min:0|max:60',
            'settings.behavior.showTypingIndicator' => 'nullable|boolean',
            'settings.behavior.enableUserRatings' => 'nullable|boolean',
            'settings.behavior.collectUserData' => 'nullable|boolean',
            'settings.behavior.persistConversation' => 'nullable|boolean',
            'settings.behavior.preChat' => 'nullable|boolean',
            'settings.behavior.postChat' => 'nullable|boolean',
            'settings.behavior.closeAfterInactivity' => 'nullable|boolean',
            'settings.behavior.inactivityTimeout' => 'nullable|numeric|min:1|max:60',

            'settings.advanced' => 'nullable|array',
            'settings.advanced.modelSelection' => 'nullable|string|in:gemini,gpt-4,huggingface,auto',
            'settings.advanced.contextRetention' => 'nullable|string|in:session,persistent,none',
            'settings.advanced.maxMessagesStored' => 'nullable|numeric|min:10|max:1000',
            'settings.advanced.enableAnalytics' => 'nullable|boolean',
            'settings.advanced.debugMode' => 'nullable|boolean',
            'settings.advanced.loadTimeoutMs' => 'nullable|numeric|min:1000|max:30000',
            'settings.advanced.webhookUrl' => 'nullable|url',
            'settings.advanced.customParameters' => 'nullable|array',

            'typography' => 'nullable|array',
            'button_customization' => 'nullable|array',
            'behavior_rules' => 'nullable|array',
            'logo_url' => ['nullable', 'string', function ($attribute, $value, $fail) {
                if (!empty($value)) {
                    // Check if it's a data URL (base64 encoded image)
                    if (preg_match('/^data:image\/(jpeg|jpg|png|gif|svg\+xml);base64,/', $value)) {
                        return; // Valid data URL
                    }
                    // Check if it's a regular HTTP/HTTPS URL
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $fail('The logo url field must be a valid URL or base64 image data.');
                    }
                }
            }],
            'allowed_domains' => 'nullable|array',
            'allowed_domains.*' => 'string|max:255',
            'position_type' => 'nullable|string|in:fixed,relative,inline',
            'position_settings' => 'nullable|array',
            'custom_css' => 'nullable|string|max:5000',
            'is_active' => 'boolean',
        ];

        // For update requests, make name nullable
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['name'] = 'nullable|string|max:255';
            $rules['is_active'] = 'nullable|boolean';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Widget name is required.',
            'name.max' => 'Widget name cannot exceed 255 characters.',
            'ai_model_id.exists' => 'The selected AI model does not exist.',
            'settings.general.welcomeMessage.max' => 'Welcome message cannot exceed 500 characters.',
            'settings.general.botName.max' => 'Bot name cannot exceed 100 characters.',
            'settings.general.placeholderText.max' => 'Placeholder text cannot exceed 100 characters.',
            'settings.general.widgetPosition.in' => 'Widget position must be one of: bottom-right, bottom-left, top-right, top-left.',
            'settings.appearance.fontSize.min' => 'Font size must be at least 10px.',
            'settings.appearance.fontSize.max' => 'Font size cannot exceed 24px.',
            'settings.appearance.borderRadius.min' => 'Border radius cannot be negative.',
            'settings.appearance.borderRadius.max' => 'Border radius cannot exceed 50px.',
            'settings.appearance.widgetWidth.min' => 'Widget width must be at least 200px.',
            'settings.appearance.widgetWidth.max' => 'Widget width cannot exceed 800px.',
            'settings.appearance.widgetHeight.min' => 'Widget height must be at least 300px.',
            'settings.appearance.widgetHeight.max' => 'Widget height cannot exceed 1000px.',
            'settings.appearance.customCSS.max' => 'Custom CSS cannot exceed 5000 characters.',
            'settings.behavior.autoOpenDelay.min' => 'Auto open delay cannot be negative.',
            'settings.behavior.autoOpenDelay.max' => 'Auto open delay cannot exceed 60 seconds.',
            'settings.behavior.inactivityTimeout.min' => 'Inactivity timeout must be at least 1 minute.',
            'settings.behavior.inactivityTimeout.max' => 'Inactivity timeout cannot exceed 60 minutes.',
            'settings.advanced.maxMessagesStored.min' => 'Must store at least 10 messages.',
            'settings.advanced.maxMessagesStored.max' => 'Cannot store more than 1000 messages.',
            'settings.advanced.loadTimeoutMs.min' => 'Load timeout must be at least 1000ms.',
            'settings.advanced.loadTimeoutMs.max' => 'Load timeout cannot exceed 30000ms.',
            'settings.advanced.webhookUrl.url' => 'Webhook URL must be a valid URL.',
            'logo_url.url' => 'Logo URL must be a valid URL.',
            'allowed_domains.*.max' => 'Domain names cannot exceed 255 characters.',
            'position_type.in' => 'Position type must be one of: fixed, relative, inline.',
            'custom_css.max' => 'Custom CSS cannot exceed 5000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'settings.general.welcomeMessage' => 'welcome message',
            'settings.general.botName' => 'bot name',
            'settings.general.placeholderText' => 'placeholder text',
            'settings.general.widgetPosition' => 'widget position',
            'settings.appearance.primaryColor' => 'primary color',
            'settings.appearance.secondaryColor' => 'secondary color',
            'settings.appearance.headerBgColor' => 'header background color',
            'settings.appearance.textColor' => 'text color',
            'settings.appearance.fontSize' => 'font size',
            'settings.appearance.borderRadius' => 'border radius',
            'settings.appearance.widgetWidth' => 'widget width',
            'settings.appearance.widgetHeight' => 'widget height',
            'settings.appearance.customCSS' => 'custom CSS',
            'settings.behavior.autoOpenDelay' => 'auto open delay',
            'settings.behavior.inactivityTimeout' => 'inactivity timeout',
            'settings.advanced.maxMessagesStored' => 'max messages stored',
            'settings.advanced.loadTimeoutMs' => 'load timeout',
            'settings.advanced.webhookUrl' => 'webhook URL',
            'logo_url' => 'logo URL',
            'allowed_domains' => 'allowed domains',
            'position_type' => 'position type',
            'custom_css' => 'custom CSS',
        ];
    }
}
