import api from './api';

// Types for widget configurations
export interface WidgetSettings {
  primaryColor?: string;
  secondaryColor?: string;
  fontFamily?: string;
  borderRadius?: number;
  chatIconSize?: number;
  autoOpenDelay?: number;
  position?: string;
  initialMessage?: string;
  mobileBehavior?: string;
  headerTitle?: string;
  inputPlaceholder?: string;
  sendButtonText?: string;
  offlineMessage?: string;
  systemPrompt?: string;
  ai_model_id?: number | null;
  requireGuestInfo?: boolean;
  persistConversation?: boolean;
  triggerAfterPageViews?: number;
  pageTargeting?: string;
  avatar?: {
    enabled?: boolean;
    imageUrl?: string;
    fallbackInitial?: string;
  };

  // Widget behavior properties
  showLogo?: boolean;
  showCloseButton?: boolean;
  darkMode?: boolean;
  glassMorphism?: boolean;
  startMinimized?: boolean;
  showTypingIndicator?: boolean;
  enableUserRatings?: boolean;
  preChat?: boolean;
  postChat?: boolean;
  closeAfterInactivity?: boolean;
  enableAnalytics?: boolean;
  debugMode?: boolean;
  inactivityTimeout?: number;
  maxMessagesStored?: number;
  loadTimeoutMs?: number;
  modelSelection?: string;
  contextRetention?: string;
  webhookUrl?: string;

  // Pre-chat and post-chat settings
  preChatFormSettings?: {
    enabled: boolean;
    title?: string;
    description?: string;
    buttonText?: string;
    required?: boolean;
    fields?: any[];
  };

  postChatSurveySettings?: {
    enabled: boolean;
    title?: string;
    description?: string;
    thankYouMessage?: string;
    buttonText?: string;
    showAlways?: boolean;
    questions?: any[];
  };

  // Custom parameters
  customParameters?: Record<string, string>;

  // Advanced positioning options
  positionType?: 'fixed' | 'relative' | 'inline';
  horizontalOffset?: number;
  verticalOffset?: number;
  width?: number;
  height?: number;
  responsiveMode?: 'maintain' | 'fullscreen' | 'collapse';

  // Domain restrictions
  allowedDomains?: string[];

  // Branding
  logoUrl?: string;
  customCSS?: string;
  welcomeMessage?: string;
  welcomeMessages?: Array<{ locale: string; message: string }>;

  // Typography customization
  typography?: {
    titleFont?: string;
    bodyFont?: string;
    fontSize?: number;
    customFonts?: string[];
  };

  // Button customization
  buttonCustomization?: {
    text?: string;
    iconName?: string;
    shape?: 'circle' | 'square' | 'rounded';
    animation?: 'pulse' | 'bounce' | 'none';
  };

  // Visitor segmentation
  visitorSegmentation?: {
    enabled?: boolean;
    rules?: Array<{
      attribute: string;
      operator: string;
      value: string;
      action: string;
    }>;
  };

  // Auto-pop timing
  autoPopSettings?: {
    enabled?: boolean;
    timing?: number;
    scrollDepth?: number;
    exitIntent?: boolean;
    afterPageCount?: number;
  };

  // Security
  securitySettings?: {
    dataSanitization?: boolean;
    preventDataCollection?: boolean;
    cspEnabled?: boolean;
    enableSRI?: boolean;
  };

  // New integrations property for replacing webhookUrl
  integrations?: Array<{
    id: string;
    type: string;
    name: string;
    url: string;
    active: boolean;
    events: string[];
    secret?: string;
    created_at: string;
  }>;
}

export interface Widget {
  id?: number;
  name: string;
  widget_id?: string;
  ai_model_id?: number | null;
  ai_model?: {
    id: number;
    name: string;
    provider: string;
    description?: string;
    template_id?: number;
    active: boolean;
    is_default: boolean;
  };
  settings?: WidgetSettings;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface AnalyticsSummary {
  total_views: number;
  total_conversations: number;
  total_messages: number;
  engagement_rate: number;
  avg_messages_per_conversation: number;
  period: string;
}

// Widget services
export const widgetService = {
  getAllWidgets: async () => {
    return api.get('widgets');
  },

  getWidget: async (id: number) => {
    return api.get(`widgets/${id}`);
  },

  createWidget: async (widgetData: Widget) => {
    return api.post('widgets', widgetData);
  },

  updateWidget: async (id: number, widgetData: Partial<Widget>) => {
    return api.put(`widgets/${id}`, widgetData);
  },

  deleteWidget: async (id: number) => {
    return api.delete(`widgets/${id}`);
  },

  getWidgetByPublicId: async (widgetId: string) => {
    return api.get(`widgets/public/${widgetId}`);
  },

  generateEmbedCode: async (widgetId: number, options: {
    type: 'standard' | 'iframe' | 'web-component',
    customizations?: {
      allowed_domains?: string[];
      position_type?: 'fixed' | 'relative' | 'inline';
      enable_sri?: boolean;
      csp_enabled?: boolean;
      guest_user_flow?: 'required' | 'auto' | 'none';
      webhook_url?: string;
      enable_analytics?: boolean;
      version?: string;
    };
  }) => {
    return api.post('embed-code/generate', {
      widget_id: widgetId,
      type: options.type,
      allowed_domains: options.customizations?.allowed_domains || ['*'],
      position_type: options.customizations?.position_type || 'fixed',
      enable_sri: options.customizations?.enable_sri || false,
      csp_enabled: options.customizations?.csp_enabled || false,
      guest_user_flow: options.customizations?.guest_user_flow || 'auto',
      webhook_url: options.customizations?.webhook_url || '',
      enable_analytics: options.customizations?.enable_analytics !== undefined ? options.customizations.enable_analytics : true
    });
  },

  // Analytics methods
  getAnalyticsSummary: async (widgetId: number, period: 'day' | 'week' | 'month' | 'all' = 'month') => {
    return api.get<AnalyticsSummary>(`widgets/${widgetId}/analytics/summary?period=${period}`);
  },

  getAnalytics: async (widgetId: number, options: {
    fromDate?: string;
    toDate?: string;
    groupBy?: 'day' | 'week' | 'month' | 'event_type' | 'url';
  } = {}) => {
    const params = new URLSearchParams();

    if (options.fromDate) {
      params.append('from_date', options.fromDate);
    }

    if (options.toDate) {
      params.append('to_date', options.toDate);
    }

    if (options.groupBy) {
      params.append('group_by', options.groupBy);
    }

    return api.get(`widgets/${widgetId}/analytics?${params.toString()}`);
  },

  // Additional widget configuration methods
  updateWidgetSettings: async (widgetId: number, settings: WidgetSettings) => {
    return api.put(`widgets/${widgetId}`, {
      settings: settings
    });
  },

  // Widget activation/deactivation
  setWidgetActive: async (widgetId: number, isActive: boolean) => {
    return api.put(`widgets/${widgetId}`, {
      is_active: isActive
    });
  },

  // Guest settings
  updateGuestSettings: async (widgetId: number, requireGuestInfo: boolean, guestFields?: string[]) => {
    return api.put(`widgets/${widgetId}`, {
      settings: {
        requireGuestInfo: requireGuestInfo,
        guestFields: guestFields
      }
    });
  },

  // Logo Management Methods
  uploadLogo: async (widgetId: number, file: File, metadata?: {
    position?: 'header' | 'footer' | 'sidebar' | 'floating';
    displayWidth?: number;
    displayHeight?: number;
    altText?: string;
    quality?: number;
  }) => {
    const formData = new FormData();
    formData.append('logo', file);

    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });
    }

    return api.post(`widgets/${widgetId}/logo/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  updateLogoUrl: async (widgetId: number, logoUrl: string, metadata?: {
    position?: 'header' | 'footer' | 'sidebar' | 'floating';
    displayWidth?: number;
    displayHeight?: number;
    altText?: string;
  }) => {
    return api.post(`widgets/${widgetId}/logo/url`, {
      logoUrl,
      ...metadata,
    });
  },

  getLogo: async (widgetId: number) => {
    return api.get(`widgets/${widgetId}/logo`);
  },

  deleteLogo: async (widgetId: number) => {
    return api.delete(`widgets/${widgetId}/logo`);
  },

  // Webhook Testing Method
  testWebhook: async (widgetId: number, webhookConfig: {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'PATCH';
    headers?: Record<string, string>;
    secret?: string;
    eventType?: 'chat_start' | 'chat_end' | 'message_sent' | 'message_received' | 'user_rating' | 'form_submission';
  }) => {
    return api.post(`widgets/${widgetId}/test-webhook`, webhookConfig);
  },

  // Configuration Export Method
  exportConfig: async (widgetId: number) => {
    return api.get(`widgets/${widgetId}/export-config`);
  },

  // Version Management Methods
  getVersionHistory: async (widgetId: number) => {
    return api.get(`widgets/${widgetId}/versions`);
  },

  createVersion: async (widgetId: number, versionData: any) => {
    return api.post(`widgets/${widgetId}/versions`, versionData);
  },

  restoreVersion: async (widgetId: number, version: string) => {
    return api.post(`widgets/${widgetId}/versions/${version}/restore`);
  },

  // Bulk Operations
  bulkUpdate: async (widgetIds: number[], updateData: Partial<Widget>) => {
    return api.post('widgets/bulk-update', {
      widget_ids: widgetIds,
      data: updateData,
    });
  },

  bulkDelete: async (widgetIds: number[]) => {
    return api.post('widgets/bulk-delete', {
      widget_ids: widgetIds,
    });
  },

  bulkActivate: async (widgetIds: number[], isActive: boolean) => {
    return api.post('widgets/bulk-activate', {
      widget_ids: widgetIds,
      is_active: isActive,
    });
  }
};
