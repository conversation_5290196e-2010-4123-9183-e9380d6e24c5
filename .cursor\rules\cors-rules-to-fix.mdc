---
description: 
globs: 
alwaysApply: true
---
**<PERSON><PERSON> CORS & Sanctum Setup for Embeddable Chat System**

---

### 🔧 Laravel Backend Setup

#### 1. **Install Sanctum**
```bash
composer require laravel/sanctum
php artisan vendor:publish --provider="<PERSON><PERSON>\Sanctum\SanctumServiceProvider"
php artisan migrate
```

#### 2. **Configure Sanctum Middleware**
In `app/Http/Kernel.php`, under `api` middleware group:
```php
'api' => [
    \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
    'throttle:api',
    \Illuminate\Routing\Middleware\SubstituteBindings::class,
],
```

#### 3. **Update `config/sanctum.php` for Cross-Domain**
```php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', 'localhost:3000,127.0.0.1:3000')), // include frontend domain
```

#### 4. **Set `.env` for Stateful Domains**
```env
SANCTUM_STATEFUL_DOMAINS=localhost:3000,127.0.0.1:3000,yourfrontenddomain.com
SESSION_DOMAIN=.yourbackenddomain.com
```

---

### 🌐 CORS Configuration

#### 1. **Edit `config/cors.php`**
```php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],

    'allowed_methods' => ['*'],

    'allowed_origins' => ['http://localhost:3000', 'https://yourfrontenddomain.com'],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => true,
];
```

#### 2. **Middleware Order**
Ensure CORS middleware is before `HandleCors` in `App\Http\Kernel.php`:
```php
\Fruitcake\Cors\HandleCors::class,
```

---

### 🔐 Authentication Flow

1. **Frontend sends a GET request to `sanctum/csrf-cookie`** to initiate the session and receive XSRF token.
2. **Frontend posts login credentials** to Laravel `/login` endpoint.
3. Laravel returns `access_token` and user info.
4. Token is stored on the client (typically in localStorage or cookie).
5. For subsequent requests, frontend includes the token via `Authorization: Bearer <token>` header.

---

### 💬 Embeddable Chat Widget Flow

#### 1. **Generate Embed Code from Backend**
Example output:
```html
<iframe 
  src="https://yourlaravelapi.com/embed/chat?key=xyz"
  style="width:100%; height:400px; border:none;"
  allow="clipboard-write; microphone; camera"
  sandbox="allow-same-origin allow-scripts allow-forms"
></iframe>
```

#### 2. **Add CORS Support for Iframe**
Update `cors.php`:
```php
'allowed_origins' => ['*'], // or be specific: ['https://clientsite.com']
'supports_credentials' => true,
```

#### 3. **Embed Controller**
Create `EmbedController.php`:
```php
public function chat(Request $request)
{
    $key = $request->get('key');

    // Validate key and load context/user if needed

    return view('embed.chat', compact('key'));
}
```

Create Blade template `resources/views/embed/chat.blade.php` to contain your JS chat widget logic.

---

### 📦 Security Notes
- **CSRF Protection**: Enabled via `sanctum/csrf-cookie`.
- **Rate Limiting**: Use `throttle` middleware for login & chat endpoints.
- **Session Domain**: Must match your backend's domain for cookie sharing.

---


Let me know if you need the frontend (React/Next.js) integration, token interceptor logic, or widget styling added.