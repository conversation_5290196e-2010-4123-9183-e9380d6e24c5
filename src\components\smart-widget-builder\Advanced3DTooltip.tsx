"use client"

import React, { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { 
  CheckCircle2, 
  <PERSON>ting<PERSON>, 
  <PERSON>ap, 
  <PERSON>rk<PERSON>,
  ArrowRight
} from 'lucide-react'

interface TooltipContent {
  title: string
  description: string
  features: string[]
  benefits: string[]
  status: 'enabled' | 'disabled' | 'configured'
  icon: React.ReactNode
  gradient: string
  examples?: string[]
  stats?: { label: string; value: string }[]
}

interface Advanced3DTooltipProps {
  children: React.ReactNode
  content: TooltipContent
  targetElementId?: string
}

const Advanced3DTooltip = ({ 
  children, 
  content,
  targetElementId = "feature-tooltip-display"
}: Advanced3DTooltipProps) => {
  const [isVisible, setIsVisible] = React.useState(false)

  const handleMouseEnter = () => {
    setIsVisible(true)
    renderTooltipInTarget()
  }

  const handleMouseLeave = () => {
    setIsVisible(false)
    clearTooltipFromTarget()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'enabled':
        return 'from-green-500 to-emerald-600'
      case 'configured':
        return 'from-blue-500 to-indigo-600'
      case 'disabled':
        return 'from-gray-400 to-gray-500'
      default:
        return 'from-gray-400 to-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'enabled':
      case 'configured':
        return <CheckCircle2 className="w-4 h-4 text-white" />
      case 'disabled':
        return <Settings className="w-4 h-4 text-white" />
      default:
        return <Settings className="w-4 h-4 text-white" />
    }
  }

  const renderTooltipInTarget = () => {
    const targetElement = document.getElementById(targetElementId)
    if (!targetElement) return

    // Clear any existing tooltip
    targetElement.innerHTML = ''

    // Create tooltip element
    const tooltipElement = document.createElement('div')
    tooltipElement.className = 'w-96 mx-auto'
    
    tooltipElement.innerHTML = `
      <div class="relative">
        <!-- Background Glow -->
        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-xl scale-110"></div>
        
        <!-- Main Tooltip Card -->
        <div class="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 rounded-2xl shadow-2xl overflow-hidden"
             style="box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)">
          
          <!-- Header with Gradient -->
          <div class="relative p-4 bg-gradient-to-r ${content.gradient} text-white overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-20">
              <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
            </div>
            
            <div class="relative flex items-center space-x-3">
              <div class="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                ${content.icon ? content.icon.toString() : ''}
              </div>
              <div class="flex-1">
                <h3 class="font-bold text-lg">${content.title}</h3>
                <div class="inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getStatusColor(content.status)}">
                  <span class="capitalize">${content.status}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Content Body -->
          <div class="p-4 space-y-4">
            <!-- Description -->
            <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
              ${content.description}
            </p>

            <!-- Features -->
            ${content.features.length > 0 ? `
              <div>
                <h4 class="font-semibold text-sm text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                  <svg class="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                  </svg>
                  Key Features
                </h4>
                <ul class="space-y-1">
                  ${content.features.map(feature => `
                    <li class="flex items-center text-xs text-gray-600 dark:text-gray-400">
                      <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                      ${feature}
                    </li>
                  `).join('')}
                </ul>
              </div>
            ` : ''}

            <!-- Benefits -->
            ${content.benefits.length > 0 ? `
              <div>
                <h4 class="font-semibold text-sm text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                  <svg class="w-4 h-4 mr-1 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"></path>
                  </svg>
                  Benefits
                </h4>
                <ul class="space-y-1">
                  ${content.benefits.map(benefit => `
                    <li class="flex items-center text-xs text-gray-600 dark:text-gray-400">
                      <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      ${benefit}
                    </li>
                  `).join('')}
                </ul>
              </div>
            ` : ''}

            <!-- Stats -->
            ${content.stats && content.stats.length > 0 ? `
              <div class="grid grid-cols-2 gap-3 pt-2 border-t border-gray-200 dark:border-gray-700">
                ${content.stats.map(stat => `
                  <div class="text-center">
                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">${stat.value}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">${stat.label}</div>
                  </div>
                `).join('')}
              </div>
            ` : ''}

            <!-- Examples -->
            ${content.examples && content.examples.length > 0 ? `
              <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold text-sm text-gray-800 dark:text-gray-200 mb-2">Examples</h4>
                <div class="flex flex-wrap gap-1">
                  ${content.examples.map(example => `
                    <span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded-md text-gray-600 dark:text-gray-400">
                      ${example}
                    </span>
                  `).join('')}
                </div>
              </div>
            ` : ''}
          </div>

          <!-- Footer Action -->
          <div class="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between text-xs">
              <span class="text-gray-500 dark:text-gray-400">Click to configure</span>
              <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- 3D Shadow -->
        <div class="absolute inset-0 bg-black/10 rounded-2xl blur-sm" style="transform: translateZ(-10px) translateY(5px); z-index: -1;"></div>
      </div>
    `

    // Add animation
    tooltipElement.style.opacity = '0'
    tooltipElement.style.transform = 'scale(0.8) rotateX(-15deg)'
    tooltipElement.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)'
    
    targetElement.appendChild(tooltipElement)
    
    // Trigger animation
    setTimeout(() => {
      tooltipElement.style.opacity = '1'
      tooltipElement.style.transform = 'scale(1) rotateX(0deg)'
    }, 10)
  }

  const clearTooltipFromTarget = () => {
    const targetElement = document.getElementById(targetElementId)
    if (!targetElement) return
    
    const tooltipElement = targetElement.firstChild as HTMLElement
    if (tooltipElement) {
      tooltipElement.style.opacity = '0'
      tooltipElement.style.transform = 'scale(0.8) rotateX(15deg)'
      setTimeout(() => {
        targetElement.innerHTML = ''
      }, 300)
    }
  }

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="relative"
    >
      {children}
    </div>
  )
}

export default Advanced3DTooltip
