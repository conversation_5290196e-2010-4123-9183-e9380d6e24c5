import React, { useState } from "react";
import { ChevronDown, ChevronUp, Globe, Search } from "lucide-react";
import { ApiRoute } from "@/utils/api-test-service";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface EndpointListProps {
  routes: ApiRoute[];
  onSelectEndpoint: (route: ApiRoute, method: string) => void;
}

export function EndpointList({ routes, onSelectEndpoint }: EndpointListProps) {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter routes based on search term
  const filteredRoutes = routes.filter(
    (route) =>
      route.uri.toLowerCase().includes(searchTerm.toLowerCase()) ||
      route.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      route.methods.some((method) =>
        method.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
  );

  // Group routes by category
  const categories = Array.from(
    new Set(filteredRoutes.map((route) => route.category)),
  );

  // Map method to color
  const getMethodColor = (method: string) => {
    switch (method.toLowerCase()) {
      case "get":
        return "bg-blue-500";
      case "post":
        return "bg-green-500";
      case "put":
        return "bg-yellow-500";
      case "delete":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="bg-card rounded-md border shadow-sm h-full">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">API Endpoints</h3>
        <p className="text-sm text-muted-foreground mb-3">
          Select an endpoint to test
        </p>
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search endpoints..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {categories.length === 0 ? (
        <div className="p-4 text-center text-muted-foreground">
          No endpoints found matching your search.
        </div>
      ) : (
        <Accordion
          type="multiple"
          className="w-full max-h-[500px] overflow-y-auto"
        >
          {categories.map((category, index) => (
            <AccordionItem value={category} key={index}>
              <AccordionTrigger className="px-4 py-2 hover:bg-muted/50">
                <span className="flex items-center">
                  <Globe className="w-4 h-4 mr-2" />
                  {category}
                </span>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-1 p-2">
                  {filteredRoutes
                    .filter((route) => route.category === category)
                    .map((route, idx) => (
                      <div
                        key={idx}
                        className="border rounded-md p-2 hover:bg-muted/30 transition-colors"
                      >
                        <div className="text-sm font-medium mb-2 break-all">
                          {route.uri}
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {route.methods.map((method, methodIdx) => (
                            <Badge
                              key={methodIdx}
                              className={`cursor-pointer ${getMethodColor(method)} hover:opacity-80`}
                              onClick={() => onSelectEndpoint(route, method)}
                            >
                              {method}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}
