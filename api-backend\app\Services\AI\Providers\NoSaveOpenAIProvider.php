<?php

namespace App\Services\AI\Providers;

use App\Models\AIModel;
use Illuminate\Support\Facades\Log;

/**
 * A version of OpenAIProvider that doesn't save models to the database
 */
class NoSaveOpenAIProvider extends OpenAIProvider
{
    /**
     * Override updateModelSettings to prevent saving to database
     *
     * @param AIModel $model
     * @param array $discoveredModels
     * @return void
     */
    protected function updateModelSettings(AIModel $model, array $discoveredModels): void
    {
        $settings = $model->settings ?? [];
        $settings['available_models'] = $discoveredModels;

        // If current model_name is invalid, update to a valid one
        if (!empty($discoveredModels)) {
            $currentModelName = $settings['model_name'] ?? null;

            if (!$currentModelName || !isset($discoveredModels[$currentModelName])) {
                // Find default model from config
                $defaultModel = $this->getConfig()['default_model'] ?? null;

                // If default model exists in discovered models, use it
                if ($defaultModel && isset($discoveredModels[$defaultModel])) {
                    $settings['model_name'] = $defaultModel;
                } else {
                    // Otherwise use the first available model
                    $settings['model_name'] = array_key_first($discoveredModels);
                }
            }
        }

        $model->settings = $settings;
        // Don't save the model
    }

    /**
     * Discover available models without saving to database
     *
     * @param AIModel $model
     * @return array
     */
    public function discoverModels(AIModel $model): array
    {
        try {
            $response = $this->makeRequest('get', '/models', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $model->api_key,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $discoveredModels = $this->parseModelsFromResponse($response);

            // Update model settings but don't save
            $this->updateModelSettings($model, $discoveredModels);

            return [
                'success' => true,
                'models' => $discoveredModels,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to discover OpenAI models: " . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Test connection to OpenAI without saving to database
     *
     * @param AIModel $model
     * @return array
     */
    public function testConnection(AIModel $model): array
    {
        try {
            $response = $this->makeRequest('get', '/models', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $model->api_key,
                    'Content-Type' => 'application/json',
                ],
            ]);

            // Discover and update available models
            $discoveredModels = $this->parseModelsFromResponse($response);

            // Update model settings but don't save
            $this->updateModelSettings($model, $discoveredModels);

            return [
                'success' => true,
                'message' => 'Successfully connected to OpenAI API',
                'data' => [
                    'available_models' => array_keys($discoveredModels),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'OpenAI connection test failed: ' . $e->getMessage(),
            ];
        }
    }
}
