import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AIModelData, ModelAnalytics, aiModelService } from "@/utils/ai-model-service";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { Bar<PERSON>hart3, <PERSON><PERSON>hart2, <PERSON>, <PERSON>auge, <PERSON><PERSON><PERSON> as PieChartIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Spinner } from "@/components/ui/spinner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertTriangle } from "lucide-react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as Bar<PERSON><PERSON><PERSON>omponent } from "@/components/ui/charts";

interface ModelAnalyticsCardProps {
  modelId: number;
}

// Create simple chart components
const LineChart = ({ data, options }: any) => (
  <div className="h-full w-full flex items-center justify-center">
    <div className="text-center text-muted-foreground">
      Chart visualization (using data from {data.labels.length} data points)
    </div>
  </div>
);

const PieChart = ({ data, options }: any) => (
  <div className="h-full w-full flex items-center justify-center">
    <div className="text-center text-muted-foreground">
      Pie Chart visualization ({data.labels.join(', ')})
    </div>
  </div>
);

export function ModelAnalyticsCard({ modelId }: ModelAnalyticsCardProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState("7d");
  const [analytics, setAnalytics] = useState<ModelAnalytics[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, [modelId, period]);

  async function fetchAnalytics() {
    setIsLoading(true);
    setError(null);

    try {
      const data = await aiModelService.getModelAnalytics(modelId, period);
      setAnalytics(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching model analytics:", error);
      setError("Failed to load analytics data");
      toast({
        title: "Error",
        description: "Failed to load model analytics",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Function to get total metrics
  const getTotalMetrics = () => {
    if (!analytics || analytics.length === 0) {
      return {
        totalRequests: 0,
        successRate: 0,
        avgResponseTime: 0,
        totalTokens: 0,
        fallbackRate: 0
      };
    }

    // Calculate totals and averages
    const totalRequests = analytics.reduce((sum, item) => sum + (item.total_requests || 0), 0);
    const successfulRequests = analytics.reduce((sum, item) => sum + (item.successful_requests || 0), 0);
    const fallbackRequests = analytics.reduce((sum, item) => sum + (item.fallback_requests || 0), 0);
    const totalInputTokens = analytics.reduce((sum, item) => sum + (item.total_input_tokens || 0), 0);
    const totalOutputTokens = analytics.reduce((sum, item) => sum + (item.total_output_tokens || 0), 0);

    // Calculate averages and percentages
    const successRate = totalRequests ? (successfulRequests / totalRequests) * 100 : 0;
    const fallbackRate = totalRequests ? (fallbackRequests / totalRequests) * 100 : 0;
    const avgResponseTime = totalRequests
      ? analytics.reduce((sum, item) => sum + (item.avg_response_time || 0) * (item.total_requests || 0), 0) / totalRequests
      : 0;

    return {
      totalRequests,
      successRate: Math.round(successRate),
      avgResponseTime: parseFloat(avgResponseTime.toFixed(2)),
      totalTokens: totalInputTokens + totalOutputTokens,
      fallbackRate: Math.round(fallbackRate)
    };
  };

  // Prepare chart data for usage trends
  const prepareUsageData = () => {
    if (!analytics || analytics.length === 0) return { labels: [], datasets: [] };

    // Sort analytics by date
    const sortedAnalytics = [...analytics].sort((a, b) => {
      return new Date(a.date || '').getTime() - new Date(b.date || '').getTime();
    });

    return {
      labels: sortedAnalytics.map(item => {
        // Format date for display (e.g., "Jun 10")
        const date = new Date(item.date || '');
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }),
      datasets: [
        {
          label: 'Requests',
          data: sortedAnalytics.map(item => item.total_requests || 0),
          borderColor: 'rgb(99, 179, 237)',
          backgroundColor: 'rgba(99, 179, 237, 0.5)',
        },
        {
          label: 'Input Tokens',
          data: sortedAnalytics.map(item => item.total_input_tokens || 0),
          borderColor: 'rgb(72, 187, 120)',
          backgroundColor: 'rgba(72, 187, 120, 0.5)',
        },
        {
          label: 'Output Tokens',
          data: sortedAnalytics.map(item => item.total_output_tokens || 0),
          borderColor: 'rgb(237, 137, 54)',
          backgroundColor: 'rgba(237, 137, 54, 0.5)',
        }
      ]
    };
  };

  // Prepare chart data for query types
  const prepareQueryTypeData = () => {
    if (!analytics || analytics.length === 0) return { labels: [], datasets: [] };

    // Group by query type
    const queryTypes: Record<string, number> = {};
    analytics.forEach(item => {
      const type = item.query_type || 'Unknown';
      queryTypes[type] = (queryTypes[type] || 0) + (item.total_requests || 0);
    });

    // Convert to chart data
    const labels = Object.keys(queryTypes);
    const data = Object.values(queryTypes);

    // Define colors for pie chart
    const backgroundColors = [
      'rgba(99, 179, 237, 0.7)',
      'rgba(72, 187, 120, 0.7)',
      'rgba(237, 137, 54, 0.7)',
      'rgba(159, 122, 234, 0.7)',
      'rgba(237, 100, 166, 0.7)',
      'rgba(245, 158, 11, 0.7)'
    ];

    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: backgroundColors,
          borderWidth: 1
        }
      ]
    };
  };

  // Get metrics summaries
  const { totalRequests, successRate, avgResponseTime, totalTokens, fallbackRate } = getTotalMetrics();

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Model Analytics</CardTitle>
          <CardDescription>
            Performance metrics and usage statistics
          </CardDescription>
        </div>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">Last 24 Hours</SelectItem>
            <SelectItem value="7d">Last 7 Days</SelectItem>
            <SelectItem value="30d">Last 30 Days</SelectItem>
            <SelectItem value="90d">Last 90 Days</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-[200px] w-full" />
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <AlertTriangle className="h-10 w-10 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Unable to load analytics</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
          </div>
        ) : analytics.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <BarChart3 className="h-10 w-10 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No analytics data available</h3>
            <p className="text-muted-foreground mb-4">
              Analytics will appear here once the model processes requests
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Summary Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="text-sm text-muted-foreground mb-1">Total Requests</div>
                  <div className="text-2xl font-bold">{totalRequests.toLocaleString()}</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="text-sm text-muted-foreground mb-1">Success Rate</div>
                  <div className="text-2xl font-bold">{successRate}%</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="text-sm text-muted-foreground mb-1">Avg Response Time</div>
                  <div className="text-2xl font-bold">{avgResponseTime}s</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="text-sm text-muted-foreground mb-1">Total Tokens</div>
                  <div className="text-2xl font-bold">{totalTokens.toLocaleString()}</div>
                </CardContent>
              </Card>
            </div>

            {/* Charts */}
            <Tabs defaultValue="usage">
              <TabsList className="mb-4">
                <TabsTrigger value="usage" className="flex items-center gap-2">
                  <BarChart2 className="h-4 w-4" />
                  Usage Trends
                </TabsTrigger>
                <TabsTrigger value="types" className="flex items-center gap-2">
                  <PieChartIcon className="h-4 w-4" />
                  Query Types
                </TabsTrigger>
                <TabsTrigger value="performance" className="flex items-center gap-2">
                  <BarChart className="h-4 w-4" />
                  Performance
                </TabsTrigger>
              </TabsList>

              <TabsContent value="usage">
                <div className="h-[300px]">
                  <LineChart
                    data={prepareUsageData()}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true
                        }
                      }
                    }}
                  />
                </div>
              </TabsContent>

              <TabsContent value="types">
                <div className="h-[300px]">
                  <PieChart
                    data={prepareQueryTypeData()}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false
                    }}
                  />
                </div>
              </TabsContent>

              <TabsContent value="performance">
                <div className="h-[300px]">
                  <BarChartComponent
                    data={{
                      labels: ['Success Rate', 'Fallback Rate', 'Avg Response Time (s)', 'Avg Confidence'],
                      datasets: [
                        {
                          label: 'Performance Metrics',
                          data: [
                            successRate,
                            fallbackRate,
                            avgResponseTime,
                            analytics.reduce((avg, item) => avg + (item.avg_confidence_score || 0), 0) / analytics.length
                          ],
                          backgroundColor: [
                            'rgba(72, 187, 120, 0.7)',
                            'rgba(237, 137, 54, 0.7)',
                            'rgba(99, 179, 237, 0.7)',
                            'rgba(159, 122, 234, 0.7)'
                          ]
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true
                        }
                      }
                    }}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
