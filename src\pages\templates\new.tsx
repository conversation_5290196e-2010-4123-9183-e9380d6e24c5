"use client"

import React from "react"
import { useNavigate } from "react-router-dom"
import { PageHeader } from "@/components/page-header"
import { AdminLayout } from "@/components/admin-layout"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { TemplateForm } from "@/components/templates/template-form"
import { useToast } from "@/components/ui/use-toast"

export default function NewTemplatePage() {
    const navigate = useNavigate()
    const { toast } = useToast()

    const handleSuccess = () => {
        toast({
            title: "Success",
            description: "Template created successfully."
        })
        navigate("/templates")
    }

    const handleCancel = () => {
        navigate("/templates")
    }

    return (
        <AdminLayout>
            <div className="w-full">
                <PageHeader
                    title="Create Template"
                    description="Create a new prompt template for your AI models"
                    actions={
                        <Button
                            variant="outline"
                            className="flex items-center gap-1"
                            onClick={handleCancel}
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Templates
                        </Button>
                    }
                />

                <div className="mt-6">
                    <TemplateForm
                        onSuccess={handleSuccess}
                        onCancel={handleCancel}
                    />
                </div>
            </div>
        </AdminLayout>
    )
} 