<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KnowledgeScrapedUrl extends Model
{
    use HasFactory;

    protected $fillable = [
        'source_id',
        'url',
        'title',
        'raw_content',
        'text_content',
        'table_content',
        'json_content',
        'status',
        'metadata',
        'project_id',
        'table_name',
        'created_by',
    ];

    protected $casts = [
        'metadata' => 'array',
        'json_content' => 'array',
    ];

    /**
     * Get the source that owns the scraped URL.
     */
    public function source()
    {
        return $this->belongsTo(KnowledgeSource::class, 'source_id');
    }

    /**
     * Get the user who created the scraped URL.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
