<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateKnowledgeDocumentEmbeddingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('knowledge_document_embeddings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->constrained('knowledge_documents')->onDelete('cascade');
            $table->integer('chunk_index');
            $table->text('chunk_text');
            $table->json('embedding');
            $table->string('provider');
            $table->string('model');
            $table->integer('dimensions');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Add index for faster lookups
            $table->index(['document_id', 'chunk_index']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('knowledge_document_embeddings');
    }
}
