<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class AuditLogService
{
    /**
     * Log an action
     *
     * @param string $action
     * @param string $modelType
     * @param int|null $modelId
     * @param int|null $userId
     * @param array $properties
     * @return bool
     */
    public function log(string $action, string $modelType, ?int $modelId = null, ?int $userId = null, array $properties = []): bool
    {
        try {
            DB::table('audit_logs')->insert([
                'action' => $action,
                'model_type' => $modelType,
                'model_id' => $modelId,
                'user_id' => $userId,
                'properties' => json_encode($properties),
                'created_at' => now(),
            ]);
            
            return true;
        } catch (Exception $e) {
            Log::error("Error logging audit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log a document action
     *
     * @param string $action
     * @param int $documentId
     * @param int|null $userId
     * @param array $properties
     * @return bool
     */
    public function logDocumentAction(string $action, int $documentId, ?int $userId = null, array $properties = []): bool
    {
        return $this->log($action, 'document', $documentId, $userId, $properties);
    }
    
    /**
     * Log a source action
     *
     * @param string $action
     * @param int $sourceId
     * @param int|null $userId
     * @param array $properties
     * @return bool
     */
    public function logSourceAction(string $action, int $sourceId, ?int $userId = null, array $properties = []): bool
    {
        return $this->log($action, 'source', $sourceId, $userId, $properties);
    }
    
    /**
     * Log a scrape action
     *
     * @param string $action
     * @param int $scrapeId
     * @param int|null $userId
     * @param array $properties
     * @return bool
     */
    public function logScrapeAction(string $action, int $scrapeId, ?int $userId = null, array $properties = []): bool
    {
        return $this->log($action, 'scrape', $scrapeId, $userId, $properties);
    }
    
    /**
     * Log a database action
     *
     * @param string $action
     * @param int $connectionId
     * @param int|null $userId
     * @param array $properties
     * @return bool
     */
    public function logDatabaseAction(string $action, int $connectionId, ?int $userId = null, array $properties = []): bool
    {
        return $this->log($action, 'database', $connectionId, $userId, $properties);
    }
    
    /**
     * Log a context action
     *
     * @param string $action
     * @param int $contextId
     * @param int|null $userId
     * @param array $properties
     * @return bool
     */
    public function logContextAction(string $action, int $contextId, ?int $userId = null, array $properties = []): bool
    {
        return $this->log($action, 'context', $contextId, $userId, $properties);
    }
}
