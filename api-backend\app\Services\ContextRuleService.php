<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\ContextRule;
use App\Models\ContextSetting;
use App\Models\Project;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

class ContextRuleService
{
    /**
     * @var VectorEmbeddingService
     */
    protected $embeddingService;

    /**
     * Constructor
     *
     * @param VectorEmbeddingService $embeddingService
     */
    public function __construct(VectorEmbeddingService $embeddingService)
    {
        $this->embeddingService = $embeddingService;
    }

    /**
     * Get context settings for a project
     *
     * @param int $projectId
     * @return array
     */
    public function getContextSettings(int $projectId): array
    {
        $settings = ContextSetting::where('project_id', $projectId)->first();

        if (!$settings) {
            // Create default settings
            $settings = ContextSetting::create([
                'project_id' => $projectId,
                'priority' => [
                    'documents' => 80,
                    'database' => 60,
                    'web' => 40
                ],
                'context_retention' => 'session',
                'relevance_threshold' => 0.75,
                'max_sources_per_query' => 3,
                'enabled_sources' => [
                    'documents' => true,
                    'database' => true,
                    'web' => false
                ]
            ]);
        }

        return $settings->toArray();
    }

    /**
     * Update context settings for a project
     *
     * @param int $projectId
     * @param array $data
     * @return ContextSetting
     */
    public function updateContextSettings(int $projectId, array $data): ContextSetting
    {
        $settings = ContextSetting::where('project_id', $projectId)->first();

        if (!$settings) {
            $settings = new ContextSetting();
            $settings->project_id = $projectId;
        }

        // Update settings
        if (isset($data['priority'])) {
            $settings->priority = $data['priority'];
        }

        if (isset($data['contextRetention'])) {
            $settings->context_retention = $data['contextRetention'];
        }

        if (isset($data['relevanceThreshold'])) {
            $settings->relevance_threshold = $data['relevanceThreshold'];
        }

        if (isset($data['maxSourcesPerQuery'])) {
            $settings->max_sources_per_query = $data['maxSourcesPerQuery'];
        }

        if (isset($data['enabledSources'])) {
            $settings->enabled_sources = $data['enabledSources'];
        }

        $settings->save();

        return $settings;
    }

    /**
     * Get all context rules for a project
     *
     * @param int $projectId
     * @return Collection
     */
    public function getContextRules(int $projectId): Collection
    {
        return ContextRule::where('project_id', $projectId)
            ->orderBy('priority')
            ->get();
    }

    /**
     * Save a new context rule
     *
     * @param int $projectId
     * @param array $data
     * @return ContextRule
     */
    public function saveContextRule(int $projectId, array $data): ContextRule
    {
        $rule = new ContextRule();
        $rule->project_id = $projectId;
        $rule->name = $data['name'];
        $rule->description = $data['description'] ?? null;
        $rule->sources = $data['sources'] ?? [];
        $rule->keywords = $data['keywords'] ?? [];
        $rule->priority = $data['priority'] ?? 10;
        $rule->active = $data['active'] ?? true;
        $rule->save();

        return $rule;
    }

    /**
     * Update an existing context rule
     *
     * @param int $projectId
     * @param int $ruleId
     * @param array $data
     * @return ContextRule
     */
    public function updateContextRule(int $projectId, int $ruleId, array $data): ContextRule
    {
        $rule = ContextRule::where('project_id', $projectId)
            ->where('id', $ruleId)
            ->firstOrFail();

        if (isset($data['name'])) {
            $rule->name = $data['name'];
        }

        if (isset($data['description'])) {
            $rule->description = $data['description'];
        }

        if (isset($data['sources'])) {
            $rule->sources = $data['sources'];
        }

        if (isset($data['keywords'])) {
            $rule->keywords = $data['keywords'];
        }

        if (isset($data['priority'])) {
            $rule->priority = $data['priority'];
        }

        if (isset($data['active'])) {
            $rule->active = $data['active'];
        }

        $rule->save();

        return $rule;
    }

    /**
     * Delete a context rule
     *
     * @param int $projectId
     * @param int $ruleId
     * @return bool
     */
    public function deleteContextRule(int $projectId, int $ruleId): bool
    {
        return ContextRule::where('project_id', $projectId)
            ->where('id', $ruleId)
            ->delete() > 0;
    }

    /**
     * Find matching rules for a query
     *
     * @param string $query
     * @param int $projectId
     * @return Collection
     */
    public function findMatchingRules(string $query, int $projectId): Collection
    {
        $rules = ContextRule::where('project_id', $projectId)
            ->where('active', true)
            ->get();

        $matchedRules = collect();

        foreach ($rules as $rule) {
            $score = $this->calculateRuleMatchScore($query, $rule);

            if ($score > 0.5) { // Threshold for matching
                $rule->match_score = $score;
                $matchedRules->push($rule);
            }
        }

        return $matchedRules->sortByDesc('match_score');
    }

    /**
     * Calculate match score for a rule
     *
     * @param string $query
     * @param ContextRule $rule
     * @return float
     */
    protected function calculateRuleMatchScore(string $query, ContextRule $rule): float
    {
        $score = 0;
        $queryLower = strtolower($query);

        // Check for keyword matches
        foreach ($rule->keywords as $keyword) {
            if (strpos($queryLower, strtolower($keyword)) !== false) {
                $score += 0.2;
            }
        }

        // Use semantic similarity if available
        try {
            // Create a combined text from keywords for semantic matching
            $ruleText = implode(' ', $rule->keywords) . ' ' . $rule->name . ' ' . ($rule->description ?? '');

            // Use vector similarity to calculate semantic match
            $similarity = $this->calculateSimilarity($query, $ruleText);
            $score += $similarity * 0.8; // Weight semantic matching higher
        } catch (Exception $e) {
            Log::warning("Error calculating semantic similarity: " . $e->getMessage());
            // If semantic matching fails, rely more on keyword matching
            $score *= 1.5;
        }

        return min(1.0, $score); // Cap at 1.0
    }

    /**
     * Calculate semantic similarity between two texts
     *
     * @param string $text1
     * @param string $text2
     * @return float
     */
    protected function calculateSimilarity(string $text1, string $text2): float
    {
        // Simple implementation - in production, use proper vector embeddings
        $words1 = array_count_values(str_word_count(strtolower($text1), 1));
        $words2 = array_count_values(str_word_count(strtolower($text2), 1));

        $intersection = array_intersect_key($words1, $words2);
        $union = array_unique(array_merge(array_keys($words1), array_keys($words2)));

        if (empty($union)) {
            return 0;
        }

        return count($intersection) / count($union);
    }

    /**
     * Test a query against context rules
     *
     * @param int $projectId
     * @param string $query
     * @return array
     */
    public function testContextRule(int $projectId, string $query): array
    {
        $matchedRules = $this->findMatchingRules($query, $projectId);

        $results = [
            'query' => $query,
            'matches' => []
        ];

        foreach ($matchedRules as $rule) {
            $results['matches'][] = [
                'rule_id' => $rule->id,
                'name' => $rule->name,
                'score' => $rule->match_score,
                'sources' => $rule->sources
            ];
        }

        return $results;
    }

    /**
     * Apply context rules to enhance messages
     *
     * @param array $messages
     * @return array Enhanced messages
     */
    public function applyRules(array $messages): array
    {
        try {
            // For now, just return the messages as-is
            // In a full implementation, this would:
            // 1. Load relevant context rules based on message content
            // 2. Apply each rule to enhance message understanding
            // 3. Return the enhanced message array

            return $messages;
        } catch (\Exception $e) {
            Log::error('Error applying context rules: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            // In case of errors, return the original messages
            return $messages;
        }
    }

    /**
     * Test if a rule applies to a specific message
     *
     * @param int $ruleId
     * @param string $message
     * @return array Test result
     */
    public function testRule(int $ruleId, string $message): array
    {
        try {
            // Placeholder for rule testing logic
            return [
                'matches' => false,
                'rule_id' => $ruleId,
                'message' => 'Rule testing not yet implemented'
            ];
        } catch (\Exception $e) {
            Log::error('Error testing context rule: ' . $e->getMessage(), [
                'exception' => $e,
                'rule_id' => $ruleId
            ]);

            return [
                'matches' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
