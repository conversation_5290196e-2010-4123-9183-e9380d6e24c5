import { AdminLayout } from "@/components/admin-layout";
import { AIModelManagerRedesigned } from "@/components/ai-configuration/ai-model-manager-redesigned";
import { useSearchParams } from "react-router-dom";

const ModelManagement = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // Handle tab changes by updating URL params
  const handleTabChange = (value: string) => {
    searchParams.set("tab", value);
    setSearchParams(searchParams);
  };

  // Get the initial tab from URL or default to "basic"
  const initialTab = searchParams.get("tab") || "basic";

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-2xl font-bold">AI Model Management</h1>
          <p className="text-muted-foreground">
            Configure AI models, manage API keys, and control which models are used in your application
          </p>
        </div>

        <AIModelManagerRedesigned
          initialTab={initialTab}
          onTabChange={handleTabChange}
        />
      </div>
    </AdminLayout>
  );
};

export default ModelManagement;
