import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import {
    Network, RefreshCw, FileText, Database, Globe,
    Brain, Settings, BrainCircuit, LayoutList, Workflow
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'

export default function ContextTab({
    projects,
    selectedProjectId,
    setSelectedProjectId,
    refreshTrigger,
    onRefresh
}) {
    // State
    const [activeTab, setActiveTab] = useState('rules')
    const [isLoading, setIsLoading] = useState(false)
    const [contextSettings, setContextSettings] = useState({
        priority: {
            documents: 80,
            database: 60,
            web: 40
        },
        contextRetention: 'session',
        relevanceThreshold: 0.75,
        maxSourcesPerQuery: 3,
        enabledSources: {
            documents: true,
            database: true,
            web: false
        }
    })
    const [contextRules, setContextRules] = useState([])
    const [selectedRule, setSelectedRule] = useState(null)
    const [testQuery, setTestQuery] = useState('')
    const [testResults, setTestResults] = useState(null)

    // Fetch context settings and rules when project changes
    useEffect(() => {
        const fetchContextData = async () => {
            if (!selectedProjectId) return

            setIsLoading(true)

            try {
                // Fetch context settings
                const settingsResponse = await knowledgeBaseService.getContextSettings(selectedProjectId)
                if (settingsResponse?.data) {
                    setContextSettings(settingsResponse.data)
                }

                // Fetch context rules
                const rulesResponse = await knowledgeBaseService.getContextRules(selectedProjectId)
                if (rulesResponse?.data) {
                    setContextRules(rulesResponse.data)
                }
            } catch (error) {
                console.error('Failed to load context data:', error)
                toast.error('Failed to load context data')
            } finally {
                setIsLoading(false)
            }
        }

        fetchContextData()
    }, [selectedProjectId, refreshTrigger])

    // Handle rules changes
    const handleRulePriorityChange = async (id, newPriority) => {
        if (!selectedProjectId) return

        setIsLoading(true)

        try {
            const rule = contextRules.find(r => r.id === id)
            if (rule) {
                await knowledgeBaseService.updateContextRule(selectedProjectId, id, {
                    priority: newPriority
                })

                // Refresh rules
                const response = await knowledgeBaseService.getContextRules(selectedProjectId)
                if (response?.data) {
                    setContextRules(response.data)
                }

                toast.success('Rule priority updated')
            }
        } catch (error) {
            console.error('Failed to update rule priority:', error)
            toast.error('Failed to update rule priority')
        } finally {
            setIsLoading(false)
        }
    }

    // Handle rule selection
    const handleSelectRule = (rule) => {
        setSelectedRule(rule)
    }

    // Handle rule save
    const handleSaveRule = async () => {
        if (!selectedProjectId || !selectedRule) return

        setIsLoading(true)

        try {
            if (selectedRule.id) {
                // Update existing rule
                await knowledgeBaseService.updateContextRule(
                    selectedProjectId,
                    selectedRule.id,
                    selectedRule
                )
                toast.success('Rule updated successfully')
            } else {
                // Create new rule
                await knowledgeBaseService.saveContextRule(
                    selectedProjectId,
                    selectedRule
                )
                toast.success('Rule created successfully')
            }

            // Refresh rules
            const response = await knowledgeBaseService.getContextRules(selectedProjectId)
            if (response?.data) {
                setContextRules(response.data)
            }

            // Clear selection
            setSelectedRule(null)
        } catch (error) {
            console.error('Failed to save rule:', error)
            toast.error('Failed to save rule')
        } finally {
            setIsLoading(false)
        }
    }

    // Handle rule delete
    const handleDeleteRule = async (id) => {
        if (!selectedProjectId) return

        setIsLoading(true)

        try {
            await knowledgeBaseService.deleteContextRule(selectedProjectId, id)

            // Refresh rules
            const response = await knowledgeBaseService.getContextRules(selectedProjectId)
            if (response?.data) {
                setContextRules(response.data)
            }

            // Clear selection if the deleted rule was selected
            if (selectedRule && selectedRule.id === id) {
                setSelectedRule(null)
            }

            toast.success('Rule deleted successfully')
        } catch (error) {
            console.error('Failed to delete rule:', error)
            toast.error('Failed to delete rule')
        } finally {
            setIsLoading(false)
        }
    }

    // Handle settings changes
    const handlePriorityChange = (source, value) => {
        setContextSettings(prev => ({
            ...prev,
            priority: {
                ...prev.priority,
                [source]: value[0]
            }
        }))
    }

    const handleToggleSource = (source, enabled) => {
        setContextSettings(prev => ({
            ...prev,
            enabledSources: {
                ...prev.enabledSources,
                [source]: enabled
            }
        }))
    }

    // Save settings
    const handleSaveSettings = async () => {
        if (!selectedProjectId) return

        setIsLoading(true)

        try {
            await knowledgeBaseService.updateContextSettings(selectedProjectId, contextSettings)
            toast.success('Context settings saved successfully')
        } catch (error) {
            console.error('Failed to save context settings:', error)
            toast.error('Failed to save settings')
        } finally {
            setIsLoading(false)
        }
    }

    // Test context
    const handleTestContext = async () => {
        if (!selectedProjectId || !testQuery) return

        setIsLoading(true)

        try {
            const response = await knowledgeBaseService.testContextRule(selectedProjectId, testQuery)

            if (response?.data) {
                setTestResults(response.data)
                toast.success('Context test completed successfully')
            }
        } catch (error) {
            console.error('Failed to test context rule:', error)
            toast.error('Failed to test context rule')
            setTestResults(null)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="space-y-6">
            {/* Project selector and controls */}
            <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                    <ProjectSelector
                        projects={projects}
                        selectedProjectId={selectedProjectId}
                        setSelectedProjectId={setSelectedProjectId}
                    />

                    <Button
                        variant="outline"
                        size="icon"
                        onClick={onRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full max-w-md grid-cols-2">
                    <TabsTrigger value="rules" className="flex items-center gap-2">
                        <Brain className="h-4 w-4" /> Context Rules
                    </TabsTrigger>
                    <TabsTrigger value="settings" className="flex items-center gap-2">
                        <Settings className="h-4 w-4" /> Source Settings
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="rules" className="pt-4">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Rules overview panel */}
                        <Card className="lg:col-span-1">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-lg">
                                    <BrainCircuit className="h-5 w-5" /> Context Rules
                                </CardTitle>
                                <CardDescription>
                                    Define rules for prioritizing knowledge sources based on context
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="border rounded-md">
                                        <div className="bg-muted/50 p-3 border-b font-medium flex items-center justify-between">
                                            <span>Active Rules</span>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSelectRule({
                                                    name: '',
                                                    description: '',
                                                    sources: [],
                                                    keywords: [],
                                                    priority: 10,
                                                    active: true
                                                })}
                                            >
                                                Add Rule
                                            </Button>
                                        </div>
                                        <div className="divide-y">
                                            {contextRules.length > 0 ? (
                                                contextRules.map(rule => (
                                                    <div
                                                        key={rule.id}
                                                        className={`p-3 hover:bg-muted/30 cursor-pointer ${selectedRule?.id === rule.id ? 'bg-muted/50' : ''}`}
                                                        onClick={() => handleSelectRule(rule)}
                                                    >
                                                        <div className="flex items-center justify-between mb-1">
                                                            <h3 className="font-medium flex items-center gap-2">
                                                                <span className="bg-primary/10 text-primary w-6 h-6 rounded-full flex items-center justify-center text-xs">
                                                                    {rule.priority}
                                                                </span>
                                                                {rule.name}
                                                            </h3>
                                                            <div className="flex items-center gap-1">
                                                                <Button
                                                                    variant="ghost"
                                                                    size="icon"
                                                                    className="h-7 w-7"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        handleDeleteRule(rule.id);
                                                                    }}
                                                                >
                                                                    <LayoutList className="h-4 w-4" />
                                                                </Button>
                                                            </div>
                                                        </div>
                                                        <p className="text-xs text-muted-foreground mb-2">
                                                            {rule.description}
                                                        </p>
                                                        <div className="flex flex-wrap gap-1 mb-2">
                                                            {rule.keywords && rule.keywords.map((keyword, index) => (
                                                                <Badge key={index} variant="outline" className="text-xs">
                                                                    {keyword}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                        <div className="flex gap-1">
                                                            {rule.sources && rule.sources.map((source, index) => (
                                                                <Badge key={index} variant="secondary" className="text-xs flex items-center gap-1">
                                                                    {source === 'documents' && <FileText className="h-3 w-3" />}
                                                                    {source === 'database' && <Database className="h-3 w-3" />}
                                                                    {source === 'web' && <Globe className="h-3 w-3" />}
                                                                    {source}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="p-6 text-center text-muted-foreground">
                                                    <p>No context rules defined yet.</p>
                                                    <p className="text-sm mt-1">Click "Add Rule" to create your first rule.</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <p className="text-xs text-muted-foreground mb-2">
                                            Rules are evaluated from top to bottom.
                                            Drag to reorder priority.
                                        </p>
                                        <Button variant="outline" size="sm" className="w-full">
                                            <Workflow className="h-4 w-4 mr-2" />
                                            Test Rules
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Rule editor panel */}
                        <Card className="lg:col-span-2">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-lg">
                                    <Settings className="h-5 w-5" /> Rule Editor
                                </CardTitle>
                                <CardDescription>
                                    Configure how context influences knowledge retrieval
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-6">
                                    <div className="border rounded-md p-4">
                                        <h3 className="font-medium mb-4">
                                            {selectedRule?.id ? `Edit Rule: ${selectedRule.name}` : 'Create New Rule'}
                                        </h3>

                                        {selectedRule ? (
                                            <div className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="rule-name">Rule Name</Label>
                                                    <Input
                                                        id="rule-name"
                                                        value={selectedRule.name}
                                                        onChange={(e) => setSelectedRule({ ...selectedRule, name: e.target.value })}
                                                        placeholder="Enter a descriptive name"
                                                    />
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="rule-description">Description</Label>
                                                    <Input
                                                        id="rule-description"
                                                        value={selectedRule.description || ''}
                                                        onChange={(e) => setSelectedRule({ ...selectedRule, description: e.target.value })}
                                                        placeholder="Describe what this rule does"
                                                    />
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="rule-keywords">Trigger Keywords (comma separated)</Label>
                                                    <Input
                                                        id="rule-keywords"
                                                        value={selectedRule.keywords ? selectedRule.keywords.join(', ') : ''}
                                                        onChange={(e) => setSelectedRule({
                                                            ...selectedRule,
                                                            keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                                                        })}
                                                        placeholder="Enter keywords that trigger this rule"
                                                    />
                                                    <p className="text-xs text-muted-foreground">
                                                        These keywords will trigger this rule when found in user queries
                                                    </p>
                                                </div>

                                                <div className="space-y-2">
                                                    <Label>Knowledge Sources</Label>
                                                    <div className="grid grid-cols-3 gap-2">
                                                        <div
                                                            className={`border rounded-md p-3 flex flex-col items-center gap-2 cursor-pointer ${selectedRule.sources?.includes('database') ? 'bg-primary/5' : ''}`}
                                                            onClick={() => {
                                                                const sources = [...(selectedRule.sources || [])];
                                                                const index = sources.indexOf('database');
                                                                if (index >= 0) {
                                                                    sources.splice(index, 1);
                                                                } else {
                                                                    sources.push('database');
                                                                }
                                                                setSelectedRule({ ...selectedRule, sources });
                                                            }}
                                                        >
                                                            <Database className={`h-5 w-5 ${selectedRule.sources?.includes('database') ? 'text-primary' : 'text-muted-foreground'}`} />
                                                            <span className="text-sm font-medium">Database</span>
                                                            <Badge variant={selectedRule.sources?.includes('database') ? "default" : "outline"} className="text-xs">
                                                                {selectedRule.sources?.includes('database') ? 'Selected' : 'Not Selected'}
                                                            </Badge>
                                                        </div>
                                                        <div
                                                            className={`border rounded-md p-3 flex flex-col items-center gap-2 cursor-pointer ${selectedRule.sources?.includes('documents') ? 'bg-primary/5' : ''}`}
                                                            onClick={() => {
                                                                const sources = [...(selectedRule.sources || [])];
                                                                const index = sources.indexOf('documents');
                                                                if (index >= 0) {
                                                                    sources.splice(index, 1);
                                                                } else {
                                                                    sources.push('documents');
                                                                }
                                                                setSelectedRule({ ...selectedRule, sources });
                                                            }}
                                                        >
                                                            <FileText className={`h-5 w-5 ${selectedRule.sources?.includes('documents') ? 'text-primary' : 'text-muted-foreground'}`} />
                                                            <span className="text-sm font-medium">Documents</span>
                                                            <Badge variant={selectedRule.sources?.includes('documents') ? "default" : "outline"} className="text-xs">
                                                                {selectedRule.sources?.includes('documents') ? 'Selected' : 'Not Selected'}
                                                            </Badge>
                                                        </div>
                                                        <div
                                                            className={`border rounded-md p-3 flex flex-col items-center gap-2 cursor-pointer ${selectedRule.sources?.includes('web') ? 'bg-primary/5' : ''}`}
                                                            onClick={() => {
                                                                const sources = [...(selectedRule.sources || [])];
                                                                const index = sources.indexOf('web');
                                                                if (index >= 0) {
                                                                    sources.splice(index, 1);
                                                                } else {
                                                                    sources.push('web');
                                                                }
                                                                setSelectedRule({ ...selectedRule, sources });
                                                            }}
                                                        >
                                                            <Globe className={`h-5 w-5 ${selectedRule.sources?.includes('web') ? 'text-primary' : 'text-muted-foreground'}`} />
                                                            <span className="text-sm font-medium">Web</span>
                                                            <Badge variant={selectedRule.sources?.includes('web') ? "default" : "outline"} className="text-xs">
                                                                {selectedRule.sources?.includes('web') ? 'Selected' : 'Not Selected'}
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="active-switch">Rule Active</Label>
                                                        <Switch
                                                            id="active-switch"
                                                            checked={selectedRule.active}
                                                            onCheckedChange={(checked) => setSelectedRule({ ...selectedRule, active: checked })}
                                                        />
                                                    </div>
                                                    <p className="text-xs text-muted-foreground">
                                                        Inactive rules will not be applied to queries
                                                    </p>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="p-6 text-center text-muted-foreground">
                                                <p>Select a rule to edit or click "Add Rule" to create a new one.</p>
                                            </div>
                                        )}

                                        <div className="flex justify-end gap-2 mt-6">
                                            <Button
                                                variant="outline"
                                                onClick={() => setSelectedRule(null)}
                                            >
                                                Cancel
                                            </Button>
                                            <Button
                                                variant="default"
                                                onClick={handleSaveRule}
                                                disabled={!selectedRule || !selectedRule.name}
                                            >
                                                Save Rule
                                            </Button>
                                        </div>
                                    </div>

                                    <div className="border rounded-md p-4">
                                        <h3 className="font-medium mb-4">Test Context Rule</h3>

                                        <div className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="test-query">Test Query</Label>
                                                <Input
                                                    id="test-query"
                                                    placeholder="Enter a query to test against rules"
                                                    value={testQuery}
                                                    onChange={(e) => setTestQuery(e.target.value)}
                                                />
                                            </div>

                                            {testResults ? (
                                                <div className="border rounded-md p-3 bg-muted/10">
                                                    <h4 className="text-sm font-medium mb-2">Test Results:</h4>
                                                    <div className="space-y-2">
                                                        {testResults.matches && testResults.matches.length > 0 ? (
                                                            testResults.matches.map((match, index) => (
                                                                <div key={index} className="space-y-2">
                                                                    <div className="flex items-center justify-between">
                                                                        <div className="flex items-center gap-2">
                                                                            <span className="bg-green-500/10 text-green-500 rounded-full w-5 h-5 flex items-center justify-center text-xs">✓</span>
                                                                            <span className="text-sm">Rule matched: <strong>{match.name}</strong></span>
                                                                        </div>
                                                                        <Badge variant="outline" className="text-xs">
                                                                            Score: {match.score.toFixed(2)}
                                                                        </Badge>
                                                                    </div>

                                                                    <div className="pl-7 space-y-1 text-sm">
                                                                        <p className="flex items-center gap-2">
                                                                            <span className="text-xs text-muted-foreground">Primary source:</span>
                                                                            {match.sources && match.sources.map((source, idx) => (
                                                                                <Badge key={idx} className="text-xs flex items-center gap-1">
                                                                                    {source === 'documents' && <FileText className="h-3 w-3" />}
                                                                                    {source === 'database' && <Database className="h-3 w-3" />}
                                                                                    {source === 'web' && <Globe className="h-3 w-3" />}
                                                                                    {source}
                                                                                </Badge>
                                                                            ))}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            ))
                                                        ) : (
                                                            <div className="text-center py-2 text-muted-foreground">
                                                                <p>No matching rules found for this query.</p>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="border rounded-md p-4 text-center text-muted-foreground">
                                                    <p>Enter a query and click "Run Test" to see which rules match.</p>
                                                </div>
                                            )}

                                            <div className="flex justify-end">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={handleTestContext}
                                                    disabled={!testQuery || isLoading}
                                                >
                                                    Run Test
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="settings" className="pt-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <Settings className="h-5 w-5" /> Knowledge Source Settings
                            </CardTitle>
                            <CardDescription>
                                Configure how different knowledge sources are used in context
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                <div className="border rounded-md p-4">
                                    <h3 className="font-medium mb-4">Source Priorities</h3>

                                    <div className="space-y-4">
                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <FileText className="h-4 w-4 text-primary" />
                                                    <Label>Documents</Label>
                                                </div>
                                                <Switch
                                                    checked={contextSettings.enabledSources.documents}
                                                    onCheckedChange={(checked) => handleToggleSource('documents', checked)}
                                                />
                                            </div>
                                            <Slider
                                                defaultValue={[contextSettings.priority.documents]}
                                                max={100}
                                                step={1}
                                                disabled={!contextSettings.enabledSources.documents}
                                                onValueChange={(value) => handlePriorityChange('documents', value)}
                                            />
                                            <div className="flex justify-between text-xs text-muted-foreground">
                                                <span>Lower Priority</span>
                                                <span>{contextSettings.priority.documents}%</span>
                                                <span>Higher Priority</span>
                                            </div>
                                        </div>

                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <Database className="h-4 w-4 text-primary" />
                                                    <Label>Database</Label>
                                                </div>
                                                <Switch
                                                    checked={contextSettings.enabledSources.database}
                                                    onCheckedChange={(checked) => handleToggleSource('database', checked)}
                                                />
                                            </div>
                                            <Slider
                                                defaultValue={[contextSettings.priority.database]}
                                                max={100}
                                                step={1}
                                                disabled={!contextSettings.enabledSources.database}
                                                onValueChange={(value) => handlePriorityChange('database', value)}
                                            />
                                            <div className="flex justify-between text-xs text-muted-foreground">
                                                <span>Lower Priority</span>
                                                <span>{contextSettings.priority.database}%</span>
                                                <span>Higher Priority</span>
                                            </div>
                                        </div>

                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <Globe className="h-4 w-4 text-primary" />
                                                    <Label>Web Sources</Label>
                                                </div>
                                                <Switch
                                                    checked={contextSettings.enabledSources.web}
                                                    onCheckedChange={(checked) => handleToggleSource('web', checked)}
                                                />
                                            </div>
                                            <Slider
                                                defaultValue={[contextSettings.priority.web]}
                                                max={100}
                                                step={1}
                                                disabled={!contextSettings.enabledSources.web}
                                                onValueChange={(value) => handlePriorityChange('web', value)}
                                            />
                                            <div className="flex justify-between text-xs text-muted-foreground">
                                                <span>Lower Priority</span>
                                                <span>{contextSettings.priority.web}%</span>
                                                <span>Higher Priority</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="border rounded-md p-4">
                                        <h3 className="font-medium mb-4">Relevance Settings</h3>

                                        <div className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="threshold-slider">Relevance Threshold</Label>
                                                <Slider
                                                    id="threshold-slider"
                                                    defaultValue={[contextSettings.relevanceThreshold * 100]}
                                                    max={100}
                                                    step={1}
                                                    onValueChange={(value) => setContextSettings(prev => ({
                                                        ...prev,
                                                        relevanceThreshold: value[0] / 100
                                                    }))}
                                                />
                                                <div className="flex justify-between text-xs text-muted-foreground">
                                                    <span>Lower (more results)</span>
                                                    <span>{Math.round(contextSettings.relevanceThreshold * 100)}%</span>
                                                    <span>Higher (fewer results)</span>
                                                </div>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="max-sources">Maximum Sources Per Query</Label>
                                                <Select
                                                    value={String(contextSettings.maxSourcesPerQuery)}
                                                    onValueChange={(value) => setContextSettings(prev => ({
                                                        ...prev,
                                                        maxSourcesPerQuery: Number(value)
                                                    }))}
                                                >
                                                    <SelectTrigger id="max-sources">
                                                        <SelectValue placeholder="Select number" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="1">1 source</SelectItem>
                                                        <SelectItem value="2">2 sources</SelectItem>
                                                        <SelectItem value="3">3 sources</SelectItem>
                                                        <SelectItem value="5">5 sources</SelectItem>
                                                        <SelectItem value="10">10 sources</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <p className="text-xs text-muted-foreground">
                                                    Maximum number of knowledge sources to include in a single query
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="border rounded-md p-4">
                                        <h3 className="font-medium mb-4">Context Memory</h3>

                                        <div className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="retention-select">Context Retention</Label>
                                                <Select
                                                    value={contextSettings.contextRetention}
                                                    onValueChange={(value) => setContextSettings(prev => ({
                                                        ...prev,
                                                        contextRetention: value
                                                    }))}
                                                >
                                                    <SelectTrigger id="retention-select">
                                                        <SelectValue placeholder="Select retention period" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="session">Session Only</SelectItem>
                                                        <SelectItem value="day">24 Hours</SelectItem>
                                                        <SelectItem value="week">One Week</SelectItem>
                                                        <SelectItem value="month">One Month</SelectItem>
                                                        <SelectItem value="permanent">Permanent</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <p className="text-xs text-muted-foreground">
                                                    How long to retain session context for knowledge retrieval
                                                </p>
                                            </div>

                                            <div className="space-y-2">
                                                <div className="flex items-center justify-between">
                                                    <Label htmlFor="user-history-switch">Include User History</Label>
                                                    <Switch id="user-history-switch" defaultChecked={true} />
                                                </div>
                                                <p className="text-xs text-muted-foreground">
                                                    Use previous user interactions to improve knowledge retrieval
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button variant="outline">
                                        Reset to Defaults
                                    </Button>
                                    <Button onClick={handleSaveSettings}>
                                        Save Settings
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
}