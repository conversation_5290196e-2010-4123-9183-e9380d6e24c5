"use client"

import React, { ReactNode } from "react"
import { UseFormReturn } from "react-hook-form"
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

interface TextareaFieldProps {
  form: UseFormReturn<any>
  name: string
  label: string
  placeholder?: string
  description?: string
  required?: boolean
  icon?: ReactNode
  className?: string
  disabled?: boolean
  rows?: number
}

/**
 * Textarea Field Component
 * 
 * A form field for multiline text input with optional icon.
 */
export function TextareaField({
  form,
  name,
  label,
  placeholder,
  description,
  required = false,
  icon,
  className,
  disabled = false,
  rows = 3
}: TextareaFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <div className="flex items-center justify-between">
            <FormLabel className="flex items-center gap-1 text-sm font-medium">
              {label}
              {required && <span className="text-red-500">*</span>}
            </FormLabel>
            {icon && <div className="text-muted-foreground">{icon}</div>}
          </div>
          <FormControl>
            <Textarea
              placeholder={placeholder}
              className="bg-background resize-none min-h-[80px] transition-colors focus-visible:ring-1"
              disabled={disabled}
              rows={rows}
              {...field}
            />
          </FormControl>
          {description && <FormDescription className="text-xs">{description}</FormDescription>}
          <FormMessage className="text-xs font-medium" />
        </FormItem>
      )}
    />
  )
}
