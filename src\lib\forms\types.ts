/**
 * Pre-Chat Form Types
 * These types define the structure of pre-chat forms.
 */

export interface PreChatFormTemplate {
    id: number;
    widgetId: number;
    title: string;
    description: string;
    fields: PreChatFormField[];
    createdAt: string;
    updatedAt: string;
}

export interface PreChatFormField {
    id: string;
    templateId: number;
    name: string;
    label: string;
    type: 'text' | 'email' | 'phone' | 'select' | 'checkbox';
    placeholder?: string;
    options?: string[]; // For select fields
    isRequired: boolean;
    validationPattern?: string;
    errorMessage?: string;
    order: number;
}

export interface PreChatFormSubmission {
    id: number;
    templateId: number;
    sessionId: string;
    data: Record<string, any>; // Field name to value mapping
    createdAt: string;
}

/**
 * State management interface for the pre-chat form
 */
export interface PreChatFormState {
    isVisible: boolean;
    template: PreChatFormTemplate | null;
    formData: Record<string, any>;
    errors: Record<string, string>;
    isSubmitting: boolean;
    isSubmitted: boolean;
}

/**
 * Form validation interface
 */
export interface FormValidationResult {
    isValid: boolean;
    errors: Record<string, string>;
} 