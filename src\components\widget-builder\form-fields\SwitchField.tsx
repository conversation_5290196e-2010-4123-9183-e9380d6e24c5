/**
 * Switch Field Component
 * 
 * A form field for boolean toggle input.
 */

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormDescription } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';

interface SwitchFieldProps {
  label: string;
  fieldName: string;
  control: Control<any>;
  description?: string;
  disabled?: boolean;
}

/**
 * Switch Field Component
 * A form field for boolean toggle input
 */
export function SwitchField({
  label,
  fieldName,
  control,
  description,
  disabled = false
}: SwitchFieldProps) {
  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
          <div className="space-y-0.5">
            <FormLabel>{label}</FormLabel>
            {description && <FormDescription>{description}</FormDescription>}
          </div>
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={disabled}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
