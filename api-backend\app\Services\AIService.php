<?php

namespace App\Services;

use App\Models\AIModel;
use App\Models\ModelUsageLog;
use App\Services\AI\ProviderInterface;
use App\Services\AI\ProviderRegistry;
use App\Services\AI\ProviderManager;
use Illuminate\Support\Facades\Log;
use App\Services\TemplateProcessingService;
use App\Services\KnowledgeBaseRetrievalService;

class AIService
{
    protected $modelSelector;
    protected $providerRegistry;
    protected $providerManager;
    protected $contextRuleService;
    protected $templateProcessingService;
    protected $knowledgeBaseRetrievalService;

    public function __construct(
        AIModelSelector $modelSelector,
        ProviderRegistry $providerRegistry,
        ProviderManager $providerManager,
        ContextRuleService $contextRuleService,
        TemplateProcessingService $templateProcessingService,
        KnowledgeBaseRetrievalService $knowledgeBaseRetrievalService
    ) {
        $this->modelSelector = $modelSelector;
        $this->providerRegistry = $providerRegistry;
        $this->providerManager = $providerManager;
        $this->contextRuleService = $contextRuleService;
        $this->templateProcessingService = $templateProcessingService;
        $this->knowledgeBaseRetrievalService = $knowledgeBaseRetrievalService;
    }

    /**
     * Process a message with the appropriate AI model.
     *
     * @param  array  $messages
     * @param  \App\Models\AIModel|null  $aiModel
     * @param  array|null  $widgetSettings
     * @param  array|null  $context
     * @return array
     */
    public function processMessage(array $messages, ?AIModel $aiModel = null, ?array $widgetSettings = null, ?array $context = null)
    {
        $startTime = microtime(true);
        $context = $context ?? [];
        $context['widget_settings'] = $widgetSettings;

        // If no specific AI model is provided, use the selector to find the best one
        if (!$aiModel) {
            $aiModel = $this->modelSelector->selectModel($context);

            // If still no AI model, return an error message
            if (!$aiModel) {
                return [
                    'content' => 'Sorry, no AI model is currently configured. Please contact support.',
                    'metadata' => ['error' => 'no_ai_model_configured'],
                ];
            }
        }

        // Get model settings with fallbacks
        $modelSettings = $aiModel->settings ?? [];
        $temperature = $modelSettings['temperature'] ?? 0.7;
        $maxTokens = $modelSettings['max_tokens'] ?? 500;
        $confidenceThreshold = $aiModel->confidence_threshold ?? 0.7;

        // Get the fallback chain for this model
        $fallbackChain = $this->modelSelector->getFallbackChain($aiModel);
        $usedFallback = false;
        $lastError = null;
        $tokensInput = $this->countTokens($messages);
        $tokensOutput = 0;
        $confidenceScore = null;

        // Enhanced context with knowledge base
        $enhancedMessages = $this->enhanceMessagesWithKnowledgeBase($messages, $context);

        // Try each model in the fallback chain
        foreach ($fallbackChain as $model) {
            try {
                // Get the appropriate provider
                $provider = $this->getProviderForModel($model);

                // Process the message with the provider
                $response = $provider->processMessage($model, $enhancedMessages, [
                    'temperature' => $temperature,
                    'max_tokens' => $maxTokens,
                    'widget_settings' => $widgetSettings
                ]);

                // Estimate token count for output
                $tokensOutput = $this->countTokens([$response['content']]);

                // Extract or calculate confidence score
                $confidenceScore = $response['metadata']['confidence'] ?? 1.0;

                // Check if the response meets the confidence threshold
                if ($confidenceScore >= $confidenceThreshold) {
                    // Log the successful response
                    $this->logModelUsage(
                        $model->id,
                        $context['user_id'] ?? null,
                        $context['tenant_id'] ?? null,
                        $context['widget_id'] ?? null,
                        $context['query_type'] ?? null,
                        $context['use_case'] ?? null,
                        $tokensInput,
                        $tokensOutput,
                        microtime(true) - $startTime,
                        $confidenceScore,
                        $usedFallback,
                        true,
                        null
                    );

                    // Add information about knowledge base sources used
                    if (isset($context['knowledge_sources_used'])) {
                        $response['metadata']['knowledge_sources_used'] = $context['knowledge_sources_used'];
                    }

                    return $response;
                }

                // If we're here, response didn't meet confidence threshold
                $usedFallback = true;
                $lastError = "Response didn't meet confidence threshold of {$confidenceThreshold}";

            } catch (\Exception $e) {
                Log::error("AI processing error with {$model->provider}: " . $e->getMessage());
                $usedFallback = true;
                $lastError = $e->getMessage();
            }
        }

        // Log the failed attempt
        $this->logModelUsage(
            $aiModel->id,
            $context['user_id'] ?? null,
            $context['tenant_id'] ?? null,
            $context['widget_id'] ?? null,
            $context['query_type'] ?? null,
            $context['use_case'] ?? null,
            $tokensInput,
            $tokensOutput,
            microtime(true) - $startTime,
            $confidenceScore,
            $usedFallback,
            false,
            $lastError
        );

        return [
            'content' => 'Sorry, I encountered an error while processing your request. Please try again later.',
            'metadata' => ['error' => $lastError],
        ];
    }

    /**
     * Enhance messages with knowledge base context
     *
     * @param array $messages
     * @param array $context
     * @return array
     */
    protected function enhanceMessagesWithKnowledgeBase(array $messages, array &$context): array
    {
        // Skip if knowledge base enhancement is disabled or no project ID
        if (isset($context['use_knowledge_base']) && $context['use_knowledge_base'] === false) {
            return $messages;
        }

        if (empty($context['project_id'])) {
            return $messages;
        }

        try {
            // Extract the latest user query for knowledge retrieval
            $lastUserMessage = '';
            foreach (array_reverse($messages) as $message) {
                if (isset($message['role']) && $message['role'] === 'user' && !empty($message['content'])) {
                    $lastUserMessage = $message['content'];
                    break;
                }
            }

            if (empty($lastUserMessage)) {
                return $messages;
            }

            // Retrieve knowledge for the query
            $knowledgeData = $this->knowledgeBaseRetrievalService->retrieveKnowledgeForQuery(
                $lastUserMessage,
                $context['project_id'],
                [
                    'limit' => $context['max_sources'] ?? 3,
                    'threshold' => $context['relevance_threshold'] ?? 0.7
                ]
            );

            // If no context was retrieved, return the original messages
            if (!$knowledgeData['context_retrieved']) {
                return $messages;
            }

            // Format knowledge for the AI prompt
            $knowledgeContext = $this->knowledgeBaseRetrievalService->formatKnowledgeForAIPrompt($knowledgeData);

            if (empty($knowledgeContext)) {
                return $messages;
            }

            // Store information about which sources were used
            $context['knowledge_sources_used'] = $knowledgeData['sources_used'];

            // Create a new array of messages with knowledge context
            $enhancedMessages = [];

            // First, add any system messages
            $systemMessageFound = false;
            foreach ($messages as $message) {
                if (isset($message['role']) && $message['role'] === 'system') {
                    // Enhance system message with knowledge context
                    $enhancedMessages[] = [
                        'role' => 'system',
                        'content' => $message['content'] . "\n\n" . $knowledgeContext
                    ];
                    $systemMessageFound = true;
                } else {
                    $enhancedMessages[] = $message;
                }
            }

            // If no system message was found, add one with the knowledge context
            if (!$systemMessageFound) {
                array_unshift($enhancedMessages, [
                    'role' => 'system',
                    'content' => $knowledgeContext
                ]);
            }

            return $enhancedMessages;
        } catch (\Exception $e) {
            Log::error('Error enhancing messages with knowledge base: ' . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Return original messages if anything goes wrong
            return $messages;
        }
    }

    /**
     * Get the appropriate AI provider for the given model.
     *
     * @param  \App\Models\AIModel  $aiModel
     * @return \App\Services\AI\ProviderInterface
     * @throws \Exception
     */
    private function getProviderForModel(AIModel $aiModel): ProviderInterface
    {
        $provider = $this->providerRegistry->getProvider($aiModel->provider);

        if (!$provider) {
            throw new \Exception("Unsupported AI provider: {$aiModel->provider}");
        }

        return $provider;
    }

    /**
     * Log the AI model usage
     *
     * @param int $modelId
     * @param int|null $userId
     * @param int|null $tenantId
     * @param int|null $widgetId
     * @param string|null $queryType
     * @param string|null $useCase
     * @param int $tokensInput
     * @param int $tokensOutput
     * @param float $responseTime
     * @param float|null $confidenceScore
     * @param bool $fallbackUsed
     * @param bool $success
     * @param string|null $errorMessage
     * @return void
     */
    private function logModelUsage(
        $modelId,
        $userId,
        $tenantId,
        $widgetId,
        $queryType,
        $useCase,
        $tokensInput,
        $tokensOutput,
        $responseTime,
        $confidenceScore,
        $fallbackUsed,
        $success,
        $errorMessage
    ) {
        try {
            ModelUsageLog::create([
                'model_id' => $modelId,
                'user_id' => $userId,
                'tenant_id' => $tenantId,
                'widget_id' => $widgetId,
                'query_type' => $queryType,
                'use_case' => $useCase,
                'tokens_input' => $tokensInput,
                'tokens_output' => $tokensOutput,
                'response_time' => $responseTime,
                'confidence_score' => $confidenceScore,
                'fallback_used' => $fallbackUsed,
                'success' => $success,
                'error_message' => $errorMessage,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log model usage: ' . $e->getMessage());
        }
    }

    /**
     * Simple token counter (estimate)
     *
     * @param array $messages
     * @return int
     */
    public function countTokens(array $messages): int
    {
        $text = '';

        foreach ($messages as $message) {
            if (is_array($message) && isset($message['content'])) {
                $text .= $message['content'] . ' ';
            } elseif (is_string($message)) {
                $text .= $message . ' ';
            }
        }

        // Very rough approximation: 1 token ≈ 4 characters
        return (int)ceil(strlen($text) / 4);
    }

    public function sendMessage(array $messages, AIModel $model, array $options = [])
    {
        $provider = $this->providerManager->getProvider($model);

        if (!$provider) {
            return [
                'error' => 'AI provider not available',
                'messages' => $messages
            ];
        }

        // Enhance messages with knowledge base
        $context = $options['context'] ?? [];
        $enhancedMessages = $this->enhanceMessagesWithKnowledgeBase($messages, $context);

        // Apply context rules to enhance messages
        $enhancedMessages = $this->contextRuleService->applyRules($enhancedMessages);

        // Apply template if model has a template
        $systemMessage = $this->getSystemMessage($model, $enhancedMessages);

        if ($systemMessage) {
            // Place system message at the beginning if it doesn't exist
            $hasSystemMessage = false;
            foreach ($enhancedMessages as $message) {
                if (isset($message['role']) && $message['role'] === 'system') {
                    $hasSystemMessage = true;
                    break;
                }
            }

            if (!$hasSystemMessage) {
                array_unshift($enhancedMessages, [
                    'role' => 'system',
                    'content' => $systemMessage
                ]);
            } else {
                // Update existing system message
                foreach ($enhancedMessages as $key => $message) {
                    if (isset($message['role']) && $message['role'] === 'system') {
                        $enhancedMessages[$key]['content'] = $systemMessage;
                        break;
                    }
                }
            }
        }

        try {
            $response = $provider->sendMessage($enhancedMessages, $options);

            // Add information about knowledge base sources used
            if (isset($context['knowledge_sources_used'])) {
                $response['metadata'] = $response['metadata'] ?? [];
                $response['metadata']['knowledge_sources_used'] = $context['knowledge_sources_used'];
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('AI provider error: ' . $e->getMessage(), [
                'model' => $model->name,
                'provider' => $model->provider,
            ]);

            return [
                'error' => 'AI provider error: ' . $e->getMessage(),
                'messages' => $messages
            ];
        }
    }

    /**
     * Get system message from template or default
     *
     * @param AIModel $model
     * @param array $messages
     * @return string|null
     */
    protected function getSystemMessage(AIModel $model, array $messages): ?string
    {
        // Try to get processed template first
        if ($model->template_id) {
            try {
                $templateContent = $this->templateProcessingService->getProcessedTemplateForModel($model, $messages);
                if ($templateContent) {
                    Log::info('Using template for model system message', [
                        'model_id' => $model->id,
                        'template_id' => $model->template_id
                    ]);
                    return $templateContent;
                }
            } catch (\Exception $e) {
                Log::error('Error processing template for model: ' . $e->getMessage(), [
                    'model_id' => $model->id,
                    'template_id' => $model->template_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Fallback to model's default system message if no template or error
        if (!empty($model->settings['system_message'])) {
            return $model->settings['system_message'];
        }

        return null;
    }
}
