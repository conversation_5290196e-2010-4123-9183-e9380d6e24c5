import { useState, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Send, RefreshCw, Plus, Trash, Save } from "lucide-react";
import {
  ApiRoute,
  ApiTestRequest,
  apiTestService,
} from "@/utils/api-test-service";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface RequestEditorProps {
  selectedRoute?: ApiRoute;
  selectedMethod?: string;
  onExecute: (request: ApiTestRequest) => void;
  isLoading: boolean;
}

interface HeaderItem {
  key: string;
  value: string;
  id: string;
}

export function RequestEditor({
  selectedRoute,
  selectedMethod,
  onExecute,
  isLoading,
}: RequestEditorProps) {
  const [url, setUrl] = useState<string>("");
  const [requestData, setRequestData] = useState<string>("");
  const [activeTab, setActiveTab] = useState("body");
  const [headers, setHeaders] = useState<HeaderItem[]>([
    {
      key: "Content-Type",
      value: "application/json",
      id: "default-content-type",
    },
    { key: "Accept", value: "application/json", id: "default-accept" },
  ]);

  // Update the form when selected route changes
  useEffect(() => {
    if (selectedRoute && selectedMethod) {
      setUrl(`${selectedRoute.uri.replace(/\{([^}]+)\}/g, ":$1")}`);

      // Generate example data
      const exampleData = apiTestService.generateExampleData(
        selectedRoute.uri,
        selectedMethod,
      );
      setRequestData(JSON.stringify(exampleData, null, 2));

      // Reset to body tab when changing endpoints
      setActiveTab("body");
    }
  }, [selectedRoute, selectedMethod]);

  const handleExecute = () => {
    try {
      const parsedData = requestData.trim() ? JSON.parse(requestData) : {};

      // Convert headers array to object
      const headerObject: Record<string, string> = {};
      headers.forEach((header) => {
        if (header.key.trim() && header.value.trim()) {
          headerObject[header.key] = header.value;
        }
      });

      const request: ApiTestRequest = {
        method: selectedMethod || "GET",
        url,
        data: parsedData,
        headers: headerObject,
      };

      onExecute(request);
    } catch (e) {
      console.error("Invalid JSON in request data:", e);
      // Show user-friendly error message
      const errorMessage =
        e instanceof Error ? e.message : "Invalid JSON format";
      // You could replace this with a proper toast notification
      alert(`Invalid JSON in request body: ${errorMessage}`);
    }
  };

  const formatJson = () => {
    try {
      if (requestData.trim()) {
        const parsed = JSON.parse(requestData);
        setRequestData(JSON.stringify(parsed, null, 2));
      }
    } catch (e) {
      console.error("Failed to format JSON:", e);
    }
  };

  const addHeader = () => {
    setHeaders([
      ...headers,
      { key: "", value: "", id: `header-${Date.now()}` },
    ]);
  };

  const removeHeader = (id: string) => {
    setHeaders(headers.filter((header) => header.id !== id));
  };

  const updateHeader = (id: string, field: "key" | "value", value: string) => {
    setHeaders(
      headers.map((header) =>
        header.id === id ? { ...header, [field]: value } : header,
      ),
    );
  };

  return (
    <div className="bg-card border rounded-md shadow-sm">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Request</h3>
      </div>
      <div className="p-4 space-y-4">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="bg-muted text-muted-foreground px-3 py-1.5 rounded-l-md font-mono text-sm border-y border-l">
              {selectedMethod || "METHOD"}
            </div>
            <Input
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="/endpoint"
              className="font-mono text-sm flex-1"
            />
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="body">Body</TabsTrigger>
            <TabsTrigger value="headers">Headers</TabsTrigger>
          </TabsList>

          <TabsContent value="body" className="pt-4">
            {(selectedMethod === "POST" || selectedMethod === "PUT") && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="request-body">Request Body</Label>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={formatJson}
                    type="button"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" /> Format
                  </Button>
                </div>
                <Textarea
                  id="request-body"
                  value={requestData}
                  onChange={(e) => setRequestData(e.target.value)}
                  className="font-mono text-sm h-60"
                  placeholder="{}"
                />
              </div>
            )}

            {selectedMethod !== "POST" && selectedMethod !== "PUT" && (
              <div className="p-4 border rounded-md bg-muted/50 text-center">
                <p className="text-muted-foreground">
                  {selectedMethod === "GET"
                    ? "GET requests typically don't have a request body."
                    : "Select a method to configure the request body."}
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="headers" className="pt-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>Request Headers</Label>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={addHeader}
                  type="button"
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Header
                </Button>
              </div>

              <div className="space-y-2">
                {headers.map((header) => (
                  <div key={header.id} className="flex items-center space-x-2">
                    <Input
                      value={header.key}
                      onChange={(e) =>
                        updateHeader(header.id, "key", e.target.value)
                      }
                      placeholder="Header name"
                      className="flex-1"
                    />
                    <Input
                      value={header.value}
                      onChange={(e) =>
                        updateHeader(header.id, "value", e.target.value)
                      }
                      placeholder="Value"
                      className="flex-1"
                    />
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => removeHeader(header.id)}
                      type="button"
                      className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <Button
          className="w-full"
          onClick={handleExecute}
          disabled={isLoading || !url || !selectedMethod}
        >
          {isLoading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Executing...
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              Execute Request
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
