import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Co<PERSON>, Check, Code, AlertTriangle, Loader2, Monitor, Smartphone, Tablet, ExternalLink, ChevronRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { widgetService } from '@/utils/widgetService';

interface EmbedCodeGeneratorProps {
  widgetId?: number;
  config: any;
  aiModel?: {
    id: string;
    name: string;
  } | null;
  promptTemplate?: {
    id: string;
    name: string;
  } | null;
}

const EmbedCodeGenerator = ({ widgetId, config, aiModel = null, promptTemplate = null }: EmbedCodeGeneratorProps) => {
  const [copied, setCopied] = useState(false);
  const [codeTab, setCodeTab] = useState('script');
  const [embedType, setEmbedType] = useState<'standard' | 'iframe' | 'web-component'>('standard');
  const [loadingEmbed, setLoadingEmbed] = useState(false);
  const [embedCode, setEmbedCode] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [allowedDomains, setAllowedDomains] = useState<string[]>(['*']);
  const [newDomain, setNewDomain] = useState<string>('');
  const [securityOptions, setSecurityOptions] = useState({
    enableSRI: false,
    cspEnabled: false,
    sanitizeInputs: true,
  });
  const [positionType, setPositionType] = useState<'fixed' | 'relative' | 'inline'>('fixed');
  const [webhookUrl, setWebhookUrl] = useState<string>('');
  const [includeAnalytics, setIncludeAnalytics] = useState(true);
  const [advancedOptions, setAdvancedOptions] = useState(false);
  const [hasSecurityIssues, setHasSecurityIssues] = useState(false);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [guestUserFlow, setGuestUserFlow] = useState<'required' | 'auto' | 'none'>('auto');
  const [showLivePreview, setShowLivePreview] = useState(false);
  const previewRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();

  // Check for potential security issues
  useEffect(() => {
    // Simple check - if custom CSS or webhook URL is used, highlight security considerations
    setHasSecurityIssues(
      (config.appearance?.customCSS && config.appearance.customCSS.length > 0) ||
      (webhookUrl && webhookUrl.length > 0)
    );
  }, [config.appearance?.customCSS, webhookUrl]);

  // Generate embed code when parameters change
  useEffect(() => {
    if (widgetId) {
      // Don't auto-generate unless the component just mounted or widgetId changed
      // to avoid unnecessary API calls - user can use the Generate button
    } else {
      setEmbedCode(getPlaceholderCode());
    }
  }, [widgetId]);

  // Generate initial embed code when the component mounts or widgetId changes
  useEffect(() => {
    if (widgetId) {
      generateEmbedCode();
    }
  }, [widgetId]);

  // Add a new domain to the allowed domains list
  const addDomain = () => {
    if (!newDomain) return;

    // Trim whitespace and convert to lowercase
    const domain = newDomain.trim().toLowerCase();

    // Basic domain validation
    const isValidDomain = domain === '*' ||
      (domain.startsWith('*.') && domain.length > 2) ||
      /^([a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?\.)+[a-z]{2,}$/i.test(domain);

    if (!isValidDomain) {
      toast({
        title: "Invalid domain format",
        description: "Please enter a valid domain like example.com, *.example.com, or use * for all domains.",
        variant: "destructive"
      });
      return;
    }

    if (!allowedDomains.includes(domain)) {
      // If adding a wildcard, remove the single wildcard if it exists
      let newDomains = [...allowedDomains];
      if (domain === '*') {
        newDomains = ['*']; // Just use the wildcard alone
      } else {
        // Filter out the global wildcard if we're adding a specific domain
        newDomains = newDomains.filter(d => d !== '*');
        newDomains.push(domain);
      }

      setAllowedDomains(newDomains);
      setNewDomain('');
    } else {
      toast({
        title: "Domain already added",
        description: "This domain is already in the allowed list.",
        variant: "default"
        
      });
    }
  };

  // Remove a domain from the allowed domains list
  const removeDomain = (domain: string) => {
    setAllowedDomains(allowedDomains.filter(d => d !== domain));
    // If no domains are left, add wildcard
    if (allowedDomains.length <= 1) {
      setAllowedDomains(['*']);
    }
  };

  // Generate embed code using the widget service
  const generateEmbedCode = async () => {
    if (!widgetId) return;

    setLoadingEmbed(true);
    setError(null);

    try {
      const payload = {
        type: embedType,
        customizations: {
          allowed_domains: allowedDomains,
          position_type: positionType,
          enable_sri: securityOptions.enableSRI,
          csp_enabled: securityOptions.cspEnabled,
          guest_user_flow: guestUserFlow,
          webhook_url: webhookUrl || undefined,
          enable_analytics: includeAnalytics,
        }
      };

      console.log('Generating embed code with payload:', payload);

      const response = await widgetService.generateEmbedCode(widgetId, payload);

      setEmbedCode(response.data.embed_code);

      // Update live preview if it's enabled
      if (showLivePreview && previewRef.current && response.data.preview_url) {
        previewRef.current.src = response.data.preview_url;
      }
    } catch (err: any) {
      console.error('Error generating embed code:', err);

      // Extract detailed error message if available
      let errorMessage = 'Failed to generate embed code. Please try again.';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      setEmbedCode(getPlaceholderCode());
    } finally {
      setLoadingEmbed(false);
    }
  };

  // Get placeholder code when no widget ID is available
  const getPlaceholderCode = () => {
    const mockWidgetId = 'preview-widget-' + Math.random().toString(36).substring(2, 10);

    // Auto-detection script for base URL
    const autoDetectionScript = `
<!-- Auto-detect the script's location -->
<script>
  window.CHAT_WIDGET_BASE_URL = (() => {
    const scriptTags = document.querySelectorAll('script');
    const currentScript = Array.from(scriptTags).find(script => 
      script.src && script.src.includes('/widget/script.js')
    );
    if (currentScript) {
      return currentScript.src.split('/widget/script.js')[0];
    }
    return 'https://your-widget-domain.com'; // Fallback
  })();
</script>`;

    switch (embedType) {
      case 'standard':
        return `<!-- Chat Widget (JavaScript) -->
${autoDetectionScript}
<script src="https://your-widget-domain.com/widget/script.js"
  data-widget-id="${mockWidgetId}"
  data-primary-color="${config.appearance?.primaryColor || '#7E69AB'}"
  data-border-radius="${config.appearance?.borderRadius || 8}"
  data-allowed-domains="${allowedDomains.join(',')}"
  data-guest-flow="${guestUserFlow}"
  ${webhookUrl ? `data-webhook-url="${webhookUrl}"` : ''}
  ${securityOptions.sanitizeInputs ? 'data-sanitize="true"' : ''}
  ${includeAnalytics ? 'data-analytics="true"' : ''}
  async>
</script>`;
      case 'iframe':
        return `<!-- Chat Widget (iframe) -->
<iframe
  src="https://your-widget-domain.com/widget/iframe/${mockWidgetId}"
  id="chat-iframe-${mockWidgetId}"
  style="position: ${positionType}; bottom: 20px; right: 20px; width: 50px; height: 50px; border: none; z-index: 9999;"
  allow="microphone; camera"
  loading="lazy"
  data-allowed-domains="${allowedDomains.join(',')}"
  data-guest-flow="${guestUserFlow}"
  ${webhookUrl ? `data-webhook-url="${webhookUrl}"` : ''}
  title="Chat Widget">
</iframe>`;
      case 'web-component':
        return `<!-- Chat Widget (Web Component) -->
${autoDetectionScript}
<script type="module" src="https://your-widget-domain.com/widget/web-component.js"></script>
<chat-widget
  widget-id="${mockWidgetId}"
  primary-color="${config.appearance?.primaryColor || '#7E69AB'}"
  border-radius="${config.appearance?.borderRadius || 8}"
  allowed-domains="${allowedDomains.join(',')}"
  guest-flow="${guestUserFlow}"
  ${webhookUrl ? `webhook-url="${webhookUrl}"` : ''}
  ${includeAnalytics ? 'analytics="true"' : ''}>
</chat-widget>`;
      default:
        return '<!-- Select an embed type to generate code -->';
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);

    toast({
      title: "Copied to clipboard",
      description: "The embed code has been copied to your clipboard.",
    });

    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground mb-2">
        Use this code to embed the chat widget on your website. Choose the method that works best for your platform.
      </p>

      {!widgetId && (
        <Alert variant="default" className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Widget Not Saved</AlertTitle>
          <AlertDescription>
            This is a preview of the embed code. Save the widget to generate a real embed code.
          </AlertDescription>
        </Alert>
      )}

      {aiModel && (
        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md flex items-start space-x-3 mb-3">
          <div className="text-blue-600 dark:text-blue-400 shrink-0 mt-0.5">
            <Code className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm font-medium text-blue-800 dark:text-blue-300">
              AI Integration Detected
            </p>
            <p className="text-sm text-blue-600 dark:text-blue-400">
              This widget uses the <strong>{aiModel.name}</strong> AI model
              {promptTemplate && (
                <> with the <strong>{promptTemplate.name}</strong> prompt template</>
              )}. The embed code includes these configurations.
            </p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <Label htmlFor="embed-type">Embed Type</Label>
          <Select
            value={embedType}
            onValueChange={(value) => setEmbedType(value as any)}
          >
            <SelectTrigger id="embed-type">
              <SelectValue placeholder="Select embed type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="standard">JavaScript</SelectItem>
              <SelectItem value="iframe">iframe</SelectItem>
              <SelectItem value="web-component">Web Component</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="position-type">Position Type</Label>
          <Select
            value={positionType}
            onValueChange={(value) => setPositionType(value as any)}
          >
            <SelectTrigger id="position-type">
              <SelectValue placeholder="Select position type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fixed">Fixed (Floating)</SelectItem>
              <SelectItem value="relative">Relative</SelectItem>
              <SelectItem value="inline">Inline</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <Label htmlFor="guest-user-flow">Guest User Flow</Label>
          <Select
            value={guestUserFlow}
            onValueChange={(value) => setGuestUserFlow(value as any)}
          >
            <SelectTrigger id="guest-user-flow">
              <SelectValue placeholder="Select guest user flow" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="required">Required (Collect guest details)</SelectItem>
              <SelectItem value="auto">Auto (Generate if needed)</SelectItem>
              <SelectItem value="none">None (No guest tracking)</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-1">
            Controls how the widget handles guest users who interact with the chat.
          </p>
        </div>
      </div>

      <div className="rounded-md border p-4 mb-4">
        <div className="flex items-center justify-between mb-2">
          <Label className="text-sm font-medium">Advanced Options</Label>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setAdvancedOptions(!advancedOptions)}
          >
            {advancedOptions ? 'Hide' : 'Show'}
          </Button>
        </div>

        {advancedOptions && (
          <div className="space-y-4 mt-4">
            <div>
              <Label className="mb-2 block">Domain Restrictions</Label>
              <div className="flex items-center space-x-2 mb-2">
                <Input
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  placeholder="example.com or *.example.com"
                  className="flex-1"
                />
                <Button size="sm" onClick={addDomain}>Add</Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {allowedDomains.map(domain => (
                  <Badge key={domain} variant="secondary" className="flex items-center gap-1">
                    {domain}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0 ml-1"
                      onClick={() => removeDomain(domain)}
                    >
                      <AlertTriangle className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Use * for all domains, or add specific domains like example.com. Use *.example.com for subdomains.
              </p>
            </div>

            <div className="space-y-2">
              <Label className="mb-2 block">Security Settings</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sri"
                  checked={securityOptions.enableSRI}
                  onCheckedChange={(checked) =>
                    setSecurityOptions({ ...securityOptions, enableSRI: !!checked })
                  }
                />
                <Label htmlFor="sri" className="text-sm font-normal">
                  Enable Subresource Integrity (SRI)
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="csp"
                  checked={securityOptions.cspEnabled}
                  onCheckedChange={(checked) =>
                    setSecurityOptions({ ...securityOptions, cspEnabled: !!checked })
                  }
                />
                <Label htmlFor="csp" className="text-sm font-normal">
                  Include Content Security Policy (CSP)
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sanitize"
                  checked={securityOptions.sanitizeInputs}
                  onCheckedChange={(checked) =>
                    setSecurityOptions({ ...securityOptions, sanitizeInputs: !!checked })
                  }
                />
                <Label htmlFor="sanitize" className="text-sm font-normal">
                  Sanitize User Inputs
                </Label>
              </div>
            </div>

            <div>
              <Label htmlFor="webhook-url" className="mb-2 block">Webhook URL (Optional)</Label>
              <Input
                id="webhook-url"
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                placeholder="https://your-webhook-url.com/endpoint"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Receive events from the widget. Must be HTTPS.
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="analytics"
                checked={includeAnalytics}
                onCheckedChange={(checked) => setIncludeAnalytics(!!checked)}
              />
              <Label htmlFor="analytics" className="text-sm font-normal">
                Enable Analytics
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="live-preview"
                checked={showLivePreview}
                onCheckedChange={(checked) => setShowLivePreview(!!checked)}
              />
              <Label htmlFor="live-preview" className="text-sm font-normal">
                Show Live Preview
              </Label>
            </div>
          </div>
        )}
      </div>

      {hasSecurityIssues && (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Security Considerations</AlertTitle>
          <AlertDescription>
            {config.appearance?.customCSS && config.appearance.customCSS.length > 0 && (
              <p>• Custom CSS may introduce security risks. Ensure it's properly sanitized.</p>
            )}
            {webhookUrl && webhookUrl.length > 0 && (
              <p>• Webhook URLs should be properly secured and use HTTPS.</p>
            )}
            <p>• Consider enabling SRI and CSP for additional security.</p>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="relative">
            <div className="rounded-md bg-muted overflow-hidden">
              <pre className="p-4 text-sm overflow-auto max-h-96">
                <code>{embedCode}</code>
              </pre>
            </div>

            {loadingEmbed && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            <Button
              size="sm"
              variant="secondary"
              className="absolute top-2 right-2"
              onClick={() => copyToClipboard(embedCode)}
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 mr-1" /> Copied
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-1" /> Copy
                </>
              )}
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        {showLivePreview && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Live Preview</h4>
              <div className="flex space-x-1 bg-muted rounded-md p-1">
                <Button
                  variant={previewMode === 'desktop' ? 'secondary' : 'ghost'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setPreviewMode('desktop')}
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewMode === 'tablet' ? 'secondary' : 'ghost'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setPreviewMode('tablet')}
                >
                  <Tablet className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewMode === 'mobile' ? 'secondary' : 'ghost'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setPreviewMode('mobile')}
                >
                  <Smartphone className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className={`border rounded-md bg-background overflow-hidden transition-all ${previewMode === 'desktop' ? 'w-full h-[400px]' :
              previewMode === 'tablet' ? 'w-[768px] max-w-full h-[500px] mx-auto' :
                'w-[375px] max-w-full h-[600px] mx-auto'
              }`}>
              <iframe
                ref={previewRef}
                src="about:blank"
                className="w-full h-full"
                title="Widget Preview"
              />
            </div>
          </div>
        )}
      </div>

      <Button
        variant="default"
        className="mt-4 w-full"
        onClick={generateEmbedCode}
        disabled={!widgetId || loadingEmbed}
      >
        {loadingEmbed ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Generating...
          </>
        ) : !widgetId ? (
          <>
            <AlertTriangle className="h-4 w-4 mr-2" />
            Save Widget First to Generate Real Code
          </>
        ) : (
          <>
            <Code className="h-4 w-4 mr-2" />
            Generate Embed Code
          </>
        )}
      </Button>

      <Accordion type="single" collapsible className="bg-muted p-4 rounded-md mt-4">
        <AccordionItem value="integration-guide">
          <AccordionTrigger className="text-sm font-medium">Integration Guide</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Quick Start</h5>
                <ol className="text-sm space-y-2 list-decimal list-inside">
                  <li>Copy the code above.</li>
                  <li>Paste it into the <code>&lt;body&gt;</code> section of your website.</li>
                  <li>The widget will automatically load with your configured settings.</li>
                  {securityOptions.cspEnabled && (
                    <li>Add the CSP meta tag to the <code>&lt;head&gt;</code> section of your website.</li>
                  )}
                </ol>
              </div>

              <div className="space-y-2">
                <h5 className="font-medium text-sm">Platform Guides</h5>
                <div className="space-y-1">
                  <Button variant="link" className="h-auto p-0 text-sm" onClick={() => { }}>
                    WordPress <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    Insert the code into a Custom HTML block or use a header/footer plugin.
                  </p>
                </div>
                <div className="space-y-1">
                  <Button variant="link" className="h-auto p-0 text-sm" onClick={() => { }}>
                    Shopify <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    Add to theme.liquid or use the Custom HTML app.
                  </p>
                </div>
                <div className="space-y-1">
                  <Button variant="link" className="h-auto p-0 text-sm" onClick={() => { }}>
                    Wix <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    Use the Custom Code feature in the Wix Editor.
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h5 className="font-medium text-sm">Webhooks</h5>
                <p className="text-xs text-muted-foreground">
                  If you've set a webhook URL, your server will receive POST requests with the following events:
                </p>
                <ul className="text-xs space-y-1 list-disc list-inside">
                  <li>Chat started</li>
                  <li>Message sent</li>
                  <li>Chat ended</li>
                  <li>User rating submitted</li>
                  <li>Guest user registered</li>
                </ul>
              </div>

              <div className="pt-2">
                <Button variant="outline" size="sm" className="w-full">
                  View Full Documentation <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="cors-and-security">
          <AccordionTrigger className="text-sm font-medium">CORS & Security</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <p className="text-xs">
                The widget uses secure cross-origin communication. Here's what you need to know:
              </p>

              <div className="space-y-1">
                <h6 className="text-xs font-medium">Domain Restrictions</h6>
                <p className="text-xs text-muted-foreground">
                  Only domains you specify can load the widget. This prevents unauthorized use.
                </p>
              </div>

              <div className="space-y-1">
                <h6 className="text-xs font-medium">Subresource Integrity (SRI)</h6>
                <p className="text-xs text-muted-foreground">
                  Ensures the script hasn't been tampered with during delivery.
                </p>
              </div>

              <div className="space-y-1">
                <h6 className="text-xs font-medium">Content Security Policy (CSP)</h6>
                <p className="text-xs text-muted-foreground">
                  Restricts which resources can be loaded, preventing XSS attacks.
                </p>
              </div>

              <div className="pt-2">
                <Button variant="outline" size="sm" className="w-full">
                  View Security Documentation <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="troubleshooting">
          <AccordionTrigger className="text-sm font-medium">Troubleshooting</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-3">
              <div className="space-y-1">
                <h6 className="text-xs font-medium">Widget doesn't appear</h6>
                <p className="text-xs text-muted-foreground">
                  Ensure your domain is in the allowed list and check console for errors.
                </p>
              </div>

              <div className="space-y-1">
                <h6 className="text-xs font-medium">CORS errors</h6>
                <p className="text-xs text-muted-foreground">
                  Verify that your domain is correctly formatted in the allowed domains list.
                </p>
              </div>

              <div className="space-y-1">
                <h6 className="text-xs font-medium">Guest user issues</h6>
                <p className="text-xs text-muted-foreground">
                  Check that cookies are enabled and the guest user flow option is properly set.
                </p>
              </div>

              <div className="pt-2">
                <Button variant="outline" size="sm" className="w-full">
                  View Full Troubleshooting Guide <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default EmbedCodeGenerator;
