# Pre-Chat Forms and Post-Chat Surveys

## Overview

The widget system supports pre-chat forms and post-chat surveys to enhance the user experience and collect valuable information:

- **Pre-Chat Forms**: Collect information from users before starting a chat
- **Post-Chat Surveys**: Gather feedback from users after a chat ends

Both features are fully customizable and can be enabled or disabled per widget.

## Pre-Chat Forms

### Purpose

Pre-chat forms serve several important purposes:

1. **User Identification**: Collect basic information like name and email
2. **Context Gathering**: Understand the user's issue before starting the chat
3. **Routing**: Use collected information to route to the appropriate AI model or template
4. **Personalization**: Customize the chat experience based on user information

### Configuration

Each widget can have multiple pre-chat form templates, but only one can be active at a time. A template consists of:

- **Title**: The form title displayed to users
- **Description**: Optional description explaining the purpose of the form
- **Fields**: Customizable form fields to collect information

Field types include:
- Text
- Email
- Phone
- Select (dropdown)
- Checkbox

Each field can be configured with:
- Label
- Placeholder
- Required flag
- Validation pattern
- Error message
- Display order

### Implementation

#### Backend

The pre-chat form system uses the following models:

- `PreChatFormTemplate`: Stores the form template configuration
- `PreChatFormField`: Stores the fields for each template
- `PreChatFormSubmission`: Stores user submissions

The controller `PreChatFormController` handles:
- Getting the active form for a widget
- Submitting form data
- Managing templates (CRUD operations)
- Retrieving submissions

#### Frontend

The pre-chat form is displayed when:
1. The widget is opened
2. The `preChat` setting is enabled for the widget

The form component renders the fields dynamically based on the template configuration and validates user input before submission.

### API Endpoints

#### Public Endpoints

```
GET    /api/pre-chat-form?widget_id={widgetId}
POST   /api/pre-chat-form/submit
```

#### Admin Endpoints (Authenticated)

```
GET    /api/widgets/{widgetId}/pre-chat-forms
POST   /api/widgets/{widgetId}/pre-chat-forms
PUT    /api/widgets/{widgetId}/pre-chat-forms/{templateId}
DELETE /api/widgets/{widgetId}/pre-chat-forms/{templateId}
GET    /api/widgets/{widgetId}/pre-chat-forms/{templateId}/submissions
```

### Example Template

```json
{
  "id": 1,
  "widget_id": 123,
  "title": "Welcome to Support Chat",
  "description": "Please provide your information to help us serve you better",
  "is_active": true,
  "fields": [
    {
      "id": 1,
      "template_id": 1,
      "name": "name",
      "label": "Your Name",
      "type": "text",
      "placeholder": "Enter your full name",
      "is_required": true,
      "order": 0
    },
    {
      "id": 2,
      "template_id": 1,
      "name": "email",
      "label": "Email Address",
      "type": "email",
      "placeholder": "<EMAIL>",
      "is_required": true,
      "validation_pattern": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
      "error_message": "Please enter a valid email address",
      "order": 1
    },
    {
      "id": 3,
      "template_id": 1,
      "name": "issue",
      "label": "What can we help you with?",
      "type": "select",
      "options": ["Technical Support", "Billing Question", "Feature Request", "Other"],
      "is_required": true,
      "order": 2
    }
  ]
}
```

## Post-Chat Surveys

### Purpose

Post-chat surveys help:

1. **Measure Satisfaction**: Gauge user satisfaction with the chat experience
2. **Collect Feedback**: Gather specific feedback about the AI responses
3. **Identify Improvement Areas**: Find areas where the AI model or templates need improvement
4. **Quality Assurance**: Ensure the chat system is meeting user needs

### Configuration

Each widget can have multiple post-chat survey templates, but only one can be active at a time. A survey consists of:

- **Title**: The survey title displayed to users
- **Description**: Optional description explaining the purpose of the survey
- **Thank You Message**: Message displayed after survey submission
- **Questions**: Customizable questions to collect feedback

Question types include:
- Rating (1-5 stars)
- Text (free-form response)
- Select (dropdown)
- Boolean (yes/no)
- Checkbox
- Multi-select

Each question can be configured with:
- Text
- Required flag
- Options (for select, checkbox, and multi-select)
- Display order

### Implementation

#### Backend

The post-chat survey system uses the following models:

- `PostChatSurvey`: Stores the survey configuration
- `SurveyQuestion`: Stores the questions for each survey
- `SurveyResponse`: Stores user responses

The controller `PostChatSurveyController` handles:
- Getting the active survey for a widget
- Submitting survey responses
- Managing surveys (CRUD operations)
- Retrieving responses
- Generating analytics

#### Frontend

The post-chat survey is displayed when:
1. The chat ends (user closes chat or after inactivity)
2. The `postChat` setting is enabled for the widget

The survey component renders the questions dynamically based on the survey configuration and validates user input before submission.

### API Endpoints

#### Public Endpoints

```
GET    /api/post-chat-survey?widget_id={widgetId}
POST   /api/post-chat-survey/submit
```

#### Admin Endpoints (Authenticated)

```
GET    /api/widgets/{widgetId}/post-chat-surveys
POST   /api/widgets/{widgetId}/post-chat-surveys
PUT    /api/widgets/{widgetId}/post-chat-surveys/{surveyId}
DELETE /api/widgets/{widgetId}/post-chat-surveys/{surveyId}
GET    /api/widgets/{widgetId}/post-chat-surveys/{surveyId}/responses
GET    /api/widgets/{widgetId}/post-chat-surveys/{surveyId}/analytics
```

### Example Survey

```json
{
  "id": 1,
  "widget_id": 123,
  "title": "How was your experience?",
  "description": "Please take a moment to provide feedback on your chat experience",
  "thank_you_message": "Thank you for your feedback! We appreciate your input.",
  "is_active": true,
  "questions": [
    {
      "id": 1,
      "survey_id": 1,
      "text": "How would you rate your overall experience?",
      "type": "rating",
      "is_required": true,
      "order": 0
    },
    {
      "id": 2,
      "survey_id": 1,
      "text": "Did the AI assistant resolve your issue?",
      "type": "boolean",
      "is_required": true,
      "order": 1
    },
    {
      "id": 3,
      "survey_id": 1,
      "text": "What could we improve?",
      "type": "text",
      "is_required": false,
      "order": 2
    },
    {
      "id": 4,
      "survey_id": 1,
      "text": "Which aspects of the chat were most helpful?",
      "type": "multiselect",
      "options": ["Speed of response", "Accuracy of information", "Clarity of explanations", "Friendliness", "None of the above"],
      "is_required": false,
      "order": 3
    }
  ]
}
```

## Integration with Chat Flow

### Pre-Chat Form Flow

1. User opens widget
2. If pre-chat is enabled, form is displayed
3. User completes required fields
4. Form is submitted and stored
5. Chat begins with context from the form data

### Post-Chat Survey Flow

1. User ends chat (explicit end or inactivity timeout)
2. If post-chat is enabled, survey appears
3. User completes survey questions
4. Survey is submitted and stored
5. Thank you message is displayed
6. Widget returns to initial state

## Analytics

The system provides analytics for both pre-chat forms and post-chat surveys:

### Pre-Chat Form Analytics

- Submission count
- Completion rate
- Field-level analytics (most common values, etc.)
- Correlation with chat outcomes

### Post-Chat Survey Analytics

- Response count
- Average ratings
- Completion rate
- Question-by-question breakdown
- Trend analysis over time
