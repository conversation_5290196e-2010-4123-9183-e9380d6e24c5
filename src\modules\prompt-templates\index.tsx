import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Plus,
  Search,
  FileText,
  Edit,
  Trash,
  MoreVertical
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { AdminLayout } from "@/components/admin-layout";
import { PageHeader } from "@/components/page-header";
import { templateService, Template } from "@/utils/template-service";
import { ModelTemplateSelector } from "@/components/templates/model-template-selector";

export function PromptTemplatesModule() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("templates");
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const response = await templateService.getTemplates();
      if (response) {
        setTemplates(response);
      }
    } catch (error) {
      console.error("Error loading templates:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load templates. Please try again."
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleEdit = (template: Template) => {
    navigate(`/templates/${template.id}`);
  };

  const handleDelete = async (template: Template) => {
    try {
      await templateService.deleteTemplate(template.id);
      toast({
        title: "Success",
        description: "Template deleted successfully."
      });
      loadTemplates();
    } catch (error) {
      console.error("Error deleting template:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete template. Please try again."
      });
    }
  };

  const handleCreateTemplate = () => {
    navigate("/templates/new");
  };

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <AdminLayout>
      <div className="w-full">
        <PageHeader
          title="Prompt Templates"
          description="Create and manage prompt templates for consistent AI responses"
          actions={
            <Button className="flex items-center gap-1" onClick={handleCreateTemplate}>
              <Plus className="h-4 w-4" />
              Create Template
            </Button>
          }
        />

        <div className="mt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="variables">Variables</TabsTrigger>
              <TabsTrigger value="testing">Testing</TabsTrigger>
              <TabsTrigger value="models">Model Assignment</TabsTrigger>
            </TabsList>

            <TabsContent value="templates" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Template Management
                    </CardTitle>
                    <div className="relative max-w-sm">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                      <Input
                        placeholder="Search templates..."
                        className="pl-8"
                        value={searchQuery}
                        onChange={handleSearch}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Variables</TableHead>
                        <TableHead>Version</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTemplates.map((template) => (
                        <TableRow key={template.id}>
                          <TableCell className="font-medium">
                            {template.name}
                            {template.is_default && (
                              <Badge variant="secondary" className="ml-2">Default</Badge>
                            )}
                          </TableCell>
                          <TableCell>{template.category}</TableCell>
                          <TableCell>
                            <Badge variant={template.status === 'active' ? 'default' : 'outline'}>
                              {template.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{template.variables?.length || 0} variables</TableCell>
                          <TableCell>v{template.version}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEdit(template)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDelete(template)}
                                  className="text-destructive"
                                >
                                  <Trash className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                      {filteredTemplates.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-6">
                            {loading ? (
                              <div className="flex justify-center">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                              </div>
                            ) : (
                              <div className="text-muted-foreground">
                                No templates found. Create your first template to get started.
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="variables">
              <Card>
                <CardHeader>
                  <CardTitle>Template Variables</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Variables allow you to create dynamic templates that can be customized for each interaction.
                    Use the format <code className="bg-gray-100 p-1 rounded">{"{{variable_name}}"}</code> in your templates.
                  </p>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Common Variables</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Variable</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Example Value</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell><code>{"{{user_name}}"}</code></TableCell>
                          <TableCell>The name of the user</TableCell>
                          <TableCell>John Doe</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell><code>{"{{user_query}}"}</code></TableCell>
                          <TableCell>The user's question or message</TableCell>
                          <TableCell>How do I reset my password?</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell><code>{"{{company_name}}"}</code></TableCell>
                          <TableCell>Your company name</TableCell>
                          <TableCell>Acme Corporation</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell><code>{"{{product_name}}"}</code></TableCell>
                          <TableCell>The product being discussed</TableCell>
                          <TableCell>Widget Pro</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell><code>{"{{date}}"}</code></TableCell>
                          <TableCell>Current date</TableCell>
                          <TableCell>{new Date().toLocaleDateString()}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="testing">
              <Card>
                <CardHeader>
                  <CardTitle>Template Testing</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-6">
                    Select a template from the Templates tab to test it with different variable values.
                  </p>

                  <div className="flex justify-center py-8">
                    <div className="text-center">
                      <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
                      <h3 className="text-lg font-medium">Select a template to test</h3>
                      <p className="text-muted-foreground mt-1">
                        Go to the Templates tab, select a template, and use the test function to see how it works with different variables.
                      </p>
                      <Button className="mt-4" onClick={() => setActiveTab("templates")}>
                        View Templates
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="models">
              <Card>
                <CardHeader>
                  <CardTitle>Model Template Assignment</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-6">
                    Assign templates to your AI models to control how they respond to user queries.
                  </p>

                  <ModelTemplateSelector modelId={1} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AdminLayout>
  );
}
