/**
 * Number Field Component
 * 
 * A form field for numeric input.
 */

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

interface NumberFieldProps {
  label: string;
  fieldName: string;
  control: Control<any>;
  min?: number;
  max?: number;
  step?: number;
  description?: string;
  disabled?: boolean;
}

/**
 * Number Field Component
 * A form field for numeric input
 */
export function NumberField({
  label,
  fieldName,
  control,
  min,
  max,
  step = 1,
  description,
  disabled = false
}: NumberFieldProps) {
  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              type="number"
              min={min}
              max={max}
              step={step}
              {...field}
              // Convert string value to number
              onChange={(e) => {
                const value = e.target.value === '' ? '' : Number(e.target.value);
                field.onChange(value);
              }}
              // Ensure value is a number for controlled input
              value={field.value === undefined ? '' : field.value}
              disabled={disabled}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
