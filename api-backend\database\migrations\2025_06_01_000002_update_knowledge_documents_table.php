<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('knowledge_documents', function (Blueprint $table) {
            // Add new columns for AI query integration if they don't exist
            if (!Schema::hasColumn('knowledge_documents', 'is_active_source')) {
                $table->boolean('is_active_source')->default(false)->after('status');
            }

            if (!Schema::hasColumn('knowledge_documents', 'category')) {
                $table->string('category')->nullable()->after('is_active_source');
            }

            if (!Schema::hasColumn('knowledge_documents', 'project_id')) {
                $table->foreignId('project_id')->nullable()->after('category');
            }

            if (!Schema::hasColumn('knowledge_documents', 'extracted_text')) {
                $table->text('extracted_text')->nullable()->after('project_id');
            }

            if (!Schema::hasColumn('knowledge_documents', 'size')) {
                $table->integer('size')->nullable()->after('file_type');
            }

            // Add indexes for faster lookups if they don't exist
            if (!Schema::hasIndex('knowledge_documents', 'knowledge_documents_is_active_source_index')) {
                $table->index(['is_active_source']);
            }

            if (!Schema::hasIndex('knowledge_documents', 'knowledge_documents_category_index')) {
                $table->index(['category']);
            }

            if (!Schema::hasIndex('knowledge_documents', 'knowledge_documents_project_id_index')) {
                $table->index(['project_id']);
            }
        });
    }

    public function down(): void
    {
        Schema::table('knowledge_documents', function (Blueprint $table) {
            $table->dropIndex(['is_active_source']);
            $table->dropIndex(['category']);
            $table->dropIndex(['project_id']);

            $table->dropColumn([
                'is_active_source',
                'category',
                'project_id',
                'extracted_text',
                'size'
            ]);
        });
    }
};
