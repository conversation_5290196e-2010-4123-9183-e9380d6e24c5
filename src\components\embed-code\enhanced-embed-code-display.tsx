import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Check, Copy, Code, ExternalLink } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface EnhancedEmbedCodeDisplayProps {
    embedCode: string;
    iframeCode?: string;
    webComponentCode?: string;
    title?: string;
    description?: string;
}

export function EnhancedEmbedCodeDisplay({
    embedCode,
    iframeCode,
    webComponentCode,
    title = "Embed Code",
    description = "Use this code to embed the widget on your website."
}: EnhancedEmbedCodeDisplayProps) {
    const [activeTab, setActiveTab] = useState<string>("standard");
    const [copied, setCopied] = useState<string | null>(null);
    const { toast } = useToast();

    const handleCopy = async (code: string, type: string) => {
        try {
            await navigator.clipboard.writeText(code);
            setCopied(type);

            toast({
                title: "Copied to clipboard",
                description: `${type} embed code has been copied to clipboard`,
            });

            setTimeout(() => setCopied(null), 2000);
        } catch (error) {
            console.error("Failed to copy:", error);
            toast({
                title: "Copy failed",
                description: "Could not copy to clipboard. Please try again.",
                variant: "destructive",
            });
        }
    };

    // Format the code with syntax highlighting using a simple approach
    const formatCode = (code: string) => {
        // Replace HTML tags with highlighted spans
        return code
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/("[^"]*")/g, "<span class='text-green-500'>$1</span>")
            .replace(/(&lt;[^\\s|&]*)/g, "<span class='text-blue-500'>$1</span>")
            .replace(/(&lt;\/[^\\s|&]*)/g, "<span class='text-blue-500'>$1</span>")
            .replace(/(data-[a-z-]*)/g, "<span class='text-yellow-500'>$1</span>");
    };

    return (
        <div className="space-y-4">
            <div>
                <Label className="text-base">{title}</Label>
                <p className="text-sm text-muted-foreground">{description}</p>
            </div>

            <Tabs
                defaultValue="standard"
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
            >
                <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="standard">Standard</TabsTrigger>
                    <TabsTrigger value="iframe" disabled={!iframeCode}>
                        iframe
                    </TabsTrigger>
                    <TabsTrigger value="web-component" disabled={!webComponentCode}>
                        Web Component
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="standard" className="mt-0">
                    <div className="relative">
                        <div className="flex justify-between items-center mb-2">
                            <div className="text-sm font-medium">
                                Standard Script (Recommended)
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCopy(embedCode, "Standard")}
                                className="h-8 gap-1"
                            >
                                {copied === "Standard" ? (
                                    <>
                                        <Check className="h-3.5 w-3.5" />
                                        <span>Copied</span>
                                    </>
                                ) : (
                                    <>
                                        <Copy className="h-3.5 w-3.5" />
                                        <span>Copy</span>
                                    </>
                                )}
                            </Button>
                        </div>

                        <div className="relative rounded-md bg-muted/80 p-4 overflow-x-auto">
                            <pre className="text-xs font-mono">
                                <code dangerouslySetInnerHTML={{ __html: formatCode(embedCode) }} />
                            </pre>
                        </div>
                    </div>
                </TabsContent>

                {iframeCode && (
                    <TabsContent value="iframe" className="mt-0">
                        <div className="relative">
                            <div className="flex justify-between items-center mb-2">
                                <div className="text-sm font-medium">
                                    iframe Embed
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleCopy(iframeCode, "iframe")}
                                    className="h-8 gap-1"
                                >
                                    {copied === "iframe" ? (
                                        <>
                                            <Check className="h-3.5 w-3.5" />
                                            <span>Copied</span>
                                        </>
                                    ) : (
                                        <>
                                            <Copy className="h-3.5 w-3.5" />
                                            <span>Copy</span>
                                        </>
                                    )}
                                </Button>
                            </div>

                            <div className="relative rounded-md bg-muted/80 p-4 overflow-x-auto">
                                <pre className="text-xs font-mono">
                                    <code dangerouslySetInnerHTML={{ __html: formatCode(iframeCode) }} />
                                </pre>
                            </div>
                        </div>
                    </TabsContent>
                )}

                {webComponentCode && (
                    <TabsContent value="web-component" className="mt-0">
                        <div className="relative">
                            <div className="flex justify-between items-center mb-2">
                                <div className="text-sm font-medium">
                                    Web Component
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleCopy(webComponentCode, "Web Component")}
                                    className="h-8 gap-1"
                                >
                                    {copied === "Web Component" ? (
                                        <>
                                            <Check className="h-3.5 w-3.5" />
                                            <span>Copied</span>
                                        </>
                                    ) : (
                                        <>
                                            <Copy className="h-3.5 w-3.5" />
                                            <span>Copy</span>
                                        </>
                                    )}
                                </Button>
                            </div>

                            <div className="relative rounded-md bg-muted/80 p-4 overflow-x-auto">
                                <pre className="text-xs font-mono">
                                    <code dangerouslySetInnerHTML={{ __html: formatCode(webComponentCode) }} />
                                </pre>
                            </div>
                        </div>
                    </TabsContent>
                )}
            </Tabs>

            <div className="flex justify-between pt-2 border-t">
                <Button variant="outline" size="sm" className="gap-1">
                    <Code className="h-4 w-4" />
                    <span>Test Embed</span>
                </Button>

                <Button variant="link" size="sm" className="gap-1">
                    <ExternalLink className="h-4 w-4" />
                    <span>Implementation Guide</span>
                </Button>
            </div>
        </div>
    );
} 