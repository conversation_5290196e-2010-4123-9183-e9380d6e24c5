import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Check, Code } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CustomCSSEditorProps {
    currentCSS?: string;
    previewElement?: string;
    onChange: (css: string) => void;
}

export function CustomCSSEditor({
    currentCSS = "",
    previewElement = "#widget-preview",
    onChange,
}: CustomCSSEditorProps) {
    const [css, setCSS] = useState(currentCSS);
    const [isSaving, setIsSaving] = useState(false);
    const [previewActive, setPreviewActive] = useState(false);
    const [validationError, setValidationError] = useState<string | null>(null);
    const { toast } = useToast();

    // Create a style element for preview
    useEffect(() => {
        const styleId = "custom-css-preview";
        let styleElement = document.getElementById(styleId) as HTMLStyleElement;

        if (!styleElement) {
            styleElement = document.createElement("style");
            styleElement.id = styleId;
            document.head.appendChild(styleElement);
        }

        return () => {
            document.head.removeChild(styleElement);
        };
    }, []);

    // Update the preview when CSS changes and preview is active
    useEffect(() => {
        if (!previewActive) return;

        const styleElement = document.getElementById("custom-css-preview") as HTMLStyleElement;
        if (styleElement) {
            // Add the preview element selector to scope CSS properly
            const scopedCSS = css
                .split("}")
                .map(rule => {
                    if (!rule.trim()) return "";
                    return `${previewElement} ${rule}}`;
                })
                .join("");

            styleElement.textContent = scopedCSS;
        }
    }, [css, previewActive, previewElement]);

    const validateCSS = (cssText: string): boolean => {
        try {
            // Create a temporary style element to validate the CSS
            const style = document.createElement("style");
            style.textContent = cssText;
            document.head.appendChild(style);
            document.head.removeChild(style);
            setValidationError(null);
            return true;
        } catch (error) {
            if (error instanceof Error) {
                setValidationError(`CSS validation error: ${error.message}`);
            } else {
                setValidationError("Invalid CSS syntax");
            }
            return false;
        }
    };

    const handleApply = () => {
        if (validateCSS(css)) {
            setIsSaving(true);

            // Simulate API call
            setTimeout(() => {
                onChange(css);
                setIsSaving(false);
                toast({
                    title: "Custom CSS saved",
                    description: "Your CSS customizations have been applied",
                });
            }, 600);
        }
    };

    const handlePreview = () => {
        if (validateCSS(css)) {
            setPreviewActive(!previewActive);
        }
    };

    return (
        <div className="space-y-4">
            <div>
                <Label htmlFor="custom-css">Custom CSS</Label>
                <p className="text-sm text-muted-foreground mb-2">
                    Add custom CSS to further personalize your widget appearance
                </p>
            </div>

            <Textarea
                id="custom-css"
                value={css}
                onChange={(e) => setCSS(e.target.value)}
                className="font-mono text-sm h-40"
                placeholder={`.ai-chat-widget {
  /* Your custom styles here */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}`}
            />

            {validationError && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{validationError}</AlertDescription>
                </Alert>
            )}

            <div className="flex flex-col sm:flex-row justify-between gap-2">
                <div className="text-sm text-muted-foreground">
                    <p className="font-medium">Available CSS selectors:</p>
                    <ul className="list-disc list-inside ml-2">
                        <li>.ai-chat-widget - Main widget container</li>
                        <li>.ai-chat-header - Widget header</li>
                        <li>.ai-chat-messages - Messages container</li>
                        <li>.ai-chat-input - User input area</li>
                    </ul>
                </div>
                <div className="flex gap-2 self-end">
                    <Button
                        variant={previewActive ? "default" : "outline"}
                        size="sm"
                        onClick={handlePreview}
                        disabled={isSaving}
                    >
                        {previewActive ? (
                            <>
                                <Check className="h-4 w-4 mr-1" />
                                Preview Active
                            </>
                        ) : (
                            <>
                                <Code className="h-4 w-4 mr-1" />
                                Preview CSS
                            </>
                        )}
                    </Button>
                    <Button
                        onClick={handleApply}
                        size="sm"
                        disabled={isSaving || !css.trim()}
                    >
                        Apply Custom CSS
                    </Button>
                </div>
            </div>
        </div>
    );
} 