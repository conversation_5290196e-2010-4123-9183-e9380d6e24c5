<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Project;
use App\Models\KnowledgeDocument;
use App\Models\KnowledgeEmbedding;
use App\Models\ContextSetting;
use App\Models\ScrapedUrl;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Str;

class KnowledgeBaseRetrievalService
{
    /**
     * @var VectorEmbeddingService
     */
    protected $embeddingService;

    /**
     * @var ContextRuleService
     */
    protected $contextRuleService;

    /**
     * @var ScraperService
     */
    protected $scraperService;

    /**
     * Constructor
     *
     * @param VectorEmbeddingService $embeddingService
     * @param ContextRuleService $contextRuleService
     * @param ScraperService $scraperService
     */
    public function __construct(
        VectorEmbeddingService $embeddingService,
        ContextRuleService $contextRuleService,
        ScraperService $scraperService
    ) {
        $this->embeddingService = $embeddingService;
        $this->contextRuleService = $contextRuleService;
        $this->scraperService = $scraperService;
    }

    /**
     * Retrieve knowledge for a query
     *
     * @param string $query
     * @param int $projectId
     * @param array $options
     * @return array
     */
    public function retrieveKnowledgeForQuery(string $query, int $projectId, array $options = []): array
    {
        try {
            // Get context settings for the project
            $settings = $this->getContextSettings($projectId);

            // Apply options over default settings
            $limit = $options['limit'] ?? $settings['max_sources_per_query'] ?? 3;
            $threshold = $options['threshold'] ?? $settings['relevance_threshold'] ?? 0.7;

            // Track sources being used for the query
            $sources = [];
            $enabledSources = $settings['enabled_sources'] ?? [
                'documents' => true,
                'database' => true,
                'web' => false
            ];

            // Check matching context rules first
            $matchingRules = $this->contextRuleService->findMatchingRules($query, $projectId);

            if ($matchingRules->isNotEmpty()) {
                // If we have matching rules, use them to determine knowledge sources
                foreach ($matchingRules as $rule) {
                    // Add sources specified in the rule
                    foreach ($rule->sources as $sourceId) {
                        $sources[] = $sourceId;
                    }
                }
            }

            $result = [
                'documents' => [],
                'database' => [],
                'web' => [],
                'rules_matched' => $matchingRules->count(),
                'context_retrieved' => false,
                'sources_used' => []
            ];

            // Retrieve information from vector database (documents)
            if ($enabledSources['documents'] && count($result['documents']) < $limit) {
                $documentsNeeded = $limit - count($result['documents']);
                $documentResults = $this->retrieveFromDocuments($query, $projectId, $sources, $documentsNeeded, $threshold);
                $result['documents'] = $documentResults;

                // Mark which sources were used
                foreach ($documentResults as $doc) {
                    $result['sources_used'][] = [
                        'type' => 'document',
                        'id' => $doc['document_id'],
                        'name' => $doc['document_name'],
                        'relevance' => $doc['relevance']
                    ];
                }
            }

            // Retrieve information from database sources
            if ($enabledSources['database'] && count($result['database']) < $limit) {
                $dbNeeded = $limit - count($result['database']);
                $dbResults = $this->retrieveFromDatabase($query, $projectId, $sources, $dbNeeded, $threshold);
                $result['database'] = $dbResults;

                // Mark which sources were used
                foreach ($dbResults as $db) {
                    $result['sources_used'][] = [
                        'type' => 'database',
                        'id' => $db['connection_id'],
                        'name' => $db['source_name'],
                        'relevance' => $db['relevance']
                    ];
                }
            }

            // Retrieve information from web sources
            if ($enabledSources['web'] && count($result['web']) < $limit) {
                $webNeeded = $limit - count($result['web']);
                $webResults = $this->retrieveFromWebScrapes($query, $projectId, $sources, $webNeeded, $threshold);
                $result['web'] = $webResults;

                // Mark which sources were used
                foreach ($webResults as $web) {
                    $result['sources_used'][] = [
                        'type' => 'web',
                        'id' => $web['url_id'],
                        'name' => $web['url'],
                        'relevance' => $web['relevance']
                    ];
                }
            }

            // Flag if we retrieved any context
            $result['context_retrieved'] =
                !empty($result['documents']) ||
                !empty($result['database']) ||
                !empty($result['web']);

            return $result;

        } catch (Exception $e) {
            Log::error('Error retrieving knowledge for query: ' . $e->getMessage(), [
                'query' => $query,
                'project_id' => $projectId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'documents' => [],
                'database' => [],
                'web' => [],
                'rules_matched' => 0,
                'context_retrieved' => false,
                'sources_used' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get context settings for a project
     *
     * @param int $projectId
     * @return array
     */
    protected function getContextSettings(int $projectId): array
    {
        $settings = ContextSetting::where('project_id', $projectId)->first();

        if (!$settings) {
            return [
                'priority' => [
                    'documents' => 80,
                    'database' => 60,
                    'web' => 40
                ],
                'context_retention' => 'session',
                'relevance_threshold' => 0.75,
                'max_sources_per_query' => 3,
                'enabled_sources' => [
                    'documents' => true,
                    'database' => true,
                    'web' => false
                ]
            ];
        }

        return $settings->toArray();
    }

    /**
     * Retrieve information from document sources
     *
     * @param string $query
     * @param int $projectId
     * @param array $preferredSources
     * @param int $limit
     * @param float $threshold
     * @return array
     */
    protected function retrieveFromDocuments(string $query, int $projectId, array $preferredSources = [], int $limit = 3, float $threshold = 0.7): array
    {
        try {
            // Get vector similarity search results
            $searchResults = $this->embeddingService->searchSimilarDocuments(
                $query,
                ['project_id' => $projectId, 'source_ids' => $preferredSources],
                $limit,
                $threshold
            );

            // Format the results
            $results = [];
            foreach ($searchResults['results'] ?? [] as $result) {
                $results[] = [
                    'content' => $result['chunk_text'],
                    'document_id' => $result['document_id'],
                    'document_name' => $result['document_name'] ?? 'Unknown Document',
                    'chunk_index' => $result['chunk_index'],
                    'relevance' => $result['similarity']
                ];
            }

            return $results;

        } catch (Exception $e) {
            Log::error('Error retrieving from documents: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Retrieve information from database sources
     *
     * @param string $query
     * @param int $projectId
     * @param array $preferredSources
     * @param int $limit
     * @param float $threshold
     * @return array
     */
    protected function retrieveFromDatabase(string $query, int $projectId, array $preferredSources = [], int $limit = 3, float $threshold = 0.7): array
    {
        try {
            // Get active database connections for this project
            $connections = DB::table('database_connections')
                ->where('project_id', $projectId)
                ->where('status', 'active')
                ->when(!empty($preferredSources), function ($query) use ($preferredSources) {
                    return $query->whereIn('id', $preferredSources);
                })
                ->get();

            if ($connections->isEmpty()) {
                return [];
            }

            $results = [];
            $keywords = $this->extractKeywords($query);

            // Process each connection
            foreach ($connections as $connection) {
                try {
                    // Create a dynamic database connection
                    $connectionConfig = [
                        'driver' => $connection->driver,
                        'host' => $connection->host,
                        'port' => $connection->port,
                        'database' => $connection->database,
                        'username' => $connection->username,
                        'password' => $connection->password,
                        'charset' => 'utf8mb4',
                        'collation' => 'utf8mb4_unicode_ci',
                        'prefix' => '',
                    ];

                    // Create a dynamic connection
                    config(["database.connections.dynamic_{$connection->id}" => $connectionConfig]);
                    $dynamicDb = DB::connection("dynamic_{$connection->id}");

                    // Get tables to search from the connection config
                    $searchTables = json_decode($connection->config ?? '{}', true)['search_tables'] ?? [];

                    if (empty($searchTables)) {
                        // If no tables specified, get all tables from the database
                        $tableQuery = "SELECT table_name FROM information_schema.tables
                                      WHERE table_schema = ? AND table_type = 'BASE TABLE'";
                        $tables = $dynamicDb->select($tableQuery, [$connection->database]);
                        $searchTables = array_map(function($table) {
                            return $table->table_name;
                        }, $tables);
                    }

                    // Search each table
                    foreach ($searchTables as $tableName) {
                        try {
                            // Get columns for this table
                            $columnsQuery = "SELECT column_name, data_type
                                           FROM information_schema.columns
                                           WHERE table_schema = ? AND table_name = ?";
                            $columns = $dynamicDb->select($columnsQuery, [$connection->database, $tableName]);

                            // Filter text and string columns for search
                            $textColumns = [];
                            foreach ($columns as $column) {
                                $dataType = strtolower($column->data_type);
                                if (in_array($dataType, ['varchar', 'text', 'longtext', 'char', 'mediumtext', 'tinytext'])) {
                                    $textColumns[] = $column->column_name;
                                }
                            }

                            if (empty($textColumns)) {
                                continue; // Skip tables with no text columns
                            }

                            // Build WHERE conditions for keyword search
                            $conditions = [];
                            $params = [];

                            foreach ($keywords as $keyword) {
                                foreach ($textColumns as $column) {
                                    $conditions[] = "`$column` LIKE ?";
                                    $params[] = "%{$keyword}%";
                                }
                            }

                            if (empty($conditions)) {
                                continue;
                            }

                            // Build the search query
                            $searchQuery = "SELECT * FROM `$tableName` WHERE " . implode(' OR ', $conditions) . " LIMIT 50";
                            $rows = $dynamicDb->select($searchQuery, $params);

                            if (empty($rows)) {
                                continue;
                            }

                            // Process the results
                            foreach ($rows as $row) {
                                $rowText = "Table {$tableName}: ";

                                // Format row data as key-value pairs
                                foreach ((array)$row as $column => $value) {
                                    if (is_string($value) && strlen($value) > 0) {
                                        $rowText .= "{$column}: {$value}; ";
                                    }
                                }

                                // Calculate relevance score based on keyword matches
                                $relevanceScore = 0;
                                foreach ($keywords as $keyword) {
                                    if (stripos($rowText, $keyword) !== false) {
                                        $relevanceScore += 0.1;
                                    }
                                }

                                // Apply threshold
                                if ($relevanceScore >= $threshold) {
                                    $results[] = [
                                        'content' => $rowText,
                                        'connection_id' => $connection->id,
                                        'source_name' => $connection->name . ' - ' . $tableName,
                                        'relevance' => min(1.0, $relevanceScore)
                                    ];

                                    // Check if we have enough results
                                    if (count($results) >= $limit) {
                                        break 2; // Break both loops
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            Log::error("Error searching table {$tableName}: " . $e->getMessage());
                            continue;
                        }
                    }
                } catch (Exception $e) {
                    Log::error("Error connecting to database {$connection->name}: " . $e->getMessage());
                    continue;
                } finally {
                    // Close the dynamic connection
                    if (isset($dynamicDb)) {
                        $dynamicDb->disconnect();
                        DB::purge("dynamic_{$connection->id}");
                    }
                }
            }

            // Sort results by relevance (highest first)
            usort($results, function($a, $b) {
                return $b['relevance'] <=> $a['relevance'];
            });

            return array_slice($results, 0, $limit);

        } catch (Exception $e) {
            Log::error('Error retrieving from database: ' . $e->getMessage(), [
                'project_id' => $projectId,
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Extract keywords from a natural language query
     *
     * @param string $query
     * @return array
     */
    protected function extractKeywords(string $query): array
    {
        // Remove stop words and extract meaningful keywords
        $stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as', 'into', 'like', 'through', 'after', 'before', 'between', 'from', 'of', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'shall', 'should', 'can', 'could', 'may', 'might', 'must', 'that', 'which', 'who', 'whom', 'whose', 'what', 'where', 'when', 'why', 'how'];

        // Convert to lowercase
        $query = strtolower($query);

        // Remove punctuation
        $query = preg_replace('/[^\w\s]/', ' ', $query);

        // Split into words
        $words = preg_split('/\s+/', $query);

        // Filter out stop words and short words
        $keywords = array_filter($words, function($word) use ($stopWords) {
            return !in_array($word, $stopWords) && strlen($word) > 2;
        });

        return array_values($keywords);
    }

    /**
     * Retrieve information from web scrapes
     *
     * @param string $query
     * @param int $projectId
     * @param array $preferredSources
     * @param int $limit
     * @param float $threshold
     * @return array
     */
    protected function retrieveFromWebScrapes(string $query, int $projectId, array $preferredSources = [], int $limit = 3, float $threshold = 0.7): array
    {
        try {
            // Get scraped URLs for this project
            $urls = ScrapedUrl::where('project_id', $projectId)
                ->where('status', 'active')
                ->when(!empty($preferredSources), function ($query) use ($preferredSources) {
                    return $query->whereIn('id', $preferredSources);
                })
                ->get();

            if ($urls->isEmpty()) {
                return [];
            }

            $results = [];
            $keywords = $this->extractKeywords($query);

            foreach ($urls as $url) {
                // Get the cached content for this URL
                $scrapedContent = $this->scraperService->getScrapedContent($url->id);

                if (!$scrapedContent || empty($scrapedContent['content'])) {
                    continue;
                }

                // Split content into paragraphs for better relevance matching
                $paragraphs = $this->splitContentIntoParagraphs($scrapedContent['content']);

                foreach ($paragraphs as $paragraph) {
                    // Skip very short paragraphs
                    if (strlen($paragraph) < 50) {
                        continue;
                    }

                    // Calculate relevance score for this paragraph
                    $relevanceScore = $this->calculateRelevance($paragraph, $keywords);

                    // Only include if it meets the threshold
                    if ($relevanceScore >= $threshold) {
                        $results[] = [
                            'content' => $paragraph,
                            'url_id' => $url->id,
                            'url' => $url->url,
                            'title' => $url->title ?? parse_url($url->url, PHP_URL_HOST),
                            'relevance' => $relevanceScore
                        ];
                    }
                }
            }

            // Sort results by relevance (highest first)
            usort($results, function($a, $b) {
                return $b['relevance'] <=> $a['relevance'];
            });

            // Return only the requested number of results
            return array_slice($results, 0, $limit);

        } catch (Exception $e) {
            Log::error('Error retrieving from web scrapes: ' . $e->getMessage(), [
                'project_id' => $projectId,
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Split content into meaningful paragraphs
     *
     * @param string $content
     * @return array
     */
    protected function splitContentIntoParagraphs(string $content): array
    {
        // Remove excessive whitespace
        $content = preg_replace('/\s+/', ' ', $content);

        // Split by double newlines or other paragraph markers
        $paragraphs = preg_split('/(\r?\n){2,}|<\/p>|<br\s*\/?>\s*<br\s*\/?>/', $content);

        // Further split very long paragraphs
        $result = [];
        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (empty($paragraph)) {
                continue;
            }

            // If paragraph is very long, split it by sentences
            if (strlen($paragraph) > 1000) {
                $sentences = preg_split('/(?<=[.!?])\s+/', $paragraph);
                $currentParagraph = '';

                foreach ($sentences as $sentence) {
                    if (strlen($currentParagraph) + strlen($sentence) < 1000) {
                        $currentParagraph .= ' ' . $sentence;
                    } else {
                        if (!empty($currentParagraph)) {
                            $result[] = trim($currentParagraph);
                        }
                        $currentParagraph = $sentence;
                    }
                }

                if (!empty($currentParagraph)) {
                    $result[] = trim($currentParagraph);
                }
            } else {
                $result[] = $paragraph;
            }
        }

        return $result;
    }

    /**
     * Calculate relevance score for a paragraph based on keywords
     *
     * @param string $paragraph
     * @param array $keywords
     * @return float
     */
    protected function calculateRelevance(string $paragraph, array $keywords): float
    {
        $relevanceScore = 0;
        $lowerParagraph = strtolower($paragraph);

        // Base score on keyword frequency and position
        foreach ($keywords as $keyword) {
            // Count occurrences of the keyword
            $count = substr_count($lowerParagraph, $keyword);

            if ($count > 0) {
                // More occurrences = more relevant
                $relevanceScore += min(0.2, $count * 0.05);

                // Keywords at the beginning are more important
                $position = stripos($lowerParagraph, $keyword);
                if ($position !== false && $position < 100) {
                    $relevanceScore += 0.1;
                }
            }
        }

        // Adjust based on paragraph length (prefer medium-length paragraphs)
        $length = strlen($paragraph);
        if ($length > 100 && $length < 1000) {
            $relevanceScore += 0.1;
        } else if ($length > 1000) {
            $relevanceScore -= 0.1; // Penalize very long paragraphs
        }

        // Cap the score at 1.0
        return min(1.0, $relevanceScore);
    }

    /**
     * Format knowledge context for AI prompt
     *
     * @param array $knowledgeData
     * @return string
     */
    public function formatKnowledgeForAIPrompt(array $knowledgeData): string
    {
        if (empty($knowledgeData['documents']) && empty($knowledgeData['database']) && empty($knowledgeData['web'])) {
            return '';
        }

        $context = "I'll answer based on the following information:\n\n";

        // Add document information
        if (!empty($knowledgeData['documents'])) {
            $context .= "Document Information:\n";
            foreach ($knowledgeData['documents'] as $index => $doc) {
                $context .= "- {$doc['content']}\n";
            }
            $context .= "\n";
        }

        // Add database information
        if (!empty($knowledgeData['database'])) {
            $context .= "Database Information:\n";
            foreach ($knowledgeData['database'] as $index => $db) {
                $context .= "- {$db['content']}\n";
            }
            $context .= "\n";
        }

        // Add web information
        if (!empty($knowledgeData['web'])) {
            $context .= "Web Information:\n";
            foreach ($knowledgeData['web'] as $index => $web) {
                $context .= "- {$web['content']}\n";
            }
            $context .= "\n";
        }

        $context .= "Based on this information, I'll provide a comprehensive and accurate answer.";

        return $context;
    }
}

