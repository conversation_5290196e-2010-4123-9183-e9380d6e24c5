import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { UseFormReturn } from 'react-hook-form';

// Import feature-specific modal components
import PreChatModal from './modals/PreChatModal';
import PostChatModal from './modals/PostChatModal';
import WebhookModal from './modals/WebhookModal';
import DomainModal from './modals/DomainModal';
import PersistenceModal from './modals/PersistenceModal';
import MobileModal from './modals/MobileModal';
import CustomCSSModal from './modals/CustomCSSModal';
import AIModelModal from './modals/AIModelModal';
import LogoUploadModal from './modals/LogoUploadModal';

interface FeatureModalsProps {
  activeModal: string | null;
  onClose: () => void;
  form: UseFormReturn<any>;
  widgetId?: string;
}

const modalConfig = {
  preChat: {
    title: "Collect Visitor Information",
    description: "Set up a form to collect visitor details before chat starts",
    component: PreChatModal,
  },
  postChat: {
    title: "Post-Chat Survey",
    description: "Collect feedback and ratings after chat sessions",
    component: PostChatModal,
  },
  webhooks: {
    title: "Connect Your Tools",
    description: "Send chat data to Slack, email, CRM, or any webhook URL",
    component: WebhookModal,
  },
  domainRestriction: {
    title: "Control Widget Access",
    description: "Choose which websites can display your widget",
    component: DomainModal,
  },
  conversationPersistence: {
    title: "Remember Conversations",
    description: "Configure how chat history is stored and retrieved",
    component: PersistenceModal,
  },
  mobileOptimization: {
    title: "Mobile Experience",
    description: "Optimize widget display and behavior for mobile devices",
    component: MobileModal,
  },
  customCSS: {
    title: "Custom CSS Styling",
    description: "Add custom CSS to completely customize your widget's appearance",
    component: CustomCSSModal,
  },
  aiModelSelection: {
    title: "AI Model Selection",
    description: "Choose specific AI models for optimal performance",
    component: AIModelModal,
  },
  logoUpload: {
    title: "Widget Logo & Avatar",
    description: "Upload custom logo to strengthen brand identity",
    component: LogoUploadModal,
  },
};

/**
 * Feature Modals Component
 *
 * Manages all feature configuration modals in a centralized way.
 * Each modal is a separate component for better maintainability.
 */
const FeatureModals = ({ activeModal, onClose, form, widgetId }: FeatureModalsProps) => {
  if (!activeModal || !modalConfig[activeModal as keyof typeof modalConfig]) {
    return null;
  }

  const config = modalConfig[activeModal as keyof typeof modalConfig];
  const ModalComponent = config.component;

  return (
    <Dialog open={!!activeModal} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">{config.title}</DialogTitle>
          <p className="text-muted-foreground text-sm">{config.description}</p>
        </DialogHeader>

        <div className="mt-6">
          <ModalComponent
            form={form}
            onClose={onClose}
            widgetId={widgetId}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FeatureModals;
