import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, ExternalLink } from "lucide-react"
import { AdminDashboard } from "@/components/admin/admin-dashboard"
import { apiService } from "@/utils/api-service"
import { useToast } from "@/hooks/use-toast"

interface Widget {
  id: number
  widget_id: string
  name: string
  is_active: boolean
  settings: any
}

export default function WidgetAdminPage() {
  const { widgetId } = useParams<{ widgetId: string }>()
  const navigate = useNavigate()
  const [widget, setWidget] = useState<Widget | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const fetchWidget = async () => {
    if (!widgetId) return

    try {
      setLoading(true)
      setError(null)
      
      // First try to get widget by widget_id
      const response = await apiService.get(`/widgets`)
      const widgets = response.data.data || response.data
      
      const foundWidget = widgets.find((w: Widget) => w.widget_id === widgetId)
      
      if (!foundWidget) {
        setError('Widget not found')
        return
      }
      
      setWidget(foundWidget)
    } catch (error: any) {
      console.error("Failed to fetch widget:", error)
      setError(error.response?.data?.message || 'Failed to load widget')
      toast({
        title: "Error",
        description: "Failed to load widget information",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBackToWidgets = () => {
    navigate('/dashboard/widgets')
  }

  const handleOpenWidget = () => {
    if (widget) {
      window.open(`/widgets/${widget.id}`, '_blank')
    }
  }

  useEffect(() => {
    fetchWidget()
  }, [widgetId])

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBackToWidgets}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Widgets
            </Button>
          </div>
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-xl font-semibold mb-2">Widget Not Found</h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={handleBackToWidgets}>
                Return to Widgets
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!widget) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBackToWidgets}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Widgets
            </Button>
          </div>
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-xl font-semibold mb-2">Widget Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The widget with ID "{widgetId}" could not be found.
              </p>
              <Button onClick={handleBackToWidgets}>
                Return to Widgets
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBackToWidgets}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Widgets
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{widget.name}</h1>
              <p className="text-muted-foreground">
                Widget ID: {widget.widget_id} • 
                Status: {widget.is_active ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleOpenWidget}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Open Widget
            </Button>
          </div>
        </div>

        {/* Admin Dashboard */}
        <AdminDashboard 
          widgetId={widget.widget_id} 
          widgetName={widget.name}
        />
      </div>
    </div>
  )
}
