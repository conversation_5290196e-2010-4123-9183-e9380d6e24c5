<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\KnowledgeDocument;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Smalot\PdfParser\Parser as PdfParser;
use PhpOffice\PhpWord\IOFactory as WordParser;
use PhpOffice\PhpSpreadsheet\IOFactory as SpreadsheetParser;
use Exception;
use Throwable;

class DocumentProcessingService
{
    /**
     * Supported file types for text extraction
     */
    protected $supportedFileTypes = [
        'pdf' => 'PDF Document',
        'docx' => 'Word Document (DOCX)',
        'doc' => 'Word Document (DOC)',
        'txt' => 'Text File',
        'csv' => 'CSV File',
        'html' => 'HTML File',
        'htm' => 'HTML File',
        'md' => 'Markdown File',
        'json' => 'JSON File',
        'xml' => 'XML File',
        'xlsx' => 'Excel Spreadsheet (XLSX)',
        'xls' => 'Excel Spreadsheet (XLS)',
        'pptx' => 'PowerPoint Presentation',
        'rtf' => 'Rich Text Format',
    ];

    /**
     * @var array
     */
    protected $supportedFormats = [
        'pdf' => ['application/pdf', 'application/x-pdf'],
        'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        'doc' => ['application/msword'],
        'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
        'xls' => ['application/vnd.ms-excel'],
        'csv' => ['text/csv', 'text/plain'],
        'txt' => ['text/plain'],
        'json' => ['application/json'],
        'html' => ['text/html'],
        'htm' => ['text/html'],
        'markdown' => ['text/markdown', 'text/plain'],
        'md' => ['text/markdown', 'text/plain'],
    ];

    /**
     * Get list of supported file types
     *
     * @return array
     */
    public function getSupportedFileTypes(): array
    {
        return $this->supportedFileTypes;
    }

    /**
     * Check if a file type is supported
     *
     * @param string $extension
     * @return bool
     */
    public function isFileTypeSupported(string $extension): bool
    {
        return array_key_exists(strtolower($extension), $this->supportedFileTypes);
    }

    /**
     * Extract text from a document based on its file type
     *
     * @param KnowledgeDocument $document
     * @return string|null
     */
    public function extractText(KnowledgeDocument $document): ?string
    {
        $cacheKey = "document_text_{$document->id}";

        // Check if we already have the extracted text in cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            if (!Storage::exists($document->file_path)) {
                Log::error("Document file not found: {$document->file_path}");
                $this->updateDocumentStatus($document, 'failed', 'File not found');
                return null;
            }

            $filePath = Storage::path($document->file_path);
            $extension = strtolower(pathinfo($document->file_name, PATHINFO_EXTENSION));

            // Check if file type is supported
            if (!$this->isFileTypeSupported($extension)) {
                Log::warning("Unsupported file type for text extraction: {$extension}");
                $this->updateDocumentStatus($document, 'failed', "Unsupported file type: {$extension}");
                return null;
            }

            // Update document status to processing
            $this->updateDocumentStatus($document, 'processing', 'Extracting text');

            $extractedText = null;

            switch ($extension) {
                case 'pdf':
                    $extractedText = $this->extractFromPdf($filePath);
                    break;
                case 'docx':
                    $extractedText = $this->extractFromDocx($filePath);
                    break;
                case 'doc':
                    $extractedText = $this->extractFromDoc($filePath);
                    break;
                case 'txt':
                case 'md':
                    $extractedText = $this->extractFromTxt($filePath);
                    break;
                case 'csv':
                    $extractedText = $this->extractFromCsv($filePath);
                    break;
                case 'html':
                case 'htm':
                    $extractedText = $this->extractFromHtml($filePath);
                    break;
                case 'json':
                    $extractedText = $this->extractFromJson($filePath);
                    break;
                case 'xml':
                    $extractedText = $this->extractFromXml($filePath);
                    break;
                case 'xlsx':
                case 'xls':
                    $extractedText = $this->extractFromExcel($filePath);
                    break;
                case 'pptx':
                    $extractedText = $this->extractFromPowerPoint($filePath);
                    break;
                case 'rtf':
                    $extractedText = $this->extractFromRtf($filePath);
                    break;
            }

            // Check if extraction was successful
            if (empty($extractedText)) {
                Log::warning("Failed to extract text from {$document->file_name}");
                $this->updateDocumentStatus($document, 'failed', 'Failed to extract text');
                return null;
            }

            // Cache the extracted text
            Cache::put($cacheKey, $extractedText, 3600); // 1 hour

            // Update document status to ready
            $this->updateDocumentStatus($document, 'ready');

            return $extractedText;
        } catch (Throwable $e) {
            Log::error("Error extracting text from document {$document->id}: " . $e->getMessage());
            $this->updateDocumentStatus($document, 'failed', $e->getMessage());
            return null;
        }
    }

    /**
     * Update document status with error tracking
     *
     * @param KnowledgeDocument $document
     * @param string $status
     * @param string|null $errorMessage
     * @return void
     */
    protected function updateDocumentStatus(KnowledgeDocument $document, string $status, ?string $errorMessage = null): void
    {
        $updateData = ['status' => $status];

        if ($errorMessage) {
            $metadata = $document->metadata ?? [];
            $metadata['last_error'] = $errorMessage;
            $metadata['error_timestamp'] = now()->toIso8601String();
            $updateData['metadata'] = $metadata;
        }

        $document->update($updateData);
    }

    /**
     * Extract text from a PDF file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromPdf(string $filePath): string
    {
        try {
            // Try using Smalot PDF Parser first
            $parser = new PdfParser();
            $pdf = $parser->parseFile($filePath);
            $text = $pdf->getText();

            // If text is empty or very short, try alternative methods
            if (empty($text) || strlen($text) < 100) {
                // Try using pdftotext command if available
                if (function_exists('exec') && exec('which pdftotext')) {
                    $tempOutput = tempnam(sys_get_temp_dir(), 'pdf_');
                    exec("pdftotext -layout " . escapeshellarg($filePath) . " " . escapeshellarg($tempOutput));
                    $alternativeText = file_get_contents($tempOutput);
                    @unlink($tempOutput);

                    if (!empty($alternativeText) && strlen($alternativeText) > strlen($text)) {
                        $text = $alternativeText;
                    }
                }
            }

            // Clean up the text
            $text = $this->cleanExtractedText($text);

            return $text;
        } catch (Throwable $e) {
            Log::error("Error extracting text from PDF: " . $e->getMessage());

            // Fallback to pdftotext command
            if (function_exists('exec') && exec('which pdftotext')) {
                try {
                    $tempOutput = tempnam(sys_get_temp_dir(), 'pdf_');
                    exec("pdftotext -layout " . escapeshellarg($filePath) . " " . escapeshellarg($tempOutput));
                    $text = file_get_contents($tempOutput);
                    @unlink($tempOutput);

                    return $this->cleanExtractedText($text);
                } catch (Throwable $e2) {
                    Log::error("Fallback PDF extraction also failed: " . $e2->getMessage());
                    throw $e; // Throw the original exception
                }
            }

            throw $e;
        }
    }

    /**
     * Extract text from a DOCX file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromDocx(string $filePath): string
    {
        try {
            $phpWord = WordParser::load($filePath);
            $text = '';

            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    if (method_exists($element, 'getText')) {
                        $text .= $element->getText() . "\n";
                    } elseif (method_exists($element, 'getElements')) {
                        foreach ($element->getElements() as $childElement) {
                            if (method_exists($childElement, 'getText')) {
                                $text .= $childElement->getText() . "\n";
                            }
                        }
                    }
                }
            }

            return $this->cleanExtractedText($text);
        } catch (Throwable $e) {
            Log::error("Error extracting text from DOCX: " . $e->getMessage());

            // Try using docx2txt command if available
            if (function_exists('exec') && exec('which docx2txt')) {
                $tempOutput = tempnam(sys_get_temp_dir(), 'docx_');
                exec("docx2txt " . escapeshellarg($filePath) . " " . escapeshellarg($tempOutput));
                $text = file_get_contents($tempOutput);
                @unlink($tempOutput);

                return $this->cleanExtractedText($text);
            }

            throw $e;
        }
    }

    /**
     * Extract text from a DOC file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromDoc(string $filePath): string
    {
        try {
            // Try using antiword if available
            if (function_exists('exec') && exec('which antiword')) {
                $command = "antiword " . escapeshellarg($filePath);
                $text = shell_exec($command);
                if ($text !== null && !empty(trim($text))) {
                    return $this->cleanExtractedText($text);
                }
            }

            // Try using catdoc if available
            if (function_exists('exec') && exec('which catdoc')) {
                $command = "catdoc " . escapeshellarg($filePath);
                $text = shell_exec($command);
                if ($text !== null && !empty(trim($text))) {
                    return $this->cleanExtractedText($text);
                }
            }

            // Fallback to reading the file directly and stripping non-printable characters
            $content = file_get_contents($filePath);
            $text = preg_replace('/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/', '', $content);

            return $this->cleanExtractedText($text);
        } catch (Throwable $e) {
            Log::error("Error extracting text from DOC: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract text from a TXT file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromTxt(string $filePath): string
    {
        $text = file_get_contents($filePath);
        return $this->cleanExtractedText($text);
    }

    /**
     * Extract text from a CSV file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromCsv(string $filePath): string
    {
        try {
            $text = '';
            $headers = [];
            $rowCount = 0;

            if (($handle = fopen($filePath, "r")) !== false) {
                while (($data = fgetcsv($handle)) !== false) {
                    if ($rowCount === 0) {
                        // Store headers
                        $headers = $data;
                        $text .= "# " . implode(" | ", $headers) . "\n";
                    } else {
                        // If we have headers, try to create a more structured output
                        if (!empty($headers) && count($headers) === count($data)) {
                            $rowText = "";
                            foreach ($data as $index => $value) {
                                $rowText .= $headers[$index] . ": " . $value . "\n";
                            }
                            $text .= "Row " . $rowCount . ":\n" . $rowText . "\n";
                        } else {
                            // Simple output
                            $text .= implode(" | ", $data) . "\n";
                        }
                    }
                    $rowCount++;
                }
                fclose($handle);
            }

            return $this->cleanExtractedText($text);
        } catch (Throwable $e) {
            Log::error("Error extracting text from CSV: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract text from an HTML file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromHtml(string $filePath): string
    {
        try {
            $html = file_get_contents($filePath);

            // Remove scripts and styles
            $html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $html);
            $html = preg_replace('/<style\b[^>]*>(.*?)<\/style>/is', '', $html);

            // Extract title
            $title = '';
            if (preg_match('/<title>(.*?)<\/title>/is', $html, $matches)) {
                $title = $matches[1];
            }

            // Extract meta description
            $description = '';
            if (preg_match('/<meta\s+name="description"\s+content="([^"]*)"/is', $html, $matches)) {
                $description = $matches[1];
            }

            // Extract main content
            $text = strip_tags($html);
            $text = html_entity_decode($text);

            // Add title and description at the beginning
            $result = '';
            if (!empty($title)) {
                $result .= "Title: " . $title . "\n\n";
            }
            if (!empty($description)) {
                $result .= "Description: " . $description . "\n\n";
            }
            $result .= $text;

            return $this->cleanExtractedText($result);
        } catch (Throwable $e) {
            Log::error("Error extracting text from HTML: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract text from a JSON file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromJson(string $filePath): string
    {
        try {
            $json = file_get_contents($filePath);
            $data = json_decode($json, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON: " . json_last_error_msg());
            }

            // Convert JSON to text representation
            return $this->jsonToText($data);
        } catch (Throwable $e) {
            Log::error("Error extracting text from JSON: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract text from an XML file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromXml(string $filePath): string
    {
        try {
            $xml = file_get_contents($filePath);
            $xml = simplexml_load_string($xml);

            if ($xml === false) {
                throw new Exception("Invalid XML");
            }

            // Convert XML to JSON and then to text
            $json = json_encode($xml);
            $data = json_decode($json, true);

            return $this->jsonToText($data);
        } catch (Throwable $e) {
            Log::error("Error extracting text from XML: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract text from an Excel file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromExcel(string $filePath): string
    {
        try {
            $spreadsheet = SpreadsheetParser::load($filePath);
            $text = '';

            // Process each worksheet
            foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                $sheetName = $worksheet->getTitle();
                $text .= "Sheet: " . $sheetName . "\n\n";

                // Get the highest row and column indexes
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

                // Get headers from first row
                $headers = [];
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $headers[] = $worksheet->getCellByColumnAndRow($col, 1)->getValue() ?? "Column " . $col;
                }

                // Process each row
                for ($row = 2; $row <= $highestRow; $row++) {
                    $rowData = [];
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $value = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                        if ($value !== null) {
                            $rowData[] = $headers[$col - 1] . ": " . $value;
                        }
                    }

                    if (!empty($rowData)) {
                        $text .= "Row " . ($row - 1) . ":\n" . implode("\n", $rowData) . "\n\n";
                    }
                }

                $text .= "\n";
            }

            return $this->cleanExtractedText($text);
        } catch (Throwable $e) {
            Log::error("Error extracting text from Excel: " . $e->getMessage());

            // Try using xlsx2csv command if available
            if (function_exists('exec') && exec('which xlsx2csv')) {
                $tempOutput = tempnam(sys_get_temp_dir(), 'xlsx_');
                exec("xlsx2csv " . escapeshellarg($filePath) . " " . escapeshellarg($tempOutput));
                $text = file_get_contents($tempOutput);
                @unlink($tempOutput);

                return $this->cleanExtractedText($text);
            }

            throw $e;
        }
    }

    /**
     * Extract text from a PowerPoint file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromPowerPoint(string $filePath): string
    {
        try {
            // Try using Apache Tika if available
            if (function_exists('exec') && exec('which tika')) {
                $command = "tika --text " . escapeshellarg($filePath);
                $text = shell_exec($command);
                if ($text !== null && !empty(trim($text))) {
                    return $this->cleanExtractedText($text);
                }
            }

            // Fallback to a simple approach
            $zip = new \ZipArchive();
            if ($zip->open($filePath) === true) {
                $text = '';

                // Extract slide content
                for ($i = 1; $i <= $zip->numFiles; $i++) {
                    $name = $zip->getNameIndex($i);
                    if (strpos($name, 'ppt/slides/slide') === 0 && strpos($name, '.xml') !== false) {
                        $content = $zip->getFromIndex($i);
                        $xml = simplexml_load_string($content);

                        if ($xml) {
                            $slideNumber = preg_replace('/[^0-9]/', '', $name);
                            $text .= "Slide " . $slideNumber . ":\n";

                            $ns = $xml->getNamespaces(true);
                            $slideText = '';

                            // Extract text from text elements
                            if (isset($ns['a'])) {
                                $xml->registerXPathNamespace('a', $ns['a']);
                                $paragraphs = $xml->xpath('//a:t');

                                foreach ($paragraphs as $paragraph) {
                                    $slideText .= (string)$paragraph . "\n";
                                }
                            }

                            $text .= $slideText . "\n\n";
                        }
                    }
                }

                $zip->close();
                return $this->cleanExtractedText($text);
            }

            throw new Exception("Failed to open PowerPoint file");
        } catch (Throwable $e) {
            Log::error("Error extracting text from PowerPoint: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract text from an RTF file
     *
     * @param string $filePath
     * @return string
     */
    protected function extractFromRtf(string $filePath): string
    {
        try {
            // Try using unrtf command if available
            if (function_exists('exec') && exec('which unrtf')) {
                $command = "unrtf --text " . escapeshellarg($filePath);
                $text = shell_exec($command);
                if ($text !== null && !empty(trim($text))) {
                    // Remove unrtf header
                    $text = preg_replace('/^###.+?###/s', '', $text);
                    return $this->cleanExtractedText($text);
                }
            }

            // Fallback to a simple approach
            $content = file_get_contents($filePath);
            $text = $this->rtfToText($content);

            return $this->cleanExtractedText($text);
        } catch (Throwable $e) {
            Log::error("Error extracting text from RTF: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Convert RTF to plain text
     *
     * @param string $rtf
     * @return string
     */
    protected function rtfToText(string $rtf): string
    {
        // Remove RTF formatting
        $pattern = '/\\\\([a-z]{1,32})(-?\\d{1,10})?[ ]?/i';
        $replacement = '';
        $rtf = preg_replace($pattern, $replacement, $rtf);

        // Remove special characters
        $rtf = preg_replace('/[\\\\][\']{1}[0-9a-zA-Z]{2}/', '', $rtf);
        $rtf = preg_replace('/[\\\\][\"]{1}[0-9a-zA-Z]{2}/', '', $rtf);

        // Remove curly braces
        $rtf = preg_replace('/[{}]/', '', $rtf);

        return $rtf;
    }

    /**
     * Convert JSON data to text representation
     *
     * @param array $data
     * @param string $prefix
     * @param int $depth
     * @return string
     */
    protected function jsonToText(array $data, string $prefix = '', int $depth = 0): string
    {
        $text = '';
        $indent = str_repeat('  ', $depth);

        foreach ($data as $key => $value) {
            $fullKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                if (empty($value)) {
                    $text .= $indent . $fullKey . ": []\n";
                } elseif ($this->isAssociative($value)) {
                    $text .= $indent . $fullKey . ":\n";
                    $text .= $this->jsonToText($value, $fullKey, $depth + 1);
                } else {
                    $text .= $indent . $fullKey . ": [";
                    $items = [];
                    foreach ($value as $item) {
                        if (is_scalar($item)) {
                            $items[] = $item;
                        } elseif (is_array($item)) {
                            // For nested arrays, add a newline and process recursively
                            $text .= "\n";
                            $text .= $this->jsonToText([$key => $item], $prefix, $depth + 1);
                        }
                    }
                    if (!empty($items)) {
                        $text .= implode(", ", $items) . "]\n";
                    } else {
                        $text .= "]\n";
                    }
                }
            } else {
                $text .= $indent . $fullKey . ": " . $value . "\n";
            }
        }

        return $text;
    }

    /**
     * Check if an array is associative
     *
     * @param array $array
     * @return bool
     */
    protected function isAssociative(array $array): bool
    {
        if (empty($array)) {
            return false;
        }
        return array_keys($array) !== range(0, count($array) - 1);
    }

    /**
     * Clean extracted text
     *
     * @param string $text
     * @return string
     */
    protected function cleanExtractedText(string $text): string
    {
        // Remove control characters
        $text = preg_replace('/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Replace multiple newlines with a single one
        $text = preg_replace('/\n\s*\n\s*\n+/', "\n\n", $text);

        // Replace multiple spaces with a single one
        $text = preg_replace('/[ \t]+/', ' ', $text);

        // Trim each line
        $lines = explode("\n", $text);
        $lines = array_map('trim', $lines);
        $text = implode("\n", $lines);

        // Trim the entire text
        $text = trim($text);

        return $text;
    }

    /**
     * Process an uploaded document
     *
     * @param UploadedFile $file
     * @param array $data
     * @param int|null $userId
     * @return KnowledgeDocument
     * @throws Exception
     */
    public function processUploadedDocument(UploadedFile $file, array $data, ?int $userId = null): KnowledgeDocument
    {
        try {
            // Validate file
            $extension = strtolower($file->getClientOriginalExtension());

            // Check if file type is supported
            if (!$this->isFileTypeSupported($extension)) {
                throw new Exception("Unsupported file type: {$extension}");
            }

            // Check file size (limit to 50MB)
            $maxSize = 50 * 1024 * 1024; // 50MB in bytes
            if ($file->getSize() > $maxSize) {
                throw new Exception("File size exceeds the maximum limit of 50MB");
            }

            // Generate a unique filename
            $fileName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $safeFileName = Str::slug($fileName);
            $uniqueId = Str::uuid()->toString();
            $storedFileName = $safeFileName . '-' . $uniqueId . '.' . $extension;

            // Store the file
            $path = $file->storeAs('documents', $storedFileName);

            if (!$path) {
                throw new Exception("Failed to store the file");
            }

            // Create metadata
            $metadata = [
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'upload_timestamp' => now()->toIso8601String(),
                'uuid' => $uniqueId,
            ];

            // Create the document record
            $document = KnowledgeDocument::create([
                'source_id' => $data['source_id'],
                'file_path' => $path,
                'file_name' => $file->getClientOriginalName(),
                'file_type' => $extension,
                'size' => $file->getSize(),
                'status' => 'processing',
                'is_active_source' => $data['is_active_source'] ?? true,
                'category' => $data['category'] ?? null,
                'project_id' => $data['project_id'] ?? null,
                'created_by' => $userId,
                'metadata' => $metadata,
            ]);

            // Extract text from the document (this will update the document status)
            $extractedText = $this->extractText($document);

            if ($extractedText) {
                $document->update([
                    'extracted_text' => $extractedText,
                    'status' => 'ready'
                ]);

                // Log success
                Log::info("Document processed successfully: {$document->file_name} (ID: {$document->id})");
            }

            return $document;
        } catch (Throwable $e) {
            Log::error("Error processing uploaded document: " . $e->getMessage());

            // If document was created, update its status
            if (isset($document) && $document instanceof KnowledgeDocument) {
                $this->updateDocumentStatus($document, 'failed', $e->getMessage());
            }

            throw new Exception("Failed to process document: " . $e->getMessage());
        }
    }

    /**
     * Validate a file before upload
     *
     * @param UploadedFile $file
     * @return array
     */
    public function validateFile(UploadedFile $file): array
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $size = $file->getSize();
        $maxSize = 50 * 1024 * 1024; // 50MB

        $result = [
            'valid' => true,
            'errors' => [],
        ];

        // Check file type
        if (!$this->isFileTypeSupported($extension)) {
            $result['valid'] = false;
            $result['errors'][] = "Unsupported file type: {$extension}";
        }

        // Check file size
        if ($size > $maxSize) {
            $result['valid'] = false;
            $result['errors'][] = "File size exceeds the maximum limit of 50MB";
        }

        // Check if file is readable
        if (!$file->isReadable()) {
            $result['valid'] = false;
            $result['errors'][] = "File is not readable";
        }

        return $result;
    }

    /**
     * Check if file format is supported.
     *
     * @param string $filename
     * @param string $mimeType
     * @return bool
     */
    public function isFormatSupported(string $filename, string $mimeType): bool
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        // Check if extension is in supported formats
        if (!isset($this->supportedFormats[$extension])) {
            return false;
        }

        // If mime type is provided, check if it matches
        if ($mimeType && !in_array($mimeType, $this->supportedFormats[$extension])) {
            // Special case for text/plain which can be used for multiple formats
            if ($mimeType === 'text/plain' && in_array($extension, ['txt', 'csv', 'md', 'markdown'])) {
                return true;
            }

            return false;
        }

        return true;
    }

    /**
     * Get list of supported file formats.
     *
     * @return array
     */
    public function getSupportedFormats(): array
    {
        return array_keys($this->supportedFormats);
    }

    /**
     * Validate document file.
     *
     * @param string $filePath
     * @param string $fileName
     * @param string $mimeType
     * @return array
     */
    public function validateDocument(string $filePath, string $fileName, string $mimeType): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
        ];

        // Check if file exists
        if (!file_exists($filePath)) {
            $result['valid'] = false;
            $result['errors'][] = 'File does not exist';
            return $result;
        }

        // Check if format is supported
        if (!$this->isFormatSupported($fileName, $mimeType)) {
            $result['valid'] = false;
            $result['errors'][] = 'File format not supported. Supported formats: ' . implode(', ', $this->getSupportedFormats());
            return $result;
        }

        // Check file size (max 100MB)
        $maxSize = 100 * 1024 * 1024; // 100MB
        $fileSize = filesize($filePath);

        if ($fileSize > $maxSize) {
            $result['valid'] = false;
            $result['errors'][] = 'File size exceeds maximum limit of 100MB';
            return $result;
        }

        return $result;
    }
}
