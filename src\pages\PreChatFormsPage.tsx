import React from 'react';
import { AdminLayout } from "@/components/admin-layout";
import PreChatForms from '@/components/pre-chat-forms/PreChatForms';
import PermissionErrorBoundary from '@/components/error-handling/PermissionErrorBoundary';

const PreChatFormsPage = () => {
  return (
    <AdminLayout>
      <PermissionErrorBoundary 
        feature="widget management"
        paths={['/widgets']}
      >
        <PreChatForms />
      </PermissionErrorBoundary>
    </AdminLayout>
  );
};

export default PreChatFormsPage;
