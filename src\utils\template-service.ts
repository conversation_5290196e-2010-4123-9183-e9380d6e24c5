// Import the API service
import api from './api';

// Helper function to generate a slug from a string
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .replace(/-+/g, '-')      // Replace multiple hyphens with a single one
    .trim();
}

export interface Template {
  id: number;
  name: string;
  description: string;
  category: string;
  content: string;
  version: number;
  is_default: boolean;
  status: string;
  variables: string[];
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
  slug: string; // Make slug required in the interface
}

interface TemplateQueryParams {
  status?: string;
  category?: string;
  is_default?: boolean;
  search?: string;
}

export const templateService = {
  /**
   * Get all templates with optional filters
   * 
   * @param params Filter parameters
   * @returns Array of templates
   */
  getTemplates: async (params: TemplateQueryParams = {}): Promise<Template[]> => {
    try {
      const response = await api.get('/templates', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching templates:', error);
      throw error;
    }
  },

  /**
   * Get a specific template by ID
   * 
   * @param id Template ID
   * @returns Template object
   */
  getTemplate: async (id: number): Promise<Template> => {
    try {
      const response = await api.get(`/templates/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new template
   * 
   * @param data Template data
   * @returns Created template
   */
  createTemplate: async (data: Partial<Template>): Promise<Template> => {
    try {
      // Generate a slug from the name if not provided
      if (!data.slug && data.name) {
        data.slug = generateSlug(data.name);
      }

      const response = await api.post('/templates', data);
      return response.data;
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    }
  },

  /**
   * Update an existing template
   * 
   * @param id Template ID
   * @param data Template data to update
   * @returns Updated template
   */
  updateTemplate: async (id: number, data: Partial<Template>): Promise<Template> => {
    try {
      // Update slug if name is changed and slug is not explicitly provided
      if (data.name && !data.slug) {
        data.slug = generateSlug(data.name);
      }

      const response = await api.put(`/templates/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a template
   * 
   * @param id Template ID
   * @returns Success status
   */
  deleteTemplate: async (id: number): Promise<{ success: boolean }> => {
    try {
      const response = await api.delete(`/templates/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Set a template as the default
   * 
   * @param id Template ID
   * @returns Updated template
   */
  setDefaultTemplate: async (id: number): Promise<Template> => {
    try {
      const response = await api.post(`/templates/${id}/set-default`);
      return response.data;
    } catch (error) {
      console.error(`Error setting template ${id} as default:`, error);
      throw error;
    }
  },

  /**
   * Get templates assigned to a specific AI model
   * 
   * @param modelId AI Model ID
   * @returns Array of templates
   */
  getTemplatesForModel: async (modelId: number): Promise<Template[]> => {
    try {
      const response = await api.get(`/ai-models/${modelId}/templates`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching templates for model ${modelId}:`, error);
      throw error;
    }
  },

  /**
   * Assign a template to an AI model
   * 
   * @param modelId AI Model ID
   * @param templateId Template ID
   * @returns Success status
   */
  assignTemplateToModel: async (modelId: number, templateId: number): Promise<{ success: boolean }> => {
    try {
      const response = await api.post(`/ai-models/${modelId}/templates`, { template_id: templateId });
      return response.data;
    } catch (error) {
      console.error(`Error assigning template ${templateId} to model ${modelId}:`, error);
      throw error;
    }
  },

  /**
   * Test a template by processing its variables
   * 
   * @param id Template ID
   * @param variables Variable values
   * @returns Processed template content
   */
  testTemplate: async (id: number, variables: Record<string, string>): Promise<{ content: string }> => {
    try {
      const response = await api.post(`/templates/${id}/test`, { variables });
      return response.data;
    } catch (error) {
      console.error(`Error testing template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get template categories 
   * 
   * @returns Array of category names
   */
  getCategories: async (): Promise<string[]> => {
    try {
      const response = await api.get('/templates/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching template categories:', error);
      throw error;
    }
  }
};
