<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContextRule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',
        'name',
        'description',
        'sources',
        'keywords',
        'priority',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sources' => 'array',
        'keywords' => 'array',
        'priority' => 'integer',
        'active' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'match_score',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * Get the project that owns the rule.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the match score.
     *
     * @return float|null
     */
    public function getMatchScoreAttribute(): ?float
    {
        return $this->attributes['match_score'] ?? null;
    }

    /**
     * Set the match score.
     *
     * @param float $value
     * @return void
     */
    public function setMatchScoreAttribute(float $value): void
    {
        $this->attributes['match_score'] = $value;
    }
}
