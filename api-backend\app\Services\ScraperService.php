<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\KnowledgeScrapedUrl;
use App\Models\KnowledgeSource;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use DOMDocument;
use DOMXPath;
use Exception;
use Throwable;

class ScraperService
{
    /**
     * User agent strings for different browsers
     */
    protected $userAgents = [
        'chrome' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'firefox' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'safari' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        'edge' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
    ];

    /**
     * Scrape content from a URL
     *
     * @param string $url
     * @param int|null $modelId
     * @param array $options
     * @return array
     */
    public function scrapeUrl(string $url, ?int $modelId = null, array $options = []): array
    {
        // Generate a cache key based on the URL
        $cacheKey = 'scrape_' . md5($url);
        $cacheTtl = $options['cache_ttl'] ?? 3600; // 1 hour default

        // Check cache if caching is enabled
        if (($options['use_cache'] ?? true) && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                throw new Exception('Invalid URL format');
            }

            // Normalize URL
            $url = $this->normalizeUrl($url);

            // Set up HTTP client with options
            $httpOptions = [
                'timeout' => $options['timeout'] ?? 30,
                'connect_timeout' => $options['connect_timeout'] ?? 10,
                'headers' => [
                    'User-Agent' => $this->userAgents[$options['user_agent'] ?? 'chrome'],
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Cache-Control' => 'no-cache',
                    'Pragma' => 'no-cache',
                ],
                'verify' => $options['verify_ssl'] ?? true,
            ];

            // Add cookies if provided
            if (!empty($options['cookies'])) {
                $httpOptions['cookies'] = $options['cookies'];
            }

            // Add proxy if provided
            if (!empty($options['proxy'])) {
                $httpOptions['proxy'] = $options['proxy'];
            }

            // Fetch the URL content
            $response = Http::withOptions($httpOptions)->get($url);

            if (!$response->successful()) {
                // Try with a different user agent if the first one fails
                if ($options['retry_with_different_agent'] ?? true) {
                    $httpOptions['headers']['User-Agent'] = $this->userAgents['firefox'];
                    $response = Http::withOptions($httpOptions)->get($url);

                    if (!$response->successful()) {
                        throw new Exception('Failed to fetch URL: ' . $response->status());
                    }
                } else {
                    throw new Exception('Failed to fetch URL: ' . $response->status());
                }
            }

            $html = $response->body();

            // Check if the page is JavaScript-heavy and might need rendering
            $isJsHeavy = $this->isJavaScriptHeavyPage($html);

            // If the page is JavaScript-heavy and we have a headless browser service available
            if ($isJsHeavy && ($options['render_js'] ?? true) && $this->canUseHeadlessBrowser()) {
                try {
                    $renderedHtml = $this->renderWithHeadlessBrowser($url, $options);
                    if (!empty($renderedHtml)) {
                        $html = $renderedHtml;
                    }
                } catch (Throwable $e) {
                    Log::warning('Failed to render JavaScript-heavy page: ' . $e->getMessage());
                    // Continue with the original HTML
                }
            }

            // Parse the HTML content
            $dom = new DOMDocument();
            @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
            $xpath = new DOMXPath($dom);

            // Extract metadata
            $metadata = $this->extractMetadata($dom, $xpath);

            // Extract title
            $title = $metadata['title'] ?? '';
            if (empty($title)) {
                $titleTags = $xpath->query('//title');
                if ($titleTags->length > 0) {
                    $title = $titleTags->item(0)->textContent;
                }
            }

            // Extract main content text
            $textContent = $this->extractTextContent($dom, $xpath);

            // Extract tables
            $tables = $this->extractTables($dom, $xpath);

            // Extract JSON-LD structured data
            $jsonData = $this->extractJsonLd($dom, $xpath);

            // Extract links
            $links = $this->extractLinks($dom, $xpath, $url);

            // Extract images
            $images = $this->extractImages($dom, $xpath, $url);

            // Prepare result
            $result = [
                'title' => $title,
                'text' => $textContent,
                'raw' => $html,
                'table' => $tables,
                'json' => $jsonData,
                'url' => $url,
                'metadata' => $metadata,
                'links' => $links,
                'images' => $images,
                'is_js_heavy' => $isJsHeavy,
                'scraped_at' => now()->toIso8601String(),
            ];

            // Cache the result if caching is enabled
            if ($options['use_cache'] ?? true) {
                Cache::put($cacheKey, $result, $cacheTtl);
            }

            return $result;
        } catch (Throwable $e) {
            Log::error('Error scraping URL: ' . $e->getMessage());
            throw new Exception('Failed to scrape URL: ' . $e->getMessage());
        }
    }

    /**
     * Normalize a URL
     *
     * @param string $url
     * @return string
     */
    protected function normalizeUrl(string $url): string
    {
        // Add https:// if no protocol is specified
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            $url = 'https://' . $url;
        }

        // Remove trailing slash
        $url = rtrim($url, '/');

        return $url;
    }

    /**
     * Check if a page is JavaScript-heavy
     *
     * @param string $html
     * @return bool
     */
    protected function isJavaScriptHeavyPage(string $html): bool
    {
        // Check for common JavaScript frameworks
        $jsFrameworks = [
            'react', 'vue', 'angular', 'ember', 'backbone', 'knockout', 'meteor', 'polymer',
            'next', 'nuxt', 'gatsby', 'svelte', 'alpine', 'preact', 'lit-element', 'stimulus',
        ];

        foreach ($jsFrameworks as $framework) {
            if (stripos($html, $framework) !== false) {
                return true;
            }
        }

        // Check for SPA indicators
        $spaIndicators = [
            '<div id="app"', '<div id="root"', 'data-reactroot', 'ng-app', 'v-app', 'ember-app',
            'data-server-rendered', '__NEXT_DATA__', '__NUXT__', 'window.__PRELOADED_STATE__',
        ];

        foreach ($spaIndicators as $indicator) {
            if (stripos($html, $indicator) !== false) {
                return true;
            }
        }

        // Check for a high ratio of script tags to content
        $scriptCount = substr_count(strtolower($html), '<script');
        $contentSize = strlen(strip_tags($html));

        if ($scriptCount > 10 && $contentSize < 1000) {
            return true;
        }

        return false;
    }

    /**
     * Check if we can use a headless browser
     *
     * @return bool
     */
    protected function canUseHeadlessBrowser(): bool
    {
        // Check if we have a headless browser service available
        // This could be Puppeteer, Playwright, or a service like Browserless
        return function_exists('exec') && (
            exec('which node') && (
                file_exists(base_path('node_modules/puppeteer')) ||
                file_exists(base_path('node_modules/playwright'))
            )
        );
    }

    /**
     * Render a page with a headless browser
     *
     * @param string $url
     * @param array $options
     * @return string
     */
    protected function renderWithHeadlessBrowser(string $url, array $options = []): string
    {
        // Create a temporary script file
        $scriptPath = storage_path('app/temp/render_script_' . Str::random(10) . '.js');
        $outputPath = storage_path('app/temp/rendered_' . Str::random(10) . '.html');

        // Ensure directory exists
        if (!file_exists(dirname($scriptPath))) {
            mkdir(dirname($scriptPath), 0755, true);
        }

        // Determine which headless browser to use
        $usePuppeteer = file_exists(base_path('node_modules/puppeteer'));

        // Authentication options
        $authCode = '';
        if (!empty($options['auth'])) {
            if (!empty($options['auth']['username']) && !empty($options['auth']['password'])) {
                // Basic authentication
                $authCode = <<<JS
  // Set HTTP authentication
  await page.authenticate({
    username: '{$options['auth']['username']}',
    password: '{$options['auth']['password']}'
  });
JS;
            } elseif (!empty($options['auth']['cookies'])) {
                // Cookie authentication
                $cookiesJson = json_encode($options['auth']['cookies']);
                $authCode = <<<JS
  // Set cookies for authentication
  await page.setCookie(...{$cookiesJson});
JS;
            } elseif (!empty($options['auth']['form'])) {
                // Form-based authentication
                $formAuth = $options['auth']['form'];
                $loginUrl = $formAuth['url'] ?? $url;
                $usernameSelector = $formAuth['username_selector'] ?? '';
                $passwordSelector = $formAuth['password_selector'] ?? '';
                $submitSelector = $formAuth['submit_selector'] ?? '';
                $username = $formAuth['username'] ?? '';
                $password = $formAuth['password'] ?? '';

                $renderTimeout = isset($options['render_timeout']) ? $options['render_timeout'] : 30000;

                if ($usePuppeteer) {
                    $authCode = <<<JS
  // Perform form-based authentication
  await page.goto('{$loginUrl}', { waitUntil: 'networkidle2', timeout: {$renderTimeout} });

  // Fill in the login form
  await page.waitForSelector('{$usernameSelector}');
  await page.type('{$usernameSelector}', '{$username}');
  await page.type('{$passwordSelector}', '{$password}');

  // Submit the form
  await Promise.all([
    page.waitForNavigation({ waitUntil: 'networkidle2', timeout: {$renderTimeout} }),
    page.click('{$submitSelector}')
  ]);
JS;
                } else {
                    // Playwright version
                    $authCode = <<<JS
  // Perform form-based authentication
  await page.goto('{$loginUrl}', { waitUntil: 'networkidle', timeout: {$renderTimeout} });

  // Fill in the login form
  await page.waitForSelector('{$usernameSelector}');
  await page.fill('{$usernameSelector}', '{$username}');
  await page.fill('{$passwordSelector}', '{$password}');

  // Submit the form
  await Promise.all([
    page.waitForNavigation({ waitUntil: 'networkidle', timeout: {$renderTimeout} }),
    page.click('{$submitSelector}')
  ]);
JS;
                }
            }
        }

        if ($usePuppeteer) {
            // Get render timeout
            $renderTimeout = $options['render_timeout'] ?? 30000;

            // Create Puppeteer script
            $script = <<<JS
const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  const page = await browser.newPage();

  // Set viewport
  await page.setViewport({
    width: 1280,
    height: 800
  });

  // Set user agent
  await page.setUserAgent('{$this->userAgents['chrome']}');

{$authCode}

  try {
    // Navigate to the page
    await page.goto('{$url}', {
      waitUntil: 'networkidle2',
      timeout: {$renderTimeout}
    });

    // Wait for content to load
    await page.waitForSelector('body', { timeout: 5000 });

    // Optional: Wait for specific selector if provided
    {$this->generateWaitForSelectorCode($options)}

    // Optional: Scroll to bottom to trigger lazy loading
    {$this->generateScrollCode($options)}

    // Get the page content
    const html = await page.content();

    // Save to file
    const fs = require('fs');
    fs.writeFileSync('{$outputPath}', html);
  } catch (error) {
    console.error('Error rendering page:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
})();
JS;
        } else {
            // Get render timeout
            $renderTimeout = $options['render_timeout'] ?? 30000;

            // Create Playwright script
            $script = <<<JS
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({
    headless: true
  });
  const context = await browser.newContext({
    userAgent: '{$this->userAgents['chrome']}',
    viewport: {
      width: 1280,
      height: 800
    }
  });
  const page = await context.newPage();

{$authCode}

  try {
    // Navigate to the page
    await page.goto('{$url}', {
      waitUntil: 'networkidle',
      timeout: {$renderTimeout}
    });

    // Wait for content to load
    await page.waitForSelector('body', { timeout: 5000 });

    // Optional: Wait for specific selector if provided
    {$this->generateWaitForSelectorCode($options, 'playwright')}

    // Optional: Scroll to bottom to trigger lazy loading
    {$this->generateScrollCode($options, 'playwright')}

    // Get the page content
    const html = await page.content();

    // Save to file
    const fs = require('fs');
    fs.writeFileSync('{$outputPath}', html);
  } catch (error) {
    console.error('Error rendering page:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
})();
JS;
        }

        // Write script to file
        file_put_contents($scriptPath, $script);

        // Execute the script
        $command = 'node ' . escapeshellarg($scriptPath) . ' 2>&1';
        exec($command, $output, $returnCode);

        // Clean up script file
        @unlink($scriptPath);

        // Check if rendering was successful
        if ($returnCode !== 0) {
            Log::error('Headless browser rendering failed: ' . implode("\n", $output));
            throw new Exception('Headless browser rendering failed');
        }

        // Read the rendered HTML
        if (file_exists($outputPath)) {
            $html = file_get_contents($outputPath);
            @unlink($outputPath);
            return $html;
        }

        throw new Exception('Failed to get rendered HTML');
    }

    /**
     * Generate code for waiting for a specific selector
     *
     * @param array $options
     * @param string $type
     * @return string
     */
    protected function generateWaitForSelectorCode(array $options, string $type = 'puppeteer'): string
    {
        if (!empty($options['wait_for_selector'])) {
            $selector = addslashes($options['wait_for_selector']);
            $timeout = $options['selector_timeout'] ?? 10000;

            return "await page.waitForSelector('{$selector}', { timeout: {$timeout} });";
        }

        return '';
    }

    /**
     * Generate code for scrolling the page
     *
     * @param array $options
     * @param string $type
     * @return string
     */
    protected function generateScrollCode(array $options, string $type = 'puppeteer'): string
    {
        if ($options['scroll_page'] ?? false) {
            return <<<JS
    // Scroll to bottom
    await page.evaluate(async () => {
      await new Promise((resolve) => {
        let totalHeight = 0;
        const distance = 100;
        const timer = setInterval(() => {
          const scrollHeight = document.body.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;

          if (totalHeight >= scrollHeight) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    });
JS;
        }

        return '';
    }

    /**
     * Extract metadata from DOM
     *
     * @param DOMDocument $dom
     * @param DOMXPath $xpath
     * @return array
     */
    protected function extractMetadata(DOMDocument $dom, DOMXPath $xpath): array
    {
        $metadata = [];

        // Extract title
        $titleTags = $xpath->query('//title');
        if ($titleTags->length > 0) {
            $metadata['title'] = trim($titleTags->item(0)->textContent);
        }

        // Extract meta tags
        $metaTags = $xpath->query('//meta');
        foreach ($metaTags as $metaTag) {
            $name = $metaTag->getAttribute('name') ?: $metaTag->getAttribute('property');
            $content = $metaTag->getAttribute('content');

            if ($name && $content) {
                $metadata[$name] = $content;
            }
        }

        // Extract canonical URL
        $canonicalTags = $xpath->query('//link[@rel="canonical"]');
        if ($canonicalTags->length > 0) {
            $metadata['canonical_url'] = $canonicalTags->item(0)->getAttribute('href');
        }

        // Extract language
        $htmlTag = $xpath->query('//html');
        if ($htmlTag->length > 0) {
            $lang = $htmlTag->item(0)->getAttribute('lang');
            if ($lang) {
                $metadata['language'] = $lang;
            }
        }

        // Extract favicon
        $faviconTags = $xpath->query('//link[@rel="icon"]|//link[@rel="shortcut icon"]');
        if ($faviconTags->length > 0) {
            $metadata['favicon'] = $faviconTags->item(0)->getAttribute('href');
        }

        return $metadata;
    }

    /**
     * Extract links from DOM
     *
     * @param DOMDocument $dom
     * @param DOMXPath $xpath
     * @param string $baseUrl
     * @return array
     */
    protected function extractLinks(DOMDocument $dom, DOMXPath $xpath, string $baseUrl): array
    {
        $links = [];
        $linkTags = $xpath->query('//a[@href]');

        $parsedBaseUrl = parse_url($baseUrl);
        $baseHost = $parsedBaseUrl['host'] ?? '';
        $baseScheme = $parsedBaseUrl['scheme'] ?? 'https';

        foreach ($linkTags as $linkTag) {
            $href = $linkTag->getAttribute('href');
            $text = trim($linkTag->textContent);

            // Skip empty or javascript links
            if (empty($href) || strpos($href, 'javascript:') === 0 || $href === '#') {
                continue;
            }

            // Resolve relative URLs
            if (strpos($href, 'http') !== 0) {
                if (strpos($href, '//') === 0) {
                    // Protocol-relative URL
                    $href = $baseScheme . ':' . $href;
                } elseif (strpos($href, '/') === 0) {
                    // Absolute path
                    $href = $baseScheme . '://' . $baseHost . $href;
                } else {
                    // Relative path
                    $basePath = isset($parsedBaseUrl['path']) ? dirname($parsedBaseUrl['path']) : '';
                    if ($basePath !== '/' && $basePath !== '') {
                        $basePath .= '/';
                    }
                    $href = $baseScheme . '://' . $baseHost . $basePath . $href;
                }
            }

            // Normalize URL
            $href = $this->normalizeUrl($href);

            // Determine if the link is internal or external
            $parsedHref = parse_url($href);
            $hrefHost = $parsedHref['host'] ?? '';
            $isInternal = ($hrefHost === $baseHost);

            $links[] = [
                'url' => $href,
                'text' => $text,
                'is_internal' => $isInternal,
                'rel' => $linkTag->getAttribute('rel'),
                'title' => $linkTag->getAttribute('title'),
            ];
        }

        return $links;
    }

    /**
     * Extract images from DOM
     *
     * @param DOMDocument $dom
     * @param DOMXPath $xpath
     * @param string $baseUrl
     * @return array
     */
    protected function extractImages(DOMDocument $dom, DOMXPath $xpath, string $baseUrl): array
    {
        $images = [];
        $imageTags = $xpath->query('//img[@src]');

        $parsedBaseUrl = parse_url($baseUrl);
        $baseHost = $parsedBaseUrl['host'] ?? '';
        $baseScheme = $parsedBaseUrl['scheme'] ?? 'https';

        foreach ($imageTags as $imageTag) {
            $src = $imageTag->getAttribute('src');

            // Skip empty or data URLs
            if (empty($src) || strpos($src, 'data:') === 0) {
                continue;
            }

            // Resolve relative URLs
            if (strpos($src, 'http') !== 0) {
                if (strpos($src, '//') === 0) {
                    // Protocol-relative URL
                    $src = $baseScheme . ':' . $src;
                } elseif (strpos($src, '/') === 0) {
                    // Absolute path
                    $src = $baseScheme . '://' . $baseHost . $src;
                } else {
                    // Relative path
                    $basePath = isset($parsedBaseUrl['path']) ? dirname($parsedBaseUrl['path']) : '';
                    if ($basePath !== '/' && $basePath !== '') {
                        $basePath .= '/';
                    }
                    $src = $baseScheme . '://' . $baseHost . $basePath . $src;
                }
            }

            $images[] = [
                'src' => $src,
                'alt' => $imageTag->getAttribute('alt'),
                'title' => $imageTag->getAttribute('title'),
                'width' => $imageTag->getAttribute('width'),
                'height' => $imageTag->getAttribute('height'),
            ];
        }

        return $images;
    }

    /**
     * Extract text content from DOM
     *
     * @param DOMDocument $dom
     * @param DOMXPath $xpath
     * @return string
     */
    protected function extractTextContent(DOMDocument $dom, DOMXPath $xpath): string
    {
        // Try to find main content using common content selectors
        $contentSelectors = [
            '//article',
            '//main',
            '//div[@id="content"]',
            '//div[@class="content"]',
            '//div[@role="main"]',
            '//div[contains(@class, "post-content")]',
            '//div[contains(@class, "article-content")]',
            '//div[contains(@class, "entry-content")]',
            '//div[contains(@class, "main-content")]',
        ];

        foreach ($contentSelectors as $selector) {
            $contentNodes = $xpath->query($selector);
            if ($contentNodes->length > 0) {
                // Clone the node to avoid modifying the original DOM
                $contentNode = $contentNodes->item(0)->cloneNode(true);

                // Create a new document to work with the content node
                $contentDom = new DOMDocument();
                $contentDom->appendChild($contentDom->importNode($contentNode, true));

                // Remove unwanted elements
                $this->removeUnwantedElements($contentDom);

                // Get the text content
                $content = $contentDom->textContent;
                return $this->cleanText($content);
            }
        }

        // Fallback to body content
        $bodyNodes = $xpath->query('//body');
        if ($bodyNodes->length > 0) {
            // Clone the body to avoid modifying the original DOM
            $bodyNode = $bodyNodes->item(0)->cloneNode(true);

            // Create a new document to work with the body node
            $bodyDom = new DOMDocument();
            $bodyDom->appendChild($bodyDom->importNode($bodyNode, true));

            // Remove unwanted elements
            $this->removeUnwantedElements($bodyDom);

            // Get the text content
            $content = $bodyDom->textContent;
            return $this->cleanText($content);
        }

        return '';
    }

    /**
     * Remove unwanted elements from a DOM document
     *
     * @param DOMDocument $dom
     * @return void
     */
    protected function removeUnwantedElements(DOMDocument $dom): void
    {
        $xpath = new DOMXPath($dom);

        // List of selectors for elements to remove
        $unwantedSelectors = [
            '//script',
            '//style',
            '//nav',
            '//header',
            '//footer',
            '//aside',
            '//form',
            '//iframe',
            '//noscript',
            '//svg',
            '//button',
            '//select',
            '//input',
            '//textarea',
            '//div[contains(@class, "comment")]',
            '//div[contains(@class, "sidebar")]',
            '//div[contains(@class, "widget")]',
            '//div[contains(@class, "banner")]',
            '//div[contains(@class, "ad")]',
            '//div[contains(@class, "cookie")]',
            '//div[contains(@class, "popup")]',
            '//div[contains(@class, "modal")]',
        ];

        foreach ($unwantedSelectors as $selector) {
            $unwantedNodes = $xpath->query($selector);
            foreach ($unwantedNodes as $node) {
                if ($node->parentNode) {
                    $node->parentNode->removeChild($node);
                }
            }
        }
    }

    /**
     * Extract tables from DOM
     *
     * @param DOMDocument $dom
     * @param DOMXPath $xpath
     * @return array
     */
    protected function extractTables(DOMDocument $dom, DOMXPath $xpath): array
    {
        $tables = [];
        $tableNodes = $xpath->query('//table');

        foreach ($tableNodes as $tableNode) {
            $table = [];
            $rows = $xpath->query('.//tr', $tableNode);

            foreach ($rows as $row) {
                $rowData = [];
                $cells = $xpath->query('.//td|.//th', $row);

                foreach ($cells as $cell) {
                    $rowData[] = trim($cell->textContent);
                }

                if (!empty($rowData)) {
                    $table[] = $rowData;
                }
            }

            if (!empty($table)) {
                $tables[] = $table;
            }
        }

        return $tables;
    }

    /**
     * Extract JSON-LD structured data
     *
     * @param DOMDocument $dom
     * @param DOMXPath $xpath
     * @return array
     */
    protected function extractJsonLd(DOMDocument $dom, DOMXPath $xpath): array
    {
        $jsonData = [];
        $scripts = $xpath->query('//script[@type="application/ld+json"]');

        foreach ($scripts as $script) {
            try {
                $data = json_decode($script->textContent, true);
                if ($data) {
                    $jsonData[] = $data;
                }
            } catch (Exception $e) {
                // Skip invalid JSON
                continue;
            }
        }

        return $jsonData;
    }

    /**
     * Clean and normalize text
     *
     * @param string $text
     * @return string
     */
    protected function cleanText(string $text): string
    {
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);

        // Remove non-printable characters
        $text = preg_replace('/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        return trim($text);
    }

    /**
     * Save scraped content to database
     *
     * @param string $content
     * @param string $format
     * @param string $tableName
     * @param string $url
     * @param string|null $title
     * @param array $metadata
     * @param int|null $sourceId
     * @param int|null $projectId
     * @param int|null $userId
     * @return array
     * @throws Exception
     */
    public function saveToDatabase(
        string $content,
        string $format,
        string $tableName,
        string $url,
        ?string $title = null,
        array $metadata = [],
        ?int $sourceId = null,
        ?int $projectId = null,
        ?int $userId = null
    ): array {
        try {
            // Validate inputs
            if (empty($url)) {
                throw new Exception('URL cannot be empty');
            }

            if (empty($content)) {
                throw new Exception('Content cannot be empty');
            }

            if (empty($tableName)) {
                throw new Exception('Table name cannot be empty');
            }

            // Sanitize table name
            $tableName = preg_replace('/[^a-zA-Z0-9_]/', '', $tableName);

            // Create the table if it doesn't exist
            if (!$this->tableExists($tableName)) {
                $this->createScrapedContentTable($tableName);
            }

            // Check if the URL already exists in the table
            $existingRecord = DB::table($tableName)
                ->where('url', $url)
                ->first();

            $now = now();
            $data = [
                'url' => $url,
                'title' => $title ?? 'Scraped content',
                'content' => $content,
                'format' => $format,
                'metadata' => json_encode($metadata),
                'source_id' => $sourceId,
                'project_id' => $projectId,
                'created_by' => $userId,
                'updated_at' => $now,
            ];

            if ($existingRecord) {
                // Update existing record
                DB::table($tableName)
                    ->where('url', $url)
                    ->update($data);

                $id = $existingRecord->id;
                $message = 'Updated existing record';
                $isNew = false;
            } else {
                // Insert new record
                $data['created_at'] = $now;
                $id = DB::table($tableName)->insertGetId($data);
                $message = 'Created new record';
                $isNew = true;
            }

            // Save to KnowledgeScrapedUrl model if source_id is provided
            $scrapedUrl = null;
            if ($sourceId) {
                $scrapedUrl = $this->saveToKnowledgeScrapedUrl(
                    $url,
                    $title,
                    $content,
                    $format,
                    $sourceId,
                    $projectId,
                    $userId,
                    $metadata
                );
            }

            return [
                'success' => true,
                'id' => $id,
                'message' => $message,
                'is_new' => $isNew,
                'table_name' => $tableName,
                'scraped_url_id' => $scrapedUrl ? $scrapedUrl->id : null,
            ];
        } catch (Throwable $e) {
            Log::error('Error saving scraped content to database: ' . $e->getMessage());
            throw new Exception('Failed to save scraped content: ' . $e->getMessage());
        }
    }

    /**
     * Save scraped content to KnowledgeScrapedUrl model
     *
     * @param string $url
     * @param string|null $title
     * @param string $content
     * @param string $format
     * @param int $sourceId
     * @param int|null $projectId
     * @param int|null $userId
     * @param array $metadata
     * @return KnowledgeScrapedUrl
     */
    protected function saveToKnowledgeScrapedUrl(
        string $url,
        ?string $title,
        string $content,
        string $format,
        int $sourceId,
        ?int $projectId = null,
        ?int $userId = null,
        array $metadata = []
    ): KnowledgeScrapedUrl {
        // Check if the URL already exists
        $scrapedUrl = KnowledgeScrapedUrl::where('url', $url)
            ->where('source_id', $sourceId)
            ->first();

        $data = [
            'url' => $url,
            'title' => $title,
            'source_id' => $sourceId,
            'project_id' => $projectId,
            'created_by' => $userId,
            'metadata' => $metadata,
            'status' => 'active',
        ];

        // Set content based on format
        switch ($format) {
            case 'raw':
                $data['raw_content'] = $content;
                break;
            case 'text':
                $data['text_content'] = $content;
                break;
            case 'table':
                $data['table_content'] = $content;
                break;
            case 'json':
                $data['json_content'] = $content;
                break;
        }

        if ($scrapedUrl) {
            // Update existing record
            $scrapedUrl->update($data);
        } else {
            // Create new record
            $scrapedUrl = KnowledgeScrapedUrl::create($data);
        }

        return $scrapedUrl;
    }

    /**
     * Check if a table exists
     *
     * @param string $tableName
     * @return bool
     */
    protected function tableExists(string $tableName): bool
    {
        return DB::getSchemaBuilder()->hasTable($tableName);
    }

    /**
     * Create a table for scraped content
     *
     * @param string $tableName
     * @return void
     * @throws Exception
     */
    protected function createScrapedContentTable(string $tableName): void
    {
        try {
            // Validate table name
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
                throw new Exception('Invalid table name. Only alphanumeric characters and underscores are allowed.');
            }

            // Create the table with an improved schema
            DB::statement("
                CREATE TABLE {$tableName} (
                    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    url VARCHAR(2048) NOT NULL,
                    title VARCHAR(255) NULL,
                    content LONGTEXT NOT NULL,
                    format VARCHAR(50) NOT NULL,
                    metadata JSON NULL,
                    source_id BIGINT UNSIGNED NULL,
                    project_id BIGINT UNSIGNED NULL,
                    created_by BIGINT UNSIGNED NULL,
                    status VARCHAR(50) DEFAULT 'active',
                    created_at TIMESTAMP NULL,
                    updated_at TIMESTAMP NULL,
                    INDEX (url(255)),
                    INDEX (source_id),
                    INDEX (project_id),
                    INDEX (status),
                    INDEX (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            Log::info("Created new table: {$tableName}");
        } catch (Throwable $e) {
            Log::error("Failed to create table {$tableName}: " . $e->getMessage());
            throw new Exception("Failed to create table: " . $e->getMessage());
        }
    }

    /**
     * Get a list of all scraped content tables
     *
     * @return array
     */
    public function getScrapedContentTables(): array
    {
        $tables = [];

        // Get all tables
        $allTables = DB::select('SHOW TABLES');

        // Extract table names
        foreach ($allTables as $table) {
            $tableName = reset($table);

            // Check if it's a scraped content table by looking for required columns
            try {
                $columns = DB::getSchemaBuilder()->getColumnListing($tableName);

                // Check if it has the required columns for a scraped content table
                if (in_array('url', $columns) && in_array('content', $columns) && in_array('format', $columns)) {
                    // Get table info
                    $count = DB::table($tableName)->count();
                    $latestRecord = DB::table($tableName)->orderBy('created_at', 'desc')->first();

                    $tables[] = [
                        'name' => $tableName,
                        'count' => $count,
                        'latest_record' => $latestRecord ? [
                            'id' => $latestRecord->id,
                            'url' => $latestRecord->url,
                            'title' => $latestRecord->title,
                            'created_at' => $latestRecord->created_at,
                        ] : null,
                    ];
                }
            } catch (Throwable $e) {
                // Skip tables that don't match our structure
                continue;
            }
        }

        return $tables;
    }

    /**
     * Get scraped content for a specific URL ID
     *
     * @param int $urlId
     * @return array|null
     */
    public function getScrapedContent(int $urlId): ?array
    {
        try {
            // Get the URL record first
            $scrapedUrl = KnowledgeScrapedUrl::find($urlId);

            if (!$scrapedUrl) {
                return null;
            }

            // Try to retrieve cached content first
            $cacheKey = 'scraped_content_' . $urlId;

            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            // If not in cache, retrieve from database
            $content = DB::table('scraped_contents')
                ->where('url_id', $urlId)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$content) {
                // No content found, try to scrape it now
                return $this->scrapeAndSaveUrl($scrapedUrl->url, $scrapedUrl->id);
            }

            $result = [
                'url_id' => $urlId,
                'url' => $scrapedUrl->url,
                'title' => $scrapedUrl->title,
                'content' => $content->content,
                'metadata' => json_decode($content->metadata ?? '{}', true),
                'created_at' => $content->created_at
            ];

            // Cache the result for 1 hour
            Cache::put($cacheKey, $result, 3600);

            return $result;
        } catch (\Exception $e) {
            \Log::error('Error retrieving scraped content: ' . $e->getMessage(), [
                'url_id' => $urlId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Scrape a URL and save the content
     *
     * @param string $url
     * @param int $urlId
     * @return array|null
     */
    protected function scrapeAndSaveUrl(string $url, int $urlId): ?array
    {
        try {
            // Scrape the URL
            $response = Http::timeout(30)->get($url);

            if (!$response->successful()) {
                \Log::error("Failed to fetch URL: {$url}, status: {$response->status()}");
                return null;
            }

            $html = $response->body();

            // Parse the HTML content
            $dom = new \DOMDocument();
            @$dom->loadHTML($html);

            // Extract title
            $titleTags = $dom->getElementsByTagName('title');
            $title = $titleTags->length > 0 ? $titleTags->item(0)->textContent : null;

            // Extract content
            $contentText = $this->extractTextFromHtml($html);

            // Save to database
            $contentId = DB::table('scraped_contents')->insertGetId([
                'url_id' => $urlId,
                'content' => $contentText,
                'metadata' => json_encode([
                    'title' => $title,
                    'scraped_at' => now()->toIso8601String(),
                    'content_length' => strlen($contentText)
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Update URL record with title if needed
            if ($title) {
                ScrapedUrl::where('id', $urlId)->update([
                    'title' => $title,
                    'last_scraped_at' => now()
                ]);
            }

            $result = [
                'url_id' => $urlId,
                'url' => $url,
                'title' => $title,
                'content' => $contentText,
                'metadata' => [
                    'scraped_at' => now()->toIso8601String(),
                    'content_length' => strlen($contentText)
                ],
                'created_at' => now()->toIso8601String()
            ];

            // Cache the result for 1 hour
            Cache::put('scraped_content_' . $urlId, $result, 3600);

            return $result;
        } catch (\Exception $e) {
            \Log::error('Error scraping URL: ' . $e->getMessage(), [
                'url' => $url,
                'url_id' => $urlId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Extract text content from HTML
     *
     * @param string $html
     * @return string
     */
    protected function extractTextFromHtml(string $html): string
    {
        // Remove script and style tags and their content
        $html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $html);
        $html = preg_replace('/<style\b[^>]*>(.*?)<\/style>/is', '', $html);

        // Create a new DOM document
        $dom = new \DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        // Get the body element
        $body = $dom->getElementsByTagName('body')->item(0);

        if (!$body) {
            return strip_tags($html);
        }

        // Get all text nodes recursively
        $text = $this->getTextFromNode($body);

        // Clean up the text
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Recursively extract text from a DOM node
     *
     * @param \DOMNode $node
     * @return string
     */
    protected function getTextFromNode(\DOMNode $node): string
    {
        $text = '';

        // Add text content for this node
        if ($node->nodeType === XML_TEXT_NODE) {
            return $node->textContent;
        }

        // Skip hidden elements
        if ($node->nodeType === XML_ELEMENT_NODE) {
            $style = $node->getAttribute('style');
            if (preg_match('/display:\s*none|visibility:\s*hidden/i', $style)) {
                return '';
            }
        }

        // Process child nodes
        if ($node->hasChildNodes()) {
            foreach ($node->childNodes as $childNode) {
                $text .= $this->getTextFromNode($childNode);

                // Add line breaks after block elements
                if ($childNode->nodeType === XML_ELEMENT_NODE) {
                    $tagName = strtolower($childNode->nodeName);
                    if (in_array($tagName, ['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'br', 'tr'])) {
                        $text .= "\n";
                    }
                }
            }
        }

        return $text;
    }
}
