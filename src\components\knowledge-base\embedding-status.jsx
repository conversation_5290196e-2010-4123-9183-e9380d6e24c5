import { Badge } from '@/components/ui/badge'
import { Too<PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, Too<PERSON>ipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { CircleCheck, CircleX, Timer, Brain } from 'lucide-react'

export function EmbeddingStatus({
    hasEmbeddings,
    embeddingsCount,
    embeddingsProvider,
    embeddingsModel,
    size = 'md' // 'sm' | 'md' | 'lg'
}) {
    // If no embeddings info provided, return nothing
    if (hasEmbeddings === undefined) return null

    const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'
    const textSize = size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Badge
                        variant={hasEmbeddings ? "success" : "outline"}
                        className={`${textSize} inline-flex items-center gap-1 ${hasEmbeddings ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/40' : ''}`}
                    >
                        {hasEmbeddings ? (
                            <>
                                <CircleCheck className={`${iconSize} text-green-600 dark:text-green-400`} />
                                <span>{embeddingsCount || ''} Embedding{embeddingsCount !== 1 ? 's' : ''}</span>
                            </>
                        ) : (
                            <>
                                <CircleX className={`${iconSize} text-muted-foreground`} />
                                <span>No Embeddings</span>
                            </>
                        )}
                    </Badge>
                </TooltipTrigger>
                <TooltipContent>
                    {hasEmbeddings ? (
                        <div className="text-xs space-y-1">
                            <p className="font-medium">Embeddings Generated</p>
                            {embeddingsCount && <p>Count: {embeddingsCount}</p>}
                            {embeddingsProvider && <p>Provider: {embeddingsProvider}</p>}
                            {embeddingsModel && <p>Model: {embeddingsModel}</p>}
                        </div>
                    ) : (
                        <div className="text-xs">
                            <p>Document has no embeddings</p>
                            <p>Generate embeddings to use in AI responses</p>
                        </div>
                    )}
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
} 