# Permission Middleware Implementation Guide

## Overview

This guide explains how to use the `permission` middleware to protect API routes based on user permissions in the Laravel 12 application.

## How Permission Checks Work

The permission system is built on a role-based access control (RBAC) model:

1. **Users** have one or more **Roles**
2. **Roles** have one or more **Permissions**
3. A user has a permission if any of their roles have that permission

## Using the Permission Middleware

### Basic Usage

To protect a route with a permission check:

```php
Route::get('/users', [UserController::class, 'index'])
    ->middleware('permission:user.view');
```

This route will only be accessible to users who have the `user.view` permission through one of their roles.

### With Route Groups

You can apply permission middleware to groups of routes:

```php
Route::middleware(['auth:sanctum', 'permission:admin.access'])->group(function () {
    // All routes in this group require authentication and the 'admin.access' permission
    Route::get('/dashboard', [DashboardController::class, 'index']);
    Route::get('/settings', [SettingsController::class, 'index']);
});
```

### With Resource Controllers

For resource controllers, you can apply different permissions to different actions:

```php
Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::get('/users', [UserController::class, 'index'])
        ->middleware('permission:user.view');
        
    Route::post('/users', [UserController::class, 'store'])
        ->middleware('permission:user.create');
        
    Route::put('/users/{user}', [UserController::class, 'update'])
        ->middleware('permission:user.edit');
        
    Route::delete('/users/{user}', [UserController::class, 'destroy'])
        ->middleware('permission:user.delete');
});
```

## Permission Naming Convention

Permissions follow a `resource.action` naming convention:

- `user.view` - Permission to view users
- `user.create` - Permission to create users
- `user.edit` - Permission to edit users
- `user.delete` - Permission to delete users

Similar patterns apply to other resources like `role`, `permission`, `ai_model`, `widget`, etc.

## Implementation in Existing Routes

To implement permission checks in the existing API routes, update the `api.php` file to include permission middleware for protected routes. For example:

```php
// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'getCurrentUser']);

    // User management routes with permission checks
    Route::apiResource('users', App\Http\Controllers\UserController::class)
        ->middleware('permission:user.view');
        
    // Override specific methods with different permissions
    Route::post('/users', [App\Http\Controllers\UserController::class, 'store'])
        ->middleware('permission:user.create');
    Route::put('/users/{user}', [App\Http\Controllers\UserController::class, 'update'])
        ->middleware('permission:user.edit');
    Route::delete('/users/{user}', [App\Http\Controllers\UserController::class, 'destroy'])
        ->middleware('permission:user.delete');
        
    // Apply similar patterns to other resources
});
```

## Testing Permission Checks

You can test permission checks using the provided `PermissionMiddlewareTest` class. This test ensures that:

1. Users with the required permission can access protected routes
2. Users without the required permission receive a 403 Forbidden response
3. Unauthenticated users receive a 401 Unauthorized response

To run the tests:

```bash
php artisan test --filter=PermissionMiddlewareTest
```

## Troubleshooting

If permission checks aren't working as expected:

1. Verify that the user has the required role(s)
2. Verify that the role has the required permission(s)
3. Check that the permission middleware is correctly applied to the route
4. Ensure that the user is properly authenticated with Sanctum

## Example Implementation

See the `permission-examples.php` file for a complete example of how to implement permission checks for various API routes.
