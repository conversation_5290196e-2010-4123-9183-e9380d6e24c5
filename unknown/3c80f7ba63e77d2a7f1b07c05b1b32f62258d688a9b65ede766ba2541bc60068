import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useWidgetSettings } from "@/hooks/use-widget-settings"
import { useEmbedCode } from "@/hooks/use-embed-code"
import { Button } from "@/components/ui/button"
import { EmbedCodeVerification } from "@/components/embed-code/embed-code-verification"
import { AdminLayout } from "@/components/admin-layout"
import { useToast } from "@/hooks/use-toast"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

const EMBED_TYPES = [
    { label: "Standard Script", value: "standard" },
    { label: "iFrame", value: "iframe" },
    { label: "Web Component", value: "webcomponent" }
]

export default function EmbedCodeTestPage() {
    const { widgets, selectedWidget, setSelectedWidget, getWidgetConfig } = useWidgetSettings()
    const { generateEmbedCode } = useEmbedCode()
    const [embedCodes, setEmbedCodes] = useState<Record<string, string>>({})
    const [loading, setLoading] = useState(false)
    const [activeTab, setActiveTab] = useState("preview")
    const { toast } = useToast()

    useEffect(() => {
        if (!selectedWidget) return
        setLoading(true)
        Promise.all(
            EMBED_TYPES.map(async t => [t.value, await generateEmbedCode(t.value, getWidgetConfig())])
        ).then(results => {
            setEmbedCodes(Object.fromEntries(results))
            setLoading(false)
        })
    }, [selectedWidget])

    return (
        <AdminLayout>
            <div className="flex flex-col">
                <div className="mb-6">
                    <h1 className="text-2xl font-bold">Verify Widget Installation</h1>
                    <p className="text-muted-foreground">
                        Check if your widget is properly installed on your website
                    </p>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Select Widget to Verify</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="w-64">
                            <Select value={selectedWidget} onValueChange={setSelectedWidget}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a widget" />
                                </SelectTrigger>
                                <SelectContent>
                                    {widgets.map(w => (
                                        <SelectItem key={w.id} value={w.id}>{w.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {selectedWidget && (
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
                        <TabsList>
                            <TabsTrigger value="verification">Verification Tool</TabsTrigger>
                            <TabsTrigger value="preview">Preview Codes</TabsTrigger>
                        </TabsList>

                        <TabsContent value="verification" className="mt-4">
                            <Card>
                                <CardContent className="pt-6">
                                    <EmbedCodeVerification
                                        widgetId={selectedWidget}
                                        onSuccess={(url) => {
                                            toast({
                                                title: "Verification successful",
                                                description: `Widget is correctly installed on ${url}`,
                                            });
                                        }}
                                    />
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="preview" className="mt-4 space-y-6">
                            {EMBED_TYPES.map(type => (
                                <Card key={type.value}>
                                    <CardHeader>
                                        <CardTitle>{type.label}</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <div className="mb-2 font-mono text-xs text-muted-foreground">Embed Code:</div>
                                            <pre className="bg-muted p-4 rounded-md overflow-x-auto text-xs">
                                                {embedCodes[type.value] || "Generating..."}
                                            </pre>
                                        </div>
                                        <div>
                                            <div className="mb-2 font-mono text-xs text-muted-foreground">Live Preview:</div>
                                            <EmbedPreview type={type.value} code={embedCodes[type.value]} />
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </TabsContent>
                    </Tabs>
                )}
            </div>
        </AdminLayout>
    )
}

function EmbedPreview({ type, code }: { type: string; code?: string }) {
    if (!code) return <div className="text-muted-foreground">No code generated.</div>
    if (type === "iframe") {
        // Extract src from iframe code
        const match = code.match(/src=\"([^\"]+)\"/)
        if (!match) return <div className="text-muted-foreground">Invalid iframe code</div>
        return (
            <iframe src={match[1]} width="100%" height="400" style={{ border: "1px solid #eee", borderRadius: 8 }} />
        )
    }
    if (type === "webcomponent" || type === "web-component") {
        // Render the web component and script
        return (
            <div>
                <div dangerouslySetInnerHTML={{ __html: code }} />
            </div>
        )
    }
    // Standard script: inject into a sandboxed iframe
    if (type === "standard") {
        return <ScriptPreview code={code} />
    }
    return null
}

function ScriptPreview({ code }: { code: string }) {
    const [iframeKey, setIframeKey] = useState(0)
    useEffect(() => { setIframeKey(k => k + 1) }, [code])
    return (
        <iframe
            key={iframeKey}
            style={{ width: "100%", height: 400, border: "1px solid #eee", borderRadius: 8 }}
            sandbox="allow-scripts allow-same-origin"
            srcDoc={`<html><body>${code}</body></html>`}
        />
    )
} 