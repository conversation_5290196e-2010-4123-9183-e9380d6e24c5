import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, Shield } from "lucide-react";

interface SecuritySettings {
    dataSanitization?: boolean;
    preventDataCollection?: boolean;
    cspEnabled?: boolean;
    enableSRI?: boolean;
}

interface SecuritySettingsProps {
    settings: SecuritySettings;
    onChange: (settings: SecuritySettings) => void;
}

export function SecuritySettings({ settings, onChange }: SecuritySettingsProps) {
    const [localSettings, setLocalSettings] = useState<SecuritySettings>({
        dataSanitization: settings.dataSanitization !== undefined ? settings.dataSanitization : true,
        preventDataCollection: settings.preventDataCollection || false,
        cspEnabled: settings.cspEnabled || false,
        enableSRI: settings.enableSRI || false
    });

    const handleChange = (key: keyof SecuritySettings, value: boolean) => {
        const updated = { ...localSettings, [key]: value };
        setLocalSettings(updated);
        onChange(updated);
    };

    return (
        <div className="space-y-6">
            <div>
                <Label className="text-base">Security Settings</Label>
                <p className="text-sm text-muted-foreground mb-4">
                    Configure security options for your widget embed code
                </p>
            </div>

            <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                    Enhanced security options help protect your widget and visitors' data from potential attacks.
                </AlertDescription>
            </Alert>

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                        <Label htmlFor="data-sanitization">Data Sanitization</Label>
                        <p className="text-sm text-muted-foreground">
                            Automatically sanitize user input to prevent XSS attacks
                        </p>
                    </div>
                    <Switch
                        id="data-sanitization"
                        checked={localSettings.dataSanitization}
                        onCheckedChange={(checked) => handleChange('dataSanitization', checked)}
                    />
                </div>

                <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                        <Label htmlFor="prevent-data-collection">Prevent 3rd-party Data Collection</Label>
                        <p className="text-sm text-muted-foreground">
                            Block third-party scripts from accessing widget data
                        </p>
                    </div>
                    <Switch
                        id="prevent-data-collection"
                        checked={localSettings.preventDataCollection}
                        onCheckedChange={(checked) => handleChange('preventDataCollection', checked)}
                    />
                </div>

                <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                        <Label htmlFor="csp-enabled">Content Security Policy</Label>
                        <p className="text-sm text-muted-foreground">
                            Add CSP meta tag to restrict resource loading
                        </p>
                    </div>
                    <Switch
                        id="csp-enabled"
                        checked={localSettings.cspEnabled}
                        onCheckedChange={(checked) => handleChange('cspEnabled', checked)}
                    />
                </div>

                <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                        <Label htmlFor="enable-sri">Subresource Integrity (SRI)</Label>
                        <p className="text-sm text-muted-foreground">
                            Ensure script hasn't been tampered with before execution
                        </p>
                    </div>
                    <Switch
                        id="enable-sri"
                        checked={localSettings.enableSRI}
                        onCheckedChange={(checked) => handleChange('enableSRI', checked)}
                    />
                </div>
            </div>

            <div className="mt-6 p-4 border rounded-md bg-muted/50 flex items-center gap-3">
                <Shield className="h-10 w-10 text-primary/70" />
                <div>
                    <h4 className="text-sm font-medium">Enhanced Security Protection</h4>
                    <p className="text-sm text-muted-foreground">
                        These security measures help protect your widget from common web vulnerabilities
                        and ensure a safe experience for your users.
                    </p>
                </div>
            </div>
        </div>
    );
} 