
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chat Widget</title>
  <style id="dynamic-styles"></style>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      height: 100vh;
      background-color: #f9fafb;
      overflow: hidden;
    }
    
    .messages-container {
      height: calc(100% - 60px);
      overflow-y: auto;
      padding: 16px;
      scroll-behavior: smooth;
    }
    
    .message {
      margin-bottom: 12px;
      max-width: 80%;
      word-wrap: break-word;
      animation: fadeIn 0.3s ease-out;
    }
    
    .message-content {
      padding: 10px 12px;
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .message.user {
      margin-left: auto;
    }
    
    .message.user .message-content {
      background-color: #e2e8f0;
      border-bottom-right-radius: 0;
    }
    
    .message.assistant .message-content {
      border-bottom-left-radius: 0;
    }
    
    .input-container {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 12px;
      background-color: white;
      border-top: 1px solid #e2e8f0;
      display: flex;
    }
    
    .input-field {
      flex-grow: 1;
      padding: 10px 12px;
      border: 1px solid #e2e8f0;
      border-right: none;
      border-radius: 4px 0 0 4px;
      outline: none;
      font-size: 14px;
    }
    
    .send-button {
      padding: 10px 16px;
      border: none;
      color: white;
      border-radius: 0 4px 4px 0;
      cursor: pointer;
      font-weight: 500;
    }
    
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .typing {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .typing-indicator {
      display: flex;
      padding: 10px 12px;
    }
    
    .typing-indicator span {
      height: 8px;
      width: 8px;
      margin: 0 2px;
      background-color: #a0aec0;
      border-radius: 50%;
      display: inline-block;
      animation: bounce 1.3s ease-in-out infinite;
    }
    
    .typing-indicator span:nth-child(2) {
      animation-delay: 0.15s;
    }
    
    .typing-indicator span:nth-child(3) {
      animation-delay: 0.3s;
    }
    
    @keyframes bounce {
      0%, 60%, 100% {
        transform: translateY(0);
      }
      30% {
        transform: translateY(-4px);
      }
    }
    
    .timestamp {
      font-size: 11px;
      color: #94a3b8;
      text-align: center;
      margin: 16px 0;
      position: relative;
    }
    
    .timestamp::before, .timestamp::after {
      content: "";
      display: inline-block;
      width: 30%;
      height: 1px;
      background-color: #e2e8f0;
      vertical-align: middle;
      margin: 0 10px;
    }
    
    /* Scrollbar styling */
    .messages-container::-webkit-scrollbar {
      width: 6px;
    }
    
    .messages-container::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .messages-container::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 20px;
    }
    
    .message.user .message-time, .message.assistant .message-time {
      font-size: 10px;
      opacity: 0.6;
      margin-top: 4px;
      display: block;
    }
    
    .message.user .message-time {
      text-align: right;
      margin-right: 4px;
    }
    
    .message.assistant .message-time {
      text-align: left;
      margin-left: 4px;
    }
    
    .info-message {
      text-align: center;
      padding: 8px;
      margin: 10px 0;
      font-size: 12px;
      color: #64748b;
    }
  </style>
</head>
<body>
  <div class="messages-container" id="messages">
    <!-- Messages will be inserted here -->
  </div>
  
  <div class="input-container">
    <input type="text" class="input-field" id="messageInput" placeholder="Type your message...">
    <button class="send-button" id="sendButton">Send</button>
  </div>

  <script>
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const primaryColor = decodeURIComponent(urlParams.get('primary_color') || '#4f46e5');
    const secondaryColor = decodeURIComponent(urlParams.get('secondary_color') || '#4f46e5');
    const fontFamily = decodeURIComponent(urlParams.get('font_family') || 'Inter');
    const borderRadius = urlParams.get('border_radius') || '8';
    const initialMessage = decodeURIComponent(urlParams.get('initial_message') || 'Hello! How can I help you today?');
    const inputPlaceholder = decodeURIComponent(urlParams.get('input_placeholder') || 'Type your message...');
    const sendButtonText = decodeURIComponent(urlParams.get('send_button_text') || 'Send');
    
    // Apply dynamic styles based on parameters
    const dynamicStyles = document.getElementById('dynamic-styles');
    dynamicStyles.textContent = `
      body {
        font-family: ${fontFamily}, system-ui, sans-serif;
      }
      .message.assistant .message-content {
        background-color: ${primaryColor}20;
      }
      .send-button {
        background-color: ${primaryColor};
      }
      .input-field:focus {
        border-color: ${primaryColor};
      }
      .message-content {
        border-radius: ${borderRadius}px;
      }
      .message.user .message-content {
        border-bottom-right-radius: 0;
      }
      .message.assistant .message-content {
        border-bottom-left-radius: 0;
      }
      .typing-indicator span {
        background-color: ${primaryColor}80;
      }
    `;
    
    // DOM elements
    const messagesContainer = document.getElementById('messages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    
    // Update placeholder and button text
    messageInput.placeholder = inputPlaceholder;
    sendButton.textContent = sendButtonText;
    
    // Function to format time
    function formatTime() {
      const now = new Date();
      return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    // Function to add a message to the chat
    function addMessage(content, role) {
      const messageDiv = document.createElement('div');
      messageDiv.className = `message ${role}`;
      
      const messageContent = document.createElement('div');
      messageContent.className = 'message-content';
      messageContent.textContent = content;
      
      const messageTime = document.createElement('div');
      messageTime.className = 'message-time';
      messageTime.textContent = formatTime();
      
      messageDiv.appendChild(messageContent);
      messageDiv.appendChild(messageTime);
      messagesContainer.appendChild(messageDiv);
      
      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Show typing indicator
    function showTypingIndicator() {
      const typingDiv = document.createElement('div');
      typingDiv.className = 'typing';
      typingDiv.id = 'typing-indicator';
      
      typingDiv.innerHTML = `
        <div class="message-content typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      `;
      
      messagesContainer.appendChild(typingDiv);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Remove typing indicator
    function removeTypingIndicator() {
      const typingIndicator = document.getElementById('typing-indicator');
      if (typingIndicator) {
        typingIndicator.remove();
      }
    }
    
    // Add timestamp
    function addTimestamp() {
      const now = new Date();
      const options = { month: 'short', day: 'numeric' };
      const dateStr = now.toLocaleDateString('en-US', options);
      
      const timestampDiv = document.createElement('div');
      timestampDiv.className = 'timestamp';
      timestampDiv.textContent = dateStr;
      
      messagesContainer.appendChild(timestampDiv);
    }
    
    // Handle send button click
    sendButton.addEventListener('click', sendMessage);
    
    // Handle enter key press
    messageInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        sendMessage();
      }
    });
    
    // Send message function
    function sendMessage() {
      const message = messageInput.value.trim();
      if (!message) return;
      
      // Clear input
      messageInput.value = '';
      
      // Add user message
      addMessage(message, 'user');
      
      // Show typing indicator
      showTypingIndicator();
      
      // Simulate response in preview mode
      setTimeout(() => {
        removeTypingIndicator();
        
        // Add sample responses based on the message content for preview
        let response;
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
          response = "Hello! How can I assist you today?";
        } else if (lowerMessage.includes('help')) {
          response = "I'm here to help. What do you need assistance with?";
        } else if (lowerMessage.includes('price') || lowerMessage.includes('cost')) {
          response = "Our pricing plans start at $9.99/month. Would you like me to share more details about our plans?";
        } else if (lowerMessage.includes('thank')) {
          response = "You're welcome! Is there anything else you'd like to know?";
        } else {
          response = "This is a preview of how the assistant would respond to your message. In a real conversation, responses would be generated by the AI based on your widget's configuration.";
        }
        
        addMessage(response, 'assistant');
      }, 1500);
    }
    
    // Initialize with welcome message and timestamp
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(() => {
        addTimestamp();
        addMessage(initialMessage, 'assistant');
      }, 300);
    });
  </script>
</body>
</html>
