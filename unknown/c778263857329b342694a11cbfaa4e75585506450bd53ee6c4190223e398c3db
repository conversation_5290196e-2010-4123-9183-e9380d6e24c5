<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('knowledge_documents', function (Blueprint $table) {
            // Add new columns for AI query integration
            $table->boolean('is_active_source')->default(false)->after('status');
            $table->string('category')->nullable()->after('is_active_source');
            $table->foreignId('project_id')->nullable()->after('category');
            $table->text('extracted_text')->nullable()->after('project_id');
            $table->integer('size')->nullable()->after('file_type');
            
            // Add indexes for faster lookups
            $table->index(['is_active_source']);
            $table->index(['category']);
            $table->index(['project_id']);
        });
    }

    public function down(): void
    {
        Schema::table('knowledge_documents', function (Blueprint $table) {
            $table->dropIndex(['is_active_source']);
            $table->dropIndex(['category']);
            $table->dropIndex(['project_id']);
            
            $table->dropColumn([
                'is_active_source',
                'category',
                'project_id',
                'extracted_text',
                'size'
            ]);
        });
    }
};
