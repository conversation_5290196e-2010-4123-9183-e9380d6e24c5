"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { v4 as uuidv4 } from "uuid"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult
} from "@hello-pangea/dnd"
import {
  AlertCircle,
  GripVertical,
  Plus,
  Trash2,
  <PERSON><PERSON>,
  Eye,
  Settings,
  Save,
  FileText,
  HelpCircle
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { EnhancedPreChatForm } from "./enhanced-pre-chat-form"
import { PreChatFormField } from "@/types/widget"

// Form schema for the template settings
const templateSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
})

// Form schema for field settings
const fieldSchema = z.object({
  name: z.string().min(1, "Field name is required"),
  label: z.string().min(1, "Label is required"),
  type: z.enum(["text", "email", "phone", "select", "checkbox"]),
  placeholder: z.string().optional(),
  options: z.string().optional(),
  isRequired: z.boolean().default(false),
  validationPattern: z.string().optional(),
  errorMessage: z.string().optional(),
})

interface PreChatFormBuilderProps {
  widgetId: number
  initialTemplate?: {
    id?: number
    title: string
    description: string
    fields: PreChatFormField[]
  }
  onSave: (data: {
    title: string
    description: string
    fields: Omit<PreChatFormField, "id" | "templateId">[]
  }) => void
  onCancel: () => void
  primaryColor?: string
}

/**
 * Pre-Chat Form Builder Component
 *
 * A user-friendly interface for creating and customizing pre-chat forms.
 * Designed for non-technical users with drag-and-drop field ordering,
 * real-time preview, and intuitive field configuration.
 */
export function PreChatFormBuilder({
  widgetId,
  initialTemplate,
  onSave,
  onCancel,
  primaryColor = "#7E69AB"
}: PreChatFormBuilderProps) {
  const [activeTab, setActiveTab] = useState("settings")
  const [fields, setFields] = useState<PreChatFormField[]>(
    initialTemplate?.fields || []
  )
  const [selectedFieldIndex, setSelectedFieldIndex] = useState<number | null>(null)

  // Form for template settings
  const templateForm = useForm<z.infer<typeof templateSchema>>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      title: initialTemplate?.title || "Welcome! Let's get to know you",
      description: initialTemplate?.description || "Please share a few details so we can provide you with better assistance."
    }
  })

  // Form for field settings
  const fieldForm = useForm<z.infer<typeof fieldSchema>>({
    resolver: zodResolver(fieldSchema),
    defaultValues: {
      name: "",
      label: "",
      type: "text",
      placeholder: "",
      options: "",
      isRequired: true,
      validationPattern: "",
      errorMessage: ""
    }
  })

  // Handle drag and drop reordering
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return

    const items = Array.from(fields)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    // Update order property
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index
    }))

    setFields(updatedItems)

    // Update selected field index if needed
    if (selectedFieldIndex === result.source.index) {
      setSelectedFieldIndex(result.destination.index)
    }
  }

  // Add a new field
  const addField = () => {
    const newField: PreChatFormField = {
      id: uuidv4(),
      templateId: initialTemplate?.id || 0,
      name: `field_${fields.length + 1}`,
      label: `Field ${fields.length + 1}`,
      type: "text",
      placeholder: "",
      isRequired: true,
      order: fields.length
    }

    setFields([...fields, newField])
    setSelectedFieldIndex(fields.length)

    // Set field form values
    fieldForm.reset({
      name: newField.name,
      label: newField.label,
      type: newField.type,
      placeholder: newField.placeholder || "",
      options: "",
      isRequired: newField.isRequired,
      validationPattern: newField.validationPattern || "",
      errorMessage: newField.errorMessage || ""
    })

    // Switch to field tab
    setActiveTab("field")
  }

  // Select a field for editing
  const selectField = (index: number) => {
    setSelectedFieldIndex(index)
    const field = fields[index]

    fieldForm.reset({
      name: field.name,
      label: field.label,
      type: field.type,
      placeholder: field.placeholder || "",
      options: field.options?.join(", ") || "",
      isRequired: field.isRequired,
      validationPattern: field.validationPattern || "",
      errorMessage: field.errorMessage || ""
    })

    setActiveTab("field")
  }

  // Update the selected field
  const updateField = (data: z.infer<typeof fieldSchema>) => {
    if (selectedFieldIndex === null) return

    const updatedFields = [...fields]
    updatedFields[selectedFieldIndex] = {
      ...updatedFields[selectedFieldIndex],
      name: data.name,
      label: data.label,
      type: data.type,
      placeholder: data.placeholder,
      options: data.options ? data.options.split(",").map(opt => opt.trim()) : undefined,
      isRequired: data.isRequired,
      validationPattern: data.validationPattern,
      errorMessage: data.errorMessage
    }

    setFields(updatedFields)
  }

  // Delete the selected field
  const deleteField = (index: number) => {
    const updatedFields = [...fields]
    updatedFields.splice(index, 1)

    // Update order property
    const reorderedFields = updatedFields.map((field, idx) => ({
      ...field,
      order: idx
    }))

    setFields(reorderedFields)

    if (selectedFieldIndex === index) {
      setSelectedFieldIndex(null)
      fieldForm.reset()
    } else if (selectedFieldIndex !== null && selectedFieldIndex > index) {
      setSelectedFieldIndex(selectedFieldIndex - 1)
    }
  }

  // Duplicate a field
  const duplicateField = (index: number) => {
    const fieldToDuplicate = fields[index]
    const newField: PreChatFormField = {
      ...fieldToDuplicate,
      id: uuidv4(),
      name: `${fieldToDuplicate.name}_copy`,
      label: `${fieldToDuplicate.label} (Copy)`,
      order: fields.length
    }

    setFields([...fields, newField])
  }

  // Handle form submission
  const handleSubmit = (data: z.infer<typeof templateSchema>) => {
    // Prepare fields data without id and templateId
    const fieldsData = fields.map(({ id, templateId, ...rest }) => rest)

    onSave({
      title: data.title,
      description: data.description,
      fields: fieldsData
    })
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Builder Panel */}
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-xl font-semibold" style={{ color: primaryColor }}>
              Pre-Chat Form Builder
            </CardTitle>
            <CardDescription>
              Create and customize your pre-chat form to collect visitor information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 mb-4">
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span>Form Settings</span>
                </TabsTrigger>
                <TabsTrigger
                  value="field"
                  className="flex items-center gap-2"
                  disabled={selectedFieldIndex === null}
                >
                  <FileText className="h-4 w-4" />
                  <span>Field Settings</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="settings" className="space-y-4">
                <Form {...templateForm}>
                  <form className="space-y-4">
                    <FormField
                      control={templateForm.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Form Title</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={templateForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Form Description</FormLabel>
                          <FormControl>
                            <Textarea {...field} rows={3} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="field" className="space-y-4">
                {selectedFieldIndex !== null ? (
                  <Form {...fieldForm}>
                    <form
                      className="space-y-4"
                      onChange={fieldForm.handleSubmit(updateField)}
                    >
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={fieldForm.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Field Name</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={fieldForm.control}
                          name="type"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Field Type</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                value={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select field type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="text">Text</SelectItem>
                                  <SelectItem value="email">Email</SelectItem>
                                  <SelectItem value="phone">Phone</SelectItem>
                                  <SelectItem value="select">Dropdown</SelectItem>
                                  <SelectItem value="checkbox">Checkbox</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={fieldForm.control}
                        name="label"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Label</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {fieldForm.watch("type") !== "checkbox" && (
                        <FormField
                          control={fieldForm.control}
                          name="placeholder"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Placeholder</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {fieldForm.watch("type") === "select" && (
                        <FormField
                          control={fieldForm.control}
                          name="options"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Options (comma separated)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Option 1, Option 2, Option 3" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      <FormField
                        control={fieldForm.control}
                        name="isRequired"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Required Field</FormLabel>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={fieldForm.control}
                        name="errorMessage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Error Message</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Please enter a valid value" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </form>
                  </Form>
                ) : (
                  <Alert variant="outline" className="bg-muted/50">
                    <AlertCircle className="h-4 w-4 text-muted-foreground" />
                    <AlertDescription>
                      Select a field from the list to edit its properties
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between border-t bg-muted/20 p-4">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              onClick={templateForm.handleSubmit(handleSubmit)}
              style={{ backgroundColor: primaryColor }}
            >
              <Save className="mr-2 h-4 w-4" />
              Save Form
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Form Fields</CardTitle>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      onClick={addField}
                      style={{ backgroundColor: primaryColor }}
                    >
                      <Plus className="mr-1 h-4 w-4" />
                      Add Field
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add a new field to your form</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <CardDescription>
              Drag and drop to reorder fields
            </CardDescription>
          </CardHeader>
          <CardContent>
            {fields.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-8 text-center border border-dashed rounded-lg bg-muted/50">
                <HelpCircle className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">
                  No fields added yet. Click "Add Field" to create your first form field.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={addField}
                >
                  <Plus className="mr-1 h-4 w-4" />
                  Add Field
                </Button>
              </div>
            ) : (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="fields">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="space-y-2"
                    >
                      {fields.map((field, index) => (
                        <Draggable key={field.id} draggableId={field.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`flex items-center justify-between p-3 border rounded-md ${selectedFieldIndex === index
                                  ? `border-2 border-primary bg-primary/5`
                                  : "border-border"
                                }`}
                              onClick={() => selectField(index)}
                            >
                              <div className="flex items-center gap-3">
                                <div {...provided.dragHandleProps} className="cursor-grab">
                                  <GripVertical className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div>
                                  <p className="font-medium text-sm">{field.label}</p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant="outline" className="text-xs">
                                      {field.type}
                                    </Badge>
                                    {field.isRequired && (
                                      <Badge variant="secondary" className="text-xs">
                                        Required
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          duplicateField(index);
                                        }}
                                      >
                                        <Copy className="h-4 w-4 text-muted-foreground" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Duplicate field</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>

                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          deleteField(index);
                                        }}
                                      >
                                        <Trash2 className="h-4 w-4 text-destructive" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Delete field</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Preview Panel */}
      <div>
        <Card className="sticky top-6">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Form Preview</CardTitle>
              <Badge variant="outline" className="font-normal">
                <Eye className="mr-1 h-3 w-3" />
                Live Preview
              </Badge>
            </div>
            <CardDescription>
              See how your form will appear to visitors
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-background">
              <EnhancedPreChatForm
                fields={fields}
                title={templateForm.watch("title")}
                description={templateForm.watch("description")}
                onSubmit={() => { }}
                onCancel={() => { }}
                primaryColor={primaryColor}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
