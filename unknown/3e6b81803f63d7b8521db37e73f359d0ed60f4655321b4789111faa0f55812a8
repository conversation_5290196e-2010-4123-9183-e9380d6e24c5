import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, Globe, Database, Network, Clock, Info } from "lucide-react";

interface FeatureExplainerProps {
  feature: string;
  onClose: () => void;
}

export function FeatureExplainer({ feature, onClose }: FeatureExplainerProps) {
  const features = {
    embeddings: {
      title: "What are Vector Embeddings?",
      icon: <Database className="h-5 w-5 text-primary" />,
      content: (
        <>
          <p className="mb-2">
            Vector embeddings are numerical representations of text that capture
            meaning and context.
          </p>

          <h4 className="font-medium mb-1 mt-3">Why they matter:</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              Enable semantic search (finding content by meaning, not just
              keywords)
            </li>
            <li>
              Help your AI assistant understand and retrieve relevant
              information
            </li>
            <li>
              Allow comparison of text similarity across different documents
            </li>
          </ul>

          <h4 className="font-medium mb-1 mt-3">How they work:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>Your documents are split into smaller chunks</li>
            <li>Each chunk is converted into a vector (a list of numbers)</li>
            <li>
              When a user asks a question, it's also converted to a vector
            </li>
            <li>
              The system finds chunks with similar vectors to the question
            </li>
            <li>
              These relevant chunks are used to generate an accurate response
            </li>
          </ol>

          <div className="bg-muted/30 p-3 rounded-md mt-3">
            <p className="text-sm font-medium">Non-technical explanation:</p>
            <p className="text-sm">
              Think of embeddings as the AI's way of understanding the "meaning"
              behind words, not just the words themselves. This helps it find
              relevant information even when the exact keywords aren't used.
            </p>
          </div>
        </>
      ),
    },
    chunks: {
      title: "Understanding Chunk Size & Overlap",
      icon: <FileText className="h-5 w-5 text-primary" />,
      content: (
        <>
          <p className="mb-2">
            When processing documents, they're split into smaller pieces called
            "chunks" for better processing.
          </p>

          <h4 className="font-medium mb-1 mt-3">Chunk Size:</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              <strong>Smaller chunks (500-1000 characters):</strong> More
              precise answers, but may miss context
            </li>
            <li>
              <strong>Larger chunks (2000-4000 characters):</strong> Better
              context, but may include irrelevant information
            </li>
            <li>
              <strong>Recommended:</strong> 1000-1500 for most documents
            </li>
          </ul>

          <h4 className="font-medium mb-1 mt-3">Chunk Overlap:</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              Determines how much text is shared between consecutive chunks
            </li>
            <li>Helps maintain context across chunk boundaries</li>
            <li>
              <strong>Recommended:</strong> 10-20% of your chunk size (100-200
              characters for 1000-character chunks)
            </li>
          </ul>

          <div className="bg-muted/30 p-3 rounded-md mt-3">
            <p className="text-sm font-medium">Simple guideline:</p>
            <p className="text-sm">
              For most use cases, start with 1000 character chunks and 200
              character overlap. Adjust if you notice the AI missing context or
              including too much irrelevant information.
            </p>
          </div>
        </>
      ),
    },
    context: {
      title: "Understanding Context Rules",
      icon: <Network className="h-5 w-5 text-primary" />,
      content: (
        <>
          <p className="mb-2">
            Context rules help your AI assistant decide which knowledge sources
            to use when answering different types of questions.
          </p>

          <h4 className="font-medium mb-1 mt-3">How context rules work:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>You create rules with specific keywords or patterns</li>
            <li>
              When a user asks a question, the system checks if it matches any
              rules
            </li>
            <li>
              Matching rules determine which knowledge sources to prioritize
            </li>
            <li>
              This ensures the most relevant information is used for each
              question
            </li>
          </ol>

          <h4 className="font-medium mb-1 mt-3">Example use cases:</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              <strong>Product questions:</strong> Prioritize product
              documentation and database
            </li>
            <li>
              <strong>Policy questions:</strong> Prioritize policy documents
            </li>
            <li>
              <strong>Website questions:</strong> Prioritize scraped web content
            </li>
          </ul>

          <div className="bg-muted/30 p-3 rounded-md mt-3">
            <p className="text-sm font-medium">Pro tip:</p>
            <p className="text-sm">
              Create a default rule with lower priority that includes all
              sources as a fallback when no specific rules match.
            </p>
          </div>
        </>
      ),
    },
  };

  const featureInfo = features[feature as keyof typeof features];

  if (!featureInfo) return null;

  return (
    <Card className="border-primary/20 bg-primary/5 mb-4">
      <CardContent className="pt-4 pb-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-2">
            {featureInfo.icon}
            <h3 className="font-medium text-lg">{featureInfo.title}</h3>
          </div>
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground text-sm underline"
          >
            Close
          </button>
        </div>

        <div className="text-sm">{featureInfo.content}</div>
      </CardContent>
    </Card>
  );
}
