<?php

namespace App\Policies;

use App\Models\User;
use App\Models\KnowledgeSource;
use App\Models\Document;
use App\Models\ScrapedUrl;
use App\Models\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class KnowledgeResourcePolicy
{
    use HandlesAuthorization;

    /**
     * Determine if the user can view any knowledge resources.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('knowledge.view') || 
               $user->hasPermissionTo('knowledge.manage');
    }

    /**
     * Determine if the user can view the knowledge source.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $resource
     * @return bool
     */
    public function view(User $user, $resource): bool
    {
        // Get project ID from the resource
        $projectId = $this->getProjectId($resource);
        
        if (!$projectId) {
            return false;
        }
        
        // Check if user has global permission
        if ($user->hasPermissionTo('knowledge.view.all') || 
            $user->hasPermissionTo('knowledge.manage')) {
            return true;
        }
        
        // Check if user has access to the project
        return $this->userHasProjectAccess($user, $projectId);
    }

    /**
     * Determine if the user can create knowledge resources.
     *
     * @param  \App\Models\User  $user
     * @param  int  $projectId
     * @return bool
     */
    public function create(User $user, int $projectId): bool
    {
        // Check if user has global permission
        if ($user->hasPermissionTo('knowledge.create') || 
            $user->hasPermissionTo('knowledge.manage')) {
            return true;
        }
        
        // Check if user has access to the project
        return $this->userHasProjectAccess($user, $projectId);
    }

    /**
     * Determine if the user can update the knowledge resource.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $resource
     * @return bool
     */
    public function update(User $user, $resource): bool
    {
        // Get project ID from the resource
        $projectId = $this->getProjectId($resource);
        
        if (!$projectId) {
            return false;
        }
        
        // Check if user has global permission
        if ($user->hasPermissionTo('knowledge.update.all') || 
            $user->hasPermissionTo('knowledge.manage')) {
            return true;
        }
        
        // Check if user created the resource
        if (property_exists($resource, 'created_by') && 
            $resource->created_by === $user->id) {
            return true;
        }
        
        // Check if user has access to the project
        return $this->userHasProjectAccess($user, $projectId);
    }

    /**
     * Determine if the user can delete the knowledge resource.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $resource
     * @return bool
     */
    public function delete(User $user, $resource): bool
    {
        // Get project ID from the resource
        $projectId = $this->getProjectId($resource);
        
        if (!$projectId) {
            return false;
        }
        
        // Check if user has global permission
        if ($user->hasPermissionTo('knowledge.delete.all') || 
            $user->hasPermissionTo('knowledge.manage')) {
            return true;
        }
        
        // Check if user created the resource
        if (property_exists($resource, 'created_by') && 
            $resource->created_by === $user->id) {
            return true;
        }
        
        // Check if user has access to the project
        return $this->userHasProjectAccess($user, $projectId);
    }

    /**
     * Get project ID from a resource.
     *
     * @param  mixed  $resource
     * @return int|null
     */
    protected function getProjectId($resource): ?int
    {
        if ($resource instanceof Project) {
            return $resource->id;
        }
        
        if (property_exists($resource, 'project_id')) {
            return $resource->project_id;
        }
        
        return null;
    }

    /**
     * Check if user has access to a project.
     *
     * @param  \App\Models\User  $user
     * @param  int  $projectId
     * @return bool
     */
    protected function userHasProjectAccess(User $user, int $projectId): bool
    {
        // Check if user is assigned to the project
        $project = Project::find($projectId);
        
        if (!$project) {
            return false;
        }
        
        // Check if user is the project owner
        if ($project->created_by === $user->id) {
            return true;
        }
        
        // Check if user is a member of the project team
        // This would depend on your project-user relationship implementation
        // For example, if you have a project_user pivot table:
        return $user->projects()->where('projects.id', $projectId)->exists();
    }
}
