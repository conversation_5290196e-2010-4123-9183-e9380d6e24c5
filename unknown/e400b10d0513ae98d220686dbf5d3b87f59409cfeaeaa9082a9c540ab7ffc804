<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('knowledge_scraped_urls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('source_id')->nullable()->constrained('knowledge_sources')->nullOnDelete();
            $table->string('url');
            $table->text('title')->nullable();
            $table->longText('raw_content')->nullable();
            $table->longText('text_content')->nullable();
            $table->longText('table_content')->nullable();
            $table->json('json_content')->nullable();
            $table->string('status')->default('active'); // active, inactive, processing, failed
            $table->json('metadata')->nullable();
            $table->foreignId('project_id')->nullable();
            $table->string('table_name')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            
            // Add index for faster lookups
            $table->index(['source_id', 'status']);
            $table->index(['url']);
            $table->index(['project_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('knowledge_scraped_urls');
    }
};
