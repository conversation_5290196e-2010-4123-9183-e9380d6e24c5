<?php

namespace App\Jobs;

use App\Models\KnowledgeSource;
use App\Models\ScheduledScrape;
use App\Services\ScraperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RunScheduledScrapeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * The scheduled scrape instance.
     *
     * @var ScheduledScrape
     */
    protected $scheduledScrape;

    /**
     * Create a new job instance.
     *
     * @param ScheduledScrape $scheduledScrape
     * @return void
     */
    public function __construct(ScheduledScrape $scheduledScrape)
    {
        $this->scheduledScrape = $scheduledScrape;
    }

    /**
     * Execute the job.
     *
     * @param ScraperService $scraperService
     * @return void
     */
    public function handle(ScraperService $scraperService)
    {
        Log::info("Running scheduled scrape job for ID: {$this->scheduledScrape->id}, URL: {$this->scheduledScrape->url}");
        
        try {
            // Update the last_run timestamp
            $this->scheduledScrape->last_run = now();
            $this->scheduledScrape->save();
            
            // Prepare scraping options
            $options = json_decode($this->scheduledScrape->options, true) ?? [];
            $options['project_id'] = $this->scheduledScrape->project_id;
            
            // Run the scraper
            $result = $scraperService->scrapeUrl(
                $this->scheduledScrape->url,
                $this->scheduledScrape->model_id,
                $options
            );
            
            // Save the scraped content
            if ($result) {
                // Get or create the source
                $source = KnowledgeSource::firstOrCreate(
                    [
                        'type' => 'scrape',
                        'project_id' => $this->scheduledScrape->project_id,
                        'table_name' => $this->scheduledScrape->table_name,
                    ],
                    [
                        'name' => 'Scheduled Web Scraping',
                        'description' => 'Content automatically scraped from the web',
                        'status' => 'active',
                    ]
                );
                
                // Prepare metadata
                $metadata = [
                    'scheduled_scrape_id' => $this->scheduledScrape->id,
                    'scraped_at' => now()->toIso8601String(),
                    'scheduled' => true,
                    'frequency' => $this->scheduledScrape->frequency,
                ];
                
                // Save to database
                $saveResult = $scraperService->saveToDatabase(
                    $result['text'] ?? '',
                    $this->scheduledScrape->format,
                    $this->scheduledScrape->table_name,
                    $this->scheduledScrape->url,
                    $result['title'] ?? 'Scheduled scrape',
                    $metadata,
                    $source->id,
                    $this->scheduledScrape->project_id
                );
                
                // Update success count
                $this->scheduledScrape->success_count += 1;
                $this->scheduledScrape->last_success = now();
                $this->scheduledScrape->save();
                
                Log::info("Successfully scraped and saved content for URL: {$this->scheduledScrape->url}", [
                    'scheduled_scrape_id' => $this->scheduledScrape->id,
                    'save_result' => $saveResult,
                ]);
            }
        } catch (\Throwable $e) {
            // Log the error
            Log::error("Scheduled scrape job failed for ID: {$this->scheduledScrape->id}, URL: {$this->scheduledScrape->url}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Update error count
            $this->scheduledScrape->error_count += 1;
            $this->scheduledScrape->last_error = now();
            $this->scheduledScrape->last_error_message = $e->getMessage();
            $this->scheduledScrape->save();
            
            // Rethrow the exception to trigger job retry
            throw $e;
        }
    }
}
