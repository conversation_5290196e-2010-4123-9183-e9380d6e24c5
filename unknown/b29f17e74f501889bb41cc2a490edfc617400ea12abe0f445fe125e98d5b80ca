
import React from "react";
import { useForm<PERSON>ontext, useWatch } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { providers } from "./model-provider-options";
import { Info } from "lucide-react";

export const ModelBasicInfoFields: React.FC = () => {
  const form = useFormContext();
  const isEditMode = !!form.getValues("id");

  // Watch for model name changes
  const modelName = useWatch({
    control: form.control,
    name: "name",
  });

  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Name <span className="text-destructive">*</span></FormLabel>
            <FormControl>
              <Input
                placeholder="My AI Model"
                {...field}
                readOnly={!isEditMode}
                className={!isEditMode ? "bg-muted cursor-not-allowed" : ""}
              />
            </FormControl>
            <FormDescription className="flex items-center gap-1">
              <Info className="h-3 w-3" />
              {!isEditMode
                ? "This will be auto-populated when you select a model"
                : "A descriptive name for this model"}
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="provider"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Provider <span className="text-destructive">*</span></FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select provider" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {providers.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe this AI model..."
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
