# Pre-Chat Form & Post-Chat Survey Design Document

## Overview

This document describes the design and implementation details for two widget features:
1. **Pre-Chat Form**: Collects user information before starting a chat
2. **Post-Chat Survey**: Gathers feedback after a chat session ends

## User Experience Flow

### Pre-Chat Form Flow

1. User opens the widget
2. If pre-chat is enabled, form is displayed instead of chat
3. User completes required fields
4. Form is submitted and validated
5. On valid submission, chat interface appears
6. User information is stored and associated with the chat session

### Post-Chat Survey Flow

1. User ends chat (explicit end or inactivity timeout)
2. If post-chat is enabled, survey appears
3. User completes survey questions
4. Survey is submitted
5. Thank you message is displayed
6. Widget returns to initial state

## Data Models

### Pre-Chat Form

```typescript
interface PreChatFormTemplate {
  id: number;
  widget_id: number;
  title: string;
  description: string;
  fields: PreChatFormField[];
  created_at: string;
  updated_at: string;
}

interface PreChatFormField {
  id: number;
  template_id: number;
  name: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'checkbox';
  placeholder?: string;
  options?: string[]; // For select fields
  is_required: boolean;
  validation_pattern?: string;
  error_message?: string;
  order: number;
}

interface PreChatFormSubmission {
  id: number;
  template_id: number;
  session_id: string;
  data: Record<string, any>; // Field name to value mapping
  created_at: string;
}
```

### Post-Chat Survey

```typescript
interface PostChatSurvey {
  id: number;
  widget_id: number;
  title: string;
  description: string;
  questions: SurveyQuestion[];
  thank_you_message: string;
  created_at: string;
  updated_at: string;
}

interface SurveyQuestion {
  id: number;
  survey_id: number;
  text: string;
  type: 'rating' | 'text' | 'select' | 'boolean';
  options?: string[]; // For select questions
  is_required: boolean;
  order: number;
}

interface SurveyResponse {
  id: number;
  survey_id: number;
  session_id: string;
  answers: Record<number, any>; // Question ID to answer mapping
  created_at: string;
}
```

## UI Components

### Pre-Chat Form Builder

The form builder will allow administrators to:
- Add, edit, and remove form fields
- Set field properties (type, required, validation)
- Reorder fields using drag and drop
- Set form title and description
- Preview the form as it will appear to users

### Post-Chat Survey Builder

The survey builder will allow administrators to:
- Add, edit, and remove survey questions
- Set question types (rating, text, etc.)
- Reorder questions using drag and drop
- Set survey title and description
- Customize thank you message
- Preview the survey as it will appear to users

### Form and Survey Display Components

These components will handle:
- Rendering form/survey fields based on configuration
- Client-side validation
- Submission handling
- Error messaging
- Success feedback
- Responsive design for mobile devices

## API Endpoints

### Pre-Chat Form

```
GET    /api/widgets/{widgetId}/pre-chat-form
POST   /api/widgets/{widgetId}/pre-chat-form
PUT    /api/widgets/{widgetId}/pre-chat-form
DELETE /api/widgets/{widgetId}/pre-chat-form

GET    /api/pre-chat-form/{formId}/fields
POST   /api/pre-chat-form/{formId}/fields
PUT    /api/pre-chat-form/{formId}/fields/{fieldId}
DELETE /api/pre-chat-form/{formId}/fields/{fieldId}

POST   /api/chat-sessions/{sessionId}/pre-chat-submission
```

### Post-Chat Survey

```
GET    /api/widgets/{widgetId}/post-chat-survey
POST   /api/widgets/{widgetId}/post-chat-survey
PUT    /api/widgets/{widgetId}/post-chat-survey
DELETE /api/widgets/{widgetId}/post-chat-survey

GET    /api/post-chat-survey/{surveyId}/questions
POST   /api/post-chat-survey/{surveyId}/questions
PUT    /api/post-chat-survey/{surveyId}/questions/{questionId}
DELETE /api/post-chat-survey/{surveyId}/questions/{questionId}

POST   /api/chat-sessions/{sessionId}/survey-response
```

## Implementation Considerations

### Integration with Widget Configuration

The widget configuration schema will be updated to include:

```typescript
behavior: z.object({
  // ... existing fields
  preChat: z.boolean().default(false),
  postChat: z.boolean().default(false),
  preCharFormId: z.number().optional(),
  postChatSurveyId: z.number().optional(),
}),
```

### Storage and Data Handling

- Form and survey submissions will be stored in the database
- User data will be handled according to privacy regulations
- Data will be associated with chat sessions for analytics
- Export functionality will be provided for data analysis

### Performance Optimization

- Form and survey templates will be cached
- Client-side validation will reduce server load
- Form and survey submissions will be processed asynchronously
- Optimistic UI updates will improve perceived performance

## Analytics and Reporting

- Dashboard will show form completion rates
- Survey response analytics will be visualized
- Data can be exported for external analysis
- Trends over time will be displayed

## Accessibility Considerations

- All form and survey components will be keyboard accessible
- ARIA attributes will be used for screen readers
- High contrast mode will be supported
- Form validation errors will be clearly communicated

## Testing Strategy

- Unit tests for validation logic
- Integration tests for submission flow
- End-to-end tests for user experience
- A/B testing capability for form/survey variations

## Future Enhancements

- Conditional logic for form fields and survey questions
- Multi-page forms and surveys
- File upload support in pre-chat forms
- Integration with CRM systems
- Real-time analytics dashboard 