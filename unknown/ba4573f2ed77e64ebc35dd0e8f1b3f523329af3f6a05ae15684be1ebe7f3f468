# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/2a6fcc9f-d442-4313-839e-2488a18059fe

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/2a6fcc9f-d442-4313-839e-2488a18059fe) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/2a6fcc9f-d442-4313-839e-2488a18059fe) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

# Widget Builder

This repository contains a customizable widget builder for creating AI chat widgets for websites.

## Recent Code Improvements

### Code Refactoring (Latest Update)

The codebase has been refactored to improve maintainability and reduce duplication:

1. **Reusable Form Components**:
   - `ColorField`: Centralized color picker implementation
   - `SwitchField`: Reusable toggle switch field with consistent styling
   - `SliderField`: Reusable slider input with consistent behavior

2. **Removed Redundancies**:
   - Eliminated duplicate form field implementations
   - Consolidated styling patterns
   - Removed references to deprecated features (welcomeButtons, etc.)

3. **Feature Implementation**:
   - **Pre-Chat Form**: Fully implemented with validation and user-friendly interface
   - **Post-Chat Survey**: Implemented with multi-step survey flow and modern UI
   - Added full integration with the Widget Preview

## Widget Features

### Fully Implemented Features
- Widget appearance customization
- Position control
- Behavior settings (auto-open, typing indicators, etc.)
- Dark mode support
- Glass morphism effects
- Preview capabilities
- Embed code generation
- Pre-Chat Form for collecting user information
- Post-Chat Survey for gathering feedback

## Development

### Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`

### Contributing

When adding new features, please follow the established patterns:
- Use reusable components for form fields
- Follow the existing code structure
- Ensure proper TypeScript typing for all components
