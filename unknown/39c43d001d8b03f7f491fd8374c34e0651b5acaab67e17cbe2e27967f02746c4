<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Widget;
use App\Models\WidgetBehavior;
use App\Models\WidgetLogo;
use App\Models\WidgetWebhook;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only run if the new tables exist
        if (!Schema::hasTable('widget_behaviors') || 
            !Schema::hasTable('widget_logos') || 
            !Schema::hasTable('widget_webhooks')) {
            return;
        }

        // Migrate existing widget data to normalized structure
        Widget::chunk(100, function ($widgets) {
            foreach ($widgets as $widget) {
                $this->migrateWidgetBehavior($widget);
                $this->migrateWidgetLogo($widget);
                $this->migrateWidgetWebhooks($widget);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible as it's a data migration
        // The original data is preserved in the main widgets table
    }

    /**
     * Migrate widget behavior settings.
     */
    protected function migrateWidgetBehavior(Widget $widget): void
    {
        $settings = $widget->settings ?? [];
        $behaviorSettings = $settings['behavior'] ?? [];

        // Check if behavior already exists
        if (WidgetBehavior::where('widget_id', $widget->id)->exists()) {
            return;
        }

        // Create behavior record
        WidgetBehavior::create([
            'widget_id' => $widget->id,
            'auto_open' => $behaviorSettings['autoOpen'] ?? false,
            'auto_open_delay' => $behaviorSettings['autoOpenDelay'] ?? 5,
            'start_minimized' => $behaviorSettings['startMinimized'] ?? false,
            'show_typing_indicator' => $behaviorSettings['showTypingIndicator'] ?? true,
            'enable_user_ratings' => $behaviorSettings['enableUserRatings'] ?? false,
            'collect_user_data' => $behaviorSettings['collectUserData'] ?? false,
            'persist_conversation' => $behaviorSettings['persistConversation'] ?? false,
            'pre_chat_enabled' => $behaviorSettings['preChat'] ?? false,
            'post_chat_enabled' => $behaviorSettings['postChat'] ?? false,
            'close_after_inactivity' => $behaviorSettings['closeAfterInactivity'] ?? false,
            'inactivity_timeout' => $behaviorSettings['inactivityTimeout'] ?? 5,
            'show_logo' => $settings['showLogo'] ?? true,
            'show_close_button' => $settings['showCloseButton'] ?? true,
        ]);
    }

    /**
     * Migrate widget logo.
     */
    protected function migrateWidgetLogo(Widget $widget): void
    {
        // Check if logo already exists
        if (WidgetLogo::where('widget_id', $widget->id)->exists()) {
            return;
        }

        // Only create if there's a logo URL
        if (empty($widget->logo_url)) {
            return;
        }

        // Determine logo type
        $logoType = 'url';
        if (str_starts_with($widget->logo_url, 'data:image/')) {
            $logoType = 'base64';
        }

        // Extract image info for base64 images
        $width = null;
        $height = null;
        $fileSize = null;
        $mimeType = null;

        if ($logoType === 'base64') {
            if (preg_match('/^data:image\/([a-zA-Z+]+);base64,/', $widget->logo_url, $matches)) {
                $mimeType = 'image/' . $matches[1];
                $base64Pure = preg_replace('/^data:image\/[a-zA-Z+]+;base64,/', '', $widget->logo_url);
                $fileSize = strlen(base64_decode($base64Pure));
                
                try {
                    $imageData = base64_decode($base64Pure);
                    $image = imagecreatefromstring($imageData);
                    if ($image !== false) {
                        $width = imagesx($image);
                        $height = imagesy($image);
                        imagedestroy($image);
                    }
                } catch (\Exception $e) {
                    // Ignore errors in image processing
                }
            }
        }

        // Create logo record
        WidgetLogo::create([
            'widget_id' => $widget->id,
            'logo_data' => $widget->logo_url,
            'logo_type' => $logoType,
            'mime_type' => $mimeType,
            'file_size' => $fileSize,
            'width' => $width,
            'height' => $height,
            'position' => 'header',
            'quality' => 92,
            'auto_optimize' => true,
            'format_preference' => 'auto',
            'is_active' => true,
            'uploaded_at' => $logoType === 'base64' ? now() : null,
        ]);
    }

    /**
     * Migrate widget webhooks.
     */
    protected function migrateWidgetWebhooks(Widget $widget): void
    {
        $settings = $widget->settings ?? [];
        
        // Check for legacy webhook URL
        $webhookUrl = null;
        if (isset($settings['advanced']['webhookUrl']) && !empty($settings['advanced']['webhookUrl'])) {
            $webhookUrl = $settings['advanced']['webhookUrl'];
        } elseif (isset($settings['webhookUrl']) && !empty($settings['webhookUrl'])) {
            $webhookUrl = $settings['webhookUrl'];
        }

        // Check for new integrations format
        $integrations = $settings['integrations'] ?? [];

        // Skip if no webhooks to migrate
        if (empty($webhookUrl) && empty($integrations)) {
            return;
        }

        // Check if webhooks already exist
        if (WidgetWebhook::where('widget_id', $widget->id)->exists()) {
            return;
        }

        // Migrate legacy webhook URL
        if ($webhookUrl) {
            WidgetWebhook::create([
                'widget_id' => $widget->id,
                'name' => 'Legacy Webhook',
                'url' => $webhookUrl,
                'method' => 'POST',
                'on_chat_start' => true,
                'on_chat_end' => true,
                'on_message_sent' => true,
                'on_message_received' => true,
                'on_user_rating' => true,
                'on_form_submission' => true,
                'timeout_seconds' => 30,
                'retry_attempts' => 3,
                'verify_ssl' => true,
                'is_active' => true,
                'integration_type' => 'generic',
            ]);
        }

        // Migrate integrations
        foreach ($integrations as $integration) {
            if (empty($integration['url'])) {
                continue;
            }

            $events = $integration['events'] ?? [];
            
            WidgetWebhook::create([
                'widget_id' => $widget->id,
                'name' => $integration['name'] ?? 'Integration Webhook',
                'url' => $integration['url'],
                'method' => 'POST',
                'on_chat_start' => in_array('session.start', $events) || in_array('chat_start', $events),
                'on_chat_end' => in_array('session.end', $events) || in_array('chat_end', $events),
                'on_message_sent' => in_array('message.new', $events) || in_array('message_sent', $events),
                'on_message_received' => in_array('message.received', $events) || in_array('message_received', $events),
                'on_user_rating' => in_array('rating.submit', $events) || in_array('user_rating', $events),
                'on_form_submission' => in_array('form.submit', $events) || in_array('form_submission', $events),
                'timeout_seconds' => 30,
                'retry_attempts' => 3,
                'verify_ssl' => true,
                'is_active' => $integration['active'] ?? true,
                'integration_type' => $integration['type'] ?? 'generic',
                'integration_config' => $integration,
            ]);
        }
    }
};
