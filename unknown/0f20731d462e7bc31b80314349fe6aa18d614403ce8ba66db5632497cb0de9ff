<?php

namespace App\Http\Controllers;

use App\Models\GuestUser;
use App\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Exception;

class GuestUserController extends Controller
{
    /**
     * Register a guest user for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'fullname' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'required|string|max:20',
            'widget_id' => 'required|string|exists:widgets,widget_id',
            'metadata' => 'nullable|array',
            'metadata.url' => 'nullable|string|max:2048',
            'metadata.referrer' => 'nullable|string|max:2048',
            'metadata.timestamp' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the widget
        $widget = Widget::where('widget_id', $request->widget_id)
                       ->where('is_active', true)
                       ->firstOrFail();

        // Check if widget allows guest registration (if flow is set to 'none', don't register users)
        if ($widget->guest_user_flow === 'none') {
            return response()->json([
                'success' => false,
                'message' => 'Guest registration is disabled for this widget',
                'guest_flow' => 'none'
            ], 403);
        }

        try {
            // Generate a unique session ID
            $sessionId = Str::uuid()->toString();

            // Prepare metadata
            $metadata = [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'registration_time' => now()->toIso8601String(),
            ];

            // Add client-provided metadata if available
            if ($request->has('metadata')) {
                if (isset($request->metadata['url'])) {
                    $metadata['url'] = $request->metadata['url'];
                }

                if (isset($request->metadata['referrer'])) {
                    $metadata['referrer'] = $request->metadata['referrer'];
                } else {
                    $metadata['referrer'] = $request->header('referer');
                }

                if (isset($request->metadata['timestamp'])) {
                    $metadata['client_timestamp'] = $request->metadata['timestamp'];
                }
            }

            // Create guest user
            $guestUser = GuestUser::create([
                'fullname' => $request->fullname,
                'email' => $request->email,
                'phone' => $request->phone,
                'session_id' => $sessionId,
                'widget_id' => $widget->id,
                'metadata' => $metadata,
            ]);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'message' => 'Guest registration successful',
                'user' => [
                    'id' => $guestUser->id,
                    'fullname' => $guestUser->fullname,
                    'created_at' => $guestUser->created_at->toIso8601String(),
                ],
            ], 201);
        } catch (Exception $e) {
            Log::error('Guest registration failed: ' . $e->getMessage(), [
                'widget_id' => $request->widget_id,
                'fullname' => $request->fullname,
                'email' => $request->email,
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to register guest user. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred',
            ], 500);
        }
    }

    /**
     * Automatically generate a guest user for a widget.
     * Used when the widget has the 'auto' guest flow setting.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function autoRegister(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'widget_id' => 'required|string|exists:widgets,widget_id',
            'metadata' => 'nullable|array',
            'metadata.url' => 'nullable|string|max:2048',
            'metadata.referrer' => 'nullable|string|max:2048',
            'metadata.timestamp' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the widget
        $widget = Widget::where('widget_id', $request->widget_id)
                       ->where('is_active', true)
                       ->firstOrFail();

        // Check if widget allows auto guest registration
        if ($widget->guest_user_flow === 'required') {
            return response()->json([
                'success' => false,
                'message' => 'Manual guest registration is required for this widget',
                'guest_flow' => 'required'
            ], 403);
        }

        if ($widget->guest_user_flow === 'none') {
            return response()->json([
                'success' => false,
                'message' => 'Guest registration is disabled for this widget',
                'guest_flow' => 'none'
            ], 403);
        }

        try {
            // Generate a unique session ID
            $sessionId = Str::uuid()->toString();

            // Generate anonymous name based on ID fragment and timestamp
            $anonymousId = substr(Str::random(6), 0, 6);
            $anonymousName = 'Guest_' . $anonymousId;

            // Prepare metadata with important tracking info
            $metadata = [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'registration_time' => now()->toIso8601String(),
                'auto_generated' => true,
            ];

            // Add client-provided metadata if available
            if ($request->has('metadata')) {
                if (isset($request->metadata['url'])) {
                    $metadata['url'] = $request->metadata['url'];
                }

                if (isset($request->metadata['referrer'])) {
                    $metadata['referrer'] = $request->metadata['referrer'];
                } else {
                    $metadata['referrer'] = $request->header('referer');
                }

                if (isset($request->metadata['timestamp'])) {
                    $metadata['client_timestamp'] = $request->metadata['timestamp'];
                }
            }

            // Create auto-generated guest user
            $guestUser = GuestUser::create([
                'fullname' => $anonymousName,
                'email' => null,
                'phone' => null,
                'session_id' => $sessionId,
                'widget_id' => $widget->id,
                'metadata' => $metadata,
            ]);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'message' => 'Auto guest registration successful',
                'user' => [
                    'id' => $guestUser->id,
                    'fullname' => $guestUser->fullname,
                    'created_at' => $guestUser->created_at->toIso8601String(),
                ],
                'auto_generated' => true,
            ], 201);
        } catch (Exception $e) {
            Log::error('Auto guest registration failed: ' . $e->getMessage(), [
                'widget_id' => $request->widget_id,
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to auto-generate guest user. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred',
            ], 500);
        }
    }

    /**
     * Check the guest flow setting for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkGuestFlow(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'widget_id' => 'required|string|exists:widgets,widget_id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $widget = Widget::where('widget_id', $request->widget_id)
                         ->where('is_active', true)
                         ->firstOrFail();

            return response()->json([
                'success' => true,
                'widget_id' => $widget->widget_id,
                'guest_flow' => $widget->guest_user_flow ?? 'auto',
                'requires_manual_registration' => $widget->guest_user_flow === 'required'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to check guest flow: ' . $e->getMessage(), [
                'widget_id' => $request->widget_id,
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check guest flow setting',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred'
            ], 500);
        }
    }

    /**
     * Validate a guest session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateSession(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string',
            'widget_id' => 'nullable|string|exists:widgets,widget_id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid session ID format'
            ], 200);
        }

        try {
            $guestUser = GuestUser::where('session_id', $request->session_id)->first();

            if (!$guestUser) {
                // If guest user is not found but widget allows auto registration, suggest auto registration
                if ($request->has('widget_id')) {
                    $widget = Widget::where('widget_id', $request->widget_id)->first();
                    if ($widget && $widget->guest_user_flow === 'auto') {
                        return response()->json([
                            'valid' => false,
                            'message' => 'Session not found, but auto registration is available',
                            'guest_flow' => 'auto',
                            'should_auto_register' => true
                        ], 200);
                    } else if ($widget && $widget->guest_user_flow === 'required') {
                        return response()->json([
                            'valid' => false,
                            'message' => 'Session not found, manual registration required',
                            'guest_flow' => 'required',
                            'should_show_registration' => true
                        ], 200);
                    }
                }

                return response()->json([
                    'valid' => false,
                    'message' => 'Session not found'
                ], 200);
            }

            // If widget_id is provided, ensure the session belongs to the specified widget
            if ($request->has('widget_id')) {
                $widget = Widget::where('widget_id', $request->widget_id)->first();
                if ($widget && $guestUser->widget_id !== $widget->id) {
                    return response()->json([
                        'valid' => false,
                        'message' => 'Session belongs to a different widget'
                    ], 200);
                }
            }

            return response()->json([
                'valid' => true,
                'user' => [
                    'id' => $guestUser->id,
                    'fullname' => $guestUser->fullname,
                    'session_id' => $guestUser->session_id,
                    'created_at' => $guestUser->created_at->toIso8601String(),
                    'auto_generated' => $guestUser->metadata['auto_generated'] ?? false,
                ],
            ]);
        } catch (Exception $e) {
            Log::error('Session validation failed: ' . $e->getMessage(), [
                'session_id' => $request->session_id,
                'exception' => $e,
            ]);

            return response()->json([
                'valid' => false,
                'message' => 'Failed to validate session',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred',
            ], 500);
        }
    }

    /**
     * Get guest user details.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDetails(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $guestUser = GuestUser::where('session_id', $request->session_id)->first();

            if (!$guestUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Guest user not found',
                ], 404);
            }

            // Get the associated widget to check guest flow settings
            $widget = Widget::find($guestUser->widget_id);
            $guestFlow = $widget ? $widget->guest_user_flow ?? 'auto' : 'auto';

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $guestUser->id,
                    'fullname' => $guestUser->fullname,
                    'email' => $guestUser->email,
                    'phone' => $guestUser->phone,
                    'session_id' => $guestUser->session_id,
                    'created_at' => $guestUser->created_at->toIso8601String(),
                    'widget_id' => $guestUser->widget_id,
                    'auto_generated' => $guestUser->metadata['auto_generated'] ?? false,
                ],
                'widget' => [
                    'guest_flow' => $guestFlow,
                    'id' => $widget ? $widget->id : null,
                    'widget_id' => $widget ? $widget->widget_id : null,
                ],
            ]);
        } catch (Exception $e) {
            Log::error('Failed to get guest user details: ' . $e->getMessage(), [
                'session_id' => $request->session_id,
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve user details',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred',
            ], 500);
        }
    }
}
