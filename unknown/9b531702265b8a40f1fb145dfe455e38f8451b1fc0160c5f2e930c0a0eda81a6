import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormDescription } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { PowerIcon } from "lucide-react";

export function ModelActiveToggle() {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name="active"
      render={({ field }) => (
        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
          <div className="space-y-0.5">
            <FormLabel className="text-base">Active Status</FormLabel>
            <FormDescription>
              When active, this model will be available for use in the system
            </FormDescription>
          </div>
          <FormControl>
            <div className="flex items-center space-x-2">
              <PowerIcon className={`h-4 w-4 ${field.value ? 'text-green-500' : 'text-gray-400'}`} />
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
