/**
 * Widget Service
 * 
 * Centralized service for all widget-related functionality.
 * This service handles API interactions, configuration management,
 * and other widget-related operations.
 */

import api from '@/utils/api';
import { WidgetSettings, Widget, AnalyticsSummary } from '@/types/widget';

/**
 * Widget Service
 * Handles all widget-related API interactions and operations
 */
class WidgetService {
  /**
   * Get all widgets for the current user
   * @returns Promise with widget data
   */
  async getAllWidgets() {
    return api.get<Widget[]>('widgets');
  }

  /**
   * Get a widget by ID
   * @param id Widget ID
   * @returns Promise with widget data
   */
  async getWidget(id: number) {
    return api.get<Widget>(`widgets/${id}`);
  }

  /**
   * Create a new widget
   * @param widgetData Widget data
   * @returns Promise with created widget
   */
  async createWidget(widgetData: Widget) {
    return api.post<Widget>('widgets', widgetData);
  }

  /**
   * Update an existing widget
   * @param id Widget ID
   * @param widgetData Widget data to update
   * @returns Promise with updated widget
   */
  async updateWidget(id: number, widgetData: Partial<Widget>) {
    return api.put<Widget>(`widgets/${id}`, widgetData);
  }

  /**
   * Delete a widget
   * @param id Widget ID
   * @returns Promise with deletion result
   */
  async deleteWidget(id: number) {
    return api.delete(`widgets/${id}`);
  }

  /**
   * Get a widget by its public ID
   * @param widgetId Public widget ID
   * @returns Promise with widget data
   */
  async getWidgetByPublicId(widgetId: string) {
    return api.get<Widget>(`widgets/public/${widgetId}`);
  }

  /**
   * Generate embed code for a widget
   * @param widgetId Widget ID
   * @param options Embed code options
   * @returns Promise with embed code
   */
  async generateEmbedCode(widgetId: number, options: {
    type: 'standard' | 'iframe' | 'web-component',
    customizations?: {
      allowed_domains?: string[];
      position_type?: 'fixed' | 'relative' | 'inline';
      csp_enabled?: boolean;
      enable_sri?: boolean;
      version?: string;
    };
  }) {
    return api.post('embed-code/generate', {
      widget_id: widgetId,
      type: options.type,
      allowed_domains: options.customizations?.allowed_domains,
      position_type: options.customizations?.position_type,
      enable_sri: options.customizations?.enable_sri,
      csp_enabled: options.customizations?.csp_enabled
    });
  }

  /**
   * Get analytics summary for a widget
   * @param widgetId Widget ID
   * @param period Time period for analytics
   * @returns Promise with analytics summary
   */
  async getAnalyticsSummary(widgetId: number, period: 'day' | 'week' | 'month' | 'all' = 'month') {
    return api.get<AnalyticsSummary>(`widgets/${widgetId}/analytics/summary?period=${period}`);
  }

  /**
   * Get detailed analytics for a widget
   * @param widgetId Widget ID
   * @param options Analytics options
   * @returns Promise with analytics data
   */
  async getAnalytics(widgetId: number, options: {
    fromDate?: string;
    toDate?: string;
    groupBy?: 'day' | 'week' | 'month' | 'event_type' | 'url';
  } = {}) {
    const params = new URLSearchParams();

    if (options.fromDate) {
      params.append('from_date', options.fromDate);
    }

    if (options.toDate) {
      params.append('to_date', options.toDate);
    }

    if (options.groupBy) {
      params.append('group_by', options.groupBy);
    }

    return api.get(`widgets/${widgetId}/analytics?${params.toString()}`);
  }

  /**
   * Validate a domain for widget embedding
   * @param widgetId Widget ID
   * @param domain Domain to validate
   * @returns Promise with validation result
   */
  async validateDomain(widgetId: string, domain: string) {
    return api.post('embed-code/validate-domain', {
      widget_id: widgetId,
      domain
    });
  }
}

// Export a singleton instance
export const widgetService = new WidgetService();
