import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Database, Loader2 } from "lucide-react";
import { EmbeddingGenerationDialog } from "./embedding-generation-dialog";
import { FileMeta } from "@/modules/knowledge-base/knowledge-base-resources";

interface BatchEmbeddingButtonProps {
  selectedFiles: FileMeta[];
  onComplete: () => void;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
}

export function BatchEmbeddingButton({
  selectedFiles,
  onComplete,
  variant = "outline",
  size = "sm"
}: BatchEmbeddingButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  
  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setDialogOpen(true)}
        disabled={selectedFiles.length === 0}
        className="gap-1"
      >
        <Database className="h-4 w-4" />
        <span>Generate Embeddings</span>
      </Button>
      
      <EmbeddingGenerationDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        selectedFiles={selectedFiles}
        onComplete={onComplete}
      />
    </>
  );
}
