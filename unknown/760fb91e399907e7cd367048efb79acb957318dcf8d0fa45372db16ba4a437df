<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        // Widget public endpoints rate limiting (more permissive as these are core widget functionality)
        RateLimiter::for('widget_public', function (Request $request) {
            return Limit::perMinute(120)
                    ->by($request->widget_id ?? $request->get('widget_id') ?? $request->ip());
        });

        // Widget analytics rate limiting (more restrictive to prevent abuse)
        RateLimiter::for('widget_analytics', function (Request $request) {
            return Limit::perMinute(60)
                    ->by($request->widget_id ?? $request->get('widget_id') ?? $request->ip());
        });

        // Guest user registration/validation rate limiting
        RateLimiter::for('widget_guest', function (Request $request) {
            // More restrictive to prevent spam accounts
            return Limit::perMinute(20)
                    ->by($request->ip())
                    ->response(function () {
                        return response()->json([
                            'error' => 'Too many requests. Please try again later.',
                            'status' => 429,
                        ], 429);
                    });
        });

        // Widget embed code and preview rate limiting
        RateLimiter::for('widget_embed', function (Request $request) {
            // Moderate limits for embed code generation and previews
            return Limit::perMinute(30)
                    ->by($request->user()?->id ?: $request->ip())
                    ->response(function () {
                        return response()->json([
                            'success' => false,
                            'message' => 'Too many embed code requests. Please try again later.',
                            'error' => 'Rate limit exceeded'
                        ], 429);
                    });
        });
    }
}
