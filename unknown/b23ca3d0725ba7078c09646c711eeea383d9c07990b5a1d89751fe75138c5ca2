<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Check if the projects table exists
        if (Schema::hasTable('projects')) {
            // Check if there are any projects
            $projectCount = DB::table('projects')->count();
            
            // If no projects exist, create a default one
            if ($projectCount === 0) {
                DB::table('projects')->insert([
                    'id' => 1, // Explicitly set ID to 1
                    'name' => 'Default Project',
                    'description' => 'Default project created automatically',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                echo "Default project created.\n";
            }
        }
    }

    public function down(): void
    {
        // No need to do anything in down migration
    }
};
