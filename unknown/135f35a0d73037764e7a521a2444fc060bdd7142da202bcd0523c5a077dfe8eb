<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('widgets', function (Blueprint $table) {
            // Version control
            $table->string('version')->default('1.0')->after('widget_id');

            // Domain restrictions
            $table->json('allowed_domains')->nullable()->after('settings');

            // Advanced positioning
            $table->string('position_type')->default('fixed')->after('allowed_domains');
            $table->json('position_settings')->nullable()->after('position_type');

            // Custom CSS
            $table->text('custom_css')->nullable()->after('position_settings');

            // Custom behavior
            $table->json('behavior_rules')->nullable()->after('custom_css');

            // Branding
            $table->string('logo_url')->nullable()->after('behavior_rules');
            $table->json('typography')->nullable()->after('logo_url');
            $table->json('button_customization')->nullable()->after('typography');

            // Mobile-specific settings
            $table->json('mobile_settings')->nullable()->after('button_customization');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('widgets', function (Blueprint $table) {
            $table->dropColumn([
                'version',
                'allowed_domains',
                'position_type',
                'position_settings',
                'custom_css',
                'behavior_rules',
                'logo_url',
                'typography',
                'button_customization',
                'mobile_settings'
            ]);
        });
    }
};
