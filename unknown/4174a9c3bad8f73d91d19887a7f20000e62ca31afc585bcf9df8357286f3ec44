import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Pencil, ToggleLeft, ToggleRight, Star, StarOff, Trash, MoreVertical, Check, AlertCircle } from "lucide-react";
import { AIModelData } from "@/utils/ai-model-service";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface ModelSelectionCardProps {
  models: AIModelData[];
  selectedModelId: number | null;
  onModelSelect: (modelId: number) => void;
  onAddNewModel: () => void;
  onEditModel: (model: AIModelData) => void;
  onToggleActive?: (model: AIModelData, active: boolean) => void;
  onSetDefault?: (model: AIModelData) => void;
  onDeleteModel?: (model: AIModelData) => void;
  isLoading: boolean;
}

export function ModelSelectionCard({
  models,
  selectedModelId,
  onModelSelect,
  onAddNewModel,
  onEditModel,
  onToggleActive,
  onSetDefault,
  onDeleteModel,
  isLoading
}: ModelSelectionCardProps) {
  return (
    <Card className="h-full">
      <CardContent className="pt-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">AI Models</h3>
          <Button onClick={onAddNewModel} size="sm" className="h-8">
            <Plus className="mr-1 h-4 w-4" /> Add Model
          </Button>
        </div>

        <div className="border rounded-md">
          {isLoading ? (
            <div className="p-4 space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : models.length === 0 ? (
            <div className="p-8 text-center">
              <AlertCircle className="mx-auto h-10 w-10 text-muted-foreground mb-2" />
              <h3 className="font-medium text-lg">No models available</h3>
              <p className="text-muted-foreground mb-4">
                Add your first AI model to get started
              </p>
              <Button onClick={onAddNewModel}>
                <Plus className="mr-1 h-4 w-4" /> Add Model
              </Button>
            </div>
          ) : (
            <ScrollArea className="h-[320px]">
              <div className="divide-y">
                {models.map((model) => (
                  <div
                    key={model.id}
                    className={`flex items-center p-4 hover:bg-accent/50 cursor-pointer transition-colors ${selectedModelId === model.id ? "bg-accent" : ""
                      }`}
                    onClick={() => model.id && onModelSelect(model.id)}
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{model.name}</span>
                        <div className="flex gap-1">
                          {model.is_default && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Badge variant="secondary" className="h-5 px-1 flex items-center">
                                    <Star className="h-3 w-3 text-yellow-500 mr-1" />
                                    Default
                                  </Badge>
                                </TooltipTrigger>
                                <TooltipContent>Default AI Model</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge
                                  variant={model.active ? "success" : "outline"}
                                  className="h-5 px-1 flex items-center"
                                >
                                  {model.active ? (
                                    <Check className="h-3 w-3 mr-1" />
                                  ) : (
                                    <AlertCircle className="h-3 w-3 mr-1" />
                                  )}
                                  {model.active ? "Active" : "Inactive"}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                {model.active
                                  ? "Model is active and available for use"
                                  : "Model is inactive and won't be used"}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2 mt-1">
                        <span className="text-xs px-1.5 py-0.5 rounded bg-secondary font-mono">
                          {model.provider}
                        </span>
                        {model.settings?.model_name && (
                          <span className="text-xs truncate">{model.settings.model_name}</span>
                        )}
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onEditModel(model);
                        }}>
                          <Pencil className="mr-2 h-4 w-4" /> Edit
                        </DropdownMenuItem>

                        {onToggleActive && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            onToggleActive(model, !model.active);
                          }}>
                            {model.active ? (
                              <>
                                <ToggleLeft className="mr-2 h-4 w-4" /> Deactivate
                              </>
                            ) : (
                              <>
                                <ToggleRight className="mr-2 h-4 w-4" /> Activate
                              </>
                            )}
                          </DropdownMenuItem>
                        )}

                        {onSetDefault && !model.is_default && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            onSetDefault(model);
                          }}>
                            <Star className="mr-2 h-4 w-4" /> Set as Default
                          </DropdownMenuItem>
                        )}

                        {onDeleteModel && (
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              onDeleteModel(model);
                            }}
                          >
                            <Trash className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
