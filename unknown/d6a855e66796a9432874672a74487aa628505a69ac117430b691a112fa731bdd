
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "./theme-toggle";
import { MessageSquare } from "lucide-react";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { useAuth } from "@/hooks/use-auth";
import { UserDropdown } from "./user-dropdown";

export function Navbar() {
  const { user } = useAuth();

  return (
    <header className="border-b bg-background sticky top-0 z-30">
      <div className="container flex h-16 items-center justify-between">
        <Link to="/" className="flex items-center gap-2">
          <MessageSquare className="h-6 w-6 text-primary" />
          <span className="font-semibold text-xl">ChatSystem</span>
        </Link>

        <NavigationMenu className="hidden md:flex">
          <NavigationMenuList>
            <NavigationMenuItem>
              <Link to="/features" className={navigationMenuTriggerStyle()}>
                Features
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger>Solutions</NavigationMenuTrigger>
              <NavigationMenuContent>
                <div className="grid gap-3 p-4 w-[400px] md:grid-cols-2">
                  <Link to="/how-it-works" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                    <div className="text-sm font-medium">How It Works</div>
                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      Learn about our AI-powered chat system
                    </div>
                  </Link>
                  <Link to="/pricing" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                    <div className="text-sm font-medium">Pricing</div>
                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      Find the right plan for your business
                    </div>
                  </Link>
                  <Link to="/testimonials" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                    <div className="text-sm font-medium">Testimonials</div>
                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      See what our customers have to say
                    </div>
                  </Link>
                </div>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>

        <div className="flex items-center gap-2">
          <ThemeToggle />

          {user ? (
            <UserDropdown
              userName={user.name || "User"}
              userEmail={user.email}
            />
          ) : (
            <>
              <Link to="/login">
                <Button variant="ghost" className="text-sm">
                  Log In
                </Button>
              </Link>
              <Link to="/register">
                <Button className="text-sm">
                  Sign Up
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
