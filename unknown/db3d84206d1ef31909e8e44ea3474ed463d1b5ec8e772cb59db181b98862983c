import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Loader2,
  AlertCircle,
  CheckCircle,
  HelpCircle,
  Info,
} from "lucide-react";
import { knowledgeBaseService } from "@/utils/knowledge-base-service";
import { FileMeta } from "@/modules/knowledge-base/knowledge-base-resources";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface EmbeddingGenerationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedFiles: FileMeta[];
  onComplete: () => void;
}

export function EmbeddingGenerationDialog({
  open,
  onOpenChange,
  selectedFiles,
  onComplete,
}: EmbeddingGenerationDialogProps) {
  const [provider, setProvider] = useState<"openai" | "huggingface">("openai");
  const [chunkSize, setChunkSize] = useState<number>(1000);
  const [chunkOverlap, setChunkOverlap] = useState<number>(200);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [availableModels, setAvailableModels] = useState<any>({});

  useEffect(() => {
    if (open) {
      // Reset state when dialog opens
      setIsGenerating(false);
      setProgress(0);
      setError(null);
      setSuccess(false);

      // Fetch available models
      knowledgeBaseService
        .getEmbeddingModels()
        .then((response) => {
          setAvailableModels(response.data.models || {});
        })
        .catch((err) => {
          console.error("Error fetching embedding models:", err);
          setError("Failed to fetch available embedding models");
        });
    }
  }, [open]);

  const handleGenerate = async () => {
    if (selectedFiles.length === 0) return;

    setIsGenerating(true);
    setProgress(0);
    setError(null);
    setSuccess(false);

    try {
      const documentIds = selectedFiles.map((file) => file.id);
      const options = {
        provider,
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      // For single file, use direct endpoint
      if (documentIds.length === 1) {
        const response = await knowledgeBaseService.generateEmbeddings(
          documentIds[0],
          options,
        );
        setSuccess(true);
        setProgress(100);
      }
      // For multiple files, use batch endpoint
      else {
        const response = await knowledgeBaseService.batchGenerateEmbeddings(
          documentIds,
          options,
        );

        if (response.data.successful > 0) {
          setSuccess(true);
          setProgress(100);

          if (response.data.failed > 0) {
            setError(
              `${response.data.successful} files processed successfully, but ${response.data.failed} files failed.`,
            );
          }
        } else {
          setError("Failed to generate embeddings for any files.");
          setProgress(0);
        }
      }

      // Notify parent component that generation is complete
      onComplete();
    } catch (err: any) {
      console.error("Error generating embeddings:", err);
      setError(err.message || "An error occurred while generating embeddings");
      setProgress(0);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Generate Vector Embeddings</DialogTitle>
          <DialogDescription>
            Generate vector embeddings for {selectedFiles.length} selected{" "}
            {selectedFiles.length === 1 ? "file" : "files"}. These embeddings
            enable semantic search and AI context retrieval.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Embedding Provider</label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-5 w-5 p-0">
                      <HelpCircle className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[300px] p-3">
                    <p className="text-sm">
                      The AI service that will convert your text into vector
                      embeddings. Different providers have different strengths
                      and pricing.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Select
              value={provider}
              onValueChange={(value: "openai" | "huggingface") =>
                setProvider(value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="huggingface">Hugging Face</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {provider === "openai"
                ? "OpenAI Ada embeddings (1536 dimensions) - Better quality, higher cost"
                : "Hugging Face Sentence Transformers (768 dimensions) - Good quality, lower cost"}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Chunk Size</label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 p-0"
                      >
                        <HelpCircle className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-[300px] p-3">
                      <p className="text-sm">
                        Documents are split into smaller pieces (chunks) for
                        processing. Smaller chunks (500-1000) give more precise
                        answers but may miss context. Larger chunks (2000-4000)
                        provide better context but may include irrelevant
                        information.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className="text-xs text-muted-foreground">
                {chunkSize} characters
              </span>
            </div>
            <Slider
              value={[chunkSize]}
              min={100}
              max={4000}
              step={100}
              onValueChange={(values) => setChunkSize(values[0])}
              disabled={isGenerating}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>More precise</span>
              <span>Recommended: 1000-1500</span>
              <span>More context</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Chunk Overlap</label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 p-0"
                      >
                        <HelpCircle className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-[300px] p-3">
                      <p className="text-sm">
                        Determines how much text is shared between consecutive
                        chunks. This helps maintain context across chunk
                        boundaries. Recommended: 10-20% of your chunk size.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className="text-xs text-muted-foreground">
                {chunkOverlap} characters
              </span>
            </div>
            <Slider
              value={[chunkOverlap]}
              min={0}
              max={500}
              step={50}
              onValueChange={(values) => setChunkOverlap(values[0])}
              disabled={isGenerating}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>No overlap</span>
              <span>Recommended: 200</span>
              <span>More overlap</span>
            </div>
          </div>

          {isGenerating && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Generating embeddings...</span>
                <span className="text-xs text-muted-foreground">
                  {progress}%
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && !error && (
            <Alert
              variant="success"
              className="bg-green-50 text-green-800 border-green-200"
            >
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription>
                <p>Embeddings generated successfully!</p>
                <p className="text-xs mt-1">
                  Your documents are now ready for semantic search and AI
                  context retrieval.
                </p>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isGenerating}
          >
            {success ? "Close" : "Cancel"}
          </Button>
          <Button onClick={handleGenerate} disabled={isGenerating || success}>
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating
              </>
            ) : (
              "Generate Embeddings"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
