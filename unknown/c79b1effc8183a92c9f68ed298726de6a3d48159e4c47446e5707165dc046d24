
// List of supported AI providers
export const providers = [
  { value: "openai", label: "OpenAI" },
  { value: "anthropic", label: "Anthropic" },
  { value: "gemini", label: "Google Gemini" },
  { value: "grok", label: "Grok AI" },
  { value: "huggingface", label: "Hugging Face" },
  { value: "openrouter", label: "OpenRouter" },
  { value: "mistral", label: "Mistral AI" },
  { value: "deepseek", label: "DeepSeek" },
  { value: "cohere", label: "Cohere" },
  { value: "custom", label: "Custom Provider" }
];

// Interface for model options
export interface ModelOption {
  value: string;
  label: string;
  description?: string;
  is_free?: boolean;
}

// Helper function to get model options based on provider
export const getModelOptions = (provider: string): ModelOption[] => {
  switch (provider) {
    case "openai":
      return [
        { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo", is_free: true },
        { value: "gpt-4o", label: "GPT-4o" },
        { value: "gpt-4o-mini", label: "GPT-4o Mini" }
      ];
    case "anthropic":
      return [
        { value: "claude-3-haiku-20240307", label: "<PERSON> 3 <PERSON>ku", is_free: true },
        { value: "claude-3-sonnet-20240229", label: "Claude 3 Sonnet" },
        { value: "claude-3-opus-20240229", label: "Claude 3 Opus" }
      ];
    case "gemini":
      return [
        { value: "gemini-1.0-pro", label: "Gemini 1.0 Pro", is_free: true },
        { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash" },
        { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro" }
      ];
    case "grok":
      return [
        { value: "grok-1", label: "Grok-1", is_free: true }
      ];
    case "huggingface":
      return [
        { value: "mistralai/Mixtral-8x7B-Instruct-v0.1", label: "Mixtral 8x7B", is_free: true },
        { value: "meta-llama/Llama-2-70b-chat-hf", label: "Llama 2 70B" },
        { value: "meta-llama/Llama-3-70b-chat-hf", label: "Llama 3 70B" }
      ];
    case "openrouter":
      return [
        { value: "meta-llama/llama-3-70b-instruct", label: "Llama 3 70B (Meta)", is_free: true },
        { value: "openai/gpt-4o", label: "GPT-4o (OpenAI)" },
        { value: "anthropic/claude-3-opus", label: "Claude 3 Opus (Anthropic)" }
      ];
    case "mistral":
      return [
        { value: "mistral-small-latest", label: "Mistral Small", is_free: true },
        { value: "mistral-medium-latest", label: "Mistral Medium" },
        { value: "mistral-large-latest", label: "Mistral Large" }
      ];
    case "deepseek":
      return [
        { value: "deepseek-chat", label: "DeepSeek Chat", is_free: true },
        { value: "deepseek-coder", label: "DeepSeek Coder" }
      ];
    case "cohere":
      return [
        { value: "command-light", label: "Command Light", is_free: true },
        { value: "command", label: "Command" },
        { value: "command-r", label: "Command R" }
      ];
    default:
      return [];
  }
};
