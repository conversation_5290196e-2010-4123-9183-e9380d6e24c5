
import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin-layout";
import { BarChart2, Users, MessageSquare, Clock, TrendingUp } from "lucide-react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiService } from "@/utils/api-service";
import { useToast } from "@/hooks/use-toast";

interface GlobalAnalytics {
  total_widgets: number;
  total_views: number;
  total_conversations: number;
  total_messages: number;
  avg_response_time: number;
  engagement_rate: number;
  active_widgets: number;
}

const Analytics = () => {
  const [analytics, setAnalytics] = useState<GlobalAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState("30d");
  const { toast } = useToast();

  const fetchGlobalAnalytics = async () => {
    try {
      setLoading(true);

      // Fetch all widgets first
      const widgetsResponse = await apiService.get('/widgets');
      const widgets = widgetsResponse.data.data || widgetsResponse.data;

      if (!widgets.length) {
        setAnalytics({
          total_widgets: 0,
          total_views: 0,
          total_conversations: 0,
          total_messages: 0,
          avg_response_time: 0,
          engagement_rate: 0,
          active_widgets: 0
        });
        return;
      }

      // Aggregate analytics from all widgets
      let totalViews = 0;
      let totalConversations = 0;
      let totalMessages = 0;
      let totalResponseTime = 0;
      let activeWidgets = 0;

      for (const widget of widgets) {
        if (widget.is_active) {
          activeWidgets++;
          try {
            const summaryResponse = await apiService.get(`/widgets/${widget.id}/analytics/summary?period=${period}`);
            const summary = summaryResponse.data;

            totalViews += summary.total_views || 0;
            totalConversations += summary.total_conversations || 0;
            totalMessages += summary.total_messages || 0;
            totalResponseTime += summary.avg_messages_per_conversation || 0;
          } catch (error) {
            console.warn(`Failed to fetch analytics for widget ${widget.id}:`, error);
          }
        }
      }

      const avgResponseTime = activeWidgets > 0 ? totalResponseTime / activeWidgets : 0;
      const engagementRate = totalViews > 0 ? (totalConversations / totalViews) * 100 : 0;

      setAnalytics({
        total_widgets: widgets.length,
        total_views: totalViews,
        total_conversations: totalConversations,
        total_messages: totalMessages,
        avg_response_time: avgResponseTime,
        engagement_rate: engagementRate,
        active_widgets: activeWidgets
      });

    } catch (error) {
      console.error("Failed to fetch global analytics:", error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGlobalAnalytics();
  }, [period]);

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex flex-col">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Analytics</h1>
            <p className="text-muted-foreground">
              Get insights into your AI chat system's usage and performance
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col">
        <div className="mb-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Analytics</h1>
            <p className="text-muted-foreground">
              Get insights into your AI chat system's usage and performance
            </p>
          </div>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {analytics && (
          <>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.total_views.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Across all widgets
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.total_messages.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    {analytics.total_conversations} conversations
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.engagement_rate.toFixed(1)}%</div>
                  <p className="text-xs text-muted-foreground">
                    Views to conversations
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Widgets</CardTitle>
                  <BarChart2 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.active_widgets}</div>
                  <p className="text-xs text-muted-foreground">
                    of {analytics.total_widgets} total
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Widget Performance</CardTitle>
                  <CardDescription>Key metrics across all widgets</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Total Widget Views</span>
                    <span className="font-medium">{analytics.total_views.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Conversations Started</span>
                    <span className="font-medium">{analytics.total_conversations.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Messages Exchanged</span>
                    <span className="font-medium">{analytics.total_messages.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Avg Messages per Conversation</span>
                    <span className="font-medium">{analytics.avg_response_time.toFixed(1)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Status</CardTitle>
                  <CardDescription>Current system health and activity</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Active Widgets</span>
                    <span className="font-medium text-green-600">{analytics.active_widgets}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Inactive Widgets</span>
                    <span className="font-medium text-gray-500">{analytics.total_widgets - analytics.active_widgets}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Engagement Rate</span>
                    <span className="font-medium">{analytics.engagement_rate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Period</span>
                    <span className="font-medium capitalize">{period.replace('d', ' days')}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default Analytics;
