import { AdminLayout } from "@/components/admin-layout";
import { ApiTesterModule } from "@/modules/api-tester";
import { useLocation } from "react-router-dom";

const ApiTester = () => {
  const location = useLocation();
  const isPublicRoute = location.pathname === "/api-tester";

  const content = (
    <div className="flex flex-col">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">API Tester</h1>
        <p className="text-muted-foreground">
          Test API endpoints and model responses
        </p>
      </div>

      <ApiTesterModule />
    </div>
  );

  // If accessed from public route, don't wrap in AdminLayout
  if (isPublicRoute) {
    return <div className="container mx-auto py-8 px-4">{content}</div>;
  }

  return <AdminLayout>{content}</AdminLayout>;
};

export default ApiTester;
