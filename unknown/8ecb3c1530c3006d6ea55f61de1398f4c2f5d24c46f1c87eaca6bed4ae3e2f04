<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ScheduledScrape extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'url',
        'project_id',
        'source_id',
        'table_name',
        'format',
        'model_id',
        'frequency',
        'status',
        'options',
        'next_run',
        'last_run',
        'last_success',
        'last_error',
        'last_error_message',
        'success_count',
        'error_count',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'next_run' => 'datetime',
        'last_run' => 'datetime',
        'last_success' => 'datetime',
        'last_error' => 'datetime',
        'options' => 'json',
    ];

    /**
     * Get the project that owns the scheduled scrape.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the source that owns the scheduled scrape.
     */
    public function source()
    {
        return $this->belongsTo(KnowledgeSource::class, 'source_id');
    }

    /**
     * Get the model that is used for the scheduled scrape.
     */
    public function model()
    {
        return $this->belongsTo(AIModel::class, 'model_id');
    }

    /**
     * Get the user that created the scheduled scrape.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the scraped URLs associated with this scheduled scrape.
     */
    public function scrapedUrls()
    {
        return $this->hasMany(KnowledgeScrapedUrl::class, 'scheduled_scrape_id');
    }
}
