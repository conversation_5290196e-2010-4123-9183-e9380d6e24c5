<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('widget_behaviors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('widget_id')->constrained()->onDelete('cascade');
            
            // Auto-open behavior
            $table->boolean('auto_open')->default(false);
            $table->integer('auto_open_delay')->default(5); // seconds
            
            // Minimization behavior
            $table->boolean('start_minimized')->default(false);
            
            // Typing indicators
            $table->boolean('show_typing_indicator')->default(true);
            
            // User interaction
            $table->boolean('enable_user_ratings')->default(false);
            $table->boolean('collect_user_data')->default(false);
            $table->boolean('persist_conversation')->default(false);
            
            // Pre/Post chat
            $table->boolean('pre_chat_enabled')->default(false);
            $table->boolean('post_chat_enabled')->default(false);
            
            // Inactivity handling
            $table->boolean('close_after_inactivity')->default(false);
            $table->integer('inactivity_timeout')->default(5); // minutes
            
            // Display preferences
            $table->boolean('show_logo')->default(true);
            $table->boolean('show_close_button')->default(true);
            
            // Advanced behavior settings
            $table->json('advanced_settings')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index('widget_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('widget_behaviors');
    }
};
