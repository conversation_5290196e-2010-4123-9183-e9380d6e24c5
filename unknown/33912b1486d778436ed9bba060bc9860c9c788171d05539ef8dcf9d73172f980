<?php

namespace App\Console\Commands;

use App\Models\KnowledgeScrapedUrl;
use App\Models\KnowledgeSource;
use App\Models\ScheduledScrape;
use App\Services\ScraperService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ScheduledScrapeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scrape:scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run scheduled web scraping tasks';

    /**
     * @var ScraperService
     */
    protected $scraperService;

    /**
     * Create a new command instance.
     *
     * @param ScraperService $scraperService
     * @return void
     */
    public function __construct(ScraperService $scraperService)
    {
        parent::__construct();
        $this->scraperService = $scraperService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting scheduled scraping tasks...');
        
        // Get all scheduled scrapes that are due
        $scheduledScrapes = ScheduledScrape::where('status', 'active')
            ->where(function ($query) {
                $query->where('next_run', '<=', now())
                    ->orWhereNull('next_run');
            })
            ->get();
        
        $this->info("Found {$scheduledScrapes->count()} scheduled scrapes to process");
        
        foreach ($scheduledScrapes as $scheduledScrape) {
            $this->processScheduledScrape($scheduledScrape);
        }
        
        $this->info('Scheduled scraping tasks completed');
        
        return 0;
    }
    
    /**
     * Process a scheduled scrape
     *
     * @param ScheduledScrape $scheduledScrape
     * @return void
     */
    protected function processScheduledScrape(ScheduledScrape $scheduledScrape)
    {
        $this->info("Processing scheduled scrape ID: {$scheduledScrape->id} for URL: {$scheduledScrape->url}");
        
        try {
            // Update the last_run timestamp
            $scheduledScrape->last_run = now();
            
            // Calculate the next run time based on frequency
            $this->calculateNextRunTime($scheduledScrape);
            
            // Save the updated scheduled scrape
            $scheduledScrape->save();
            
            // Prepare scraping options
            $options = json_decode($scheduledScrape->options, true) ?? [];
            $options['project_id'] = $scheduledScrape->project_id;
            
            // Run the scraper
            $result = $this->scraperService->scrapeUrl(
                $scheduledScrape->url,
                $scheduledScrape->model_id,
                $options
            );
            
            // Save the scraped content
            if ($result) {
                // Get or create the source
                $source = KnowledgeSource::firstOrCreate(
                    [
                        'type' => 'scrape',
                        'project_id' => $scheduledScrape->project_id,
                        'table_name' => $scheduledScrape->table_name,
                    ],
                    [
                        'name' => 'Scheduled Web Scraping',
                        'description' => 'Content automatically scraped from the web',
                        'status' => 'active',
                    ]
                );
                
                // Prepare metadata
                $metadata = [
                    'scheduled_scrape_id' => $scheduledScrape->id,
                    'scraped_at' => now()->toIso8601String(),
                    'scheduled' => true,
                    'frequency' => $scheduledScrape->frequency,
                ];
                
                // Save to database
                $this->scraperService->saveToDatabase(
                    $result['text'] ?? '',
                    $scheduledScrape->format,
                    $scheduledScrape->table_name,
                    $scheduledScrape->url,
                    $result['title'] ?? 'Scheduled scrape',
                    $metadata,
                    $source->id,
                    $scheduledScrape->project_id
                );
                
                // Update success count
                $scheduledScrape->success_count += 1;
                $scheduledScrape->last_success = now();
                $scheduledScrape->save();
                
                $this->info("Successfully scraped and saved content for URL: {$scheduledScrape->url}");
            }
        } catch (\Throwable $e) {
            // Log the error
            Log::error("Scheduled scrape failed for ID: {$scheduledScrape->id}, URL: {$scheduledScrape->url}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Update error count
            $scheduledScrape->error_count += 1;
            $scheduledScrape->last_error = now();
            $scheduledScrape->last_error_message = $e->getMessage();
            $scheduledScrape->save();
            
            $this->error("Failed to scrape URL: {$scheduledScrape->url} - {$e->getMessage()}");
        }
    }
    
    /**
     * Calculate the next run time based on frequency
     *
     * @param ScheduledScrape $scheduledScrape
     * @return void
     */
    protected function calculateNextRunTime(ScheduledScrape $scheduledScrape)
    {
        $now = Carbon::now();
        
        switch ($scheduledScrape->frequency) {
            case 'hourly':
                $scheduledScrape->next_run = $now->addHour();
                break;
            case 'daily':
                $scheduledScrape->next_run = $now->addDay();
                break;
            case 'weekly':
                $scheduledScrape->next_run = $now->addWeek();
                break;
            case 'monthly':
                $scheduledScrape->next_run = $now->addMonth();
                break;
            default:
                // Default to daily if frequency is not recognized
                $scheduledScrape->next_run = $now->addDay();
                break;
        }
    }
}
