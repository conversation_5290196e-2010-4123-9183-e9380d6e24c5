import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { FileMeta } from "@/modules/knowledge-base/knowledge-base-resources";
import {
  FileText,
  FileCode,
  FileSpreadsheet,
  File,
  Sparkles,
  Trash2,
  Download,
} from "lucide-react";

interface DocumentCardProps {
  document: FileMeta;
  onToggleActive: (id: number, active: boolean) => void;
  onDelete: (id: number) => void;
  onDownload: (id: number) => void;
  onGenerateEmbeddings: (id: number) => void;
  selected: boolean;
  onSelect: (id: number, selected: boolean) => void;
}

export function DocumentCard({
  document,
  onToggleActive,
  onDelete,
  onDownload,
  onGenerateEmbeddings,
  selected,
  onSelect,
}: DocumentCardProps) {
  const getFileIcon = () => {
    const type = document.file_type.toLowerCase();
    if (type.includes("pdf"))
      return <FileText className="h-8 w-8 text-red-500" />;
    if (type.includes("doc"))
      return <FileText className="h-8 w-8 text-blue-500" />;
    if (type.includes("csv") || type.includes("xls"))
      return <FileSpreadsheet className="h-8 w-8 text-green-500" />;
    if (type.includes("json") || type.includes("xml"))
      return <FileCode className="h-8 w-8 text-yellow-500" />;
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <Card
      className={`overflow-hidden transition-all ${selected ? "border-primary ring-1 ring-primary" : ""}`}
    >
      <div className="p-4 flex items-start gap-3">
        <div className="flex-shrink-0">{getFileIcon()}</div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div>
              <h3 className="font-medium truncate" title={document.file_name}>
                {document.file_name}
              </h3>
              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                <span>{formatFileSize(document.file_size)}</span>
                <span>•</span>
                <span>{formatDate(document.created_at)}</span>
              </div>
            </div>

            <div className="flex items-center">
              <Switch
                checked={document.is_active_source}
                onCheckedChange={(checked) =>
                  onToggleActive(document.id, checked)
                }
                aria-label="Toggle active status"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-1 mt-2">
            {document.category && (
              <Badge variant="outline" className="text-xs">
                {document.category}
              </Badge>
            )}

            {document.has_embeddings ? (
              <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-200 text-xs">
                <Sparkles className="h-3 w-3 mr-1" /> Embeddings Ready
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="text-xs text-amber-600 border-amber-200 bg-amber-50"
              >
                No Embeddings
              </Badge>
            )}
          </div>
        </div>
      </div>

      <CardFooter className="bg-muted/20 p-2 flex justify-between">
        <div>
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(document.id, e.target.checked)}
            className="mr-2"
            id={`select-${document.id}`}
          />
          <label htmlFor={`select-${document.id}`} className="text-xs">
            Select
          </label>
        </div>

        <div className="flex gap-1">
          {!document.has_embeddings && (
            <Button
              size="sm"
              variant="ghost"
              className="h-8 px-2 text-xs"
              onClick={() => onGenerateEmbeddings(document.id)}
            >
              <Sparkles className="h-3 w-3 mr-1" /> Generate
            </Button>
          )}

          <Button
            size="sm"
            variant="ghost"
            className="h-8 px-2 text-xs"
            onClick={() => onDownload(document.id)}
          >
            <Download className="h-3 w-3 mr-1" /> Download
          </Button>

          <Button
            size="sm"
            variant="ghost"
            className="h-8 px-2 text-xs text-red-500 hover:text-red-600 hover:bg-red-50"
            onClick={() => onDelete(document.id)}
          >
            <Trash2 className="h-3 w-3 mr-1" /> Delete
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
