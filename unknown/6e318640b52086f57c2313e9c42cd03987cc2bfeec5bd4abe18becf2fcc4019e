# Widget System Documentation

## Overview

The Widget System allows users to create, customize, and deploy AI chat widgets on their websites. The system consists of several components:

1. **Widget Builder**: A user interface for creating and customizing widgets
2. **Widget API**: Backend services for managing widgets and their settings
3. **Widget Script**: A JavaScript script that can be embedded on websites to display the chat widget
4. **Pre-Chat Forms**: Customizable forms that can be shown before starting a chat
5. **Post-Chat Surveys**: Customizable surveys that can be shown after a chat ends

## Widget Settings Structure

Widget settings are stored in a nested structure in the database but are flattened when sent to the widget script. The nested structure organizes settings into categories:

### Nested Structure (Database)

```json
{
  "general": {
    "name": "Support Widget",
    "welcomeMessage": "Hello! How can I help you today?",
    "botName": "AI Assistant",
    "placeholderText": "Type your message...",
    "widgetPosition": "bottom-right",
    "presetTheme": "modern"
  },
  "appearance": {
    "primaryColor": "#4f46e5",
    "secondaryColor": "#ffffff",
    "headerBgColor": "#4f46e5",
    "textColor": "#111827",
    "fontSize": 14,
    "borderRadius": 8,
    "widgetWidth": 350,
    "widgetHeight": 600,
    "showLogo": true,
    "showCloseButton": true,
    "darkMode": false,
    "customCSS": "",
    "animation": "fade",
    "shadow": "md",
    "glassMorphism": false
  },
  "behavior": {
    "startMinimized": false,
    "autoOpen": false,
    "autoOpenDelay": 5,
    "showTypingIndicator": true,
    "enableUserRatings": true,
    "collectUserData": true,
    "preChat": false,
    "postChat": false,
    "closeAfterInactivity": false,
    "inactivityTimeout": 5
  },
  "advanced": {
    "modelSelection": "auto",
    "contextRetention": "session",
    "maxMessagesStored": 100,
    "enableAnalytics": true,
    "debugMode": false,
    "loadTimeoutMs": 5000,
    "webhookUrl": "",
    "customParameters": {}
  }
}
```

### Flat Structure (Widget Script)

```json
{
  "name": "Support Widget",
  "welcomeMessage": "Hello! How can I help you today?",
  "botName": "AI Assistant",
  "placeholderText": "Type your message...",
  "widgetPosition": "bottom-right",
  "presetTheme": "modern",
  "primaryColor": "#4f46e5",
  "secondaryColor": "#ffffff",
  "headerBgColor": "#4f46e5",
  "textColor": "#111827",
  "fontSize": 14,
  "borderRadius": 8,
  "widgetWidth": 350,
  "widgetHeight": 600,
  "showLogo": true,
  "showCloseButton": true,
  "darkMode": false,
  "customCSS": "",
  "animation": "fade",
  "shadow": "md",
  "glassMorphism": false,
  "startMinimized": false,
  "autoOpen": false,
  "autoOpenDelay": 5,
  "showTypingIndicator": true,
  "enableUserRatings": true,
  "collectUserData": true,
  "preChat": false,
  "postChat": false,
  "closeAfterInactivity": false,
  "inactivityTimeout": 5,
  "modelSelection": "auto",
  "contextRetention": "session",
  "maxMessagesStored": 100,
  "enableAnalytics": true,
  "debugMode": false,
  "loadTimeoutMs": 5000,
  "webhookUrl": "",
  "customParameters": {}
}
```

## Pre-Chat Forms

Pre-chat forms allow collecting information from users before starting a chat. Each widget can have multiple pre-chat form templates, but only one can be active at a time.

### Data Models

```
PreChatFormTemplate
├── id: number
├── widget_id: number
├── title: string
├── description: string
├── is_active: boolean
├── fields: PreChatFormField[]
└── submissions: PreChatFormSubmission[]

PreChatFormField
├── id: number
├── template_id: number
├── name: string
├── label: string
├── type: 'text' | 'email' | 'phone' | 'select' | 'checkbox'
├── placeholder: string (optional)
├── options: string[] (for select fields)
├── is_required: boolean
├── validation_pattern: string (optional)
├── error_message: string (optional)
└── order: number

PreChatFormSubmission
├── id: number
├── template_id: number
├── session_id: string
└── data: Record<string, any> (field name to value mapping)
```

### API Endpoints

```
# Public endpoints
GET    /api/pre-chat-form?widget_id={widgetId}
POST   /api/pre-chat-form/submit

# Admin endpoints (authenticated)
GET    /api/widgets/{widgetId}/pre-chat-forms
POST   /api/widgets/{widgetId}/pre-chat-forms
PUT    /api/widgets/{widgetId}/pre-chat-forms/{templateId}
DELETE /api/widgets/{widgetId}/pre-chat-forms/{templateId}
GET    /api/widgets/{widgetId}/pre-chat-forms/{templateId}/submissions
```

## Post-Chat Surveys

Post-chat surveys allow collecting feedback from users after a chat ends. Each widget can have multiple post-chat survey templates, but only one can be active at a time.

### Data Models

```
PostChatSurvey
├── id: number
├── widget_id: number
├── title: string
├── description: string
├── thank_you_message: string
├── is_active: boolean
├── questions: SurveyQuestion[]
└── responses: SurveyResponse[]

SurveyQuestion
├── id: number
├── survey_id: number
├── text: string
├── type: 'rating' | 'text' | 'select' | 'boolean' | 'checkbox' | 'multiselect'
├── options: string[] (for select questions)
├── is_required: boolean
└── order: number

SurveyResponse
├── id: number
├── survey_id: number
├── session_id: string
└── answers: Record<number, any> (question ID to answer mapping)
```

### API Endpoints

```
# Public endpoints
GET    /api/post-chat-survey?widget_id={widgetId}
POST   /api/post-chat-survey/submit

# Admin endpoints (authenticated)
GET    /api/widgets/{widgetId}/post-chat-surveys
POST   /api/widgets/{widgetId}/post-chat-surveys
PUT    /api/widgets/{widgetId}/post-chat-surveys/{surveyId}
DELETE /api/widgets/{widgetId}/post-chat-surveys/{surveyId}
GET    /api/widgets/{widgetId}/post-chat-surveys/{surveyId}/responses
GET    /api/widgets/{widgetId}/post-chat-surveys/{surveyId}/analytics
```

## Widget Embedding

To embed a widget on a website, add the following script tag to your HTML:

```html
<script src="https://your-domain.com/widget/v1/widget.js?id=YOUR_WIDGET_ID" async></script>
```

Replace `YOUR_WIDGET_ID` with the actual widget ID from the dashboard.
