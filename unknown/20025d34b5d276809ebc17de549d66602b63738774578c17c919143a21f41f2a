/**
 * AI Chat Widget Loader v1.0
 * This script loads and initializes the AI chat widget on websites.
 * It handles dynamic configuration, CORS, and guest user flows.
 */

(function () {
  'use strict';

  // Widget configuration
  const config = {
    baseUrl: '',
    widgetId: '',
    primaryColor: '#4f46e5',
    borderRadius: '8',
    allowedDomains: '*',
    guestFlow: 'auto',
    position: 'fixed',
    webhookUrl: '',
    enableAnalytics: true,
    sanitizeInputs: true,
    debugMode: false,
    sessionId: null,
    containerSelector: null, // For inline placement
  };

  // Dynamic state
  let widget = null;
  let iframe = null;
  let initialized = false;
  let isOpen = false;

  /**
   * Initialize the widget with configuration from script data attributes
   */
  function initWidget() {
    // Only initialize once
    if (initialized) return;
    initialized = true;

    // Get current script element
    const scripts = document.getElementsByTagName('script');
    const currentScript = scripts[scripts.length - 1];

    // Auto-detect base URL
    config.baseUrl = getBaseUrl(currentScript);

    // Get configuration from data attributes
    config.widgetId = currentScript.getAttribute('data-widget-id') || '';
    config.primaryColor = currentScript.getAttribute('data-primary-color') || config.primaryColor;
    config.borderRadius = currentScript.getAttribute('data-border-radius') || config.borderRadius;
    config.allowedDomains = currentScript.getAttribute('data-allowed-domains') || config.allowedDomains;
    config.guestFlow = currentScript.getAttribute('data-guest-flow') || config.guestFlow;
    config.containerSelector = currentScript.getAttribute('data-container') || null;
    config.webhookUrl = currentScript.getAttribute('data-webhook-url') || '';
    config.enableAnalytics = currentScript.getAttribute('data-analytics') !== 'false';
    config.sanitizeInputs = currentScript.getAttribute('data-sanitize') !== 'false';
    config.debugMode = currentScript.getAttribute('data-debug') === 'true';

    // Position settings
    const position = currentScript.getAttribute('data-position') || 'bottom-right';
    config.position = currentScript.getAttribute('data-position-type') || 'fixed';

    // Validate required parameters
    if (!config.widgetId) {
      console.error('AI Chat Widget: Missing widget ID');
      return;
    }

    // Validate the domain (CORS check)
    validateDomain()
      .then(result => {
        if (result.allowed) {
          createWidget(position);
          if (config.enableAnalytics) {
            trackWidgetLoaded();
          }
        } else {
          console.error('AI Chat Widget: Domain not allowed', result.message);
        }
      })
      .catch(error => {
        console.error('AI Chat Widget: Domain validation failed', error);
      });
  }

  /**
   * Auto-detect the base URL from the script source
   */
  function getBaseUrl(scriptElement) {
    if (window.CHAT_WIDGET_BASE_URL) {
      return window.CHAT_WIDGET_BASE_URL;
    }

    const src = scriptElement.src || '';
    if (src) {
      const srcUrl = new URL(src);
      const pathSegments = srcUrl.pathname.split('/');
      const widgetIndex = pathSegments.indexOf('widget');

      if (widgetIndex !== -1) {
        // Remove segments after 'widget'
        const basePath = pathSegments.slice(0, widgetIndex + 1).join('/');
        return `${srcUrl.protocol}//${srcUrl.host}${basePath}`;
      }

      // Fallback to origin
      return srcUrl.origin;
    }

    // Last resort fallback
    return 'https://widget.example.com/widget';
  }

  /**
   * Validate if the current domain is allowed to use the widget
   */
  function validateDomain() {
    const domain = window.location.hostname;
    const url = `${config.baseUrl}/api/widget/validate-domain`;

    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        widget_id: config.widgetId,
        domain: domain
      })
    })
      .then(response => response.json())
      .catch(() => {
        // Fallback to allow the domain if the validation endpoint is down
        return { allowed: true, message: 'Validation endpoint unreachable, allowing domain' };
      });
  }

  /**
   * Check or create guest user session based on the configured flow
   */
  function handleGuestUser() {
    // First check if we have a session ID in localStorage
    const sessionId = localStorage.getItem(`ai-chat-session-${config.widgetId}`);

    if (sessionId) {
      // Validate existing session
      return validateGuestSession(sessionId)
        .then(result => {
          if (result.valid) {
            config.sessionId = sessionId;
            return { success: true, sessionId: sessionId };
          } else if (result.should_auto_register) {
            // Auto-register a new guest if policy allows
            return autoRegisterGuest();
          } else if (result.should_show_registration) {
            // Show registration form
            return { success: false, requiresRegistration: true };
          } else {
            // Clear invalid session
            localStorage.removeItem(`ai-chat-session-${config.widgetId}`);
            return { success: false };
          }
        });
    } else {
      // Check the guest flow setting
      return fetchGuestFlowSetting()
        .then(result => {
          if (result.success) {
            const guestFlow = result.guest_flow || 'auto';

            if (guestFlow === 'auto') {
              return autoRegisterGuest();
            } else if (guestFlow === 'none') {
              return { success: true, guestFlowDisabled: true };
            } else {
              return { success: false, requiresRegistration: true };
            }
          } else {
            // Fallback to auto if we can't fetch the setting
            return autoRegisterGuest();
          }
        })
        .catch(() => {
          // Fallback to auto if the request fails
          return autoRegisterGuest();
        });
    }
  }

  /**
   * Validate an existing guest session
   */
  function validateGuestSession(sessionId) {
    const url = `${config.baseUrl}/api/guest/validate`;

    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: sessionId,
        widget_id: config.widgetId
      })
    })
      .then(response => response.json())
      .catch(() => {
        return { valid: false, message: 'Validation request failed' };
      });
  }

  /**
   * Fetch the guest flow setting for the widget
   */
  function fetchGuestFlowSetting() {
    const url = `${config.baseUrl}/api/guest/flow?widget_id=${config.widgetId}`;

    return fetch(url)
      .then(response => response.json())
      .catch(() => {
        return { success: false, guest_flow: 'auto' };
      });
  }

  /**
   * Automatically register a guest user
   */
  function autoRegisterGuest() {
    const url = `${config.baseUrl}/api/guest/auto-register`;

    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        widget_id: config.widgetId,
        metadata: {
          url: window.location.href,
          referrer: document.referrer,
          timestamp: new Date().toISOString()
        }
      })
    })
      .then(response => response.json())
      .then(result => {
        if (result.success && result.session_id) {
          // Store the session ID in localStorage
          localStorage.setItem(`ai-chat-session-${config.widgetId}`, result.session_id);
          config.sessionId = result.session_id;
          return { success: true, sessionId: result.session_id, autoGenerated: true };
        } else {
          return { success: false, message: result.message || 'Auto-registration failed' };
        }
      })
      .catch(() => {
        return { success: false, message: 'Auto-registration request failed' };
      });
  }

  /**
   * Track widget loaded event for analytics
   */
  function trackWidgetLoaded() {
    if (!config.enableAnalytics) return;

    const url = `${config.baseUrl}/api/widget/analytics/view`;
    const data = {
      widget_id: config.widgetId,
      event: 'widget_loaded',
      url: window.location.href,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      sessionId: config.sessionId
    };

    // Use sendBeacon if available for reliability during page unload
    if (navigator.sendBeacon) {
      navigator.sendBeacon(url, JSON.stringify(data));
    } else {
      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        keepalive: true
      }).catch(() => { });
    }
  }

  /**
   * Create the widget UI based on the configuration
   */
  function createWidget(positionName) {
    // Handle guest user flow first
    handleGuestUser()
      .then(result => {
        if (!result.success && result.requiresRegistration && config.guestFlow === 'required') {
          // Will show registration form in the iframe
        }

        // Create container for widget or use existing
        if (config.containerSelector) {
          widget = document.querySelector(config.containerSelector);
          if (!widget) {
            console.error(`AI Chat Widget: Container element "${config.containerSelector}" not found`);
            return;
          }
        } else {
          // Create floating widget
          widget = document.createElement('div');
          widget.id = `ai-chat-widget-${config.widgetId}`;
          widget.style.position = config.position;
          widget.style.zIndex = '9999';

          // Position the widget
          setWidgetPosition(widget, positionName);

          document.body.appendChild(widget);
        }

        // Create and configure iframe
        createIframe(result);

        // Add messaging between iframe and parent
        setupPostMessageHandling();
      })
      .catch(error => {
        console.error('AI Chat Widget: Failed to initialize guest user', error);
      });
  }

  /**
   * Set the position of the widget based on position name
   */
  function setWidgetPosition(element, positionName) {
    // Reset all positions
    element.style.top = 'auto';
    element.style.right = 'auto';
    element.style.bottom = 'auto';
    element.style.left = 'auto';

    // Set positions based on name
    switch (positionName) {
      case 'bottom-right':
        element.style.bottom = '20px';
        element.style.right = '20px';
        break;
      case 'bottom-left':
        element.style.bottom = '20px';
        element.style.left = '20px';
        break;
      case 'top-right':
        element.style.top = '20px';
        element.style.right = '20px';
        break;
      case 'top-left':
        element.style.top = '20px';
        element.style.left = '20px';
        break;
      default:
        element.style.bottom = '20px';
        element.style.right = '20px';
    }
  }

  /**
   * Create the iframe for the widget content
   */
  function createIframe(guestResult) {
    iframe = document.createElement('iframe');
    iframe.id = `ai-chat-iframe-${config.widgetId}`;
    iframe.style.border = 'none';
    iframe.style.width = '60px';  // Initial button size
    iframe.style.height = '60px';
    iframe.style.borderRadius = '50%';
    iframe.style.transition = 'all 0.3s ease';
    iframe.allow = 'microphone; camera';
    iframe.title = 'AI Chat Widget';

    // Build iframe URL with parameters
    let iframeUrl = `${config.baseUrl}/v1/iframe/${config.widgetId}`;
    iframeUrl += `?primaryColor=${encodeURIComponent(config.primaryColor)}`;
    iframeUrl += `&borderRadius=${encodeURIComponent(config.borderRadius)}`;
    iframeUrl += `&guestFlow=${encodeURIComponent(config.guestFlow)}`;

    if (config.sessionId) {
      iframeUrl += `&sessionId=${encodeURIComponent(config.sessionId)}`;
    }

    if (guestResult && guestResult.requiresRegistration) {
      iframeUrl += '&showRegistration=true';
    }

    if (config.sanitizeInputs) {
      iframeUrl += '&sanitize=true';
    }

    if (config.debugMode) {
      iframeUrl += '&debug=true';
    }

    iframe.src = iframeUrl;
    widget.appendChild(iframe);
  }

  /**
   * Set up post message handling between parent and iframe
   */
  function setupPostMessageHandling() {
    window.addEventListener('message', function (event) {
      // Verify source
      if (iframe && event.source === iframe.contentWindow) {
        const message = event.data;

        // Handle messages from the iframe
        switch (message.type) {
          case 'widget:open':
            openWidget(message.width || 350, message.height || 600);
            break;

          case 'widget:close':
            closeWidget();
            break;

          case 'widget:resize':
            resizeWidget(message.width, message.height);
            break;

          case 'widget:sessionUpdate':
            if (message.sessionId) {
              localStorage.setItem(`ai-chat-session-${config.widgetId}`, message.sessionId);
              config.sessionId = message.sessionId;
            }
            break;

          case 'widget:event':
            handleWidgetEvent(message.event, message.data);
            break;

          default:
            if (config.debugMode) {
              console.log('AI Chat Widget: Unknown message', message);
            }
        }
      }
    });
  }

  /**
   * Open the widget (expand to full size)
   */
  function openWidget(width, height) {
    if (!iframe) return;

    iframe.style.width = `${width}px`;
    iframe.style.height = `${height}px`;
    iframe.style.borderRadius = `${config.borderRadius}px`;
    isOpen = true;

    // Track open event
    if (config.enableAnalytics) {
      trackWidgetEvent('widget_opened');
    }
  }

  /**
   * Close the widget (collapse to button)
   */
  function closeWidget() {
    if (!iframe) return;

    iframe.style.width = '60px';
    iframe.style.height = '60px';
    iframe.style.borderRadius = '50%';
    isOpen = false;

    // Track close event
    if (config.enableAnalytics) {
      trackWidgetEvent('widget_closed');
    }
  }

  /**
   * Resize the widget
   */
  function resizeWidget(width, height) {
    if (!iframe || !isOpen) return;

    iframe.style.width = `${width}px`;
    iframe.style.height = `${height}px`;
  }

  /**
   * Handle widget events and forward to webhook if configured
   */
  function handleWidgetEvent(eventName, eventData) {
    // Track events for analytics
    if (config.enableAnalytics) {
      trackWidgetEvent(eventName, eventData);
    }

    // Forward to webhook if configured
    if (config.webhookUrl) {
      const webhookData = {
        event: eventName,
        widget_id: config.widgetId,
        session_id: config.sessionId,
        timestamp: new Date().toISOString(),
        data: eventData,
        url: window.location.href
      };

      fetch(config.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookData),
        keepalive: true
      }).catch(() => {
        if (config.debugMode) {
          console.error('AI Chat Widget: Failed to send webhook', eventName);
        }
      });
    }
  }

  /**
   * Track widget events for analytics
   */
  function trackWidgetEvent(eventName, eventData = {}) {
    if (!config.enableAnalytics) return;

    const url = `${config.baseUrl}/api/widget/analytics/view`;
    const data = {
      widget_id: config.widgetId,
      event: eventName,
      url: window.location.href,
      sessionId: config.sessionId,
      timestamp: new Date().toISOString(),
      data: eventData
    };

    // Use beacon API for better reliability during page unload
    if (navigator.sendBeacon) {
      navigator.sendBeacon(url, JSON.stringify(data));
    } else {
      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        keepalive: true
      }).catch(() => { });
    }
  }

  // Initialize the widget
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWidget);
  } else {
    initWidget();
  }

  // Expose public API
  window.AIChatWidget = window.AIChatWidget || {};
  window.AIChatWidget[config.widgetId] = {
    open: function () {
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({ type: 'parent:open' }, '*');
      }
    },
    close: function () {
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({ type: 'parent:close' }, '*');
      }
    },
    toggle: function () {
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({
          type: isOpen ? 'parent:close' : 'parent:open'
        }, '*');
      }
    },
    clearSession: function () {
      localStorage.removeItem(`ai-chat-session-${config.widgetId}`);
      config.sessionId = null;

      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({ type: 'parent:clearSession' }, '*');
      }
    }
  };

})();
