/**
 * Post-Chat Survey Types
 * These types define the structure of post-chat surveys.
 */

export interface PostChatSurvey {
    id: number;
    widgetId: number;
    title: string;
    description: string;
    questions: SurveyQuestion[];
    thankYouMessage: string;
    createdAt: string;
    updatedAt: string;
}

export interface SurveyQuestion {
    id: number;
    surveyId: number;
    text: string;
    type: 'rating' | 'text' | 'select' | 'boolean';
    options?: string[]; // For select questions
    isRequired: boolean;
    order: number;
}

export interface SurveyResponse {
    id: number;
    surveyId: number;
    sessionId: string;
    answers: Record<number, any>; // Question ID to answer mapping
    createdAt: string;
}

/**
 * State management interface for the post-chat survey
 */
export interface PostChatSurveyState {
    isVisible: boolean;
    survey: PostChatSurvey | null;
    responses: Record<number, any>;
    errors: Record<string, string>;
    isSubmitting: boolean;
    isSubmitted: boolean;
    currentStep: number;
    totalSteps: number;
}

/**
 * Survey analytics types
 */
export interface SurveyAnalytics {
    totalResponses: number;
    averageRating: number;
    completionRate: number;
    questionBreakdown: Record<number, QuestionAnalytics>;
}

export interface QuestionAnalytics {
    questionId: number;
    questionText: string;
    responseCount: number;
    averageRating?: number; // For rating questions
    responseBreakdown?: Record<string, number>; // For select questions
} 