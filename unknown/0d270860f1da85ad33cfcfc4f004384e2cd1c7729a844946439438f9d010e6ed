<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Test</title>
</head>
<body>
    <h1>Widget Test Page</h1>
    <p>This page is used to test the widget API endpoints.</p>

    <div id="result"></div>

    <script>
        // Test the widget analytics endpoint
        async function testWidgetAnalytics() {
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('/test/widget-analytics-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        widget_id: 'test-widget',
                        event_type: 'view',
                        visitor_id: 'test-visitor',
                        url: window.location.href
                    })
                });

                const data = await response.json();
                resultDiv.innerHTML = `
                    <h2>Test Result</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h2>Test Failed</h2>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // Run the test when the page loads
        window.addEventListener('load', testWidgetAnalytics);
    </script>
</body>
</html>
