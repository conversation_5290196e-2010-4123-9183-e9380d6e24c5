import { AIModelData } from "@/utils/ai-model-service"
import { Template } from "@/utils/template-service"
import { TemplateProcessingService } from "./template-processing-service"
import api from "@/utils/api"

/**
 * Chat Message type
 */
export interface ChatMessage {
    role: "system" | "user" | "assistant"
    content: string
}

/**
 * Chat Request options
 */
export interface ChatRequestOptions {
    temperature?: number
    max_tokens?: number
    template_variables?: Record<string, string>
}

/**
 * Chat Response type
 */
export interface ChatResponse {
    message: string
    metadata: {
        model: string
        provider: string
        response_time: number
        tokens_input: number
        tokens_output: number
        total_tokens: number
        template_used?: boolean
        error?: string
    }
}

/**
 * Chat Request type
 */
interface ChatRequest {
    model_id: number
    message: string
    temperature: number
    max_tokens: number
    system_prompt?: string
}

/**
 * Error response type
 */
interface ErrorResponse {
    status: number
    message: string
    details?: any
}

/**
 * AIService
 * 
 * Handles communication with different AI providers
 */
export class AIService {
    /**
     * Send a chat message to an AI model
     * 
     * @param model The AI model to use
     * @param message The user message
     * @param template Optional template to use
     * @param options Additional options
     * @returns The AI response
     */
    public static async sendChatMessage(
        model: AIModelData,
        message: string,
        template: Template | null = null,
        options: ChatRequestOptions = {}
    ): Promise<ChatResponse> {
        try {
            const startTime = Date.now()

            // Get the messages to send to the model
            const messages = await this.prepareMessages(model, message, template, options)

            // Make the API request to the provider
            const response = await this.makeProviderRequest(model, messages, options)

            const endTime = Date.now()
            const responseTime = endTime - startTime

            // Return the formatted response
            return {
                message: response.message,
                metadata: {
                    model: model.name || "unknown",
                    provider: model.provider || "unknown",
                    response_time: responseTime,
                    tokens_input: response.tokens_input || 0,
                    tokens_output: response.tokens_output || 0,
                    total_tokens: response.total_tokens || 0,
                    template_used: !!template,
                    ...response.metadata
                }
            }
        } catch (error) {
            console.error("Error in sendChatMessage:", error)
            throw error
        }
    }

    /**
     * Prepare messages for the AI model, including template if available
     * 
     * @param model The AI model
     * @param message The user message
     * @param template The template (if available)
     * @param options Additional options
     * @returns Array of messages to send to the model
     */
    private static async prepareMessages(
        model: AIModelData,
        message: string,
        template: Template | null,
        options: ChatRequestOptions
    ): Promise<ChatMessage[]> {
        const messages: ChatMessage[] = []

        // If we have a template, process it and use as system message
        if (template) {
            const variables = {
                ...options.template_variables,
                user_query: message
            }

            const templateContent = await TemplateProcessingService.process(template, variables)

            // Add the processed template as a system message
            messages.push({
                role: "system",
                content: templateContent
            })
        }

        // Add the user message
        messages.push({
            role: "user",
            content: message
        })

        return messages
    }

    /**
     * Make a request to the AI provider
     * 
     * @param model The AI model to use
     * @param messages The messages to send
     * @param options Additional options
     * @returns The provider response
     */
    private static async makeProviderRequest(
        model: AIModelData,
        messages: ChatMessage[],
        options: ChatRequestOptions
    ): Promise<{
        message: string,
        tokens_input?: number,
        tokens_output?: number,
        total_tokens?: number,
        metadata?: any
    }> {
        try {
            // Prepare the request data based on the model provider
            const requestData = {
                model: model.settings?.model_name || model.name || "unknown",
                messages,
                temperature: options.temperature ?? model.settings?.temperature ?? 0.7,
                max_tokens: options.max_tokens ?? model.settings?.max_tokens ?? 1000,
                api_key: model.api_key
            }

            // Make the API request
            const response = await api.post(`/ai/chat`, {
                provider: model.provider,
                data: requestData
            })

            // Extract and return the response data
            return {
                message: response.data.message,
                tokens_input: response.data.usage?.prompt_tokens,
                tokens_output: response.data.usage?.completion_tokens,
                total_tokens: response.data.usage?.total_tokens,
                metadata: response.data.metadata
            }
        } catch (error) {
            console.error("Error making provider request:", error)
            throw error
        }
    }

    /**
     * Get a template for a model if available
     * 
     * @param model The AI model
     * @returns Template or null if not available
     */
    public static async getTemplateForModel(model: AIModelData): Promise<Template | null> {
        if (!model || !model.template_id) return null

        try {
            const response = await api.get(`/templates/${model.template_id}`)
            return response.data
        } catch (error) {
            console.error(`Error fetching template for model ${model.id}:`, error)
            return null
        }
    }

    /**
     * Generate embeddings for text
     * 
     * @param text Text to generate embeddings for
     * @param modelId Optional model ID to use (defaults to default embedding model)
     * @returns Embedding vector
     */
    public static async generateEmbeddings(
        text: string,
        modelId?: number
    ): Promise<number[]> {
        if (!text || text.trim() === "") {
            throw new Error("Text is required for generating embeddings")
        }

        try {
            const response = await api.post("/ai/embeddings", {
                text,
                model_id: modelId
            })

            if (!response.data || !response.data.embeddings) {
                throw new Error("No embeddings returned from API")
            }

            return response.data.embeddings
        } catch (error) {
            console.error("Error generating embeddings:", error)
            throw error
        }
    }

    /**
     * Format error message from various error types
     * 
     * @param error Error object
     * @returns Formatted error message
     */
    private static formatErrorMessage(error: any): string {
        if (error.response?.data?.message) {
            return error.response.data.message
        }

        if (error.response?.data?.error) {
            return error.response.data.error
        }

        if (error.message) {
            return error.message
        }

        return "Unknown error occurred"
    }
} 