import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Trash, Plus, Save } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { AIModelData, aiModelService } from "@/utils/ai-model-service";
import { useToast } from "@/hooks/use-toast";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

// Rule types
export interface ModelRule {
    id?: number;
    name: string;
    condition_type: string;
    condition_value: string;
    active: boolean;
}

interface ModelRulesCardProps {
    selectedModel: AIModelData;
    isSaving: boolean;
    onUpdateModel: (model: AIModelData) => void;
}

export function ModelRulesCard({
    selectedModel,
    isSaving,
    onUpdateModel
}: ModelRulesCardProps) {
    const { toast } = useToast();
    const [isLoading, setIsLoading] = useState(false);
    const [rules, setRules] = useState<ModelRule[]>([]);
    const [newRule, setNewRule] = useState<ModelRule>({
        name: "",
        condition_type: "intent",
        condition_value: "",
        active: true
    });

    // Load rules when selectedModel changes
    useEffect(() => {
        if (!selectedModel || !selectedModel.id) return;

        const fetchRules = async () => {
            setIsLoading(true);
            try {
                const modelRules = await aiModelService.getModelRules(selectedModel.id!);
                setRules(modelRules);
            } catch (error) {
                console.error("Failed to fetch model rules:", error);
                toast({
                    title: "Error",
                    description: "Failed to load model activation rules",
                    variant: "destructive"
                });
            } finally {
                setIsLoading(false);
            }
        };

        fetchRules();
    }, [selectedModel, toast]);

    // Add new rule
    const handleAddRule = async () => {
        if (!selectedModel || !selectedModel.id) return;
        if (!newRule.name.trim() || !newRule.condition_value.trim()) {
            toast({
                title: "Validation Error",
                description: "Name and condition value are required",
                variant: "destructive"
            });
            return;
        }

        setIsLoading(true);
        try {
            const addedRule = await aiModelService.createModelRule(selectedModel.id, newRule);
            setRules([...rules, addedRule]);
            setNewRule({
                name: "",
                condition_type: "intent",
                condition_value: "",
                active: true
            });

            toast({
                title: "Rule Added",
                description: `Rule "${newRule.name}" has been added`,
                variant: "success"
            });
        } catch (error) {
            console.error("Failed to add rule:", error);
            toast({
                title: "Error",
                description: "Failed to add activation rule",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Update existing rule
    const handleUpdateRule = async (updatedRule: ModelRule) => {
        if (!selectedModel || !selectedModel.id || !updatedRule.id) return;

        setIsLoading(true);
        try {
            await aiModelService.updateModelRule(selectedModel.id, updatedRule.id, updatedRule);

            setRules(rules.map(rule =>
                rule.id === updatedRule.id ? updatedRule : rule
            ));

            toast({
                title: "Rule Updated",
                description: `Rule "${updatedRule.name}" has been updated`
            });
        } catch (error) {
            console.error("Failed to update rule:", error);
            toast({
                title: "Error",
                description: "Failed to update activation rule",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Delete rule
    const handleDeleteRule = async (ruleId: number) => {
        if (!selectedModel || !selectedModel.id) return;

        setIsLoading(true);
        try {
            await aiModelService.deleteModelRule(selectedModel.id, ruleId);
            setRules(rules.filter(rule => rule.id !== ruleId));

            toast({
                title: "Rule Deleted",
                description: "Activation rule has been deleted"
            });
        } catch (error) {
            console.error("Failed to delete rule:", error);
            toast({
                title: "Error",
                description: "Failed to delete activation rule",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Toggle rule activation
    const handleToggleRuleActive = (ruleId: number, active: boolean) => {
        const ruleToUpdate = rules.find(rule => rule.id === ruleId);
        if (!ruleToUpdate) return;

        const updatedRule = { ...ruleToUpdate, active };
        handleUpdateRule(updatedRule);
    };

    // Update rule condition value
    const handleUpdateConditionValue = (ruleId: number, value: string) => {
        const updatedRules = rules.map(rule =>
            rule.id === ruleId ? { ...rule, condition_value: value } : rule
        );
        setRules(updatedRules);
    };

    // Save rule after editing
    const handleSaveRule = (ruleId: number) => {
        const ruleToSave = rules.find(rule => rule.id === ruleId);
        if (!ruleToSave) return;
        handleUpdateRule(ruleToSave);
    };

    // Handle condition type change for new rule
    const handleConditionTypeChange = (value: string) => {
        setNewRule({ ...newRule, condition_type: value });
    };

    return (
        <Card className="h-full">
            <CardHeader>
                <CardTitle>Activation Rules</CardTitle>
                <CardDescription>
                    Define when this model should be automatically activated
                </CardDescription>
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                    </div>
                ) : (
                    <div className="space-y-4">
                        {/* Existing Rules */}
                        {rules.length > 0 ? (
                            <div className="space-y-3">
                                {rules.map((rule) => (
                                    <div key={rule.id} className="flex items-center space-x-2 p-3 border rounded-md">
                                        <div className="flex-1 space-y-1">
                                            <p className="font-medium text-sm">{rule.name}</p>
                                            <div className="flex flex-wrap items-center gap-2 text-xs text-muted-foreground">
                                                <span className="font-semibold">Type:</span>
                                                <span>{rule.condition_type}</span>

                                                <span className="font-semibold ml-2">If:</span>
                                                <Input
                                                    className="h-7 text-xs w-40"
                                                    value={rule.condition_value}
                                                    onChange={(e) => handleUpdateConditionValue(rule.id!, e.target.value)}
                                                />

                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6 ml-1"
                                                    onClick={() => handleSaveRule(rule.id!)}
                                                >
                                                    <Save className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                checked={rule.active}
                                                onCheckedChange={(checked) => handleToggleRuleActive(rule.id!, checked)}
                                            />

                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="text-destructive"
                                                onClick={() => handleDeleteRule(rule.id!)}
                                            >
                                                <Trash className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-6 border rounded-md bg-muted/20">
                                <p className="text-muted-foreground mb-2">No activation rules defined</p>
                                <p className="text-xs text-muted-foreground mb-4">
                                    Add rules to automatically activate this model based on specific conditions
                                </p>
                            </div>
                        )}

                        {/* Add New Rule Form */}
                        <div className="space-y-3 pt-4 border-t">
                            <h4 className="font-medium">Add New Rule</h4>

                            <div className="grid gap-2">
                                <Label htmlFor="rule-name">Rule Name</Label>
                                <Input
                                    id="rule-name"
                                    value={newRule.name}
                                    onChange={(e) => setNewRule({ ...newRule, name: e.target.value })}
                                    placeholder="E.g., Technical Queries"
                                />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="condition-type">Condition Type</Label>
                                <Select
                                    value={newRule.condition_type}
                                    onValueChange={handleConditionTypeChange}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a condition type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="intent">Intent</SelectItem>
                                        <SelectItem value="keyword">Keyword</SelectItem>
                                        <SelectItem value="category">Category</SelectItem>
                                        <SelectItem value="length">Message Length</SelectItem>
                                        <SelectItem value="language">Language</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="condition-value">
                                    {newRule.condition_type === "intent" && "Intent Name"}
                                    {newRule.condition_type === "keyword" && "Keyword or Phrase"}
                                    {newRule.condition_type === "category" && "Category Name"}
                                    {newRule.condition_type === "length" && "Character Count (e.g., >500)"}
                                    {newRule.condition_type === "language" && "Language Code (e.g., en, es)"}
                                </Label>
                                <Input
                                    id="condition-value"
                                    value={newRule.condition_value}
                                    onChange={(e) => setNewRule({ ...newRule, condition_value: e.target.value })}
                                    placeholder={
                                        newRule.condition_type === "intent" ? "E.g., technical_support" :
                                            newRule.condition_type === "keyword" ? "E.g., API, code, developer" :
                                                newRule.condition_type === "category" ? "E.g., technical" :
                                                    newRule.condition_type === "length" ? "E.g., >500 or <100" :
                                                        "E.g., en, fr, es"
                                    }
                                />
                            </div>

                            <div className="flex items-center space-x-2 pt-2">
                                <Switch
                                    id="activate"
                                    checked={newRule.active}
                                    onCheckedChange={(checked) => setNewRule({ ...newRule, active: checked })}
                                />
                                <Label htmlFor="activate">Active</Label>
                            </div>

                            <Button
                                className="w-full mt-2"
                                onClick={handleAddRule}
                                disabled={isLoading || isSaving || !newRule.name || !newRule.condition_value}
                            >
                                <Plus className="mr-2 h-4 w-4" /> Add Rule
                            </Button>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}