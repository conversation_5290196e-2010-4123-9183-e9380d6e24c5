import * as React from "react"
import { Check, ChevronsUpDown, Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface ComboboxOption {
  value: string
  label: string | React.ReactNode
  description?: string
  is_free?: boolean
}

interface ComboboxProps {
  options: ComboboxOption[]
  value: string
  onChange: (value: string) => void
  placeholder?: string
  emptyMessage?: string
  className?: string
  disabled?: boolean
  showSearch?: boolean
  searchPlaceholder?: string
  highlightRecentSelections?: boolean
}

export function Combobox({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  emptyMessage = "No options found.",
  className,
  disabled = false,
  showSearch = true,
  searchPlaceholder = "Search options...",
  highlightRecentSelections = false,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState("")
  const [recentSelections, setRecentSelections] = React.useState<string[]>([])

  // Find the selected option
  const selectedOption = options.find((option) => option.value === value)

  // Add to recent selections when value changes
  React.useEffect(() => {
    if (value && highlightRecentSelections) {
      setRecentSelections(prev => {
        // Remove if already exists
        const filtered = prev.filter(v => v !== value)
        // Add to beginning and limit to 5 recent selections
        return [value, ...filtered].slice(0, 5)
      })
    }
  }, [value, highlightRecentSelections])

  // Filter options based on search query
  const filteredOptions = options.filter((option) => {
    if (!searchQuery) return true
    return (
      option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      option.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "justify-between h-auto py-3 px-4 text-left font-normal border-2",
            selectedOption
              ? "bg-background border-primary/20"
              : "text-muted-foreground",
            open ? "border-primary/50 shadow-sm" : "border-input",
            className
          )}
          disabled={disabled}
        >
          <div className="flex flex-col items-start gap-1 truncate max-w-[90%]">
            <span className={cn(
              "font-medium",
              selectedOption ? "text-foreground" : "text-muted-foreground"
            )}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
            {selectedOption?.description && (
              <span className="text-xs text-muted-foreground line-clamp-1">
                {selectedOption.description}
              </span>
            )}
          </div>
          <ChevronsUpDown className={cn(
            "ml-2 h-4 w-4 shrink-0 flex-shrink-0",
            open ? "text-primary opacity-100" : "opacity-50"
          )} />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 min-w-[350px] max-w-[500px] w-auto border-2 border-primary/10 shadow-lg rounded-lg overflow-hidden"
        align="start"
        sideOffset={5}
      >
        <Command className="w-full">
          {showSearch && (
            <div className="flex items-center border-b px-3 bg-muted/30" cmdk-input-wrapper="">
              <Search className="mr-2 h-4 w-4 shrink-0 text-primary opacity-70" />
              <CommandInput
                placeholder={searchPlaceholder}
                className="h-10 flex-1"
                value={searchQuery}
                onValueChange={setSearchQuery}
              />
            </div>
          )}
          <CommandList className="max-h-[350px]">
            <CommandEmpty className="py-6 text-center text-sm">
              {emptyMessage}
            </CommandEmpty>
            <CommandGroup className="overflow-visible">
              {filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => {
                    onChange(option.value)
                    setOpen(false)
                  }}
                  className={cn(
                    "flex flex-col items-start py-3 px-4 border-b last:border-0 transition-colors duration-150",
                    option.value === value
                      ? "bg-primary/10 hover:bg-primary/15 border-l-4 border-l-primary"
                      : option.is_free
                        ? "bg-green-50 hover:bg-green-100 border-l-4 border-l-green-500 dark:bg-green-900/20 dark:hover:bg-green-900/30 dark:border-l-green-400"
                        : highlightRecentSelections && recentSelections.includes(option.value)
                          ? "bg-primary/5 hover:bg-primary/10 border-l-2 border-l-primary/50"
                          : "hover:bg-accent"
                  )}
                >
                  <div className="flex w-full items-start justify-between">
                    <div className="flex flex-col items-start">
                      <div className="flex items-center gap-1">
                        <span className={cn(
                          "font-medium",
                          option.value === value
                            ? "text-primary font-semibold"
                            : highlightRecentSelections && recentSelections.includes(option.value) && recentSelections[0] !== option.value
                              ? "text-primary/80"
                              : "text-foreground"
                        )}>
                          {option.label}
                        </span>
                        {option.is_free && (
                          <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full font-medium dark:bg-green-900 dark:text-green-300">FREE</span>
                        )}
                        {highlightRecentSelections && recentSelections.includes(option.value) && recentSelections[0] !== option.value && (
                          <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">recent</span>
                        )}
                      </div>
                      {option.description && (
                        <span className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {option.description}
                        </span>
                      )}
                    </div>
                    {option.value === value && (
                      <Check className="h-4 w-4 text-primary flex-shrink-0 ml-2" />
                    )}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
