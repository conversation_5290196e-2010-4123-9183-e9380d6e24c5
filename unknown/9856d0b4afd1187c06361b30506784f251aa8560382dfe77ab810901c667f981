<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Template;
use App\Models\AIModel;
use Illuminate\Support\Facades\Log;
use Exception;

class TemplateProcessingService
{
    /**
     * Process a template by replacing variables with values
     *
     * @param Template $template Template to process
     * @param array $variables Variables to replace in the template
     * @return string Processed template content
     */
    public function processTemplate(Template $template, array $variables = []): string
    {
        $content = $template->content;

        try {
            // Replace variables in the format {{variable_name}}
            foreach ($variables as $key => $value) {
                // Ensure value is a string
                if (!is_string($value)) {
                    if (is_array($value) || is_object($value)) {
                        $value = json_encode($value);
                    } else {
                        $value = (string)$value;
                    }
                }

                $content = str_replace("{{" . $key . "}}", $value, $content);
            }

            // Check for any remaining variables that weren't replaced
            $unreplacedVariables = [];
            preg_match_all('/\{\{([a-zA-Z0-9_]+)\}\}/', $content, $matches);

            if (!empty($matches[1])) {
                $unreplacedVariables = $matches[1];
                Log::warning("Template processing: Found unreplaced variables", [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'variables' => $unreplacedVariables
                ]);
            }

            return $content;
        } catch (Exception $e) {
            Log::error("Error processing template: " . $e->getMessage(), [
                'template_id' => $template->id,
                'template_name' => $template->name,
                'error' => $e->getMessage()
            ]);

            // Return original content if there's an error
            return $content;
        }
    }

    /**
     * Extract template variables from the content
     *
     * @param Template $template The template
     * @return array List of variable names
     */
    public function extractTemplateVariables(Template $template): array
    {
        $variables = [];
        preg_match_all('/\{\{([a-zA-Z0-9_]+)\}\}/', $template->content, $matches);

        if (!empty($matches[1])) {
            $variables = $matches[1];
        }

        return $variables;
    }

    /**
     * Extract variables from user messages
     *
     * @param array $messages Array of messages
     * @return array Extracted variables
     */
    public function extractVariablesFromMessages(array $messages): array
    {
        $variables = [
            'user_query' => '', // Default variable for the user's query
            'timestamp' => date('Y-m-d H:i:s'),
            'date' => date('Y-m-d'),
            'time' => date('H:i:s'),
        ];

        // Get the last user message as the main query
        foreach (array_reverse($messages) as $message) {
            if (isset($message['role']) && $message['role'] === 'user' && isset($message['content'])) {
                $variables['user_query'] = $message['content'];
                break;
            }
        }

        // Extract any key-value pairs from structured messages or content
        foreach ($messages as $message) {
            if (isset($message['role']) && $message['role'] === 'user') {
                // Try to extract key-value pairs from the message content
                if (isset($message['content']) && is_string($message['content'])) {
                    // Look for patterns like "key: value" or "key=value"
                    if (preg_match_all('/([a-zA-Z0-9_]+)[\s]*[:=][\s]*([^\n,]+)/', $message['content'], $matches)) {
                        for ($i = 0; $i < count($matches[0]); $i++) {
                            $key = trim($matches[1][$i]);
                            $value = trim($matches[2][$i]);
                            $variables[$key] = $value;
                        }
                    }
                }
            }
        }

        return $variables;
    }

    /**
     * Get processed template for an AI model
     *
     * @param AIModel $model The AI model
     * @param array $messages Array of messages
     * @return string|null Processed template content or null if no template
     */
    public function getProcessedTemplateForModel(AIModel $model, array $messages): ?string
    {
        // Check if model has a template
        if (!$model->template_id) {
            return null;
        }

        try {
            $template = Template::find($model->template_id);

            if (!$template) {
                Log::warning("Template not found for model", [
                    'model_id' => $model->id,
                    'template_id' => $model->template_id
                ]);
                return null;
            }

            // Extract variables from messages
            $variables = $this->extractVariablesFromMessages($messages);

            // Add model-specific variables
            $variables['model_name'] = $model->name;
            $variables['provider'] = $model->provider;

            // Process template
            return $this->processTemplate($template, $variables);
        } catch (Exception $e) {
            Log::error("Error getting processed template for model: " . $e->getMessage(), [
                'model_id' => $model->id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }
}
