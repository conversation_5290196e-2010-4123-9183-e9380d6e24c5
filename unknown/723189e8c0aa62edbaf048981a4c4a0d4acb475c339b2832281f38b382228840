<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class WidgetWebhook extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'widget_id',
        'name',
        'url',
        'method',
        'headers',
        'on_chat_start',
        'on_chat_end',
        'on_message_sent',
        'on_message_received',
        'on_user_rating',
        'on_form_submission',
        'secret_key',
        'timeout_seconds',
        'retry_attempts',
        'verify_ssl',
        'is_active',
        'last_triggered_at',
        'success_count',
        'failure_count',
        'last_error',
        'integration_type',
        'integration_config',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'headers' => 'array',
        'on_chat_start' => 'boolean',
        'on_chat_end' => 'boolean',
        'on_message_sent' => 'boolean',
        'on_message_received' => 'boolean',
        'on_user_rating' => 'boolean',
        'on_form_submission' => 'boolean',
        'timeout_seconds' => 'integer',
        'retry_attempts' => 'integer',
        'verify_ssl' => 'boolean',
        'is_active' => 'boolean',
        'last_triggered_at' => 'datetime',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'integration_config' => 'array',
    ];

    /**
     * Boot function from Laravel.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate secret key when creating a new webhook
        static::creating(function ($webhook) {
            if (empty($webhook->secret_key)) {
                $webhook->secret_key = Str::random(32);
            }
        });
    }

    /**
     * Get the widget that owns this webhook.
     */
    public function widget()
    {
        return $this->belongsTo(Widget::class);
    }

    /**
     * Check if webhook should trigger for a specific event.
     */
    public function shouldTriggerFor(string $event): bool
    {
        if (!$this->is_active) {
            return false;
        }

        return match ($event) {
            'chat_start' => $this->on_chat_start,
            'chat_end' => $this->on_chat_end,
            'message_sent' => $this->on_message_sent,
            'message_received' => $this->on_message_received,
            'user_rating' => $this->on_user_rating,
            'form_submission' => $this->on_form_submission,
            default => false,
        };
    }

    /**
     * Record a successful webhook trigger.
     */
    public function recordSuccess(): void
    {
        $this->increment('success_count');
        $this->update([
            'last_triggered_at' => now(),
            'last_error' => null,
        ]);
    }

    /**
     * Record a failed webhook trigger.
     */
    public function recordFailure(string $error): void
    {
        $this->increment('failure_count');
        $this->update([
            'last_triggered_at' => now(),
            'last_error' => $error,
        ]);
    }

    /**
     * Get webhook configuration as array for API responses.
     */
    public function toConfigArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'url' => $this->url,
            'method' => $this->method,
            'headers' => $this->headers,
            'events' => [
                'chatStart' => $this->on_chat_start,
                'chatEnd' => $this->on_chat_end,
                'messageSent' => $this->on_message_sent,
                'messageReceived' => $this->on_message_received,
                'userRating' => $this->on_user_rating,
                'formSubmission' => $this->on_form_submission,
            ],
            'security' => [
                'timeoutSeconds' => $this->timeout_seconds,
                'retryAttempts' => $this->retry_attempts,
                'verifySSL' => $this->verify_ssl,
            ],
            'status' => [
                'isActive' => $this->is_active,
                'lastTriggered' => $this->last_triggered_at,
                'successCount' => $this->success_count,
                'failureCount' => $this->failure_count,
                'lastError' => $this->last_error,
            ],
            'integration' => [
                'type' => $this->integration_type,
                'config' => $this->integration_config,
            ],
        ];
    }
}
