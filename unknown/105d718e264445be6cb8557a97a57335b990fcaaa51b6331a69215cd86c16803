<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Lara<PERSON>\Sanctum\Sanctum;

class PermissionMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $adminRole;
    protected $viewUserPermission;
    protected $editUserPermission;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test permissions
        $this->viewUserPermission = Permission::create([
            'name' => 'user.view',
            'description' => 'Can view users',
            'category' => 'User Management'
        ]);

        $this->editUserPermission = Permission::create([
            'name' => 'user.edit',
            'description' => 'Can edit users',
            'category' => 'User Management'
        ]);

        // Create test role with view permission only
        $this->adminRole = Role::create([
            'name' => 'Admin',
            'description' => 'Administrator role'
        ]);

        // Assign view permission to admin role
        $this->adminRole->permissions()->attach($this->viewUserPermission->id);

        // Create test user with admin role
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);

        // Assign admin role to user
        $this->user->roles()->attach($this->adminRole->id);
    }

    /**
     * Test that a user with the required permission can access a protected route.
     */
    public function test_user_with_permission_can_access_route(): void
    {
        // Authenticate as the test user
        Sanctum::actingAs($this->user);

        // Define a test route with the permission middleware
        $response = $this->get('/api/test-permission-middleware');

        // Assert that the response is successful
        $response->assertStatus(200);
    }

    /**
     * Test that a user without the required permission cannot access a protected route.
     */
    public function test_user_without_permission_cannot_access_route(): void
    {
        // Authenticate as the test user
        Sanctum::actingAs($this->user);

        // Define a test route with a permission the user doesn't have
        $response = $this->get('/api/test-permission-middleware-denied');

        // Assert that the response is forbidden
        $response->assertStatus(403);
    }

    /**
     * Test that an unauthenticated user cannot access a protected route.
     */
    public function test_unauthenticated_user_cannot_access_route(): void
    {
        // Don't authenticate

        // Define a test route with the permission middleware
        $response = $this->get('/api/test-permission-middleware');

        // Assert that the response is unauthorized
        $response->assertStatus(401);
    }
}
