
import React, { useEffect, useMemo } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from "@/components/ui/form";
import { <PERSON>lider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { getModelOptions, ModelOption } from "./model-provider-options";
import { AlertCircle, Zap } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

interface ModelSettingsFieldsProps {
  availableModels?: Array<{
    value: string;
    label: string;
    description?: string;
  }>;
  onModelChange?: () => void;
}

export const ModelSettingsFields: React.FC<ModelSettingsFieldsProps> = ({
  availableModels = [],
  onModelChange
}) => {
  const form = useFormContext();
  const selectedProvider = useWatch({
    control: form.control,
    name: "provider",
  });

  const selectedModelName = useWatch({
    control: form.control,
    name: "settings.model_name",
  });

  // Use fetched models if available, otherwise fall back to static options
  const modelOptions = useMemo(() => {
    const models = availableModels.length > 0
      ? availableModels
      : getModelOptions(selectedProvider);

    // Sort models to put free ones at the top
    return [...models].sort((a, b) => {
      // If a is free and b is not, a comes first
      if (a.is_free && !b.is_free) return -1;
      // If b is free and a is not, b comes first
      if (!a.is_free && b.is_free) return 1;
      // Otherwise, maintain original order
      return 0;
    });
  }, [availableModels, selectedProvider]);

  // When model selection changes, update the model name in the form
  useEffect(() => {
    if (selectedModelName && availableModels.length > 0) {
      const selectedModel = availableModels.find(model => model.value === selectedModelName);
      if (selectedModel) {
        // Generate a name based on the provider and model
        const generatedName = `${selectedModel.label} (${selectedProvider})`;
        form.setValue("name", generatedName);
      }
    }
  }, [selectedModelName, availableModels, form, selectedProvider]);

  // Function to render temperature and max tokens fields
  const renderTemperatureAndTokensFields = () => {
    return (
      <>
        {/* Temperature slider */}
        <FormField
          control={form.control}
          name="settings.temperature"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Temperature: {field.value}</FormLabel>
              <div className="flex items-center gap-4">
                <FormControl>
                  <Slider
                    min={0}
                    max={2}
                    step={0.1}
                    value={[field.value]}
                    onValueChange={(values) => {
                      field.onChange(values[0]);
                      if (onModelChange) {
                        setTimeout(onModelChange, 100);
                      }
                    }}
                  />
                </FormControl>
                <Input
                  type="number"
                  min={0}
                  max={2}
                  step={0.1}
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(parseFloat(e.target.value));
                    if (onModelChange) {
                      setTimeout(onModelChange, 100);
                    }
                  }}
                  className="w-20"
                />
              </div>
              <FormDescription>
                Controls randomness: Lower values are more deterministic, higher values are more creative
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Max tokens slider */}
        <FormField
          control={form.control}
          name="settings.max_tokens"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Max Tokens: {field.value}</FormLabel>
              <div className="flex items-center gap-4">
                <FormControl>
                  <Slider
                    min={1}
                    max={32000}
                    step={1}
                    value={[field.value]}
                    onValueChange={(values) => {
                      field.onChange(values[0]);
                      if (onModelChange) {
                        setTimeout(onModelChange, 100);
                      }
                    }}
                  />
                </FormControl>
                <Input
                  type="number"
                  min={1}
                  max={32000}
                  step={1}
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(parseInt(e.target.value));
                    if (onModelChange) {
                      setTimeout(onModelChange, 100);
                    }
                  }}
                  className="w-24"
                />
              </div>
              <FormDescription>
                Maximum number of tokens to generate in the response
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    );
  };

  if (!selectedProvider) {
    return (
      <div className="text-center py-6 text-muted-foreground">
        Please select a provider first to configure model settings
      </div>
    );
  }

  if (availableModels.length === 0 && selectedProvider) {
    return (
      <div className="space-y-4">
        <Alert variant="warning">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No models were fetched from the provider. Using default options instead.
            Please ensure your API key is correct and try again.
          </AlertDescription>
        </Alert>

        {/* Model name selection with static options */}
        <FormField
          control={form.control}
          name="settings.model_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Model Name</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value);

                  // Update is_free based on the selected model
                  const selectedModel = modelOptions.find(model => model.value === value);
                  if (selectedModel) {
                    form.setValue("is_free", !!selectedModel.is_free);

                    // Call onModelChange if provided
                    if (onModelChange) {
                      setTimeout(onModelChange, 100);
                    }
                  }
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {modelOptions.map((model) => (
                    <SelectItem key={model.value} value={model.value} className={model.is_free ? "bg-green-50 dark:bg-green-900/20" : ""}>
                      <div className="flex items-center gap-2">
                        <span>{model.label}</span>
                        {model.is_free && (
                          <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200 text-xs font-medium dark:bg-green-900 dark:text-green-300">
                            FREE
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                The specific model to use from this provider
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Temperature and max tokens sliders */}
        {renderTemperatureAndTokensFields()}
      </div>
    );
  }
  return (
    <div className="space-y-6">
      {/* Model name selection with fetched models */}
      <FormField
        control={form.control}
        name="settings.model_name"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Model Name</FormLabel>
            <FormControl>
              <div className="relative">
                <Combobox
                  options={modelOptions.map(model => ({
                    value: model.value,
                    label: model.label,
                    description: model.description,
                    is_free: model.is_free
                  }))}
                  value={field.value}
                  onChange={(value) => {
                    field.onChange(value);

                    // When model selection changes, update the model name
                    const selectedModel = modelOptions.find(model => model.value === value);
                    if (selectedModel) {
                      // Generate a name based on the provider and model
                      const generatedName = `${selectedModel.label} (${selectedProvider})`;
                      form.setValue("name", generatedName);

                      // Update is_free based on the selected model
                      form.setValue("is_free", !!selectedModel.is_free);

                      // Call onModelChange if provided
                      if (onModelChange) {
                        setTimeout(onModelChange, 100); // Small delay to ensure form values are updated
                      }
                    }
                  }}
                  placeholder="Search and select a model..."
                  emptyMessage="No models found. Try a different search term."
                  className="w-full max-w-[450px]"
                  showSearch={true}
                  searchPlaceholder="Search models..."
                  highlightRecentSelections={true}
                />
                {availableModels.length > 0 && (
                  <div className="absolute right-[-30px] top-1/2 transform -translate-y-1/2 flex items-center">
                    <span className="inline-flex h-2 w-2 animate-pulse rounded-full bg-green-500 mr-2"></span>
                  </div>
                )}
              </div>
            </FormControl>
            <FormDescription>
              {availableModels.length > 0
                ? `${availableModels.length} models available from this provider`
                : "The specific model to use from this provider"}
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Temperature and max tokens sliders */}
      {renderTemperatureAndTokensFields()}
    </div>
  );
};
