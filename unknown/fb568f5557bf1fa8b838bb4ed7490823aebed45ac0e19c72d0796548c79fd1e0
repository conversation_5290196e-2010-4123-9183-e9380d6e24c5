import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  AlertCircle,
  Clock,
  RefreshCw,
  Copy,
  Download,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ApiTestResponse } from "@/utils/api-test-service";
import { Badge } from "@/components/ui/badge";

interface ResponseViewerProps {
  response?: ApiTestResponse;
  isLoading: boolean;
}

export function ResponseViewer({ response, isLoading }: ResponseViewerProps) {
  const [activeTab, setActiveTab] = useState("body");

  const getStatusColor = (status?: number) => {
    if (!status) return "bg-gray-500";
    if (status >= 200 && status < 300) return "bg-green-500";
    if (status >= 300 && status < 400) return "bg-blue-500";
    if (status >= 400 && status < 500) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getStatusText = (status?: number) => {
    if (!status) return "Unknown";
    if (status >= 200 && status < 300) return "Success";
    if (status >= 300 && status < 400) return "Redirection";
    if (status >= 400 && status < 500) return "Client Error";
    return "Server Error";
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could replace this with a proper toast notification
      alert("Copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy to clipboard:", err);
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      alert("Copied to clipboard!");
    }
  };

  const downloadResponse = () => {
    if (!response) return;

    const dataStr = JSON.stringify(response.data, null, 2);
    const dataUri =
      "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);

    const exportFileDefaultName = `api-response-${new Date().getTime()}.json`;

    const linkElement = document.createElement("a");
    linkElement.setAttribute("href", dataUri);
    linkElement.setAttribute("download", exportFileDefaultName);
    linkElement.click();
  };

  if (isLoading) {
    return (
      <div className="bg-card border rounded-md shadow-sm h-full flex items-center justify-center p-8">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-primary" />
          <p className="text-muted-foreground">Waiting for response...</p>
        </div>
      </div>
    );
  }

  if (!response) {
    return (
      <div className="bg-card border rounded-md shadow-sm h-full flex items-center justify-center p-8">
        <div className="text-center">
          <Clock className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">
            Execute a request to see the response
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card border rounded-md shadow-sm">
      <div className="p-4 border-b flex justify-between items-center">
        <h3 className="text-lg font-medium">Response</h3>
        <div className="flex items-center space-x-4">
          <Badge className={getStatusColor(response.status)}>
            {response.status} {getStatusText(response.status)}
          </Badge>
          <div className="text-sm text-muted-foreground">
            {response.duration.toFixed(0)}ms
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="p-2 border-b">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="body">Body</TabsTrigger>
            <TabsTrigger value="headers">Headers</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="body" className="p-4">
          <div className="relative">
            <div className="absolute top-2 right-2 z-10 flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  copyToClipboard(JSON.stringify(response.data, null, 2))
                }
              >
                <Copy className="h-4 w-4 mr-1" /> Copy
              </Button>
              <Button variant="outline" size="sm" onClick={downloadResponse}>
                <Download className="h-4 w-4 mr-1" /> Download
              </Button>
            </div>
            <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96 whitespace-pre-wrap mt-10">
              <code className="text-sm">
                {JSON.stringify(response.data, null, 2)}
              </code>
            </pre>
          </div>
        </TabsContent>

        <TabsContent value="headers" className="p-4">
          <div className="bg-muted p-4 rounded-md overflow-auto max-h-96">
            <table className="w-full text-sm">
              <thead className="text-left">
                <tr>
                  <th className="pb-2 font-medium">Header</th>
                  <th className="pb-2 font-medium">Value</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(response.headers).map(([key, value], idx) => (
                  <tr key={idx} className="border-t border-border">
                    <td className="py-2 pr-4 font-mono">{key}</td>
                    <td className="py-2 font-mono break-all">
                      {String(value)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
