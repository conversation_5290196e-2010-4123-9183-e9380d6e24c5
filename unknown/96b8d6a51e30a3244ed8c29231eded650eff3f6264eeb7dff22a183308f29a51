import { FileText, ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface NoDocumentsPlaceholderProps {
  onUploadClick?: () => void;
}

export default function NoDocumentsPlaceholder({
  onUploadClick,
}: NoDocumentsPlaceholderProps) {
  return (
    <div className="h-[400px] flex items-center justify-center flex-col">
      <FileText className="h-12 w-12 text-muted-foreground/30 mb-4" />
      <p className="text-muted-foreground font-medium">No documents found</p>
      <p className="text-xs text-muted-foreground mt-1 mb-4">
        Upload your first document to get started
      </p>
      {onUploadClick && (
        <Button
          onClick={onUploadClick}
          className="mt-2 flex items-center gap-2"
        >
          Upload Documents <ArrowRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
