/**
 * AI Chat Widget Web Component
 * A web component version of the chat widget that can be used with custom elements
 */

class AIChatWidget extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });

    // Widget state
    this.state = {
      isOpen: false,
      sessionId: null,
      messages: [],
      isTyping: false,
      unreadMessages: 0,
      preChatComplete: false,
      visitorInfo: { name: '', email: '', phone: '' },
    };

    // Configuration from attributes
    this.config = {
      widgetId: '',
      primaryColor: '#4f46e5',
      borderRadius: 8,
      position: 'bottom-right',
      headerTitle: 'AI Chat Assistant',
      initialMessage: 'Hello! How can I help you today?',
      inputPlaceholder: 'Type your message...',
      sendButtonText: 'Send',
      chatIconSize: 50,
      mobileBehavior: 'responsive',
      persistConversation: false,
      visitorId: this.generateVisitorId(),
      apiBaseUrl: 'http://localhost:9000',
    };
  }

  connectedCallback() {
    // Get attributes
    this.config.widgetId = this.getAttribute('widget-id') || this.config.widgetId;
    this.config.primaryColor = this.getAttribute('primary-color') || this.config.primaryColor;
    this.config.borderRadius = parseInt(this.getAttribute('border-radius') || this.config.borderRadius);
    this.config.position = this.getAttribute('position') || this.config.position;
    this.config.headerTitle = this.getAttribute('header-title') || this.config.headerTitle;
    this.config.initialMessage = this.getAttribute('initial-message') || this.config.initialMessage;
    this.config.inputPlaceholder = this.getAttribute('input-placeholder') || this.config.inputPlaceholder;
    this.config.sendButtonText = this.getAttribute('send-button-text') || this.config.sendButtonText;
    this.config.chatIconSize = parseInt(this.getAttribute('chat-icon-size') || this.config.chatIconSize);
    this.config.persistConversation = this.getAttribute('persist-conversation') === 'true';
    const attributeBaseUrl = this.getAttribute('api-base-url');
    const scriptBaseUrl = document.currentScript ? new URL(document.currentScript.src).origin : this._findScriptBaseUrl();

    // Default to API URL if script URL is on port 9090
    let defaultBaseUrl = scriptBaseUrl;
    if (scriptBaseUrl.includes('localhost:9090')) {
      console.log('Detected frontend URL, switching to API URL');
      defaultBaseUrl = 'http://localhost:9000';
    }

    this.config.apiBaseUrl = attributeBaseUrl || defaultBaseUrl;
    console.log('Using API base URL:', this.config.apiBaseUrl,
      'Attribute URL:', attributeBaseUrl,
      'Script URL:', scriptBaseUrl,
      'Default URL:', defaultBaseUrl);

    // Validate required attributes
    if (!this.config.widgetId) {
      console.error('AI Chat Widget: Missing required widget-id attribute');
      return;
    }

    // Initialize widget
    this.initWidget();

    // Track widget view for analytics
    this.trackWidgetView();
  }

  generateVisitorId() {
    // Check if visitor ID already exists in localStorage
    const existingId = localStorage.getItem('ai_chat_visitor_id');
    if (existingId) {
      return existingId;
    }

    // Generate a new visitor ID
    const visitorId = 'visitor_' + Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);

    // Store in localStorage for future use
    localStorage.setItem('ai_chat_visitor_id', visitorId);

    return visitorId;
  }

  /**
   * Sanitize HTML content to prevent XSS attacks
   * @param {string} html HTML content to sanitize
   * @returns {string} Sanitized HTML
   */
  sanitizeHtml(html) {
    if (!html) return '';

    // Create a temporary element
    const tempElement = document.createElement('div');
    tempElement.textContent = html;
    return tempElement.innerHTML;
  }

  /**
   * Sanitize user input for chat messages
   * @param {string} message Message to sanitize
   * @returns {string} Sanitized message
   */
  sanitizeChatMessage(message) {
    if (!message) return '';

    // Remove any HTML tags
    const sanitized = message.replace(/<[^>]*>?/gm, '');

    // Trim whitespace
    return sanitized.trim();
  }

  initWidget() {
    // Create styles
    const styles = document.createElement('style');
    styles.textContent = this.getStyles();
    this.shadowRoot.appendChild(styles);

    // Create widget container
    const container = document.createElement('div');
    container.className = 'ai-chat-widget-container';

    // Set position based on config
    const positionStyle = this.positionWidget(this.config.position);
    Object.keys(positionStyle).forEach(key => {
      container.style[key] = positionStyle[key];
    });

    // Create widget button
    const button = this.createWidgetButton();
    container.appendChild(button);

    // Create pre-chat form
    this.preChatForm = this.createPreChatForm();
    container.appendChild(this.preChatForm);

    // Create post-chat survey
    this.postChatSurvey = this.createPostChatSurvey();
    container.appendChild(this.postChatSurvey);

    // Create chat window
    this.chatContainer = this.createChatWindow();
    container.appendChild(this.chatContainer);

    // Add to shadow DOM
    this.shadowRoot.appendChild(container);
    this.widgetContainer = container;

    // Hide chat window, pre-chat form and post-chat survey initially
    this.chatContainer.style.display = 'none';
    this.preChatForm.style.display = 'none';
    this.postChatSurvey.style.display = 'none';
  }

  getStyles() {
    return `
      .ai-chat-widget-container * {
        box-sizing: border-box;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      }

      .ai-chat-widget-container {
        position: fixed;
        z-index: 9999;
        margin: 0;
        padding: 0;
      }

      .ai-chat-widget-button {
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: transform 0.2s ease;
      }

      .ai-chat-widget-button:hover {
        transform: scale(1.05);
      }

      .ai-chat-widget-button svg {
        fill: none;
        stroke: white;
        stroke-width: 2;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      .ai-chat-widget-chat {
        display: flex;
        flex-direction: column;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        background-color: white;
        max-height: 500px;
        width: 350px;
        height: 100%;
        transition: opacity 0.3s ease, transform 0.3s ease;
        overflow: hidden;
      }

      @media (max-width: 480px) {
        .ai-chat-widget-chat {
          width: calc(100vw - 20px);
          max-height: calc(100vh - 80px);
        }
      }

      .ai-chat-header {
        padding: 12px 16px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .ai-chat-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }

      .ai-chat-header-button {
        background: none;
        border: none;
        cursor: pointer;
        color: white;
        padding: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s ease;
      }

      .ai-chat-header-button:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .ai-chat-messages {
        flex-grow: 1;
        overflow-y: auto;
        padding: 16px;
        background-color: #f9fafb;
        display: flex;
        flex-direction: column;
      }

      .ai-chat-message {
        margin-bottom: 12px;
        max-width: 80%;
        word-wrap: break-word;
      }

      .ai-chat-message-content {
        padding: 10px 12px;
        border-radius: 8px;
      }

      .ai-chat-message.user {
        align-self: flex-end;
      }

      .ai-chat-message.user .ai-chat-message-content {
        background-color: #e2e8f0;
        border-bottom-right-radius: 0;
      }

      .ai-chat-message.assistant .ai-chat-message-content {
        border-bottom-left-radius: 0;
      }

      .ai-chat-typing {
        display: flex;
        padding: 8px;
      }

      .ai-chat-typing span {
        width: 8px;
        height: 8px;
        margin: 0 2px;
        background-color: #a0aec0;
        border-radius: 50%;
        animation: typingAnimation 1s infinite ease-in-out;
      }

      .ai-chat-typing span:nth-child(2) {
        animation-delay: 0.2s;
      }

      .ai-chat-typing span:nth-child(3) {
        animation-delay: 0.4s;
      }

      @keyframes typingAnimation {
        0%, 100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-4px);
        }
      }

      .ai-chat-input-container {
        padding: 12px;
        border-top: 1px solid #e2e8f0;
        background-color: white;
      }

      .ai-chat-input-form {
        display: flex;
      }

      .ai-chat-input {
        flex-grow: 1;
        padding: 8px 12px;
        border: 1px solid #e2e8f0;
        border-right: none;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        outline: none;
      }

      .ai-chat-input:focus {
        border-color: #a0aec0;
      }

      .ai-chat-send-button {
        padding: 8px 16px;
        border: none;
        color: white;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        cursor: pointer;
      }

      .ai-chat-unread {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #ef4444;
        color: white;
        font-size: 12px;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
      }

      .ai-chat-postchat-form {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.95);
        z-index: 100001;
        display: none;
      }
    `;
  }

  positionWidget(position) {
    const positions = {
      'bottom-right': { bottom: '20px', right: '20px' },
      'bottom-left': { bottom: '20px', left: '20px' },
      'top-right': { top: '20px', right: '20px' },
      'top-left': { top: '20px', left: '20px' }
    };

    return positions[position] || positions['bottom-right'];
  }

  createWidgetButton() {
    const button = document.createElement('div');
    button.className = 'ai-chat-widget-button';
    button.style.width = `${this.config.chatIconSize}px`;
    button.style.height = `${this.config.chatIconSize}px`;
    button.style.backgroundColor = this.config.primaryColor;

    button.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
    `;

    button.addEventListener('click', () => this.toggleChat());
    return button;
  }

  createChatWindow() {
    const chat = document.createElement('div');
    chat.className = 'ai-chat-widget-chat';
    chat.style.borderRadius = `${this.config.borderRadius}px`;
    chat.style.display = 'none';

    // Header
    const header = document.createElement('div');
    header.className = 'ai-chat-header';
    header.style.backgroundColor = this.config.primaryColor;
    header.innerHTML = `
      <h3>${this.config.headerTitle}</h3>
      <button class="ai-chat-header-button">
        <svg width="20" height="20" viewBox="0 0 24 24">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    `;
    header.querySelector('button').addEventListener('click', () => this.toggleChat());

    // Messages container
    const messagesContainer = document.createElement('div');
    messagesContainer.className = 'ai-chat-messages';
    this.messagesList = messagesContainer;

    // Input area
    const inputContainer = document.createElement('div');
    inputContainer.className = 'ai-chat-input-container';

    const inputForm = document.createElement('form');
    inputForm.className = 'ai-chat-input-form';

    this.inputField = document.createElement('input');
    this.inputField.type = 'text';
    this.inputField.className = 'ai-chat-input';
    this.inputField.placeholder = this.config.inputPlaceholder;

    const sendButton = document.createElement('button');
    sendButton.type = 'submit';
    sendButton.className = 'ai-chat-send-button';
    sendButton.textContent = this.config.sendButtonText;
    sendButton.style.backgroundColor = this.config.primaryColor;

    inputForm.appendChild(this.inputField);
    inputForm.appendChild(sendButton);
    inputContainer.appendChild(inputForm);

    inputForm.addEventListener('submit', (e) => this.handleSubmitMessage(e));

    // Combine all elements
    chat.appendChild(header);
    chat.appendChild(messagesContainer);
    chat.appendChild(inputContainer);

    // Add end chat button
    const endChatBtn = document.createElement('button');
    endChatBtn.className = 'ai-chat-end-button';
    endChatBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="9" y1="9" x2="15" y2="15"></line>
        <line x1="15" y1="9" x2="9" y2="15"></line>
      </svg>
      <span>End Chat</span>
    `;
    endChatBtn.addEventListener('click', () => this.endChat());

    // Add end chat button to the header or footer as appropriate
    const chatHeader = chat.querySelector('.ai-chat-header');
    chatHeader.appendChild(endChatBtn);

    return chat;
  }

  createPreChatForm() {
    const form = document.createElement('form');
    form.className = 'ai-chat-prechat-form';
    form.innerHTML = `
      <div style="display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:32px 16px;box-sizing:border-box;">
        <div style="width:100%;max-width:320px;background:#fff;border-radius:12px;box-shadow:0 4px 16px rgba(0,0,0,0.08);padding:24px 20px 20px 20px;display:flex;flex-direction:column;align-items:stretch;">
          <h2 style="margin:0 0 8px 0;font-size:1.25rem;font-weight:600;color:${this.config.primaryColor};text-align:center;">Start Chat</h2>
          <p style="margin:0 0 20px 0;font-size:0.95rem;color:#6b7280;text-align:center;">Please fill in your details to begin chatting with us.</p>
          <div class="ai-chat-prechat-error" style="display:none;color:#ef4444;font-size:0.95rem;margin-bottom:10px;text-align:center;"></div>
          <label for="prechat-name" style="font-size:0.95rem;color:#374151;margin-bottom:4px;">Name</label>
          <input id="prechat-name" name="name" placeholder="Your name" required aria-required="true" style="margin-bottom:12px;padding:10px 12px;border-radius:6px;border:1px solid #e5e7eb;font-size:1rem;outline:none;transition:border-color 0.2s;" />
          <label for="prechat-email" style="font-size:0.95rem;color:#374151;margin-bottom:4px;">Email</label>
          <input id="prechat-email" name="email" type="email" placeholder="<EMAIL>" required aria-required="true" style="margin-bottom:12px;padding:10px 12px;border-radius:6px;border:1px solid #e5e7eb;font-size:1rem;outline:none;transition:border-color 0.2s;" />
          <label for="prechat-phone" style="font-size:0.95rem;color:#374151;margin-bottom:4px;">Phone</label>
          <input id="prechat-phone" name="phone" placeholder="Phone number" required aria-required="true" inputmode="numeric" pattern="[0-9]*" style="margin-bottom:18px;padding:10px 12px;border-radius:6px;border:1px solid #e5e7eb;font-size:1rem;outline:none;transition:border-color 0.2s;" />
          <button type="submit" aria-label="Start Chat" class="ai-chat-submit-btn" style="position:relative;padding:10px 0;background:${this.config.primaryColor};color:white;border:none;border-radius:6px;font-size:1rem;font-weight:600;cursor:pointer;transition:background 0.2s;">
            <span>Start Chat</span>
            <div class="ai-chat-loading-spinner" style="display:none;position:absolute;right:15px;top:50%;transform:translateY(-50%);width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:white;animation:ai-chat-spin 1s linear infinite;"></div>
          </button>
          <style>
            @keyframes ai-chat-spin {
              to { transform: translateY(-50%) rotate(360deg); }
            }
          </style>
        </div>
      </div>
    `;
    // Prevent non-numeric input for phone
    const phoneInput = form.querySelector('input[name="phone"]');
    phoneInput.addEventListener('input', function () {
      this.value = this.value.replace(/\D/g, '');
    });
    form.addEventListener('submit', (e) => this.handlePreChatSubmit(e));
    // Add focus/active styles
    Array.from(form.querySelectorAll('input')).forEach(input => {
      input.addEventListener('focus', function () {
        this.style.borderColor = form.closest('form').__primaryColor || '#4f46e5';
      });
      input.addEventListener('blur', function () {
        this.style.borderColor = '#e5e7eb';
      });
    });
    form.__primaryColor = this.config.primaryColor;
    return form;
  }

  /**
   * Handle pre-chat form submission
   * @param {Event} e Submit event
   */
  async handlePreChatSubmit(e) {
    e.preventDefault();
    const form = e.target;

    // Get and sanitize form values
    const rawName = form.name.value.trim();
    const rawEmail = form.email.value.trim();
    const rawPhone = form.phone.value.trim();

    // Sanitize inputs
    const name = this.sanitizeHtml(rawName);
    const email = this.sanitizeHtml(rawEmail);
    const phone = this.sanitizeHtml(rawPhone);

    const errorDiv = form.querySelector('.ai-chat-prechat-error');
    const submitBtn = form.querySelector('.ai-chat-submit-btn');
    const loadingSpinner = form.querySelector('.ai-chat-loading-spinner');

    // Reset previous errors
    errorDiv.textContent = '';
    errorDiv.style.display = 'none';
    form.name.style.borderColor = '#e5e7eb';
    form.email.style.borderColor = '#e5e7eb';
    form.phone.style.borderColor = '#e5e7eb';

    let hasError = false;

    // Validate required fields
    if (!name) {
      form.name.style.borderColor = '#ef4444';
      hasError = true;
    }

    // Email validation with regex
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!email) {
      form.email.style.borderColor = '#ef4444';
      hasError = true;
    } else if (!emailRegex.test(email)) {
      form.email.style.borderColor = '#ef4444';
      errorDiv.textContent = 'Please enter a valid email address.';
      errorDiv.style.display = 'block';
      hasError = true;
    }

    // Phone validation
    if (!phone) {
      form.phone.style.borderColor = '#ef4444';
      hasError = true;
    } else if (!/^\d+$/.test(phone)) {
      form.phone.style.borderColor = '#ef4444';
      errorDiv.textContent = 'Phone number must contain only digits.';
      errorDiv.style.display = 'block';
      hasError = true;
    }

    if (hasError) {
      if (!errorDiv.textContent) {
        errorDiv.textContent = 'All fields are required.';
        errorDiv.style.display = 'block';
      }
      return;
    }

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.style.opacity = '0.7';
    loadingSpinner.style.display = 'block';

    // Call API to save visitor info
    const baseUrl = this.config.apiBaseUrl;
    try {
      const response = await fetch(`${baseUrl}/api/guest/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include',
        body: JSON.stringify({
          fullname: name,
          email,
          phone,
          widget_id: this.config.widgetId,
          metadata: {
            url: window.location.href,
            referrer: document.referrer || null,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          }
        })
      });

      // Hide loading state
      submitBtn.disabled = false;
      submitBtn.style.opacity = '1';
      loadingSpinner.style.display = 'none';

      if (!response.ok) {
        const errorData = await response.json();
        errorDiv.textContent = errorData.message || 'Failed to register. Please try again.';
        errorDiv.style.display = 'block';
        return;
      }

      const data = await response.json();
      this.state.sessionId = data.session_id;
      this.state.preChatComplete = true;
      this.state.visitorInfo = { fullname: name, email, phone };
      this.preChatForm.style.display = 'none';
      this.showChatWindow();
      // No need to call initChatSession if session is already created
    } catch (err) {
      // Hide loading state
      submitBtn.disabled = false;
      submitBtn.style.opacity = '1';
      loadingSpinner.style.display = 'none';

      // Only log in development
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.error('Pre-chat form submission error:', err);
      }

      errorDiv.textContent = 'Failed to register. Please try again.';
      errorDiv.style.display = 'block';
    }
  }

  showChatWindow() {
    this.chatContainer.style.display = 'flex';
    this.widgetContainer.querySelector('.ai-chat-widget-button').style.display = 'none';
    this.state.isOpen = true;
    // Focus input field
    setTimeout(() => {
      this.inputField.focus();
    }, 100);
    // If sessionId is not set, initialize session
    if (!this.state.sessionId) {
      this.initChatSession();
    }
  }

  toggleChat() {
    if (!this.widgetContainer) return;
    if (this.state.isOpen) {
      // Close chat
      this.chatContainer.style.display = 'none';
      this.widgetContainer.querySelector('.ai-chat-widget-button').style.display = 'flex';
      this.state.isOpen = false;
    } else {
      // Open chat
      if (!this.state.preChatComplete) {
        this.preChatForm.style.display = 'flex';
        this.preChatForm.style.height = '100%';
        this.chatContainer.style.display = 'none';
        this.widgetContainer.querySelector('.ai-chat-widget-button').style.display = 'none';
        return;
      }
      this.showChatWindow();
      // Reset unread counter
      this.state.unreadMessages = 0;
      const unreadBadge = this.widgetContainer.querySelector('.ai-chat-unread');
      if (unreadBadge) unreadBadge.remove();
    }
  }

  /**
   * Display a message in the chat window
   * @param {string} message Message content
   * @param {string} role Message role (user or assistant)
   */
  displayMessage(message, role) {
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `ai-chat-message ${role}`;

    // Create message content container
    const messageContent = document.createElement('div');
    messageContent.className = 'ai-chat-message-content';

    // Set background color for assistant messages
    if (role === 'assistant') {
      messageContent.style.backgroundColor = `${this.config.primaryColor}20`;
    }

    // Sanitize and set message content
    messageContent.textContent = this.sanitizeHtml(message);

    // Append to DOM
    messageElement.appendChild(messageContent);
    this.messagesList.appendChild(messageElement);

    // Scroll to bottom
    this.messagesList.scrollTop = this.messagesList.scrollHeight;

    // If chat is closed, increment unread counter
    if (!this.state.isOpen && role === 'assistant') {
      this.state.unreadMessages++;
      this.updateUnreadCounter();
    }
  }

  updateUnreadCounter() {
    // Remove existing badge if any
    const existingBadge = this.widgetContainer.querySelector('.ai-chat-unread');
    if (existingBadge) existingBadge.remove();

    // Add new badge if needed
    if (this.state.unreadMessages > 0) {
      const badge = document.createElement('div');
      badge.className = 'ai-chat-unread';
      badge.textContent = this.state.unreadMessages > 9 ? '9+' : this.state.unreadMessages;

      const button = this.widgetContainer.querySelector('.ai-chat-widget-button');
      button.style.position = 'relative';
      button.appendChild(badge);
    }
  }

  showTypingIndicator() {
    const typingElement = document.createElement('div');
    typingElement.className = 'ai-chat-message assistant ai-chat-typing';
    typingElement.innerHTML = `
      <div class="ai-chat-message-content">
        <span></span>
        <span></span>
        <span></span>
      </div>
    `;
    this.messagesList.appendChild(typingElement);
    this.messagesList.scrollTop = this.messagesList.scrollHeight;
    return typingElement;
  }

  removeTypingIndicator(element) {
    if (element && element.parentNode === this.messagesList) {
      this.messagesList.removeChild(element);
    }
  }

  /**
   * Handle message submission from the user
   * @param {Event} e Submit event
   */
  async handleSubmitMessage(e) {
    e.preventDefault();

    // Get and sanitize the message
    const rawMessage = this.inputField.value.trim();
    if (!rawMessage) return;

    // Sanitize the message to prevent XSS attacks
    const message = this.sanitizeChatMessage(rawMessage);

    // Clear input
    this.inputField.value = '';

    // Display user message
    this.displayMessage(message, 'user');

    // Add to state
    this.state.messages.push({
      role: 'user',
      content: message
    });

    // Show typing indicator
    this.state.isTyping = true;
    const typingIndicator = this.showTypingIndicator();

    // Send to backend
    try {
      const response = await this.sendMessage(message);

      // Remove typing indicator
      this.state.isTyping = false;
      this.removeTypingIndicator(typingIndicator);

      // Sanitize the response message
      const sanitizedResponse = this.sanitizeHtml(response.message || '');

      // Display response
      this.displayMessage(sanitizedResponse, 'assistant');

      // Add to state
      this.state.messages.push({
        role: 'assistant',
        content: sanitizedResponse
      });

      // Persist the conversation if enabled
      if (this.config.persistConversation) {
        this.persistSession();
      }
    } catch (error) {
      // Remove typing indicator
      this.state.isTyping = false;
      this.removeTypingIndicator(typingIndicator);

      // Display error message
      this.displayMessage('Sorry, there was a problem processing your request. Please try again.', 'assistant');

      // Only log in development
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.error('Chat widget error:', error);
      }
    }
  }

  async initChatSession() {
    // Check for persisted session if persistConversation is enabled
    if (this.config.persistConversation) {
      const persistedSession = localStorage.getItem(`ai-chat-session-${this.config.widgetId}`);
      if (persistedSession) {
        try {
          const sessionData = JSON.parse(persistedSession);
          this.state.sessionId = sessionData.sessionId;
          console.log('Restored persisted session:', this.state.sessionId);

          // Load persisted messages if available
          if (sessionData.messages && sessionData.messages.length > 0) {
            this.messagesList.innerHTML = '';
            this.state.messages = [];

            sessionData.messages.forEach(msg => {
              this.displayMessage(msg.content, msg.role);
              this.state.messages.push({
                role: msg.role,
                content: msg.content
              });
            });

            console.log('Restored persisted messages:', sessionData.messages.length);
            return; // Skip the rest of initialization if we restored a session
          }
        } catch (e) {
          console.error('Error restoring persisted session:', e);
          // Clear invalid data
          localStorage.removeItem(`ai-chat-session-${this.config.widgetId}`);
        }
      }
    }

    if (this.state.sessionId) return; // Don't re-initialize if already set
    try {
      const baseUrl = this.config.apiBaseUrl;
      const response = await fetch(`${baseUrl}/api/chat/session/init`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include',
        body: JSON.stringify({
          widget_id: this.config.widgetId,
          session_id: this.state.sessionId,
          fullname: this.state.visitorInfo.fullname,
          email: this.state.visitorInfo.email,
          phone: this.state.visitorInfo.phone,
          metadata: {
            url: window.location.href,
            referrer: document.referrer,
            userAgent: navigator.userAgent,
            language: navigator.language,
          }
        })
      });
      if (!response.ok) {
        throw new Error(`Failed to initialize chat session: ${response.statusText}`);
      }
      const data = await response.json();
      this.state.sessionId = data.session_id;

      // Persist the session if enabled
      if (this.config.persistConversation) {
        this.persistSession();
      }

      // Add initial message
      if (this.config.initialMessage) {
        this.displayMessage(this.config.initialMessage, 'assistant');
        this.state.messages.push({
          role: 'assistant',
          content: this.config.initialMessage
        });

        // Update persisted messages
        if (this.config.persistConversation) {
          this.persistSession();
        }
      }

      // Get chat history
      await this.loadChatHistory();
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      this.displayMessage('Failed to initialize chat. Please try again later.', 'assistant');
    }
  }

  // Helper method to persist the session and messages to localStorage
  persistSession() {
    if (!this.config.persistConversation) return;

    try {
      const sessionData = {
        sessionId: this.state.sessionId,
        messages: this.state.messages,
        timestamp: new Date().toISOString()
      };

      localStorage.setItem(`ai-chat-session-${this.config.widgetId}`, JSON.stringify(sessionData));
      console.log('Session persisted successfully');
    } catch (e) {
      console.error('Error persisting session:', e);
    }
  }

  async loadChatHistory() {
    if (!this.state.sessionId) return;
    try {
      const baseUrl = this.config.apiBaseUrl;
      const response = await fetch(`${baseUrl}/api/chat/history?session_id=${this.state.sessionId}`, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch chat history: ${response.statusText}`);
      }
      const messages = await response.json();
      // Clear current messages
      this.messagesList.innerHTML = '';
      this.state.messages = [];
      // Display messages
      messages.forEach(msg => {
        this.displayMessage(msg.content, msg.role);
        this.state.messages.push({
          role: msg.role,
          content: msg.content
        });
      });

      // Persist the loaded messages if enabled
      if (this.config.persistConversation) {
        this.persistSession();
      }
    } catch (error) {
      console.error('Failed to load chat history:', error);
    }
  }

  /**
   * Send a message to the backend API
   * @param {string} message Message to send
   * @returns {Promise<Object>} Response from the API
   */
  async sendMessage(message) {
    // Validate session
    if (!this.state.sessionId) {
      throw new Error('Chat session not initialized');
    }

    // Sanitize message
    const sanitizedMessage = this.sanitizeChatMessage(message);

    // Prepare API request
    const baseUrl = this.config.apiBaseUrl;
    const response = await fetch(`${baseUrl}/api/chat/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'include',
      body: JSON.stringify({
        session_id: this.state.sessionId,
        message: sanitizedMessage,
        metadata: {
          url: window.location.href,
          timestamp: new Date().toISOString(),
          referrer: document.referrer || null
        }
      })
    });

    // Handle errors
    if (!response.ok) {
      throw new Error(`Failed to send message: ${response.statusText}`);
    }

    // Return response data
    return response.json();
  }

  /**
   * Track widget view for analytics
   * Uses a reliable tracking method with fallbacks
   */
  trackWidgetView() {
    const baseUrl = this.config.apiBaseUrl;

    // Don't log in production
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('Using API base URL:', baseUrl);
      console.log('Using widget ID:', this.config.widgetId);
    }

    // Validate required data
    if (!this.config.widgetId) {
      console.warn('Widget ID is required for tracking');
      return;
    }

    try {
      // Prepare tracking data
      const data = {
        widget_id: this.config.widgetId,
        event_type: 'view',
        visitor_id: this.config.visitorId,
        url: window.location.href,
        timestamp: new Date().toISOString()
      };

      // Use fetch with keepalive to ensure the request completes
      // even if the page is unloaded
      fetch(`${baseUrl}/api/widget/analytics/view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data),
        keepalive: true,
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            return response.json()
              .then(errorData => {
                // Only log in development
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                  console.error('Error response:', errorData);
                }

                // Check for widget ID validation error
                if (errorData.errors && errorData.errors.widget_id) {
                  throw new Error(`Widget ID validation failed: ${errorData.errors.widget_id[0]}`);
                }

                throw new Error(`Analytics tracking failed: ${response.status} ${response.statusText}`);
              })
              .catch(jsonError => {
                // If JSON parsing fails, throw the original error
                throw new Error(`Analytics tracking failed: ${response.status} ${response.statusText}`);
              });
          }

          // Only log in development
          if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('Analytics tracking successful');
          }

          return response.json();
        })
        .catch(err => {
          // Log errors but don't break the widget
          // Only log in development
          if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.warn('Analytics tracking error:', err);
          }

          // Check if it's a widget ID validation error
          if (err.message.includes('widget_id') && err.message.includes('invalid')) {
            // Only log in development
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
              console.error('Invalid widget ID. Please check that the widget ID exists in your database.');
              console.error(`%cWidget ID Error: "${this.config.widgetId}" is not valid.`, 'color: red; font-weight: bold;');
              console.error('%cPlease use a valid widget ID from your database.', 'color: red;');
            }
            return;
          }

          // Only try image beacon fallback if it's a network error, not a server error
          if (err.message.includes('Failed to fetch')) {
            try {
              const img = new Image();
              const params = new URLSearchParams({
                widget_id: this.config.widgetId,
                event_type: 'view',
                visitor_id: this.config.visitorId,
                url: window.location.href,
                timestamp: new Date().toISOString()
              }).toString();

              img.src = `${baseUrl}/api/widget/analytics/view?${params}`;
              img.style.display = 'none';
              document.body.appendChild(img);

              // Remove the image after a timeout
              setTimeout(() => {
                if (img.parentNode) {
                  img.parentNode.removeChild(img);
                }
              }, 10000);
            } catch (beaconError) {
              // Silently fail - we've tried our best
              if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.warn('Failed to send beacon for widget view:', beaconError);
              }
            }
          }
        });
    } catch (err) {
      // Fail silently to not disrupt user experience
      // Only log in development
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.warn('Failed to track widget view:', err);
      }
    }
  }

  _findScriptBaseUrl() {
    // Fallback: find the script tag by src
    const scripts = document.querySelectorAll('script[src]');
    for (const s of scripts) {
      if (s.src.includes('/widget/v1/web-component.js')) {
        console.log('Found script URL:', s.src);
        const url = new URL(s.src);
        console.log('Script origin:', url.origin);
        return url.origin;
      }
    }
    console.warn('Could not find script tag, using window.location.origin as fallback');
    return window.location.origin;
  }

  createPostChatSurvey() {
    const form = document.createElement('form');
    form.className = 'ai-chat-postchat-form';
    form.innerHTML = `
      <div style="display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:32px 16px;box-sizing:border-box;">
        <div style="width:100%;max-width:320px;background:#fff;border-radius:12px;box-shadow:0 4px 16px rgba(0,0,0,0.08);padding:24px 20px 20px 20px;display:flex;flex-direction:column;align-items:stretch;">
          <h2 style="margin:0 0 8px 0;font-size:1.25rem;font-weight:600;color:${this.config.primaryColor};text-align:center;">Rate Your Experience</h2>
          <p style="margin:0 0 20px 0;font-size:0.95rem;color:#6b7280;text-align:center;">Thank you for chatting with us. Please rate your experience.</p>
          <div class="ai-chat-postchat-error" style="display:none;color:#ef4444;font-size:0.95rem;margin-bottom:10px;text-align:center;"></div>

          <div style="display:flex;justify-content:center;margin-bottom:20px;">
            <div class="rating-container" style="display:flex;gap:10px;">
              <button type="button" class="rating-btn" data-rating="1" style="background:none;border:1px solid #e5e7eb;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;color:#6b7280;">1</button>
              <button type="button" class="rating-btn" data-rating="2" style="background:none;border:1px solid #e5e7eb;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;color:#6b7280;">2</button>
              <button type="button" class="rating-btn" data-rating="3" style="background:none;border:1px solid #e5e7eb;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;color:#6b7280;">3</button>
              <button type="button" class="rating-btn" data-rating="4" style="background:none;border:1px solid #e5e7eb;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;color:#6b7280;">4</button>
              <button type="button" class="rating-btn" data-rating="5" style="background:none;border:1px solid #e5e7eb;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;color:#6b7280;">5</button>
            </div>
          </div>

          <label style="font-size:0.95rem;color:#374151;margin-bottom:4px;">Feedback (optional)</label>
          <textarea name="feedback" placeholder="Please share your thoughts..." style="margin-bottom:18px;padding:10px 12px;border-radius:6px;border:1px solid #e5e7eb;font-size:1rem;outline:none;transition:border-color 0.2s;min-height:80px;resize:vertical;"></textarea>

          <button type="submit" style="padding:10px 0;background-color:${this.config.primaryColor};color:white;border:none;border-radius:6px;font-size:1rem;font-weight:500;cursor:pointer;transition:opacity 0.2s;">Submit Feedback</button>
          <button type="button" class="skip-feedback-btn" style="padding:10px 0;background:none;border:none;color:#6b7280;font-size:0.9rem;margin-top:8px;cursor:pointer;">Skip</button>
        </div>
      </div>
    `;

    // Set up rating buttons
    const ratingBtns = form.querySelectorAll('.rating-btn');
    let selectedRating = 0;

    ratingBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        // Reset all buttons
        ratingBtns.forEach(b => {
          b.style.backgroundColor = '';
          b.style.borderColor = '#e5e7eb';
          b.style.color = '#6b7280';
        });

        // Highlight selected button
        const rating = parseInt(btn.getAttribute('data-rating'));
        selectedRating = rating;
        btn.style.backgroundColor = this.config.primaryColor;
        btn.style.borderColor = this.config.primaryColor;
        btn.style.color = 'white';
      });
    });

    // Handle form submission
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const errorDiv = form.querySelector('.ai-chat-postchat-error');

      if (selectedRating === 0) {
        errorDiv.textContent = 'Please select a rating.';
        errorDiv.style.display = 'block';
        return;
      }

      const feedback = form.querySelector('textarea[name="feedback"]').value.trim();

      try {
        const baseUrl = this.config.apiBaseUrl;
        const response = await fetch(`${baseUrl}/api/chat/feedback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          },
          credentials: 'include',
          body: JSON.stringify({
            session_id: this.state.sessionId,
            widget_id: this.config.widgetId,
            rating: selectedRating,
            feedback: feedback || null,
          })
        });

        if (!response.ok) {
          throw new Error('Failed to submit feedback');
        }

        // Hide survey and show thank you message
        this.closeWidget();

        // Show a brief thank you toast message
        this.showToast('Thank you for your feedback!');
      } catch (err) {
        console.error('Error submitting feedback:', err);
        errorDiv.textContent = 'Failed to submit feedback. Please try again.';
        errorDiv.style.display = 'block';
      }
    });

    // Handle skip button
    const skipBtn = form.querySelector('.skip-feedback-btn');
    skipBtn.addEventListener('click', () => {
      this.closeWidget();
    });

    // Style focus for textarea
    const textarea = form.querySelector('textarea');
    textarea.addEventListener('focus', function () {
      this.style.borderColor = form.closest('form').__primaryColor || '#4f46e5';
    });
    textarea.addEventListener('blur', function () {
      this.style.borderColor = '#e5e7eb';
    });

    form.__primaryColor = this.config.primaryColor;
    return form;
  }

  showToast(message) {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.position = 'fixed';
    toast.style.bottom = '20px';
    toast.style.left = '50%';
    toast.style.transform = 'translateX(-50%)';
    toast.style.backgroundColor = '#333';
    toast.style.color = '#fff';
    toast.style.padding = '10px 20px';
    toast.style.borderRadius = '4px';
    toast.style.zIndex = '10000';
    document.body.appendChild(toast);

    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transition = 'opacity 0.5s ease';
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 500);
    }, 3000);
  }

  endChat() {
    // Show post-chat survey if enabled
    if (this.config.postChat && this.state.messages.length > 1) {
      this.showPostChatSurvey();
    } else {
      this.closeWidget();
    }
  }

  showPostChatSurvey() {
    if (!this.widgetContainer) return;

    this.state.showPostChatSurvey = true;
    this.chatContainer.style.display = 'none';
    this.postChatSurvey.style.display = 'flex';
    this.postChatSurvey.style.height = '100%';
    this.widgetContainer.querySelector('.ai-chat-widget-button').style.display = 'none';
  }
}

// Register the custom element
customElements.define('ai-chat-widget', AIChatWidget);
