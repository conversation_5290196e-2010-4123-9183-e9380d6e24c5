<?php

namespace App\Services\AI\Providers;

use App\Models\AIModel;
use App\Services\AI\AbstractProvider;
use Illuminate\Support\Facades\Log;

class OpenAIProvider extends AbstractProvider
{
    /**
     * Get provider name
     *
     * @return string
     */
    protected function getProviderName(): string
    {
        return 'openai';
    }

    /**
     * Process a message using OpenAI
     *
     * @param AIModel $model
     * @param array $messages
     * @param array $options
     * @return array
     */
    public function processMessage(AIModel $model, array $messages, array $options = [])
    {
        try {
            $modelName = $this->getValidModelName($model);
            $temperature = $options['temperature'] ?? $model->settings['temperature'] ?? 0.7;
            $maxTokens = $options['max_tokens'] ?? $model->settings['max_tokens'] ?? 2048;

            // Format messages for OpenAI
            $formattedMessages = [];
            foreach ($messages as $message) {
                $formattedMessages[] = [
                    'role' => $message['role'],
                    'content' => $message['content'],
                ];
            }

            $payload = [
                'model' => $modelName,
                'messages' => $formattedMessages,
                'temperature' => $temperature,
                'max_tokens' => $maxTokens,
            ];

            $response = $this->makeRequest('post', '/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $model->api_key,
                    'Content-Type' => 'application/json',
                ],
                'data' => $payload,
            ]);

            return [
                'content' => $response['choices'][0]['message']['content'] ?? 'No response content',
                'metadata' => [
                    'model' => $response['model'] ?? $modelName,
                    'provider' => $this->getProviderName(),
                    'finish_reason' => $response['choices'][0]['finish_reason'] ?? null,
                ],
            ];
        } catch (\Exception $e) {
            Log::error("OpenAI API error: " . $e->getMessage(), [
                'model_id' => $model->id,
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Test connection to OpenAI
     *
     * @param AIModel $model
     * @return array
     */
    public function testConnection(AIModel $model): array
    {
        try {
            $response = $this->makeRequest('get', '/models', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $model->api_key,
                    'Content-Type' => 'application/json',
                ],
            ]);

            // Discover and update available models
            $discoveredModels = $this->parseModelsFromResponse($response);
            $this->updateModelSettings($model, $discoveredModels);

            return [
                'success' => true,
                'message' => 'Successfully connected to OpenAI API',
                'data' => [
                    'available_models' => array_keys($discoveredModels),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'OpenAI connection test failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Discover available models
     *
     * @param AIModel $model
     * @return array
     */
    public function discoverModels(AIModel $model): array
    {
        try {
            $response = $this->makeRequest('get', '/models', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $model->api_key,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $discoveredModels = $this->parseModelsFromResponse($response);

            // Update model settings
            $this->updateModelSettings($model, $discoveredModels);

            return [
                'success' => true,
                'models' => $discoveredModels,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to discover OpenAI models: " . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Parse models from API response
     *
     * @param array $response
     * @return array
     */
    protected function parseModelsFromResponse(array $response): array
    {
        $models = [];

        foreach ($response['data'] ?? [] as $model) {
            $modelId = $model['id'] ?? '';

            // Skip non-chat models
            if (!str_contains($modelId, 'gpt') && !str_contains($modelId, 'text-embedding')) {
                continue;
            }

            $models[$modelId] = [
                'name' => $modelId,
                'display_name' => $this->formatModelName($modelId),
                'description' => $model['description'] ?? '',
                'input_token_limit' => $this->getContextSizeForModel($modelId),
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => $this->supportsVision($modelId),
                ],
            ];
        }

        // If no models were found, return default models
        if (empty($models)) {
            return $this->getDefaultModels();
        }

        return $models;
    }

    /**
     * Format model name for display
     *
     * @param string $modelId
     * @return string
     */
    protected function formatModelName(string $modelId): string
    {
        // Convert model IDs like "gpt-4" to "GPT-4"
        $parts = explode('-', $modelId);
        $formattedParts = [];

        foreach ($parts as $part) {
            if ($part === 'gpt') {
                $formattedParts[] = 'GPT';
            } elseif ($part === 'o') {
                $formattedParts[] = 'o';
            } elseif (is_numeric($part)) {
                $formattedParts[] = $part;
            } elseif ($part === 'turbo') {
                $formattedParts[] = 'Turbo';
            } else {
                $formattedParts[] = ucfirst($part);
            }
        }

        return implode('-', $formattedParts);
    }

    /**
     * Get context size for a model
     *
     * @param string $modelId
     * @return int
     */
    protected function getContextSizeForModel(string $modelId): int
    {
        if (str_contains($modelId, 'gpt-4o')) {
            return 128000;
        } elseif (str_contains($modelId, 'gpt-4-turbo')) {
            return 128000;
        } elseif (str_contains($modelId, 'gpt-4-32k')) {
            return 32768;
        } elseif (str_contains($modelId, 'gpt-4')) {
            return 8192;
        } elseif (str_contains($modelId, 'gpt-3.5-turbo-16k')) {
            return 16384;
        } elseif (str_contains($modelId, 'gpt-3.5-turbo')) {
            return 4096;
        } else {
            return 4096; // Default
        }
    }

    /**
     * Check if a model supports vision
     *
     * @param string $modelId
     * @return bool
     */
    protected function supportsVision(string $modelId): bool
    {
        return str_contains($modelId, 'gpt-4o') ||
               str_contains($modelId, 'gpt-4-vision') ||
               str_contains($modelId, 'gpt-4-turbo');
    }

    /**
     * Get default models when API doesn't return any
     *
     * @return array
     */
    protected function getDefaultModels(): array
    {
        return [
            'gpt-4o' => [
                'name' => 'gpt-4o',
                'display_name' => 'GPT-4o',
                'description' => 'OpenAI\'s most capable multimodal model',
                'input_token_limit' => 128000,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => true,
                ],
            ],
            'gpt-4-turbo' => [
                'name' => 'gpt-4-turbo',
                'display_name' => 'GPT-4 Turbo',
                'description' => 'OpenAI\'s most capable model with a 128k context window',
                'input_token_limit' => 128000,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => true,
                ],
            ],
            'gpt-3.5-turbo' => [
                'name' => 'gpt-3.5-turbo',
                'display_name' => 'GPT-3.5 Turbo',
                'description' => 'OpenAI\'s fast and efficient model',
                'input_token_limit' => 4096,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => false,
                ],
            ],
        ];
    }

    /**
     * Get provider capabilities
     *
     * @return array
     */
    public function getCapabilities(): array
    {
        return [
            'streaming' => true,
            'function_calling' => true,
            'vision' => true,
            'embeddings' => true,
            'max_context_length' => 128000,
        ];
    }

    /**
     * Send message to the model
     *
     * @param array $messages Array of messages
     * @param array $options Request options
     * @return array Response
     */
    public function sendMessage(array $messages, array $options = []): array
    {
        $modelSettings = $this->model->settings ?? [];
        $modelName = $modelSettings['model_name'] ?? $this->getConfig()['default_model'] ?? 'gpt-3.5-turbo';

        $temperature = $this->model->temperature ?? $this->getConfig()['default_temperature'] ?? 0.7;
        $maxTokens = $options['max_tokens'] ?? $this->getConfig()['default_max_tokens'] ?? 1000;

        $data = [
            'model' => $modelName,
            'messages' => $this->formatMessages($messages),
            'temperature' => (float)$temperature,
            'max_tokens' => (int)$maxTokens
        ];

        // Add function calling if provided
        if (!empty($options['functions'])) {
            $data['functions'] = $options['functions'];

            if (!empty($options['function_call'])) {
                $data['function_call'] = $options['function_call'];
            }
        }

        try {
            // Make the API request
            $response = $this->makeRequest('post', '/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->model->secret_key,
                    'Content-Type' => 'application/json',
                ],
                'data' => $data,
            ]);

            if (!$response || !isset($response['choices'][0]['message'])) {
                Log::error('Invalid response from OpenAI API', [
                    'response' => $response
                ]);

                return [
                    'error' => 'Invalid response from AI provider',
                    'messages' => $messages
                ];
            }

            $aiMessage = $response['choices'][0]['message'];

            // Add the AI message to the conversation
            $messages[] = [
                'role' => $aiMessage['role'],
                'content' => $aiMessage['content'] ?? ''
            ];

            // Handle function calls if present
            if (isset($aiMessage['function_call'])) {
                $messages[count($messages) - 1]['function_call'] = $aiMessage['function_call'];
            }

            $usage = $response['usage'] ?? null;

            return [
                'messages' => $messages,
                'usage' => $usage
            ];
        } catch (\Exception $e) {
            Log::error('Error in OpenAI API request: ' . $e->getMessage());

            return [
                'error' => 'Error communicating with AI provider: ' . $e->getMessage(),
                'messages' => $messages
            ];
        }
    }

    /**
     * Format messages for OpenAI API
     *
     * @param array $messages
     * @return array
     */
    protected function formatMessages(array $messages): array
    {
        $formattedMessages = [];

        foreach ($messages as $message) {
            if (!isset($message['role']) || !isset($message['content'])) {
                continue;
            }

            $formattedMessage = [
                'role' => $message['role'],
                'content' => $message['content']
            ];

            // Add function call if present
            if (isset($message['function_call'])) {
                $formattedMessage['function_call'] = $message['function_call'];
            }

            // Add name if present (for functions)
            if (isset($message['name'])) {
                $formattedMessage['name'] = $message['name'];
            }

            $formattedMessages[] = $formattedMessage;
        }

        return $formattedMessages;
    }
}
