"use client"

import { useState } from "react"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Co<PERSON>, 
  Check, 
  FileText, 
  AlertCircle, 
  CheckCircle2 
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { usePreChatForm } from "@/hooks/use-pre-chat-form"
import { PreChatFormBuilder } from "./PreChatFormBuilder"
import { PreChatFormTemplate, PreChatFormField } from "@/types/widget"

interface PreChatFormTemplatesProps {
  widgetId: number
  primaryColor?: string
}

/**
 * Pre-Chat Form Templates Component
 * 
 * A user-friendly interface for managing pre-chat form templates.
 * Designed for non-technical users with intuitive controls and visual feedback.
 */
export function PreChatFormTemplates({
  widgetId,
  primaryColor = "#7E69AB"
}: PreChatFormTemplatesProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<PreChatFormTemplate | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [templateToDelete, setTemplateToDelete] = useState<number | null>(null)
  
  const { 
    templates, 
    activeTemplate,
    isLoading, 
    error, 
    refetch,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setActiveTemplate
  } = usePreChatForm({ widgetId })
  
  // Handle creating a new template
  const handleCreateTemplate = (data: {
    title: string
    description: string
    fields: Omit<PreChatFormField, "id" | "templateId">[]
  }) => {
    createTemplate(data)
    setIsCreating(false)
  }
  
  // Handle updating an existing template
  const handleUpdateTemplate = (data: {
    title: string
    description: string
    fields: Omit<PreChatFormField, "id" | "templateId">[]
  }) => {
    if (!selectedTemplate) return
    
    updateTemplate({
      id: selectedTemplate.id,
      ...data
    })
    
    setIsEditing(false)
    setSelectedTemplate(null)
  }
  
  // Handle deleting a template
  const handleDeleteTemplate = () => {
    if (templateToDelete === null) return
    
    deleteTemplate(templateToDelete)
    setDeleteDialogOpen(false)
    setTemplateToDelete(null)
  }
  
  // Handle setting a template as active
  const handleSetActiveTemplate = (templateId: number) => {
    setActiveTemplate(templateId)
  }
  
  // Handle editing a template
  const handleEditTemplate = (template: PreChatFormTemplate) => {
    setSelectedTemplate(template)
    setIsEditing(true)
  }
  
  // Handle duplicating a template
  const handleDuplicateTemplate = (template: PreChatFormTemplate) => {
    const { id, ...rest } = template
    
    createTemplate({
      title: `${rest.title} (Copy)`,
      description: rest.description,
      fields: rest.fields.map(({ id, templateId, ...fieldRest }) => fieldRest)
    })
  }
  
  // Handle confirming template deletion
  const confirmDeleteTemplate = (templateId: number) => {
    setTemplateToDelete(templateId)
    setDeleteDialogOpen(true)
  }
  
  // Render template cards
  const renderTemplateCards = () => {
    if (!templates || templates.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center border border-dashed rounded-lg bg-muted/50">
          <FileText className="h-12 w-12 text-muted-foreground mb-3" />
          <h3 className="text-lg font-medium mb-1">No Templates Yet</h3>
          <p className="text-sm text-muted-foreground max-w-md">
            Create your first pre-chat form template to collect visitor information before starting a chat.
          </p>
          <Button 
            className="mt-4"
            onClick={() => setIsCreating(true)}
            style={{ backgroundColor: primaryColor }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Template
          </Button>
        </div>
      )
    }
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {templates.map((template: PreChatFormTemplate) => (
          <Card key={template.id} className={`border ${
            activeTemplate?.id === template.id ? 'border-primary border-2' : ''
          }`}>
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg font-medium">
                    {template.title}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {template.description}
                  </CardDescription>
                </div>
                <div className="flex items-center">
                  {activeTemplate?.id === template.id && (
                    <Badge className="mr-2 bg-green-100 text-green-800 hover:bg-green-100">
                      <CheckCircle2 className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicateTemplate(template)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </DropdownMenuItem>
                      {activeTemplate?.id !== template.id && (
                        <DropdownMenuItem onClick={() => handleSetActiveTemplate(template.id)}>
                          <Check className="mr-2 h-4 w-4" />
                          Set as Active
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive"
                        onClick={() => confirmDeleteTemplate(template.id)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pb-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                <span>{template.fields.length} fields</span>
                <span>•</span>
                <span>
                  {template.fields.filter(f => f.isRequired).length} required
                </span>
              </div>
              <Separator className="mb-3" />
              <div className="flex flex-wrap gap-2">
                {template.fields.map((field) => (
                  <Badge key={field.id} variant="outline" className="text-xs">
                    {field.label}
                  </Badge>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between pt-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleEditTemplate(template)}
              >
                <Edit className="mr-2 h-3 w-3" />
                Edit
              </Button>
              {activeTemplate?.id !== template.id ? (
                <Button 
                  size="sm"
                  onClick={() => handleSetActiveTemplate(template.id)}
                  style={{ backgroundColor: primaryColor }}
                >
                  <Check className="mr-2 h-3 w-3" />
                  Set as Active
                </Button>
              ) : (
                <Badge variant="outline" className="pointer-events-none">
                  Currently Active
                </Badge>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }
  
  // If loading, show loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pre-Chat Form Templates</CardTitle>
          <CardDescription>Loading templates...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center p-8">
            <div className="h-8 w-8 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // If error, show error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pre-Chat Form Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load templates. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Button onClick={() => refetch()}>Retry</Button>
        </CardFooter>
      </Card>
    )
  }
  
  // If creating a new template, show builder
  if (isCreating) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold">Create Pre-Chat Form Template</h2>
          <Button variant="outline" onClick={() => setIsCreating(false)}>
            Cancel
          </Button>
        </div>
        <PreChatFormBuilder
          widgetId={widgetId}
          onSave={handleCreateTemplate}
          onCancel={() => setIsCreating(false)}
          primaryColor={primaryColor}
        />
      </div>
    )
  }
  
  // If editing a template, show builder with template data
  if (isEditing && selectedTemplate) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold">Edit Pre-Chat Form Template</h2>
          <Button variant="outline" onClick={() => {
            setIsEditing(false)
            setSelectedTemplate(null)
          }}>
            Cancel
          </Button>
        </div>
        <PreChatFormBuilder
          widgetId={widgetId}
          initialTemplate={selectedTemplate}
          onSave={handleUpdateTemplate}
          onCancel={() => {
            setIsEditing(false)
            setSelectedTemplate(null)
          }}
          primaryColor={primaryColor}
        />
      </div>
    )
  }
  
  // Default view - template list
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Pre-Chat Form Templates</h2>
          <p className="text-muted-foreground mt-1">
            Create and manage forms to collect visitor information before starting a chat
          </p>
        </div>
        <Button 
          onClick={() => setIsCreating(true)}
          style={{ backgroundColor: primaryColor }}
        >
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>
      
      {renderTemplateCards()}
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this template. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteTemplate}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
