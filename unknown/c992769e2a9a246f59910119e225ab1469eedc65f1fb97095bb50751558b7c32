import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Spinner } from "@/components/ui/spinner";
import { AIModelData, aiModelService } from "@/utils/ai-model-service";
import { useToast } from "@/hooks/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Send, Clock, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface ModelQuickTestProps {
  model: AIModelData | null;
}

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface ResponseMetadata {
  model: string;
  provider: string;
  response_time: number;
  tokens_input: number;
  tokens_output: number;
  error?: string;
}

export function ModelQuickTest({ model }: ModelQuickTestProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [responseMetadata, setResponseMetadata] = useState<ResponseMetadata | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      // Use a small timeout to ensure the DOM has updated
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "end"
        });
      }, 100);
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !model) return;

    // Add user message to chat
    const userMessage: ChatMessage = {
      role: "user",
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);
    setError(null);

    try {
      let result;

      if (model.id) {
        // For saved models with an ID, use the testChat endpoint
        result = await aiModelService.testChat(model.id, inputMessage, {
          temperature: model.settings?.temperature || 0.7,
          max_tokens: model.settings?.max_tokens || 2048
        });
      } else {
        // For temporary models without an ID, use the testChatDirect endpoint
        result = await aiModelService.testChatDirect(model, inputMessage);
      }

      // Add AI response to chat
      const aiMessage: ChatMessage = {
        role: "assistant",
        content: result.response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
      setResponseMetadata(result.metadata);

      if (!result.success) {
        setError(result.metadata.error || "Unknown error occurred");
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "Failed to get response from AI model";
      setError(errorMessage);

      // Add error message as AI response
      const errorResponse: ChatMessage = {
        role: "assistant",
        content: `Error: ${errorMessage}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorResponse]);

      toast({
        title: "Test Failed",
        description: "Failed to get response from AI model.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    setResponseMetadata(null);
    setError(null);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">Test Chat</span>
        </div>
        <div className="flex items-center gap-2">
          {responseMetadata && (
            <div className="flex items-center gap-1.5">
              <Badge variant="outline" className="text-xs px-1.5 py-0 h-5">
                {responseMetadata.response_time}ms
              </Badge>
              <Badge variant="outline" className="text-xs px-1.5 py-0 h-5">
                {responseMetadata.tokens_input + responseMetadata.tokens_output} tokens
              </Badge>
            </div>
          )}
          {messages.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearChat}
              className="h-7 px-2 text-xs"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      {error && (
        <div className="flex items-center gap-2 text-destructive text-xs mb-2 p-2 bg-destructive/10 rounded-md">
          <AlertCircle className="h-3.5 w-3.5 flex-shrink-0" />
          <span className="text-xs">{error}</span>
        </div>
      )}

      <ScrollArea
        className="flex-1 pr-4 h-[250px] mb-3 border rounded-md bg-muted/30 overflow-y-auto"
        scrollHideDelay={100}
        type="always"
      >
        {messages.length === 0 ? (
          <div className="h-full flex items-center justify-center text-muted-foreground p-4 text-center">
            <p className="text-sm">
              Send a message to test this model's capabilities
            </p>
          </div>
        ) : (
          <div className="p-3 space-y-4">
            {messages.map((message, index) => (
              <div
                key={index}
                className={cn(
                  "flex flex-col rounded-lg p-3 shadow-sm",
                  message.role === "user"
                    ? "bg-primary text-primary-foreground ml-auto max-w-[85%]"
                    : "bg-muted max-w-[90%] border border-border/30"
                )}
              >
                <div className="text-sm whitespace-pre-wrap break-words leading-relaxed">
                  {message.content}
                </div>
                <div className="flex items-center mt-1.5 justify-end">
                  <Clock className="h-3 w-3 mr-1 opacity-70" />
                  <span className="text-[10px] opacity-70 font-mono">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>

      <div className="relative">
        <Textarea
          placeholder="Type a message to test..."
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          className="flex-1 min-h-[60px] max-h-[100px] text-sm pr-12"
          disabled={isLoading || !model}
        />
        <Button
          onClick={handleSendMessage}
          disabled={isLoading || !inputMessage.trim() || !model}
          className="absolute bottom-2 right-2 h-8 w-8 p-0 rounded-full"
          size="sm"
          variant={inputMessage.trim() ? "default" : "ghost"}
        >
          {isLoading ? <Spinner size="sm" /> : <Send className="h-4 w-4" />}
        </Button>
        {isLoading && (
          <div className="absolute bottom-2 left-2 text-xs text-muted-foreground animate-pulse">
            Generating response...
          </div>
        )}
      </div>
    </div>
  );
}
