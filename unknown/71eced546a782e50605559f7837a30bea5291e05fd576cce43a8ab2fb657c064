import { useState, useEffect } from 'react';
import { ApiErrorBus, ApiError } from '@/utils/api-error-handler';

type TemplateErrorType = 'template_list' | 'writing_templates' | 'template_permissions' | 'other';

interface TemplateErrorState {
    errors: Record<TemplateErrorType, boolean>;
    errorMessages: string[];
    lastError: ApiError | null;
}

/**
 * Hook for handling template API errors with enhanced reliability
 */
export function useTemplateErrorHandler() {
    const [errorState, setErrorState] = useState<TemplateErrorState>({
        errors: {
            template_list: false,
            writing_templates: false,
            template_permissions: false,
            other: false
        },
        errorMessages: [],
        lastError: null
    });

    useEffect(() => {
        // Create a more robust handler for site integration template errors
        const handleTemplateListError = (error: ApiError) => {
            setErrorState(prev => ({
                ...prev,
                errors: { ...prev.errors, template_list: true },
                errorMessages: addUniqueMessage(prev.errorMessages, `Template list unavailable: ${error.message}`),
                lastError: error
            }));
        };

        // Handle writing templates errors
        const handleWritingTemplatesError = (error: ApiError) => {
            setErrorState(prev => ({
                ...prev,
                errors: { ...prev.errors, writing_templates: true },
                errorMessages: addUniqueMessage(prev.errorMessages, `Writing templates unavailable: ${error.message}`),
                lastError: error
            }));
        };

        // Handle general template permission errors
        const handleGeneralTemplateError = (error: ApiError) => {
            // Only handle template-related errors 
            if (
                error.path &&
                (error.path.includes('template') || error.path.includes('templates')) &&
                error.code === 403
            ) {
                setErrorState(prev => ({
                    ...prev,
                    errors: { ...prev.errors, template_permissions: true },
                    errorMessages: addUniqueMessage(prev.errorMessages, `Template access error: ${error.message}`),
                    lastError: error
                }));
            }
        };

        // Subscribe to specific error paths
        const unsubscribe1 = ApiErrorBus.subscribe('/site_integration/template_list', handleTemplateListError);
        const unsubscribe2 = ApiErrorBus.subscribe('/writing/get_template_list', handleWritingTemplatesError);
        const unsubscribe3 = ApiErrorBus.subscribe('*', handleGeneralTemplateError);

        return () => {
            unsubscribe1();
            unsubscribe2();
            unsubscribe3();
        };
    }, []);

    // Helper to add unique error messages
    const addUniqueMessage = (messages: string[], newMessage: string): string[] => {
        return messages.includes(newMessage) ? messages : [...messages, newMessage];
    };

    // Helper to clear all errors
    const clearErrors = () => {
        setErrorState({
            errors: {
                template_list: false,
                writing_templates: false,
                template_permissions: false,
                other: false
            },
            errorMessages: [],
            lastError: null
        });
    };

    // Check if any template errors exist
    const hasTemplateErrors = Object.values(errorState.errors).some(Boolean);

    return {
        errors: errorState.errors,
        hasTemplateErrors,
        errorMessages: errorState.errorMessages,
        lastError: errorState.lastError,
        clearErrors
    };
}

export default useTemplateErrorHandler; 