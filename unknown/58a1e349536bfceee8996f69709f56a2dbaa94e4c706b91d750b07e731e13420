<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Project;

class ProjectSeeder extends Seeder
{
    public function run(): void
    {
        // Check if any projects exist
        if (Project::count() === 0) {
            Project::insert([
                [
                    'id' => 1, // Explicitly set ID to 1
                    'name' => 'Default Project',
                    'description' => 'Default project for general use',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'AI Knowledge Base',
                    'description' => 'Knowledge base for AI context',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ]);

            $this->command->info('Default projects created.');
        }
    }
}
