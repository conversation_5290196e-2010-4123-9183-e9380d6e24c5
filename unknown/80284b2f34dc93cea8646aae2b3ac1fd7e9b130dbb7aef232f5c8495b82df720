<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Post-chat surveys
        Schema::create('post_chat_surveys', function (Blueprint $table) {
            $table->id();
            $table->foreignId('widget_id')->constrained('widgets')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('thank_you_message')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Survey questions
        Schema::create('survey_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('survey_id')->constrained('post_chat_surveys')->onDelete('cascade');
            $table->text('text');
            $table->enum('type', ['rating', 'text', 'select', 'boolean', 'checkbox', 'multiselect']);
            $table->json('options')->nullable(); // For select questions
            $table->boolean('is_required')->default(false);
            $table->integer('order')->default(0);
            $table->timestamps();
        });

        // Survey responses
        Schema::create('survey_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('survey_id')->constrained('post_chat_surveys');
            $table->string('session_id');
            $table->json('answers'); // Question ID to answer mapping
            $table->timestamps();

            // Add index for faster lookups
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('survey_responses');
        Schema::dropIfExists('survey_questions');
        Schema::dropIfExists('post_chat_surveys');
    }
};
