import React from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  Globe,
  Database,
  Network,
  Clock,
  HelpCircle,
  X,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  <PERSON><PERSON><PERSON>P<PERSON>ider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface KnowledgeBaseGuideProps {
  activeTab: string;
  onClose: () => void;
}

export function KnowledgeBaseGuide({
  activeTab,
  onClose,
}: KnowledgeBaseGuideProps) {
  const guides = {
    documents: {
      title: "Documents Guide",
      description:
        "Upload and manage your documents to teach your AI assistant.",
      steps: [
        "Select or create a knowledge base from the dropdown",
        "Upload documents using the panel on the left",
        "Select documents and click 'Generate Embeddings' to make them searchable",
        "Toggle documents as AI sources to include them in responses",
      ],
      tips: [
        "Supported formats: PDF, DOCX, TXT, CSV, JSON",
        "Organize documents with categories for better management",
        "Generate embeddings to enable semantic search",
      ],
    },
    "web-scraping": {
      title: "Web Scraping Guide",
      description: "Import content directly from your website pages.",
      steps: [
        "Enter the URL you want to scrape",
        "Set depth level to control how many linked pages to include",
        "Add URL patterns to include or exclude specific pages",
        "Click 'Start Scraping' to begin the process",
      ],
      tips: [
        "Start with a low depth to avoid scraping too many pages",
        "Use URL patterns to focus on relevant content",
        "Check the preview before scraping to ensure correct content",
      ],
    },
    "scheduled-scraping": {
      title: "Scheduled Scraping Guide",
      description:
        "Set up automatic, recurring web scraping to keep your knowledge base updated.",
      steps: [
        "Create a new scheduled scrape with a descriptive name",
        "Configure the URL, depth, and patterns as with manual scraping",
        "Set the frequency (daily, weekly, monthly)",
        "Enable or disable the schedule as needed",
      ],
      tips: [
        "Use this for websites that update regularly",
        "Set reasonable intervals to avoid overloading the target website",
        "Monitor the scraping history to ensure it's working correctly",
      ],
    },
    database: {
      title: "Database Guide",
      description:
        "Connect to databases to use structured data as knowledge sources.",
      steps: [
        "Select a database table from the list",
        "Review the table schema and data",
        "Select an embedding model for processing",
        "Configure sync settings and add to knowledge base",
      ],
      tips: [
        "Enable auto-sync for frequently changing data",
        "Use the SQL editor to test queries before embedding",
        "Be selective about which tables to include to optimize performance",
      ],
    },
    context: {
      title: "Context Rules Guide",
      description:
        "Define how your AI assistant prioritizes different knowledge sources.",
      steps: [
        "Create rules that determine when to use specific knowledge sources",
        "Add keywords that trigger each rule",
        "Select which sources (documents, database, web) to use",
        "Test your rules with sample queries",
      ],
      tips: [
        "Create specific rules for different types of questions",
        "Use the source settings to adjust priorities",
        "Test your rules thoroughly with different queries",
      ],
    },
  };

  const currentGuide =
    guides[activeTab as keyof typeof guides] || guides.documents;

  return (
    <Card className="border-primary/20 bg-primary/5 mb-6">
      <CardContent className="pt-4 pb-4">
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-primary" />
            <h3 className="font-medium text-lg">{currentGuide.title}</h3>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-7 w-7"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <p className="text-sm mb-4">{currentGuide.description}</p>

        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Quick Steps:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              {currentGuide.steps.map((step, index) => (
                <li key={index} className="text-muted-foreground">
                  {step}
                </li>
              ))}
            </ol>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Pro Tips:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {currentGuide.tips.map((tip, index) => (
                <li key={index} className="text-muted-foreground">
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function TabTooltip({
  children,
  content,
}: {
  children: React.ReactNode;
  content: string;
}) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent className="max-w-[300px] p-3">
          <p className="text-sm">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
