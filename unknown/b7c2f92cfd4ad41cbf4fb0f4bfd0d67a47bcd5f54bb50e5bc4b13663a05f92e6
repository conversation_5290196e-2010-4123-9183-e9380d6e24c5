<?php

namespace App\Http\Controllers;

use App\Models\AIModel;
use App\Services\AIService;
use App\Services\AI\ProviderInterface;
use App\Services\AI\ProviderRegistry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;

class AIModelController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        try {
            // Get all models but handle potential decryption errors
            $aiModels = AIModel::all();

            // Filter out sensitive data like API keys for security
            $aiModels = $aiModels->map(function ($model) {
                // Create a new array with only the data we want to expose
                return [
                    'id' => $model->id,
                    'name' => $model->name,
                    'provider' => $model->provider,
                    'description' => $model->description,
                    'settings' => $model->settings,
                    'is_default' => $model->is_default,
                    'active' => $model->active,
                    'fallback_model_id' => $model->fallback_model_id,
                    'confidence_threshold' => $model->confidence_threshold,
                    'template_id' => $model->template_id,
                    'created_at' => $model->created_at,
                    'updated_at' => $model->updated_at,
                    // Don't include the API key in the response
                ];
            });

            return response()->json(['data' => $aiModels, 'success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch AI models: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to fetch AI models',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'provider' => 'required|string|max:255',
            'description' => 'nullable|string',
            'api_key' => 'nullable|string',
            'settings' => 'nullable|array',
            'is_default' => 'boolean',
            'active' => 'boolean',
            'is_free' => 'boolean',
            'fallback_model_id' => 'nullable|integer',
            'template_id' => 'nullable|integer|exists:templates,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors(), 'success' => false], 422);
        }

        try {
            // If this model is set as default, unset other defaults
            if ($request->input('is_default')) {
                AIModel::where('is_default', true)->update(['is_default' => false]);
            }

            $aiModel = AIModel::create($request->all());
            return response()->json(['data' => $aiModel, 'success' => true], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create AI model: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to create AI model',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $aiModel = AIModel::findOrFail($id);
            return response()->json(['data' => $aiModel, 'success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch AI model: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to fetch AI model',
                'error' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'provider' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'api_key' => 'nullable|string',
            'settings' => 'nullable|array',
            'is_default' => 'boolean',
            'active' => 'boolean',
            'is_free' => 'boolean',
            'template_id' => 'nullable|integer|exists:templates,id',
            'fallback_model_id' => 'nullable|integer',
            'confidence_threshold' => 'nullable|numeric|min:0|max:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors(), 'success' => false], 422);
        }

        try {
            $aiModel = AIModel::findOrFail($id);

            // If this model is being set as default, unset other defaults
            if ($request->has('is_default') && $request->input('is_default') && !$aiModel->is_default) {
                AIModel::where('is_default', true)->update(['is_default' => false]);
            }

            $aiModel->update($request->all());
            return response()->json(['data' => $aiModel, 'success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to update AI model: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to update AI model',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $aiModel = AIModel::findOrFail($id);

            // Check if model is in use by any widgets using the correct column name
            $widgetsCount = \App\Models\Widget::where('ai_model_id', $id)->count();
            if ($widgetsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete this AI model because it is being used by one or more widgets.',
                    'success' => false
                ], 422);
            }

            $aiModel->delete();
            return response()->json(['message' => 'Model deleted successfully', 'success' => true], 200);
        } catch (\Exception $e) {
            Log::error('Failed to delete AI model: ' . $e->getMessage(), [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'message' => 'Failed to delete AI model',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Test connection to the AI model provider.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function testConnection(Request $request, $id)
    {
        try {
            $aiModel = AIModel::findOrFail($id);

            // Get the appropriate provider for this model
            $provider = $this->getProviderForModel($aiModel);

            // Test the connection using the provider's implementation
            $result = $provider->testConnection($aiModel);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ]);
        } catch (\Exception $e) {
            Log::error('Connection test failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test connection without an existing model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function testConnectionDirect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|string|max:255',
            'api_key' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors(), 'success' => false], 422);
        }

        try {
            // Create a temporary model object (not saved to database)
            $tempModel = new AIModel();
            $tempModel->provider = $request->input('provider');
            $tempModel->api_key = $request->input('api_key');
            $tempModel->settings = $request->input('settings', []);

            // For OpenAI, test connection directly
            if ($tempModel->provider === 'openai') {
                $baseUrl = config('ai.providers.openai.base_url', 'https://api.openai.com/v1');

                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $tempModel->api_key,
                    'Content-Type' => 'application/json',
                ])->get($baseUrl . '/models');

                if ($response->successful()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Successfully connected to OpenAI API',
                        'data' => [
                            'available_models' => count($response->json()['data'] ?? []),
                        ]
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'OpenAI connection test failed: ' . $response->body(),
                    ]);
                }
            } else {
                // For other providers, use a modified version of the provider's implementation
                // Get the appropriate provider for this model
                $provider = $this->getProviderForModel($tempModel);

                // Create a custom method to test connection without saving
                $baseUrl = config('ai.providers.' . $tempModel->provider . '.base_url', '');
                $testEndpoint = config('ai.providers.' . $tempModel->provider . '.test_endpoint', '');

                if ($baseUrl && $testEndpoint) {
                    $response = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $tempModel->api_key,
                        'Content-Type' => 'application/json',
                    ])->get($baseUrl . $testEndpoint);

                    if ($response->successful()) {
                        return response()->json([
                            'success' => true,
                            'message' => 'Successfully connected to ' . ucfirst($tempModel->provider) . ' API',
                        ]);
                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => ucfirst($tempModel->provider) . ' connection test failed: ' . $response->body(),
                        ]);
                    }
                } else if ($tempModel->provider === 'openai') {
                    // Use our custom NoSaveOpenAIProvider for OpenAI
                    $provider = new \App\Services\AI\Providers\NoSaveOpenAIProvider();
                    $result = $provider->testConnection($tempModel);

                    return response()->json([
                        'success' => $result['success'],
                        'message' => $result['message'],
                        'data' => $result['data'] ?? null
                    ]);
                } else {
                    // For other providers, just return success if we got this far
                    return response()->json([
                        'success' => true,
                        'message' => 'Successfully connected to ' . ucfirst($tempModel->provider) . ' API',
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Direct connection test failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test chat with a temporary AI model (not saved in database).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function testChatDirect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:2000',
            'provider' => 'required|string',
            'api_key' => 'required|string',
            'model_name' => 'nullable|string',
            'temperature' => 'nullable|numeric|min:0|max:2',
            'max_tokens' => 'nullable|integer|min:1|max:32000',
            'system_prompt' => 'nullable|string|max:4000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors(), 'success' => false], 422);
        }

        try {
            // Create a temporary model object
            $tempModel = new \App\Models\AIModel();
            $tempModel->provider = $request->provider;
            $tempModel->api_key = $request->api_key;
            $tempModel->settings = [
                'model_name' => $request->model_name,
                'temperature' => $request->temperature ?? 0.7,
                'max_tokens' => $request->max_tokens ?? 2048,
            ];

            // Create messages array with system prompt if provided
            $messages = [];

            // Add system prompt if provided
            if ($request->has('system_prompt') && !empty($request->input('system_prompt'))) {
                $messages[] = [
                    'role' => 'system',
                    'content' => $request->input('system_prompt')
                ];
            }

            // Add user message
            $messages[] = [
                'role' => 'user',
                'content' => $request->input('message')
            ];

            // Get the appropriate provider for this model
            $provider = $this->getProviderForModel($tempModel);

            // Start timing the response
            $startTime = microtime(true);

            // Create AIService instance
            $aiService = app(AIService::class);

            // Process the message
            $response = $aiService->processMessage($messages, $tempModel);

            // Calculate response time
            $responseTime = round((microtime(true) - $startTime) * 1000);

            // Calculate tokens (if available in response)
            $tokensInput = $aiService->countTokens($messages);
            $tokensOutput = $aiService->countTokens([$response['content']]);

            return response()->json([
                'success' => !isset($response['metadata']['error']),
                'response' => $response['content'],
                'metadata' => [
                    'model' => $request->model_name ?? 'unknown',
                    'provider' => $tempModel->provider,
                    'response_time' => $responseTime,
                    'tokens_input' => $tokensInput,
                    'tokens_output' => $tokensOutput,
                    'error' => $response['metadata']['error'] ?? null
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Direct chat test failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'response' => 'Error: ' . $e->getMessage(),
                'metadata' => [
                    'model' => $request->model_name ?? 'unknown',
                    'provider' => $request->provider,
                    'response_time' => 0,
                    'tokens_input' => 0,
                    'tokens_output' => 0,
                    'error' => $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * Test chat with a model
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function testChat(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'message' => 'required|string',
            'temperature' => 'nullable|numeric|min:0|max:2',
            'max_tokens' => 'nullable|integer|min:1|max:32000',
            'system_prompt' => 'nullable|string',
            'use_knowledge_base' => 'nullable|boolean',
            'project_id' => 'nullable|exists:projects,id'
        ]);

        try {
            $model = AIModel::findOrFail($id);
            $message = $request->input('message');

            // Format messages for AI processing
            $messages = [
                ['role' => 'user', 'content' => $message]
            ];

            // Add system prompt if provided
            if ($request->has('system_prompt') && !empty($request->input('system_prompt'))) {
                array_unshift($messages, [
                    'role' => 'system',
                    'content' => $request->input('system_prompt')
                ]);
            }

            // Configure options for the model
            $modelSettings = $model->settings ?? [];

            // Override with request values if provided
            if ($request->has('temperature')) {
                $modelSettings['temperature'] = $request->input('temperature');
            }

            if ($request->has('max_tokens')) {
                $modelSettings['max_tokens'] = $request->input('max_tokens');
            }

            // Setup context for AI processing
            $context = [
                'query_type' => 'test',
                'user_id' => $request->user()->id ?? null,
                'tenant_id' => $request->user()->tenant_id ?? null
            ];

            // Add knowledge base context if requested
            if ($request->input('use_knowledge_base', false) && $request->input('project_id')) {
                $context['project_id'] = $request->input('project_id');
                $context['use_knowledge_base'] = true;
            }

            // Get response from AI service
            $startTime = microtime(true);
            $response = $this->aiService->processMessage($messages, $model, $modelSettings, $context);
            $endTime = microtime(true);

            // Calculate response time in milliseconds
            $responseTime = round(($endTime - $startTime) * 1000);

            // Extract token counts if available
            $inputTokens = $response['metadata']['tokens_input'] ?? 0;
            $outputTokens = $response['metadata']['tokens_output'] ?? 0;

            // Format the response
            $result = [
                'success' => true,
                'response' => $response['content'],
                'metadata' => [
                    'model' => $model->name,
                    'provider' => $model->provider,
                    'response_time' => $responseTime,
                    'tokens_input' => $inputTokens,
                    'tokens_output' => $outputTokens
                ]
            ];

            // Include knowledge sources if available
            if (isset($response['metadata']['knowledge_sources_used'])) {
                $result['metadata']['knowledge_sources_used'] = $response['metadata']['knowledge_sources_used'];
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Error testing chat with model:', [
                'model_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'response' => 'Error: ' . $e->getMessage(),
                'metadata' => [
                    'error' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get the appropriate AI provider for the given model.
     *
     * @param  \App\Models\AIModel  $aiModel
     * @return \App\Services\AI\ProviderInterface
     * @throws \Exception
     */
    private function getProviderForModel(AIModel $aiModel): ProviderInterface
    {
        $registry = app(ProviderRegistry::class);
        $provider = $registry->getProvider($aiModel->provider);

        if (!$provider) {
            throw new \Exception("Unsupported AI provider: {$aiModel->provider}");
        }

        return $provider;
    }

    /**
     * Get available fallback options for a model.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getFallbackOptions($id)
    {
        try {
            // Find the current model
            $currentModel = AIModel::findOrFail($id);

            // Get all active models except the current one
            $fallbackOptions = AIModel::where('id', '!=', $id)
                ->where('active', true)
                ->get();

            return response()->json(['data' => $fallbackOptions, 'success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch fallback options: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to fetch fallback options',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Toggle model activation status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function toggleActivation(Request $request, $id)
    {
        try {
            $aiModel = AIModel::findOrFail($id);

            // Don't allow deactivating the default model
            if ($aiModel->is_default && !$request->input('active', true)) {
                return response()->json([
                    'message' => 'Cannot deactivate the default model',
                    'success' => false
                ], 422);
            }

            $aiModel->active = $request->input('active', true);
            $aiModel->save();

            return response()->json([
                'data' => $aiModel,
                'success' => true,
                'message' => $aiModel->active ? 'Model activated successfully' : 'Model deactivated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to toggle model activation: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to toggle model activation',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get available models for a specific AI model.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getAvailableModels($id)
    {
        try {
            $aiModel = AIModel::findOrFail($id);

            // Get available models from settings
            $availableModels = $aiModel->settings['available_models'] ?? [];

            // Convert to array format for the frontend
            $models = [];
            foreach ($availableModels as $name => $info) {
                $models[] = [
                    'name' => $name,
                    'display_name' => $info['display_name'] ?? $name,
                    'description' => $info['description'] ?? '',
                    'input_token_limit' => $info['input_token_limit'] ?? 0,
                    'output_token_limit' => $info['output_token_limit'] ?? 0,
                    'supported_features' => $info['supported_features'] ?? [],
                ];
            }

            return response()->json([
                'data' => $models,
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get available models: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to get available models',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Discover available models from the provider.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function discoverModels($id)
    {
        try {
            $aiModel = AIModel::findOrFail($id);

            // Get the appropriate provider for this model
            $provider = $this->getProviderForModel($aiModel);

            // Discover models using the provider
            $result = $provider->discoverModels($aiModel);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Models discovered successfully',
                    'models' => $result['models'],
                    'data' => [
                        'models' => array_keys($result['models']),
                        'current_model' => $aiModel->settings['model_name'] ?? null,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to discover models: ' . ($result['error'] ?? 'Unknown error'),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to discover models: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to discover models',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Discover available models directly without an existing model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function discoverModelsDirect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|string|max:255',
            'api_key' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors(), 'success' => false], 422);
        }

        try {
            // Create a temporary model object (not saved to database)
            $tempModel = new AIModel();
            $tempModel->provider = $request->input('provider');
            $tempModel->api_key = $request->input('api_key');
            $tempModel->settings = $request->input('settings', []);

            // Use our custom NoSaveOpenAIProvider for OpenAI
            if ($tempModel->provider === 'openai') {
                $provider = new \App\Services\AI\Providers\NoSaveOpenAIProvider();
                $result = $provider->discoverModels($tempModel);

                if ($result['success']) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Models discovered successfully',
                        'models' => $result['models'],
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to discover models: ' . ($result['error'] ?? 'Unknown error'),
                    ]);
                }
            }

            // For other providers, call the API directly
            $baseUrl = config('ai.providers.' . $tempModel->provider . '.base_url', '');

            // For OpenRouter, fetch models directly
            if ($tempModel->provider === 'openrouter') {
                try {
                    $response = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $tempModel->api_key,
                        'Content-Type' => 'application/json',
                    ])->get($baseUrl . '/models');

                    if ($response->successful()) {
                        $responseData = $response->json();
                        $discoveredModels = [];

                        // Parse OpenRouter models
                        foreach ($responseData['data'] ?? [] as $model) {
                            $modelId = $model['id'] ?? '';
                            $modelName = $model['name'] ?? $modelId;

                            $discoveredModels[$modelId] = [
                                'name' => $modelId,
                                'display_name' => $modelName,
                                'description' => $model['description'] ?? '',
                            ];
                        }

                        // If no models were found, return default models
                        if (empty($discoveredModels)) {
                            $discoveredModels = [
                                'openai/gpt-4o' => [
                                    'name' => 'openai/gpt-4o',
                                    'display_name' => 'GPT-4o',
                                    'description' => 'OpenAI\'s most capable multimodal model',
                                ],
                                'anthropic/claude-3-opus' => [
                                    'name' => 'anthropic/claude-3-opus',
                                    'display_name' => 'Claude 3 Opus',
                                    'description' => 'Anthropic\'s most capable model',
                                ],
                                'anthropic/claude-3-sonnet' => [
                                    'name' => 'anthropic/claude-3-sonnet',
                                    'display_name' => 'Claude 3 Sonnet',
                                    'description' => 'Anthropic\'s balanced model',
                                ],
                            ];
                        }

                        return response()->json([
                            'success' => true,
                            'message' => 'Models discovered successfully',
                            'models' => $discoveredModels,
                        ]);
                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to discover models: ' . $response->body(),
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to discover OpenRouter models: ' . $e->getMessage());
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to discover models',
                        'error' => $e->getMessage()
                    ], 500);
                }
            } else {
                // For other providers, use a direct HTTP request
                try {
                    // Try to get the models endpoint from config
                    $modelsEndpoint = config('ai.providers.' . $tempModel->provider . '.models_endpoint', '/models');

                    $response = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $tempModel->api_key,
                        'Content-Type' => 'application/json',
                    ])->get($baseUrl . $modelsEndpoint);

                    if ($response->successful()) {
                        // Try to parse the response based on common formats
                        $responseData = $response->json();
                        $discoveredModels = [];

                        // Generic parsing logic
                        if (isset($responseData['data']) && is_array($responseData['data'])) {
                            foreach ($responseData['data'] as $model) {
                                $modelId = $model['id'] ?? '';
                                $modelName = $model['name'] ?? $modelId;

                                $discoveredModels[$modelId] = [
                                    'name' => $modelId,
                                    'display_name' => $modelName,
                                    'description' => $model['description'] ?? '',
                                ];
                            }
                        } elseif (is_array($responseData)) {
                            // Try to parse as a direct array of models
                            foreach ($responseData as $model) {
                                if (is_array($model) && isset($model['id'])) {
                                    $modelId = $model['id'];
                                    $modelName = $model['name'] ?? $modelId;

                                    $discoveredModels[$modelId] = [
                                        'name' => $modelId,
                                        'display_name' => $modelName,
                                        'description' => $model['description'] ?? '',
                                    ];
                                }
                            }
                        }

                        // If we found models, return them
                        if (!empty($discoveredModels)) {
                            return response()->json([
                                'success' => true,
                                'message' => 'Models discovered successfully',
                                'models' => $discoveredModels,
                            ]);
                        } else {
                            // Return a generic error if we couldn't parse any models
                            return response()->json([
                                'success' => false,
                                'message' => 'Could not parse models from provider response',
                            ]);
                        }
                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to discover models: ' . $response->body(),
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to discover models for provider ' . $tempModel->provider . ': ' . $e->getMessage());
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to discover models',
                        'error' => $e->getMessage()
                    ], 500);
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to discover models directly: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to discover models',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format OpenAI model name for display
     *
     * @param string $modelId
     * @return string
     */
    protected function formatOpenAIModelName(string $modelId): string
    {
        // Convert model IDs like "gpt-4" to "GPT-4"
        $parts = explode('-', $modelId);
        $formattedParts = [];

        foreach ($parts as $part) {
            if ($part === 'gpt') {
                $formattedParts[] = 'GPT';
            } elseif ($part === 'o') {
                $formattedParts[] = 'o';
            } elseif (is_numeric($part)) {
                $formattedParts[] = $part;
            } elseif ($part === 'turbo') {
                $formattedParts[] = 'Turbo';
            } else {
                $formattedParts[] = ucfirst($part);
            }
        }

        return implode('-', $formattedParts);
    }
}
