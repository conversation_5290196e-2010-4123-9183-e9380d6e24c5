import { AIModelData, aiModelService } from "@/utils/ai-model-service"
import { Template } from "@/utils/template-service"
import { AIService, ChatRequestOptions, ChatResponse } from "./ai-service"
import { TemplateProcessingService } from "./template-processing-service"

/**
 * Provider Manager
 * 
 * Manages different AI providers and handles template integration
 */
export class ProviderManager {
    /**
     * Send a message to an AI model with template support
     * 
     * @param modelId The ID of the model to use
     * @param message The user message
     * @param options Additional options
     * @returns The AI response
     */
    public static async sendMessage(
        modelId: number,
        message: string,
        options: ChatRequestOptions = {}
    ): Promise<ChatResponse> {
        try {
            // 1. Fetch the model data
            const model = await aiModelService.getModel(modelId)
            if (!model) {
                throw new Error(`Model with ID ${modelId} not found`)
            }

            // 2. Check if the model has a template assigned
            let template: Template | null = null
            if (model.template_id) {
                template = await AIService.getTemplateForModel(model)
            }

            // 3. Send the message with the template if available
            return await AIService.sendChatMessage(model, message, template, options)
        } catch (error) {
            console.error("Error sending message:", error)
            throw error
        }
    }

    /**
     * Get the default AI model
     * 
     * @returns The default AI model or null if none is found
     */
    public static async getDefaultModel(): Promise<AIModelData | null> {
        try {
            const models = await aiModelService.getModels()
            const defaultModel = models.find(model => model.is_default && model.active)
            return defaultModel || null
        } catch (error) {
            console.error("Error getting default model:", error)
            return null
        }
    }

    /**
     * Get all active AI models
     * 
     * @returns Array of active AI models
     */
    public static async getActiveModels(): Promise<AIModelData[]> {
        try {
            const models = await aiModelService.getModels()
            return models.filter(model => model.active)
        } catch (error) {
            console.error("Error getting active models:", error)
            return []
        }
    }

    /**
     * Process a template for a specific model
     * 
     * @param modelId The model ID
     * @param variables The template variables
     * @returns The processed template content or null if no template is assigned
     */
    public static async processTemplateForModel(
        modelId: number,
        variables: Record<string, string> = {}
    ): Promise<string | null> {
        try {
            // 1. Fetch the model
            const model = await aiModelService.getModel(modelId)
            if (!model || !model.template_id) {
                return null
            }

            // 2. Fetch the template
            const template = await AIService.getTemplateForModel(model)
            if (!template) {
                return null
            }

            // 3. Process the template
            return TemplateProcessingService.process(template, variables)
        } catch (error) {
            console.error(`Error processing template for model ${modelId}:`, error)
            return null
        }
    }
} 