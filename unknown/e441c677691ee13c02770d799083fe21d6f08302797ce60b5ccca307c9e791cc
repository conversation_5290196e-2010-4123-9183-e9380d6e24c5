import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Combines multiple class names and applies Tailwind CSS specificity rules.
 * This function is used for conditional class name application throughout the app.
 * 
 * @param {...ClassValue[]} inputs - Class names to be combined
 * @returns {string} - Merged class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
