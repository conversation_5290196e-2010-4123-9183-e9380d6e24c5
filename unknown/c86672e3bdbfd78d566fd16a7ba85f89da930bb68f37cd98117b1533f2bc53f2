<?php

namespace App\Http\Controllers;

use App\Models\Widget;
use App\Models\PreChatFormTemplate;
use App\Models\PreChatFormField;
use App\Models\PreChatFormSubmission;
use App\Models\ChatSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PreChatFormController extends Controller
{
    /**
     * Get pre-chat form for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'widget_id' => 'required|string|exists:widgets,widget_id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::where('widget_id', $request->widget_id)
                          ->where('is_active', true)
                          ->firstOrFail();

            // Get the active pre-chat form template with its fields
            $template = PreChatFormTemplate::where('widget_id', $widget->id)
                                        ->where('is_active', true)
                                        ->with(['fields' => function($query) {
                                            $query->orderBy('order');
                                        }])
                                        ->first();

            if (!$template) {
                return response()->json(['message' => 'No active pre-chat form found'], 404);
            }

            return response()->json($template);
        } catch (\Exception $e) {
            Log::error('Failed to get pre-chat form: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get pre-chat form'], 500);
        }
    }

    /**
     * Submit pre-chat form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|integer|exists:pre_chat_form_templates,id',
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Check if the template is active
            $template = PreChatFormTemplate::where('id', $request->template_id)
                                        ->where('is_active', true)
                                        ->firstOrFail();

            // Get required fields
            $requiredFields = PreChatFormField::where('template_id', $template->id)
                                          ->where('is_required', true)
                                          ->pluck('name')
                                          ->toArray();

            // Validate required fields
            foreach ($requiredFields as $field) {
                if (!isset($request->data[$field]) || empty($request->data[$field])) {
                    return response()->json([
                        'error' => 'Validation failed',
                        'message' => "Field '$field' is required"
                    ], 422);
                }
            }

            // Create submission
            $submission = new PreChatFormSubmission([
                'template_id' => $request->template_id,
                'session_id' => $request->session_id,
                'data' => $request->data,
            ]);

            $submission->save();

            // Update chat session with user data
            $session = ChatSession::where('session_id', $request->session_id)->first();
            if ($session) {
                $metadata = $session->metadata ?? [];
                $session->metadata = array_merge($metadata, [
                    'pre_chat_data' => $request->data,
                    'pre_chat_submission_id' => $submission->id,
                ]);
                $session->save();
            }

            return response()->json([
                'message' => 'Form submitted successfully',
                'submission_id' => $submission->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to submit pre-chat form: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to submit pre-chat form'], 500);
        }
    }

    /**
     * Get all pre-chat form templates for a widget.
     */
    public function getTemplates(Request $request, $widgetId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);

            $templates = PreChatFormTemplate::where('widget_id', $widget->id)
                ->with(['fields' => function($query) {
                    $query->orderBy('order');
                }])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json($templates);
        } catch (\Exception $e) {
            Log::error('Failed to get pre-chat templates: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get pre-chat templates'], 500);
        }
    }

    /**
     * Create a new pre-chat form template.
     */
    public function createTemplate(Request $request, $widgetId)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'fields' => 'required|array',
            'fields.*.name' => 'required|string',
            'fields.*.type' => 'required|string|in:text,email,phone,textarea,select,checkbox,radio',
            'fields.*.label' => 'required|string',
            'fields.*.placeholder' => 'nullable|string',
            'fields.*.is_required' => 'boolean',
            'fields.*.options' => 'nullable|array',
            'fields.*.order' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::findOrFail($widgetId);

            $template = new PreChatFormTemplate([
                'widget_id' => $widget->id,
                'title' => $request->title,
                'description' => $request->description,
                'is_active' => false,
            ]);
            $template->save();

            // Create fields
            foreach ($request->fields as $index => $fieldData) {
                $field = new PreChatFormField([
                    'template_id' => $template->id,
                    'name' => $fieldData['name'],
                    'type' => $fieldData['type'],
                    'label' => $fieldData['label'],
                    'placeholder' => $fieldData['placeholder'] ?? null,
                    'is_required' => $fieldData['is_required'] ?? false,
                    'options' => $fieldData['options'] ?? null,
                    'order' => $fieldData['order'] ?? $index,
                ]);
                $field->save();
            }

            $template->load('fields');
            return response()->json($template, 201);
        } catch (\Exception $e) {
            Log::error('Failed to create pre-chat template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create pre-chat template'], 500);
        }
    }

    /**
     * Update a pre-chat form template.
     */
    public function updateTemplate(Request $request, $widgetId, $templateId)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'fields' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::findOrFail($widgetId);
            $template = PreChatFormTemplate::where('widget_id', $widget->id)
                ->findOrFail($templateId);

            $template->update([
                'title' => $request->title,
                'description' => $request->description,
            ]);

            // Delete existing fields and recreate
            PreChatFormField::where('template_id', $template->id)->delete();

            foreach ($request->fields as $index => $fieldData) {
                $field = new PreChatFormField([
                    'template_id' => $template->id,
                    'name' => $fieldData['name'],
                    'type' => $fieldData['type'],
                    'label' => $fieldData['label'],
                    'placeholder' => $fieldData['placeholder'] ?? null,
                    'is_required' => $fieldData['is_required'] ?? false,
                    'options' => $fieldData['options'] ?? null,
                    'order' => $fieldData['order'] ?? $index,
                ]);
                $field->save();
            }

            $template->load('fields');
            return response()->json($template);
        } catch (\Exception $e) {
            Log::error('Failed to update pre-chat template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update pre-chat template'], 500);
        }
    }

    /**
     * Delete a pre-chat form template.
     */
    public function deleteTemplate(Request $request, $widgetId, $templateId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);
            $template = PreChatFormTemplate::where('widget_id', $widget->id)
                ->findOrFail($templateId);

            // Delete fields first
            PreChatFormField::where('template_id', $template->id)->delete();

            // Delete template
            $template->delete();

            return response()->json(['message' => 'Template deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete pre-chat template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete pre-chat template'], 500);
        }
    }

    /**
     * Activate a pre-chat form template.
     */
    public function activateTemplate(Request $request, $widgetId, $templateId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);

            // Deactivate all templates for this widget
            PreChatFormTemplate::where('widget_id', $widget->id)
                ->update(['is_active' => false]);

            // Activate the selected template
            $template = PreChatFormTemplate::where('widget_id', $widget->id)
                ->findOrFail($templateId);
            $template->update(['is_active' => true]);

            return response()->json(['message' => 'Template activated successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to activate pre-chat template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to activate pre-chat template'], 500);
        }
    }
}
