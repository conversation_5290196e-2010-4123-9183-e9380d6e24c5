<?php

namespace App\Services\AI;

use App\Models\AIModel;

/**
 * A wrapper for AbstractProvider that prevents saving models to the database
 */
class NoSaveProvider
{
    /**
     * The wrapped provider
     * 
     * @var AbstractProvider
     */
    protected $provider;
    
    /**
     * Constructor
     * 
     * @param AbstractProvider $provider
     */
    public function __construct(AbstractProvider $provider)
    {
        $this->provider = $provider;
    }
    
    /**
     * Update model settings without saving to database
     * 
     * @param AIModel $model
     * @param array $discoveredModels
     * @return void
     */
    public function updateModelSettings(AIModel $model, array $discoveredModels): void
    {
        $settings = $model->settings ?? [];
        $settings['available_models'] = $discoveredModels;
        
        // If current model_name is invalid, update to a valid one
        if (!empty($discoveredModels)) {
            $currentModelName = $settings['model_name'] ?? null;
            
            if (!$currentModelName || !isset($discoveredModels[$currentModelName])) {
                // Find default model from config
                $defaultModel = $this->provider->getConfig()['default_model'] ?? null;
                
                // If default model exists in discovered models, use it
                if ($defaultModel && isset($discoveredModels[$defaultModel])) {
                    $settings['model_name'] = $defaultModel;
                } else {
                    // Otherwise use the first available model
                    $settings['model_name'] = array_key_first($discoveredModels);
                }
            }
        }
        
        $model->settings = $settings;
        // Don't save the model
    }
    
    /**
     * Proxy all other method calls to the wrapped provider
     * 
     * @param string $method
     * @param array $arguments
     * @return mixed
     */
    public function __call($method, $arguments)
    {
        return $this->provider->$method(...$arguments);
    }
}
