<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\WidgetService;
use App\Services\WidgetBehaviorService;
use App\Services\WidgetLogoService;
use App\Services\WebhookService;

class WidgetServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register WidgetBehaviorService
        $this->app->singleton(WidgetBehaviorService::class, function ($app) {
            return new WidgetBehaviorService();
        });

        // Register WidgetLogoService
        $this->app->singleton(WidgetLogoService::class, function ($app) {
            return new WidgetLogoService();
        });

        // Register WebhookService (already exists, but ensure it's registered)
        $this->app->singleton(WebhookService::class, function ($app) {
            return new WebhookService();
        });

        // Register WidgetService with its dependencies
        $this->app->singleton(WidgetService::class, function ($app) {
            return new WidgetService(
                $app->make(WidgetBehaviorService::class),
                $app->make(WidgetLogoService::class),
                $app->make(WebhookService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
