"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

interface PreChatFormProps {
    onSubmit: (data: Record<string, string>) => void
    onCancel: () => void
    fields?: PreChatFormField[]
    title?: string
    description?: string
}

interface PreChatFormField {
    id: string
    name: string
    label: string
    type: 'text' | 'email' | 'phone' | 'select' | 'checkbox'
    placeholder?: string
    options?: string[]
    isRequired: boolean
    validationPattern?: string
    errorMessage?: string
    order: number
}

export function PreChatForm({
    onSubmit,
    onCancel,
    fields = [],
    title = "Please introduce yourself",
    description = "Before we start chatting, please provide your information"
}: PreChatFormProps) {
    const [formData, setFormData] = useState<Record<string, string>>({
        name: "",
        email: ""
    })

    const [errors, setErrors] = useState<Record<string, string>>({})
    const [isSubmitting, setIsSubmitting] = useState(false)

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        // Basic validation for name
        if (!formData.name?.trim()) {
            newErrors.name = "Please enter your name"
        }

        // Basic validation for email
        if (formData.email?.trim()) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!emailRegex.test(formData.email)) {
                newErrors.email = "Please enter a valid email address"
            }
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = () => {
        if (validateForm()) {
            setIsSubmitting(true)
            onSubmit(formData)
            setIsSubmitting(false)
        }
    }

    return (
        <Card className="border-0 shadow-none w-full mx-auto">
            <CardHeader className="pb-4">
                <CardTitle className="text-lg">{title}</CardTitle>
                <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent className="pb-3">
                <Alert variant="default" className="mb-4 bg-blue-50/50 border-blue-100 text-blue-800">
                    <InfoIcon className="h-4 w-4 mr-2 text-blue-500" />
                    <AlertDescription className="text-sm">
                        Your information helps us provide you with better assistance.
                    </AlertDescription>
                </Alert>

                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="name" className="font-medium">
                            Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="name"
                            placeholder="Your name"
                            value={formData.name || ""}
                            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                            className={errors.name ? "border-red-500 focus-visible:ring-red-500" : ""}
                        />
                        {errors.name && (
                            <p className="text-xs text-red-500 mt-1">{errors.name}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="email" className="font-medium">
                            Email
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            placeholder="Your email"
                            value={formData.email || ""}
                            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                            className={errors.email ? "border-red-500 focus-visible:ring-red-500" : ""}
                        />
                        {errors.email && (
                            <p className="text-xs text-red-500 mt-1">{errors.email}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="message" className="font-medium">
                            Initial Message
                        </Label>
                        <Input
                            id="message"
                            placeholder="What would you like to discuss?"
                            value={formData.message || ""}
                            onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                        />
                    </div>
                </div>
            </CardContent>
            <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={onCancel}>
                    Skip
                </Button>
                <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className="relative"
                >
                    {isSubmitting ? (
                        <>
                            <span className="mr-2 h-4 w-4 rounded-full border-2 border-b-transparent border-white animate-spin inline-block"></span>
                            Processing...
                        </>
                    ) : (
                        "Start Chat"
                    )}
                </Button>
            </CardFooter>
        </Card>
    )
} 