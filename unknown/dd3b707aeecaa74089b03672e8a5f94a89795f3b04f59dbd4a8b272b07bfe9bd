<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_type');
            $table->unsignedBigInteger('file_size');
            $table->string('category')->nullable();
            $table->foreignId('source_id')->constrained('knowledge_sources')->onDelete('cascade');
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active_source')->default(true);
            $table->string('status')->default('ready');
            $table->boolean('has_embeddings')->default(false);
            $table->integer('embeddings_count')->default(0);
            $table->integer('embeddings_progress')->default(0);
            $table->string('embeddings_provider')->nullable();
            $table->string('embeddings_model')->nullable();
            $table->timestamp('embeddings_updated_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
