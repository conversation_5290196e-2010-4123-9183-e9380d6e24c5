<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Widget extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'widget_id',
        'version',
        'ai_model_id',
        'settings',
        'allowed_domains',
        'position_type',
        'position_settings',
        'custom_css',
        'behavior_rules',
        'logo_url',
        'typography',
        'button_customization',
        'mobile_settings',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
        'allowed_domains' => 'array',
        'position_settings' => 'array',
        'behavior_rules' => 'array',
        'typography' => 'array',
        'button_customization' => 'array',
        'mobile_settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Boot function from Laravel.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate widget_id when creating a new widget
        static::creating(function ($widget) {
            if (empty($widget->widget_id)) {
                $widget->widget_id = Str::random(12);
            }

            // Set default version if not provided
            if (empty($widget->version)) {
                $widget->version = '1.0';
            }
        });
    }

    /**
     * Get the user that owns the widget.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the AI model used by this widget.
     */
    public function aiModel()
    {
        return $this->belongsTo(AIModel::class, 'ai_model_id');
    }

    /**
     * Get the chat sessions for this widget.
     */
    public function chatSessions()
    {
        return $this->hasMany(ChatSession::class);
    }

    /**
     * Get the guest users for this widget.
     */
    public function guestUsers()
    {
        return $this->hasMany(GuestUser::class);
    }

    /**
     * Get the pre-chat form templates for this widget.
     */
    public function preChatFormTemplates()
    {
        return $this->hasMany(PreChatFormTemplate::class);
    }

    /**
     * Get the post-chat surveys for this widget.
     */
    public function postChatSurveys()
    {
        return $this->hasMany(PostChatSurvey::class);
    }

    /**
     * Get the behavior configuration for this widget.
     */
    public function behavior()
    {
        return $this->hasOne(WidgetBehavior::class);
    }

    /**
     * Get the webhooks for this widget.
     */
    public function webhooks()
    {
        return $this->hasMany(WidgetWebhook::class);
    }

    /**
     * Get the active webhooks for this widget.
     */
    public function activeWebhooks()
    {
        return $this->hasMany(WidgetWebhook::class)->where('is_active', true);
    }

    /**
     * Get the logo for this widget.
     */
    public function logo()
    {
        return $this->hasOne(WidgetLogo::class)->where('is_active', true);
    }

    /**
     * Check if a domain is allowed to embed this widget
     *
     * @param string $domain
     * @return bool
     */
    public function isDomainAllowed($domain)
    {
        // Clean input
        $domain = trim(strtolower($domain));

        // If no allowed domains are specified, allow all domains
        if (empty($this->allowed_domains)) {
            return true;
        }

        // If wildcard is in the allowed list, allow all domains
        if (in_array('*', $this->allowed_domains)) {
            return true;
        }

        // Check if exact domain is in the allowed list
        if (in_array($domain, $this->allowed_domains)) {
            return true;
        }

        // Check for wildcard domains (*.example.com)
        foreach ($this->allowed_domains as $allowedDomain) {
            if (strpos($allowedDomain, '*') === 0) {
                // Extract the domain part after the wildcard
                $wildcardDomain = substr($allowedDomain, 1); // Remove the * character

                // Check if domain ends with the wildcard domain part
                if ($wildcardDomain === '.' . $domain ||
                    substr($domain, -strlen($wildcardDomain)) === $wildcardDomain) {
                    return true;
                }
            }
        }

        return false;
    }
}
