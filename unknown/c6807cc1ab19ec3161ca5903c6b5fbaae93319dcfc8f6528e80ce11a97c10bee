<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContextSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',
        'priority',
        'context_retention',
        'relevance_threshold',
        'max_sources_per_query',
        'enabled_sources',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'priority' => 'array',
        'enabled_sources' => 'array',
        'relevance_threshold' => 'float',
        'max_sources_per_query' => 'integer',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * Get the project that owns the settings.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}
