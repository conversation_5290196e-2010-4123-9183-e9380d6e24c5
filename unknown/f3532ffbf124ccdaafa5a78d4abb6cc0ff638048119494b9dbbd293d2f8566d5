<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create context_settings table
        if (!Schema::hasTable('context_settings')) {
            Schema::create('context_settings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('project_id')->constrained()->onDelete('cascade');
                $table->json('priority')->nullable();
                $table->string('context_retention')->default('session');
                $table->float('relevance_threshold')->default(0.75);
                $table->integer('max_sources_per_query')->default(3);
                $table->json('enabled_sources')->nullable();
                $table->timestamps();

                // Add index for faster lookups
                $table->index('project_id');
            });
        }

        // Create context_rules table
        if (!Schema::hasTable('context_rules')) {
            Schema::create('context_rules', function (Blueprint $table) {
                $table->id();
                $table->foreignId('project_id')->constrained()->onDelete('cascade');
                $table->string('name');
                $table->text('description')->nullable();
                $table->json('sources')->nullable();
                $table->json('keywords')->nullable();
                $table->integer('priority')->default(10);
                $table->boolean('active')->default(true);
                $table->timestamps();

                // Add indexes for faster lookups
                $table->index('project_id');
                $table->index(['project_id', 'active']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('context_rules');
        Schema::dropIfExists('context_settings');
    }
};
