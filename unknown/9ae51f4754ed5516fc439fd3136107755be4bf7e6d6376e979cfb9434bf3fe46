'use client'

import { useFormContext } from 'react-hook-form'
import { FormField, FormItem, FormLabel, FormControl, FormDescription } from '@/components/ui/form'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent } from '@/components/ui/card'
import { useEffect, useState } from 'react'
import { Spinner } from '@/components/ui/spinner'
import api from '@/utils/api'

interface Project {
    id: number
    name: string
}

export function ModelKnowledgeTab() {
    const form = useFormContext()
    const [projects, setProjects] = useState<Project[]>([])
    const [isLoading, setIsLoading] = useState(true)

    // Load projects
    useEffect(() => {
        const fetchProjects = async () => {
            try {
                setIsLoading(true)
                const response = await api.get('/projects')
                if (response.data) {
                    setProjects(response.data)
                }
            } catch (error) {
                console.error('Error fetching projects:', error)
            } finally {
                setIsLoading(false)
            }
        }

        fetchProjects()
    }, [])

    return (
        <Card>
            <CardContent className="pt-6 space-y-4">
                {isLoading ? (
                    <div className="flex justify-center p-4">
                        <Spinner />
                    </div>
                ) : (
                    <>
                        <FormField
                            control={form.control}
                            name="settings.knowledge_base.enabled"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between space-y-0 rounded-md border p-4">
                                    <div className="space-y-0.5">
                                        <FormLabel className="text-base">Use Knowledge Base</FormLabel>
                                        <FormDescription>
                                            Enable this model to use knowledge base when generating responses
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        {form.watch('settings.knowledge_base.enabled') && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="settings.knowledge_base.project_id"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Knowledge Base Project</FormLabel>
                                            <Select
                                                value={field.value?.toString() || ''}
                                                onValueChange={(value) => field.onChange(parseInt(value, 10))}
                                            >
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select a project" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {projects.map((project) => (
                                                        <SelectItem
                                                            key={project.id}
                                                            value={project.id.toString()}
                                                        >
                                                            {project.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormDescription>
                                                Select the knowledge base project to use with this model
                                            </FormDescription>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="settings.knowledge_base.relevance_threshold"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>
                                                Relevance Threshold: {field.value?.toFixed(2) || '0.70'}
                                            </FormLabel>
                                            <FormControl>
                                                <Slider
                                                    min={0}
                                                    max={1}
                                                    step={0.05}
                                                    value={[field.value || 0.7]}
                                                    onValueChange={(values) => field.onChange(values[0])}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                Higher values ensure more relevant content but may return fewer results
                                            </FormDescription>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="settings.knowledge_base.max_sources"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>
                                                Max Sources Per Query: {field.value || 3}
                                            </FormLabel>
                                            <FormControl>
                                                <Slider
                                                    min={1}
                                                    max={10}
                                                    step={1}
                                                    value={[field.value || 3]}
                                                    onValueChange={(values) => field.onChange(values[0])}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                Maximum number of knowledge sources to include in each response
                                            </FormDescription>
                                        </FormItem>
                                    )}
                                />
                            </>
                        )}
                    </>
                )}
            </CardContent>
        </Card>
    )
} 