<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('scheduled_scrapes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('url', 2048);
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->unsignedBigInteger('source_id')->nullable(); // We'll add the foreign key constraint in a later migration
            $table->string('table_name');
            $table->string('format')->default('text');
            $table->foreignId('model_id')->nullable()->constrained('ai_models')->onDelete('set null');
            $table->string('frequency')->default('daily'); // hourly, daily, weekly, monthly
            $table->string('status')->default('active');
            $table->json('options')->nullable();
            $table->timestamp('next_run')->nullable();
            $table->timestamp('last_run')->nullable();
            $table->timestamp('last_success')->nullable();
            $table->timestamp('last_error')->nullable();
            $table->text('last_error_message')->nullable();
            $table->integer('success_count')->default(0);
            $table->integer('error_count')->default(0);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index('next_run');
            $table->index('status');
            $table->index('frequency');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('scheduled_scrapes');
    }
};
