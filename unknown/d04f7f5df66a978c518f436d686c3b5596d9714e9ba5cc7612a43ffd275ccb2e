"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { useToast } from "@/components/ui/use-toast"
import api from "@/utils/api"
import { PreChatFormTemplate, PreChatFormField } from "@/types/widget"

interface UsePreChatFormOptions {
  widgetId: number
}

interface CreateTemplateData {
  title: string
  description: string
  fields: Omit<PreChatFormField, "id" | "templateId">[]
}

interface UpdateTemplateData {
  id: number
  title?: string
  description?: string
  fields?: Omit<PreChatFormField, "id" | "templateId">[]
  isActive?: boolean
}

/**
 * Hook for managing pre-chat form templates
 * 
 * Provides functions for fetching, creating, updating, and deleting pre-chat form templates.
 */
export function usePreChatForm({ widgetId }: UsePreChatFormOptions) {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  
  // Fetch pre-chat form templates for a widget
  const {
    data: templates,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["pre-chat-templates", widgetId],
    queryFn: async () => {
      try {
        const response = await api.get(`/api/widgets/${widgetId}/pre-chat-templates`)
        return response.data
      } catch (error) {
        console.error("Error fetching pre-chat templates:", error)
        throw error
      }
    },
    enabled: !!widgetId
  })
  
  // Get active template
  const activeTemplate = templates?.find((template: PreChatFormTemplate) => template.isActive)
  
  // Create a new pre-chat form template
  const createTemplateMutation = useMutation({
    mutationFn: async (data: CreateTemplateData) => {
      const response = await api.post(`/api/widgets/${widgetId}/pre-chat-templates`, data)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pre-chat-templates", widgetId] })
      toast({
        title: "Template created",
        description: "Pre-chat form template has been created successfully.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error("Error creating template:", error)
      toast({
        title: "Error",
        description: "Failed to create pre-chat form template.",
        variant: "destructive",
      })
    }
  })
  
  // Update an existing pre-chat form template
  const updateTemplateMutation = useMutation({
    mutationFn: async (data: UpdateTemplateData) => {
      const response = await api.put(`/api/widgets/${widgetId}/pre-chat-templates/${data.id}`, data)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pre-chat-templates", widgetId] })
      toast({
        title: "Template updated",
        description: "Pre-chat form template has been updated successfully.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error("Error updating template:", error)
      toast({
        title: "Error",
        description: "Failed to update pre-chat form template.",
        variant: "destructive",
      })
    }
  })
  
  // Delete a pre-chat form template
  const deleteTemplateMutation = useMutation({
    mutationFn: async (templateId: number) => {
      const response = await api.delete(`/api/widgets/${widgetId}/pre-chat-templates/${templateId}`)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pre-chat-templates", widgetId] })
      toast({
        title: "Template deleted",
        description: "Pre-chat form template has been deleted successfully.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error("Error deleting template:", error)
      toast({
        title: "Error",
        description: "Failed to delete pre-chat form template.",
        variant: "destructive",
      })
    }
  })
  
  // Set a template as active
  const setActiveTemplateMutation = useMutation({
    mutationFn: async (templateId: number) => {
      const response = await api.put(`/api/widgets/${widgetId}/pre-chat-templates/${templateId}/activate`)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pre-chat-templates", widgetId] })
      toast({
        title: "Template activated",
        description: "Pre-chat form template has been set as active.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error("Error activating template:", error)
      toast({
        title: "Error",
        description: "Failed to activate pre-chat form template.",
        variant: "destructive",
      })
    }
  })
  
  // Create a new template
  const createTemplate = (data: CreateTemplateData) => {
    createTemplateMutation.mutate(data)
  }
  
  // Update an existing template
  const updateTemplate = (data: UpdateTemplateData) => {
    updateTemplateMutation.mutate(data)
  }
  
  // Delete a template
  const deleteTemplate = (templateId: number) => {
    deleteTemplateMutation.mutate(templateId)
  }
  
  // Set a template as active
  const setActiveTemplate = (templateId: number) => {
    setActiveTemplateMutation.mutate(templateId)
  }
  
  return {
    templates,
    activeTemplate,
    isLoading,
    error,
    refetch,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setActiveTemplate,
    isCreating: createTemplateMutation.isPending,
    isUpdating: updateTemplateMutation.isPending,
    isDeleting: deleteTemplateMutation.isPending,
    isActivating: setActiveTemplateMutation.isPending
  }
}
