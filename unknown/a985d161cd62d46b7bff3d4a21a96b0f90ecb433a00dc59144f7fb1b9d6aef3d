import { useState } from "react";
import { widgetService } from "@/utils/widgetService";
import { useToast } from "@/hooks/use-toast";

interface WidgetConfig {
  id: string; // public id (for fallback)
  numericId?: number; // numeric DB id for API
  primaryColor: string;
  borderRadius: string;
  position: string;
  initialMessage: string;
}

export function useEmbedCode() {
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [embedCodes, setEmbedCodes] = useState<Record<string, string>>({
    standard: '',
    iframe: '',
    webcomponent: ''
  });
  const { toast } = useToast();

  const generateEmbedCode = async (type: string, widgetConfig: WidgetConfig) => {
    // Map UI tab value to backend type
    let backendType = type;
    if (type === 'webcomponent') backendType = 'web-component';
    // Check if we've already generated this type of embed code
    if (embedCodes[type]) {
      return embedCodes[type];
    }

    try {
      setIsLoading(true);

      // Call API to generate embed code
      if (widgetConfig.numericId) {
        try {
          const response = await widgetService.generateEmbedCode(widgetConfig.numericId, {
            type: backendType as 'standard' | 'iframe' | 'web-component',
            customizations: {
              primaryColor: widgetConfig.primaryColor,
              borderRadius: widgetConfig.borderRadius,
              position: widgetConfig.position,
              initialMessage: widgetConfig.initialMessage
            }
          });
          const code = response.data.embed_code;
          setEmbedCodes(prev => ({
            ...prev,
            [type]: code
          }));
          return code;
        } catch (apiError) {
          console.warn("API error generating embed code, falling back to client-side generation:", apiError);
          // Fall back to client-side generation if API fails
          return fallbackGenerateEmbedCode(type, widgetConfig);
        }
      } else {
        // Fallback to client-side generation if no numericId
        return fallbackGenerateEmbedCode(type, widgetConfig);
      }
    } catch (error) {
      console.error("Error generating embed code:", error);
      toast({
        title: "Error",
        description: "Failed to generate embed code",
        variant: "destructive"
      });
      // Fall back to client-side generation if API fails
      return fallbackGenerateEmbedCode(type, widgetConfig);
    } finally {
      setIsLoading(false);
    }
  };

  // Fallback embed code generation in case the API call fails
  const fallbackGenerateEmbedCode = (type: string, widgetConfig: WidgetConfig) => {
    const baseUrl = typeof window !== "undefined"
      ? window.location.origin
      : "http://localhost:9090"; // fallback for SSR

    if (type === "standard") {
      return `<!-- CHAT WIDGET EMBED CODE - PASTE THIS IN YOUR WEBSITE -->
<!-- Add this code just before the closing </body> tag of your website -->

<script src="${baseUrl}/widget/v1/script.js"
  data-widget-id="${widgetConfig.id}"
  data-primary-color="${widgetConfig.primaryColor}"
  data-border-radius="${widgetConfig.borderRadius}"
  data-position="${widgetConfig.position || 'bottom-right'}"
  data-initial-message="${widgetConfig.initialMessage || 'How can I help you today?'}"
  data-persist-conversation="${widgetConfig.persistConversation || 'false'}"
  async>
</script>`;
    } else if (type === "iframe") {
      return `<!-- CHAT WIDGET EMBED CODE - PASTE THIS IN YOUR WEBSITE -->
<!-- Add this code where you want the chat widget to appear -->

<iframe
  src="${baseUrl}/widget/v1.0/iframe/${widgetConfig.id}"
  id="ai-chat-iframe-${widgetConfig.id}"
  style="position: fixed; bottom: 20px; right: 20px; width: 50px; height: 50px; border: none; z-index: 9999;"
  allow="microphone; camera"
  loading="lazy"
  data-allowed-domains="*"
  data-guest-flow="auto"
  title="AI Chat Widget">
</iframe>

<script>
  // Listen for messages from the widget iframe
  window.addEventListener("message", function(event) {
    // Verify source in production
    const iframe = document.getElementById("ai-chat-iframe-${widgetConfig.id}");

    // Handle resize requests from the widget
    if (event.data.type === "resize_widget") {
      iframe.style.width = event.data.width + "px";
      iframe.style.height = event.data.height + "px";
    }

    // Handle widget state changes
    if (event.data.type === "widget_opened") {
      console.log("Chat widget opened");
    }

    if (event.data.type === "widget_closed") {
      console.log("Chat widget closed");
    }
  });
</script>`;
    } else if (type === "webcomponent" || type === "web-component") {
      return `<!-- CHAT WIDGET EMBED CODE - PASTE THIS IN YOUR WEBSITE -->
<!-- Add this code just before the closing </body> tag of your website -->

<!-- First, load the web component script -->
<script src="${baseUrl}/widget/v1/web-component.js" async></script>

<!-- Then add the widget component -->
<ai-chat-widget
  widget-id="${widgetConfig.id}"
  primary-color="${widgetConfig.primaryColor}"
  border-radius="${widgetConfig.borderRadius || '8'}"
  position="${widgetConfig.position || 'bottom-right'}"
  initial-message="${widgetConfig.initialMessage || 'How can I help you today?'}"
  persist-conversation="${widgetConfig.persistConversation || 'false'}"
  api-base-url="${baseUrl}">
</ai-chat-widget>`;
    } else {
      toast({
        title: "Error",
        description: `Unknown embed code type: ${type}`,
        variant: "destructive"
      });
      return "";
    }
  };

  const getEmbedDescription = (type: string) => {
    if (type === "standard") {
      return "The standard integration adds a small script to your website that will load the widget when needed. Simply copy and paste this code just before the closing </body> tag of your website. No coding knowledge required!";
    } else if (type === "iframe") {
      return "The iframe integration is the simplest way to add the chat widget to your website. Just copy and paste this code into your HTML where you want the widget to appear. Works on any website platform.";
    } else {
      return "The Web Component integration offers more control over styling. Copy and paste this code just before the closing </body> tag of your website. Works best on modern websites.";
    }
  };

  return {
    copied,
    setCopied,
    isLoading,
    generateEmbedCode,
    getEmbedDescription
  };
}
