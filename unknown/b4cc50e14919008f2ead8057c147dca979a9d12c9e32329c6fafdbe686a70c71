<?php

namespace App\Http\Controllers;

use App\Models\Widget;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\PreChatFormSubmission;
use App\Models\SurveyResponse;
use App\Models\WidgetAnalytics;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class WidgetStatisticsController extends Controller
{
    /**
     * Get comprehensive statistics for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $widgetId
     * @return \Illuminate\Http\Response
     */
    public function getStats(Request $request, $widgetId)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'string|in:24h,7d,30d,90d,1y',
            'timezone' => 'string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::findOrFail($widgetId);
            $period = $request->input('period', '30d');
            $timezone = $request->input('timezone', 'UTC');

            $startDate = $this->getStartDate($period, $timezone);

            $stats = [
                'overview' => $this->getOverviewStats($widget->id, $startDate),
                'conversations' => $this->getConversationStats($widget->id, $startDate),
                'engagement' => $this->getEngagementStats($widget->id, $startDate),
                'forms' => $this->getFormStats($widget->id, $startDate),
                'surveys' => $this->getSurveyStats($widget->id, $startDate),
                'timeline' => $this->getTimelineStats($widget->id, $startDate, $period),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Failed to get widget statistics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get widget statistics'], 500);
        }
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($widgetId, $startDate)
    {
        $totalSessions = ChatSession::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $totalMessages = ChatMessage::whereHas('session', function($query) use ($widgetId) {
                $query->where('widget_id', $widgetId);
            })
            ->where('created_at', '>=', $startDate)
            ->count();

        $totalViews = WidgetAnalytics::where('widget_id', $widgetId)
            ->where('event_type', 'view')
            ->where('created_at', '>=', $startDate)
            ->count();

        $avgSessionDuration = ChatSession::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('ended_at')
            ->avg(DB::raw('TIMESTAMPDIFF(SECOND, created_at, ended_at)'));

        return [
            'total_sessions' => $totalSessions,
            'total_messages' => $totalMessages,
            'total_views' => $totalViews,
            'avg_session_duration' => round($avgSessionDuration ?? 0),
            'conversion_rate' => $totalViews > 0 ? round(($totalSessions / $totalViews) * 100, 2) : 0,
        ];
    }

    /**
     * Get conversation statistics.
     */
    private function getConversationStats($widgetId, $startDate)
    {
        $sessions = ChatSession::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->get();

        $completedSessions = $sessions->where('ended_at', '!=', null)->count();
        $activeSessions = $sessions->where('ended_at', null)->count();

        $avgMessagesPerSession = $sessions->count() > 0 
            ? ChatMessage::whereIn('session_id', $sessions->pluck('id'))
                ->count() / $sessions->count()
            : 0;

        return [
            'completed_sessions' => $completedSessions,
            'active_sessions' => $activeSessions,
            'avg_messages_per_session' => round($avgMessagesPerSession, 1),
            'completion_rate' => $sessions->count() > 0 
                ? round(($completedSessions / $sessions->count()) * 100, 2) 
                : 0,
        ];
    }

    /**
     * Get engagement statistics.
     */
    private function getEngagementStats($widgetId, $startDate)
    {
        $analytics = WidgetAnalytics::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->get();

        $events = $analytics->groupBy('event_type')->map->count();

        return [
            'widget_loads' => $events->get('load', 0),
            'widget_opens' => $events->get('open', 0),
            'widget_closes' => $events->get('close', 0),
            'button_clicks' => $events->get('click', 0),
            'engagement_rate' => $events->get('load', 0) > 0 
                ? round(($events->get('open', 0) / $events->get('load', 0)) * 100, 2)
                : 0,
        ];
    }

    /**
     * Get form statistics.
     */
    private function getFormStats($widgetId, $startDate)
    {
        $submissions = PreChatFormSubmission::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $sessions = ChatSession::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->count();

        return [
            'total_submissions' => $submissions,
            'completion_rate' => $sessions > 0 
                ? round(($submissions / $sessions) * 100, 2)
                : 0,
        ];
    }

    /**
     * Get survey statistics.
     */
    private function getSurveyStats($widgetId, $startDate)
    {
        $responses = SurveyResponse::whereHas('survey', function($query) use ($widgetId) {
                $query->where('widget_id', $widgetId);
            })
            ->where('created_at', '>=', $startDate)
            ->get();

        $avgRating = $responses->where('answers.rating', '!=', null)
            ->avg('answers.rating');

        return [
            'total_responses' => $responses->count(),
            'avg_rating' => $avgRating ? round($avgRating, 1) : null,
            'satisfaction_rate' => $responses->count() > 0 
                ? round($responses->where('answers.resolved', true)->count() / $responses->count() * 100, 2)
                : 0,
        ];
    }

    /**
     * Get timeline statistics.
     */
    private function getTimelineStats($widgetId, $startDate, $period)
    {
        $interval = $this->getTimelineInterval($period);
        
        $sessions = ChatSession::where('widget_id', $widgetId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw("DATE_FORMAT(created_at, '{$interval}') as period, COUNT(*) as count")
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        $messages = ChatMessage::whereHas('session', function($query) use ($widgetId) {
                $query->where('widget_id', $widgetId);
            })
            ->where('created_at', '>=', $startDate)
            ->selectRaw("DATE_FORMAT(created_at, '{$interval}') as period, COUNT(*) as count")
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        return [
            'sessions' => $sessions,
            'messages' => $messages,
        ];
    }

    /**
     * Get start date based on period.
     */
    private function getStartDate($period, $timezone)
    {
        $now = Carbon::now($timezone);
        
        switch ($period) {
            case '24h':
                return $now->subHours(24);
            case '7d':
                return $now->subDays(7);
            case '30d':
                return $now->subDays(30);
            case '90d':
                return $now->subDays(90);
            case '1y':
                return $now->subYear();
            default:
                return $now->subDays(30);
        }
    }

    /**
     * Get timeline interval format.
     */
    private function getTimelineInterval($period)
    {
        switch ($period) {
            case '24h':
                return '%Y-%m-%d %H:00:00';
            case '7d':
                return '%Y-%m-%d';
            case '30d':
                return '%Y-%m-%d';
            case '90d':
                return '%Y-%u';
            case '1y':
                return '%Y-%m';
            default:
                return '%Y-%m-%d';
        }
    }

    /**
     * Get real-time statistics.
     */
    public function getRealTimeStats(Request $request, $widgetId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);

            $stats = [
                'active_sessions' => ChatSession::where('widget_id', $widgetId)
                    ->whereNull('ended_at')
                    ->count(),
                'sessions_today' => ChatSession::where('widget_id', $widgetId)
                    ->whereDate('created_at', Carbon::today())
                    ->count(),
                'messages_today' => ChatMessage::whereHas('session', function($query) use ($widgetId) {
                        $query->where('widget_id', $widgetId);
                    })
                    ->whereDate('created_at', Carbon::today())
                    ->count(),
                'last_activity' => ChatMessage::whereHas('session', function($query) use ($widgetId) {
                        $query->where('widget_id', $widgetId);
                    })
                    ->latest()
                    ->first()?->created_at,
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Failed to get real-time statistics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get real-time statistics'], 500);
        }
    }
}
