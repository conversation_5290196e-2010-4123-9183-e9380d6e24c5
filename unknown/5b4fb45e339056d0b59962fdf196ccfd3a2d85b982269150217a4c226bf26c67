import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import {
    Globe, RefreshCw, Link2, Table, FileJson,
    FileText, CheckCircle2, XCircle, Eye, Trash2,
    Database, BookOpen, ChevronDown, ChevronUp, Download,
    Clock, Calendar, Play, Pause, AlertTriangle, Info
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { AIModelSelector } from '@/components/ai-models/ai-model-selector'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'

export default function ScheduledScrapingTab({
    projects,
    selectedProjectId,
    setSelectedProjectId,
    refreshTrigger,
    onRefresh
}) {
    // State
    const [scheduledScrapes, setScheduledScrapes] = useState([])
    const [isLoading, setIsLoading] = useState(false)
    const [isLoadingTables, setIsLoadingTables] = useState(false)
    const [scrapedContentTables, setScrapedContentTables] = useState([])
    const [showCreateDialog, setShowCreateDialog] = useState(false)
    const [showAuthDialog, setShowAuthDialog] = useState(false)
    const [authType, setAuthType] = useState('none')

    // Form state
    const [formData, setFormData] = useState({
        id: null,
        name: '',
        url: '',
        project_id: selectedProjectId,
        table_name: '',
        format: 'text',
        frequency: 'daily',
        model_id: null,
        status: 'active',
        options: {
            render_js: true,
            use_cache: true,
            scroll_page: false,
            wait_for_selector: ''
        },
        auth: {
            username: '',
            password: '',
            cookies: [],
            form: {
                url: '',
                username_selector: '',
                password_selector: '',
                submit_selector: '',
                username: '',
                password: ''
            }
        }
    })

    // Fetch scheduled scrapes when project changes
    useEffect(() => {
        if (selectedProjectId) {
            fetchScheduledScrapes()
            fetchScrapedContentTables()
        }
    }, [selectedProjectId, refreshTrigger])

    // Fetch scheduled scrapes
    const fetchScheduledScrapes = async () => {
        if (!selectedProjectId) return

        setIsLoading(true)

        try {
            const response = await knowledgeBaseService.getScheduledScrapes({
                project_id: selectedProjectId
            })

            if (response?.data) {
                setScheduledScrapes(response.data.data || [])
            }
        } catch (error) {
            console.error('Failed to load scheduled scrapes:', error)
            toast.error('Failed to load scheduled scrapes')
        } finally {
            setIsLoading(false)
        }
    }

    // Fetch scraped content tables
    const fetchScrapedContentTables = async () => {
        setIsLoadingTables(true)

        try {
            const response = await knowledgeBaseService.getScrapedContentTables()

            if (response?.data) {
                setScrapedContentTables(response.data)

                // If we have tables and no table name is set, use the first one
                if (response.data.length > 0 && !formData.table_name) {
                    setFormData(prev => ({
                        ...prev,
                        table_name: response.data[0].name
                    }))
                }
            }
        } catch (error) {
            console.error('Failed to load scraped content tables:', error)
            toast.error('Failed to load scraped content tables')
        } finally {
            setIsLoadingTables(false)
        }
    }

    // Handle form input changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    // Handle options changes
    const handleOptionsChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            options: {
                ...prev.options,
                [field]: value
            }
        }))
    }

    // Handle auth changes
    const handleAuthChange = (field, value) => {
        if (field === 'type') {
            setAuthType(value)
            return
        }

        setFormData(prev => ({
            ...prev,
            auth: {
                ...prev.auth,
                [field]: value
            }
        }))
    }

    // Handle form auth changes
    const handleFormAuthChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            auth: {
                ...prev.auth,
                form: {
                    ...prev.auth.form,
                    [field]: value
                }
            }
        }))
    }

    // Reset form
    const resetForm = () => {
        setFormData({
            id: null,
            name: '',
            url: '',
            project_id: selectedProjectId,
            table_name: scrapedContentTables.length > 0 ? scrapedContentTables[0].name : '',
            format: 'text',
            frequency: 'daily',
            model_id: null,
            status: 'active',
            options: {
                render_js: true,
                use_cache: true,
                scroll_page: false,
                wait_for_selector: ''
            },
            auth: {
                username: '',
                password: '',
                cookies: [],
                form: {
                    url: '',
                    username_selector: '',
                    password_selector: '',
                    submit_selector: '',
                    username: '',
                    password: ''
                }
            }
        })
        setAuthType('none')
    }

    // Handle save
    const handleSave = async () => {
        if (!formData.name || !formData.url || !formData.table_name) {
            toast.error('Please fill in all required fields')
            return
        }

        setIsLoading(true)

        try {
            // Prepare data
            const data = { ...formData }

            // Set project ID
            data.project_id = selectedProjectId

            // Handle auth based on type
            if (authType === 'none') {
                delete data.auth
            } else if (authType === 'basic') {
                data.auth = {
                    username: formData.auth.username,
                    password: formData.auth.password
                }
            } else if (authType === 'form') {
                data.auth = {
                    form: formData.auth.form
                }
            }

            const response = await knowledgeBaseService.saveScheduledScrape(data)

            if (response?.success) {
                toast.success(response.message || 'Scheduled scrape saved successfully')
                setShowCreateDialog(false)
                resetForm()
                fetchScheduledScrapes()
            } else {
                toast.error(response?.message || 'Failed to save scheduled scrape')
            }
        } catch (error) {
            console.error('Save error:', error)
            toast.error('Error saving scheduled scrape: ' + (error.response?.data?.message || error.message))
        } finally {
            setIsLoading(false)
        }
    }

    // Handle edit
    const handleEdit = (item) => {
        setFormData({
            id: item.id,
            name: item.name,
            url: item.url,
            project_id: item.project_id,
            table_name: item.table_name,
            format: item.format,
            frequency: item.frequency,
            model_id: item.model_id,
            status: item.status,
            options: item.options || {
                render_js: true,
                use_cache: true,
                scroll_page: false,
                wait_for_selector: ''
            },
            auth: item.options?.auth || {
                username: '',
                password: '',
                cookies: [],
                form: {
                    url: '',
                    username_selector: '',
                    password_selector: '',
                    submit_selector: '',
                    username: '',
                    password: ''
                }
            }
        })

        // Set auth type
        if (item.options?.auth) {
            if (item.options.auth.username && item.options.auth.password) {
                setAuthType('basic')
            } else if (item.options.auth.form) {
                setAuthType('form')
            } else {
                setAuthType('none')
            }
        } else {
            setAuthType('none')
        }

        setShowCreateDialog(true)
    }

    // Handle delete
    const handleDelete = async (id) => {
        if (!confirm('Are you sure you want to delete this scheduled scrape?')) return

        try {
            const response = await knowledgeBaseService.deleteScheduledScrape(id)

            if (response?.success) {
                toast.success('Scheduled scrape deleted successfully')
                fetchScheduledScrapes()
            } else {
                toast.error('Failed to delete scheduled scrape')
            }
        } catch (error) {
            console.error('Delete error:', error)
            toast.error('Error deleting scheduled scrape')
        }
    }

    // Handle run now
    const handleRunNow = async (id) => {
        try {
            const response = await knowledgeBaseService.runScheduledScrape(id)

            if (response?.success) {
                toast.success('Scheduled scrape has been queued to run')
            } else {
                toast.error('Failed to run scheduled scrape')
            }
        } catch (error) {
            console.error('Run error:', error)
            toast.error('Error running scheduled scrape')
        }
    }

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'Never'
        return new Date(dateString).toLocaleString()
    }

    // Get frequency display
    const getFrequencyDisplay = (frequency) => {
        switch (frequency) {
            case 'hourly': return 'Every hour'
            case 'daily': return 'Every day'
            case 'weekly': return 'Every week'
            case 'monthly': return 'Every month'
            default: return frequency
        }
    }

    // Get status badge
    const getStatusBadge = (status) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800 border-green-300">Active</Badge>
            case 'paused':
                return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">Paused</Badge>
            default:
                return <Badge variant="outline">{status}</Badge>
        }
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <div>
                        <h2 className="text-2xl font-bold">Scheduled Scraping</h2>
                        <p className="text-muted-foreground">
                            Schedule automatic scraping of websites at regular intervals
                        </p>
                    </div>

                    <ProjectSelector
                        projects={projects}
                        selectedProjectId={selectedProjectId}
                        setSelectedProjectId={setSelectedProjectId}
                    />
                </div>

                <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchScheduledScrapes}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>

                    <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                        <DialogTrigger asChild>
                            <Button size="sm" onClick={() => {
                                resetForm()
                                setShowCreateDialog(true)
                            }}>
                                <Clock className="h-4 w-4 mr-2" />
                                Create Schedule
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                            <DialogHeader>
                                <DialogTitle>{formData.id ? 'Edit' : 'Create'} Scheduled Scrape</DialogTitle>
                                <DialogDescription>
                                    Set up automatic scraping of a website at regular intervals
                                </DialogDescription>
                            </DialogHeader>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Name</Label>
                                    <Input
                                        id="name"
                                        placeholder="Daily News Scrape"
                                        value={formData.name}
                                        onChange={e => handleInputChange('name', e.target.value)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="url">URL</Label>
                                    <Input
                                        id="url"
                                        placeholder="https://example.com"
                                        value={formData.url}
                                        onChange={e => handleInputChange('url', e.target.value)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="frequency">Frequency</Label>
                                    <Select
                                        value={formData.frequency}
                                        onValueChange={value => handleInputChange('frequency', value)}
                                    >
                                        <SelectTrigger id="frequency">
                                            <SelectValue placeholder="Select frequency" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="hourly">Hourly</SelectItem>
                                            <SelectItem value="daily">Daily</SelectItem>
                                            <SelectItem value="weekly">Weekly</SelectItem>
                                            <SelectItem value="monthly">Monthly</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="format">Format</Label>
                                    <Select
                                        value={formData.format}
                                        onValueChange={value => handleInputChange('format', value)}
                                    >
                                        <SelectTrigger id="format">
                                            <SelectValue placeholder="Select format" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="text">Text</SelectItem>
                                            <SelectItem value="raw">HTML</SelectItem>
                                            <SelectItem value="table">Table</SelectItem>
                                            <SelectItem value="json">JSON</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="table-name">Table Name</Label>
                                    {isLoadingTables ? (
                                        <div className="flex items-center space-x-2">
                                            <Input
                                                id="table-name"
                                                placeholder="Loading tables..."
                                                disabled
                                            />
                                            <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
                                        </div>
                                    ) : scrapedContentTables.length > 0 ? (
                                        <Select
                                            value={formData.table_name}
                                            onValueChange={value => handleInputChange('table_name', value)}
                                        >
                                            <SelectTrigger id="table-name">
                                                <SelectValue placeholder="Select a table" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {scrapedContentTables.map(table => (
                                                    <SelectItem key={table.name} value={table.name}>
                                                        {table.name} ({table.count} records)
                                                    </SelectItem>
                                                ))}
                                                <SelectItem value="new_table">
                                                    + Create new table
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <Input
                                            id="table-name"
                                            placeholder="scraped_content"
                                            value={formData.table_name}
                                            onChange={e => handleInputChange('table_name', e.target.value)}
                                        />
                                    )}
                                    {formData.table_name === 'new_table' && (
                                        <Input
                                            className="mt-2"
                                            placeholder="Enter new table name"
                                            onChange={e => handleInputChange('table_name', e.target.value)}
                                        />
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={formData.status}
                                        onValueChange={value => handleInputChange('status', value)}
                                    >
                                        <SelectTrigger id="status">
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="paused">Paused</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="ai-model-selector">Processing Model (Optional)</Label>
                                    <AIModelSelector
                                        id="ai-model-selector"
                                        selectedModelId={formData.model_id}
                                        onModelChange={(value) => {
                                            handleInputChange('model_id', value);
                                        }}
                                        placeholder="Select AI model"
                                        isOptional={true}
                                        className="w-full"
                                    />
                                </div>

                                <div className="md:col-span-2">
                                    <Accordion type="single" collapsible className="w-full">
                                        <AccordionItem value="options">
                                            <AccordionTrigger>Advanced Options</AccordionTrigger>
                                            <AccordionContent>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                                                    <div className="space-y-2">
                                                        <div className="flex items-center justify-between">
                                                            <Label htmlFor="render-js" className="cursor-pointer">
                                                                Render JavaScript
                                                            </Label>
                                                            <Switch
                                                                id="render-js"
                                                                checked={formData.options.render_js}
                                                                onCheckedChange={value => handleOptionsChange('render_js', value)}
                                                            />
                                                        </div>
                                                        <p className="text-xs text-muted-foreground">
                                                            Use headless browser to render JavaScript-heavy pages
                                                        </p>
                                                    </div>

                                                    <div className="space-y-2">
                                                        <div className="flex items-center justify-between">
                                                            <Label htmlFor="use-cache" className="cursor-pointer">
                                                                Use Cache
                                                            </Label>
                                                            <Switch
                                                                id="use-cache"
                                                                checked={formData.options.use_cache}
                                                                onCheckedChange={value => handleOptionsChange('use_cache', value)}
                                                            />
                                                        </div>
                                                        <p className="text-xs text-muted-foreground">
                                                            Use cached content if available (faster)
                                                        </p>
                                                    </div>

                                                    <div className="space-y-2">
                                                        <div className="flex items-center justify-between">
                                                            <Label htmlFor="scroll-page" className="cursor-pointer">
                                                                Scroll Page
                                                            </Label>
                                                            <Switch
                                                                id="scroll-page"
                                                                checked={formData.options.scroll_page}
                                                                onCheckedChange={value => handleOptionsChange('scroll_page', value)}
                                                            />
                                                        </div>
                                                        <p className="text-xs text-muted-foreground">
                                                            Scroll to bottom to trigger lazy loading
                                                        </p>
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="wait-for-selector">Wait for Selector</Label>
                                                        <Input
                                                            id="wait-for-selector"
                                                            placeholder=".content, #main, etc."
                                                            value={formData.options.wait_for_selector}
                                                            onChange={e => handleOptionsChange('wait_for_selector', e.target.value)}
                                                        />
                                                        <p className="text-xs text-muted-foreground">
                                                            CSS selector to wait for before capturing content
                                                        </p>
                                                    </div>
                                                </div>
                                            </AccordionContent>
                                        </AccordionItem>

                                        <AccordionItem value="auth">
                                            <AccordionTrigger>Authentication</AccordionTrigger>
                                            <AccordionContent>
                                                <div className="space-y-4 pt-4">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="auth-type">Authentication Type</Label>
                                                        <Select
                                                            value={authType}
                                                            onValueChange={value => handleAuthChange('type', value)}
                                                        >
                                                            <SelectTrigger id="auth-type">
                                                                <SelectValue placeholder="Select authentication type" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="none">None</SelectItem>
                                                                <SelectItem value="basic">Basic (Username/Password)</SelectItem>
                                                                <SelectItem value="form">Form-based Login</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                    </div>

                                                    {authType === 'basic' && (
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="space-y-2">
                                                                <Label htmlFor="auth-username">Username</Label>
                                                                <Input
                                                                    id="auth-username"
                                                                    placeholder="Username"
                                                                    value={formData.auth.username}
                                                                    onChange={e => handleAuthChange('username', e.target.value)}
                                                                />
                                                            </div>

                                                            <div className="space-y-2">
                                                                <Label htmlFor="auth-password">Password</Label>
                                                                <Input
                                                                    id="auth-password"
                                                                    type="password"
                                                                    placeholder="Password"
                                                                    value={formData.auth.password}
                                                                    onChange={e => handleAuthChange('password', e.target.value)}
                                                                />
                                                            </div>
                                                        </div>
                                                    )}

                                                    {authType === 'form' && (
                                                        <div className="space-y-4">
                                                            <div className="space-y-2">
                                                                <Label htmlFor="form-url">Login Page URL</Label>
                                                                <Input
                                                                    id="form-url"
                                                                    placeholder="https://example.com/login"
                                                                    value={formData.auth.form.url}
                                                                    onChange={e => handleFormAuthChange('url', e.target.value)}
                                                                />
                                                                <p className="text-xs text-muted-foreground">
                                                                    Leave empty to use the same URL as the scrape target
                                                                </p>
                                                            </div>

                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                <div className="space-y-2">
                                                                    <Label htmlFor="form-username">Username</Label>
                                                                    <Input
                                                                        id="form-username"
                                                                        placeholder="Username"
                                                                        value={formData.auth.form.username}
                                                                        onChange={e => handleFormAuthChange('username', e.target.value)}
                                                                    />
                                                                </div>

                                                                <div className="space-y-2">
                                                                    <Label htmlFor="form-password">Password</Label>
                                                                    <Input
                                                                        id="form-password"
                                                                        type="password"
                                                                        placeholder="Password"
                                                                        value={formData.auth.form.password}
                                                                        onChange={e => handleFormAuthChange('password', e.target.value)}
                                                                    />
                                                                </div>

                                                                <div className="space-y-2">
                                                                    <Label htmlFor="username-selector">Username Field Selector</Label>
                                                                    <Input
                                                                        id="username-selector"
                                                                        placeholder="#username, input[name='username']"
                                                                        value={formData.auth.form.username_selector}
                                                                        onChange={e => handleFormAuthChange('username_selector', e.target.value)}
                                                                    />
                                                                </div>

                                                                <div className="space-y-2">
                                                                    <Label htmlFor="password-selector">Password Field Selector</Label>
                                                                    <Input
                                                                        id="password-selector"
                                                                        placeholder="#password, input[name='password']"
                                                                        value={formData.auth.form.password_selector}
                                                                        onChange={e => handleFormAuthChange('password_selector', e.target.value)}
                                                                    />
                                                                </div>

                                                                <div className="space-y-2 md:col-span-2">
                                                                    <Label htmlFor="submit-selector">Submit Button Selector</Label>
                                                                    <Input
                                                                        id="submit-selector"
                                                                        placeholder="button[type='submit'], .login-button"
                                                                        value={formData.auth.form.submit_selector}
                                                                        onChange={e => handleFormAuthChange('submit_selector', e.target.value)}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </AccordionContent>
                                        </AccordionItem>
                                    </Accordion>
                                </div>
                            </div>

                            <DialogFooter>
                                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleSave} disabled={isLoading}>
                                    {isLoading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
                                    {formData.id ? 'Update' : 'Create'} Schedule
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {isLoading && scheduledScrapes.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                    <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
            ) : scheduledScrapes.length === 0 ? (
                <Card>
                    <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                        <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No scheduled scrapes yet</h3>
                        <p className="text-muted-foreground mt-2 mb-4">
                            Create a schedule to automatically scrape websites at regular intervals
                        </p>
                        <Button onClick={() => {
                            resetForm()
                            setShowCreateDialog(true)
                        }}>
                            Create Schedule
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                <div className="grid grid-cols-1 gap-4">
                    {scheduledScrapes.map(item => (
                        <Card key={item.id}>
                            <CardHeader className="pb-2">
                                <div className="flex items-start justify-between">
                                    <div>
                                        <CardTitle className="flex items-center gap-2">
                                            {item.name}
                                            {getStatusBadge(item.status)}
                                        </CardTitle>
                                        <CardDescription className="mt-1">
                                            <a href={item.url} target="_blank" rel="noopener noreferrer" className="flex items-center hover:underline text-blue-500">
                                                <Globe className="h-3 w-3 mr-1" />
                                                {item.url}
                                            </a>
                                        </CardDescription>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleRunNow(item.id)}
                                            title="Run now"
                                        >
                                            <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleEdit(item)}
                                            title="Edit"
                                        >
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleDelete(item.id)}
                                            title="Delete"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <div className="text-muted-foreground">Frequency</div>
                                        <div className="font-medium flex items-center">
                                            <Calendar className="h-3 w-3 mr-1" />
                                            {getFrequencyDisplay(item.frequency)}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-muted-foreground">Format</div>
                                        <div className="font-medium">
                                            {item.format === 'text' && 'Text'}
                                            {item.format === 'raw' && 'HTML'}
                                            {item.format === 'table' && 'Table'}
                                            {item.format === 'json' && 'JSON'}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-muted-foreground">Table</div>
                                        <div className="font-medium flex items-center">
                                            <Database className="h-3 w-3 mr-1" />
                                            {item.table_name}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-muted-foreground">Next Run</div>
                                        <div className="font-medium">
                                            {formatDate(item.next_run)}
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-4 pt-4 border-t grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <div className="text-muted-foreground">Last Run</div>
                                        <div className="font-medium">
                                            {formatDate(item.last_run)}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-muted-foreground">Last Success</div>
                                        <div className="font-medium">
                                            {formatDate(item.last_success)}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-muted-foreground">Stats</div>
                                        <div className="font-medium flex items-center gap-2">
                                            <span className="flex items-center text-green-600">
                                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                                {item.success_count || 0}
                                            </span>
                                            <span className="flex items-center text-red-600">
                                                <XCircle className="h-3 w-3 mr-1" />
                                                {item.error_count || 0}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {item.last_error_message && (
                                    <div className="mt-4 p-2 bg-red-50 border border-red-200 rounded-md text-sm text-red-800 flex items-start gap-2">
                                        <AlertTriangle className="h-4 w-4 flex-shrink-0 mt-0.5" />
                                        <div>
                                            <div className="font-medium">Last Error ({formatDate(item.last_error)})</div>
                                            <div className="text-xs mt-1">{item.last_error_message}</div>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    )
}