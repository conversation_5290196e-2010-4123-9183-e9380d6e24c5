<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScrapedUrl extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'url',
        'title',
        'content',
        'format',
        'project_id',
        'source_id',
        'table_name',
        'model_id',
        'is_stored_in_db',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_stored_in_db' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the source that owns the scraped URL.
     */
    public function source(): BelongsTo
    {
        return $this->belongsTo(KnowledgeSource::class, 'source_id');
    }

    /**
     * Get the project that owns the scraped URL.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the AI model used for processing.
     */
    public function model(): BelongsTo
    {
        return $this->belongsTo(AIModel::class, 'model_id');
    }

    /**
     * Get scraped URLs by format.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $format
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByFormat($query, $format)
    {
        return $query->where('format', $format);
    }

    /**
     * Get scraped URLs for a specific project.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $projectId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProject($query, $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * Get scraped URLs stored in database.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStoredInDb($query)
    {
        return $query->where('is_stored_in_db', true);
    }

    /**
     * Get short content preview.
     *
     * @param int $length
     * @return string
     */
    public function getContentPreview($length = 100): string
    {
        return \Str::limit($this->content, $length);
    }
}
