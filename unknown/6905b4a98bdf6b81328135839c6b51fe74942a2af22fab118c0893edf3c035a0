import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Globe, Trash, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface WelcomeMessage {
    locale: string;
    message: string;
}

interface WelcomeMessageEditorProps {
    initialMessages: WelcomeMessage[];
    defaultMessage?: string;
    onChange: (messages: WelcomeMessage[]) => void;
    onDefaultChange?: (message: string) => void;
}

const AVAILABLE_LOCALES = [
    { code: "en", name: "English" },
    { code: "es", name: "Spanish" },
    { code: "fr", name: "French" },
    { code: "de", name: "German" },
    { code: "ar", name: "Arabic" },
    { code: "zh", name: "Chinese" },
    { code: "ja", name: "Japanese" },
    { code: "ru", name: "Russian" },
    { code: "pt", name: "Portuguese" },
    { code: "hi", name: "Hindi" },
];

export function WelcomeMessageEditor({
    initialMessages = [],
    defaultMessage = "Hello! How can I help you today?",
    onChange,
    onDefaultChange,
}: WelcomeMessageEditorProps) {
    const [messages, setMessages] = useState<WelcomeMessage[]>(initialMessages);
    const [defaultWelcomeMessage, setDefaultWelcomeMessage] = useState<string>(defaultMessage);
    const [newLocale, setNewLocale] = useState<string>("");
    const [selectedTab, setSelectedTab] = useState<string>("default");
    const { toast } = useToast();

    const handleDefaultMessageChange = (message: string) => {
        setDefaultWelcomeMessage(message);
        if (onDefaultChange) {
            onDefaultChange(message);
        }
    };

    const handleAddLocale = () => {
        if (!newLocale) return;

        // Check if locale already exists
        if (messages.some(m => m.locale === newLocale)) {
            toast({
                title: "Locale exists",
                description: "This language is already in your welcome messages",
                variant: "destructive",
            });
            return;
        }

        const updatedMessages = [...messages, { locale: newLocale, message: defaultWelcomeMessage }];
        setMessages(updatedMessages);
        onChange(updatedMessages);
        setNewLocale("");

        // Switch to the new locale tab
        setSelectedTab(newLocale);

        toast({
            title: "Language added",
            description: `Added ${AVAILABLE_LOCALES.find(l => l.code === newLocale)?.name || newLocale} welcome message`,
        });
    };

    const handleUpdateMessage = (locale: string, message: string) => {
        const updatedMessages = messages.map(m =>
            m.locale === locale ? { ...m, message } : m
        );
        setMessages(updatedMessages);
        onChange(updatedMessages);
    };

    const handleRemoveLocale = (locale: string) => {
        const updatedMessages = messages.filter(m => m.locale !== locale);
        setMessages(updatedMessages);
        onChange(updatedMessages);

        // Switch back to default tab if we're removing the current one
        if (selectedTab === locale) {
            setSelectedTab("default");
        }

        toast({
            title: "Language removed",
            description: `Removed ${AVAILABLE_LOCALES.find(l => l.code === locale)?.name || locale} welcome message`,
        });
    };

    // Get available locales that haven't been used yet
    const getAvailableLocales = () => {
        const usedLocales = messages.map(m => m.locale);
        return AVAILABLE_LOCALES.filter(locale => !usedLocales.includes(locale.code));
    };

    return (
        <div className="space-y-4">
            <div>
                <Label className="text-base">Welcome Messages</Label>
                <p className="text-sm text-muted-foreground">
                    Customize the initial message visitors see when they open the chat
                </p>
            </div>

            <Tabs
                value={selectedTab}
                onValueChange={setSelectedTab}
                className="w-full"
            >
                <div className="flex justify-between items-center mb-4">
                    <TabsList className="h-9">
                        <TabsTrigger value="default" className="h-8">
                            Default
                        </TabsTrigger>

                        {messages.map((message) => (
                            <TabsTrigger
                                key={message.locale}
                                value={message.locale}
                                className="h-8"
                            >
                                {AVAILABLE_LOCALES.find(l => l.code === message.locale)?.name || message.locale}
                            </TabsTrigger>
                        ))}
                    </TabsList>

                    <div className="flex items-center gap-2">
                        <Select
                            value={newLocale}
                            onValueChange={setNewLocale}
                        >
                            <SelectTrigger className="w-32 h-8">
                                <SelectValue placeholder="Add language" />
                            </SelectTrigger>
                            <SelectContent>
                                {getAvailableLocales().map((locale) => (
                                    <SelectItem key={locale.code} value={locale.code}>
                                        {locale.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleAddLocale}
                            disabled={!newLocale}
                            className="h-8"
                        >
                            <PlusCircle className="h-4 w-4 mr-1" />
                            Add
                        </Button>
                    </div>
                </div>

                <TabsContent value="default" className="mt-0">
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center gap-2 mb-3">
                                <Globe className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium">Default Message (All Languages)</span>
                            </div>

                            <Textarea
                                value={defaultWelcomeMessage}
                                onChange={(e) => handleDefaultMessageChange(e.target.value)}
                                className="h-24"
                                placeholder="Enter a welcoming message to greet your visitors"
                            />

                            <p className="text-xs text-muted-foreground mt-2">
                                This message will be shown to visitors if their language isn't specifically configured.
                            </p>
                        </CardContent>
                    </Card>
                </TabsContent>

                {messages.map((message) => (
                    <TabsContent key={message.locale} value={message.locale} className="mt-0">
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center gap-2">
                                        <Globe className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm font-medium">
                                            {AVAILABLE_LOCALES.find(l => l.code === message.locale)?.name || message.locale} Welcome Message
                                        </span>
                                    </div>

                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleRemoveLocale(message.locale)}
                                        className="h-8 text-destructive hover:text-destructive"
                                    >
                                        <Trash className="h-4 w-4 mr-1" />
                                        Remove
                                    </Button>
                                </div>

                                <Textarea
                                    value={message.message}
                                    onChange={(e) => handleUpdateMessage(message.locale, e.target.value)}
                                    className="h-24"
                                    placeholder={`Enter welcome message in ${AVAILABLE_LOCALES.find(l => l.code === message.locale)?.name || message.locale}`}
                                />

                                <div className="flex justify-end mt-3">
                                    <Button
                                        size="sm"
                                        onClick={() => {
                                            toast({
                                                title: "Message saved",
                                                description: "Your welcome message has been updated",
                                            });
                                        }}
                                        className="h-8"
                                    >
                                        <Save className="h-4 w-4 mr-1" />
                                        Save Message
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    );
} 