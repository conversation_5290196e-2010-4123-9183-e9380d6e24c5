<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KnowledgeDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'source_id',
        'file_path',
        'file_name',
        'file_type',
        'size',
        'metadata',
        'status',
        'is_active_source',
        'category',
        'project_id',
        'extracted_text',
        'created_by',
        'has_embeddings',
        'embeddings_count',
        'embeddings_provider',
        'embeddings_model',
        'embeddings_generated_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_active_source' => 'boolean',
        'size' => 'integer',
        'has_embeddings' => 'boolean',
        'embeddings_count' => 'integer',
        'embeddings_generated_at' => 'datetime',
    ];

    public function source()
    {
        return $this->belongsTo(KnowledgeSource::class, 'source_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function embeddings()
    {
        return $this->hasMany(KnowledgeEmbedding::class, 'document_id');
    }
}
