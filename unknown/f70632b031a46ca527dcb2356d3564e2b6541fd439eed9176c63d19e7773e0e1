import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  FileText,
  Globe,
  Database,
  ArrowRight,
  ArrowLeft,
  Check,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";

interface SetupWizardProps {
  onComplete: (data: any) => void;
  onCancel: () => void;
  setupType: "document" | "website" | "database";
}

export function SetupWizard({
  onComplete,
  onCancel,
  setupType,
}: SetupWizardProps) {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<Record<string, any>>({});

  const totalSteps =
    setupType === "document" ? 3 : setupType === "website" ? 4 : 5;

  const updateForm = (key: string, value: any) => {
    setF<PERSON>Data((prev) => ({ ...prev, [key]: value }));
  };

  const nextStep = () => setStep((prev) => Math.min(prev + 1, totalSteps));
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));

  const handleComplete = () => {
    onComplete(formData);
  };

  const getStepContent = () => {
    if (setupType === "document") {
      switch (step) {
        case 1:
          return (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="doc-name">Document Name</Label>
                <Input
                  id="doc-name"
                  placeholder="Enter a name for this document"
                  value={formData.name || ""}
                  onChange={(e) => updateForm("name", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="doc-category">Category (Optional)</Label>
                <Input
                  id="doc-category"
                  placeholder="E.g., Product Manual, Policy, FAQ"
                  value={formData.category || ""}
                  onChange={(e) => updateForm("category", e.target.value)}
                />
              </div>
            </div>
          );
        case 2:
          return (
            <div className="space-y-4">
              <div className="border-2 border-dashed rounded-lg p-8 text-center">
                <FileText className="h-10 w-10 mx-auto mb-4 text-muted-foreground" />
                <p className="mb-2">Drag & drop your file here</p>
                <p className="text-sm text-muted-foreground mb-4">or</p>
                <Button>Browse Files</Button>
                <p className="text-xs text-muted-foreground mt-4">
                  Supported formats: PDF, DOCX, TXT, CSV, JSON
                </p>
              </div>
            </div>
          );
        case 3:
          return (
            <div className="space-y-4">
              <div className="bg-muted/30 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Document Summary</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Name:</div>
                  <div className="font-medium">
                    {formData.name || "Untitled"}
                  </div>

                  <div>Category:</div>
                  <div className="font-medium">
                    {formData.category || "None"}
                  </div>

                  <div>File:</div>
                  <div className="font-medium">document.pdf</div>
                </div>
              </div>

              <div className="flex items-center gap-2 bg-green-50 p-3 rounded-md text-green-700">
                <Check className="h-5 w-5" />
                <span>Ready to add to knowledge base</span>
              </div>
            </div>
          );
      }
    }

    if (setupType === "website") {
      switch (step) {
        case 1:
          return (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="website-url">Website URL</Label>
                <Input
                  id="website-url"
                  placeholder="https://example.com"
                  value={formData.url || ""}
                  onChange={(e) => updateForm("url", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Enter the main URL of the website you want to scrape
                </p>
              </div>
            </div>
          );
        // Additional website steps would go here
      }
    }

    // Default fallback
    return <div>Step content not available</div>;
  };

  const getStepTitle = () => {
    if (setupType === "document") {
      switch (step) {
        case 1:
          return "Document Details";
        case 2:
          return "Upload File";
        case 3:
          return "Review & Confirm";
      }
    }

    if (setupType === "website") {
      switch (step) {
        case 1:
          return "Website URL";
        case 2:
          return "Scraping Depth";
        case 3:
          return "URL Patterns";
        case 4:
          return "Review & Confirm";
      }
    }

    return `Step ${step}`;
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{getStepTitle()}</CardTitle>
        <CardDescription>
          Step {step} of {totalSteps} -
          {setupType === "document" && "Add a document to your knowledge base"}
          {setupType === "website" && "Import content from a website"}
          {setupType === "database" && "Connect to a database"}
        </CardDescription>
        <Progress value={(step / totalSteps) * 100} className="h-1 mt-2" />
      </CardHeader>

      <CardContent>{getStepContent()}</CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={step === 1 ? onCancel : prevStep}>
          {step === 1 ? (
            "Cancel"
          ) : (
            <>
              <ArrowLeft className="h-4 w-4 mr-1" /> Back
            </>
          )}
        </Button>

        <Button onClick={step === totalSteps ? handleComplete : nextStep}>
          {step === totalSteps ? (
            "Complete Setup"
          ) : (
            <>
              Next <ArrowRight className="h-4 w-4 ml-1" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
