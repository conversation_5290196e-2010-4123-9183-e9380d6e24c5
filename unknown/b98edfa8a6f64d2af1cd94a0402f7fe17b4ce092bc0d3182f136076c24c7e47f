# Permission Middleware for Laravel 12

This directory contains the custom permission middleware for enforcing role-based access control in the Laravel API.

## CheckPermission Middleware

The `CheckPermission` middleware allows you to protect routes based on specific permissions that a user has through their assigned roles.

### How It Works

1. The middleware checks if the user is authenticated
2. It then verifies if the user has the specified permission through their roles
3. If the user lacks the required permission, a 403 Forbidden response is returned

### Usage

The middleware is registered with the alias `permission` in `bootstrap/app.php`. You can use it in your routes like this:

```php
Route::get('/users', [UserController::class, 'index'])
    ->middleware('permission:user.view');
```

You can also apply it to route groups:

```php
Route::prefix('admin')->middleware('permission:admin.access')->group(function () {
    // All routes in this group require the 'admin.access' permission
    // ...
});
```

### Permission Structure

Permissions follow a `resource.action` naming convention:

- `user.view` - Permission to view users
- `user.create` - Permission to create users
- `user.edit` - Permission to edit users
- `user.delete` - Permission to delete users

Similar patterns apply to other resources like `role`, `permission`, `ai_model`, `widget`, etc.

### Implementation Details

The permission check is performed using the `hasPermission()` method on the User model, which checks if any of the user's roles have the specified permission.

```php
public function hasPermission($permission)
{
    return $this->roles->flatMap->permissions->contains('name', $permission);
}
```

## Testing

You can test the middleware by creating a user with specific roles and permissions, then attempting to access routes that require those permissions.

See the `tests/Feature/PermissionMiddlewareTest.php` file for examples of how to test the middleware.
