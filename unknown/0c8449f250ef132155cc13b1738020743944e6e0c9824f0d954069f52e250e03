import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
  {
    variants: {
      variant: {
        default:
          "bg-primary/10 text-primary ring-primary/20",
        secondary:
          "bg-secondary text-secondary-foreground ring-secondary/20",
        outline:
          "text-foreground ring-border bg-background",
        destructive:
          "bg-destructive/10 text-destructive ring-destructive/20",
        success:
          "bg-green-50 text-green-700 ring-green-600/20",
        warning:
          "bg-amber-50 text-amber-700 ring-amber-600/20",
        info:
          "bg-blue-50 text-blue-700 ring-blue-600/20",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof badgeVariants> { }

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, ...props }, ref) => {
    return (
      <div ref={ref} className={cn(badgeVariants({ variant }), className)} {...props} />
    )
  }
)

Badge.displayName = "Badge"

export { Badge, badgeVariants }
