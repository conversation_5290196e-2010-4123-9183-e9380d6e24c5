import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Globe,
  Database,
  ArrowRight,
  Check,
  AlertCircle,
} from "lucide-react";

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: "pending" | "active" | "completed" | "error";
  action?: () => void;
}

interface VisualWorkflowProps {
  activeTab: string;
  steps: WorkflowStep[];
}

export function VisualWorkflow({ activeTab, steps }: VisualWorkflowProps) {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-medium mb-4">Setup Process</h3>

      <div className="relative">
        {/* Connection line */}
        <div className="absolute left-6 top-10 bottom-10 w-0.5 bg-muted-foreground/20 z-0"></div>

        {/* Steps */}
        <div className="space-y-4 relative z-10">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-start gap-4">
              <div
                className={`
                w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0
                ${step.status === "active" ? "bg-primary text-primary-foreground" : ""}
                ${step.status === "completed" ? "bg-green-100 text-green-700" : ""}
                ${step.status === "error" ? "bg-red-100 text-red-700" : ""}
                ${step.status === "pending" ? "bg-muted text-muted-foreground" : ""}
              `}
              >
                {step.status === "completed" ? (
                  <Check className="h-6 w-6" />
                ) : step.status === "error" ? (
                  <AlertCircle className="h-6 w-6" />
                ) : (
                  step.icon
                )}
              </div>

              <Card
                className={`flex-1 ${step.status === "active" ? "border-primary/50 shadow-md" : ""}`}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{step.title}</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        {step.description}
                      </p>
                    </div>

                    {step.action && step.status === "active" && (
                      <Button
                        onClick={step.action}
                        className="flex items-center gap-1"
                      >
                        Start <ArrowRight className="h-4 w-4" />
                      </Button>
                    )}

                    {step.status === "completed" && (
                      <span className="text-xs text-green-600 font-medium">
                        Completed
                      </span>
                    )}

                    {step.status === "error" && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={step.action}
                      >
                        Retry
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
