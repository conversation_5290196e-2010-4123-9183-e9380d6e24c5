/**
 * AI Chat Widget - Standard Embed Script
 * v1.0.0
 */

(function () {
    // Configuration values read from data attributes
    const scriptTag = document.currentScript;
    const widgetId = scriptTag.getAttribute('data-widget-id');
    const primaryColor = scriptTag.getAttribute('data-primary-color') || '#4f46e5';
    const borderRadius = scriptTag.getAttribute('data-border-radius') || '8';
    const allowedDomains = scriptTag.getAttribute('data-allowed-domains') || '*';
    const guestFlow = scriptTag.getAttribute('data-guest-flow') || 'auto';
    const webhookUrl = scriptTag.getAttribute('data-webhook-url') || '';
    const enableAnalytics = scriptTag.getAttribute('data-analytics') === 'true';

    // Base URL detection
    const baseUrl = (window.CHAT_WIDGET_BASE_URL || scriptTag.src.split('/widget/')[0]) || 'http://localhost:9090';

    // Create iframe
    function createWidgetIframe() {
        const iframe = document.createElement('iframe');
        iframe.id = `ai-chat-iframe-${widgetId}`;
        iframe.src = `${baseUrl}/widget/v1.0/iframe/${widgetId}`;
        iframe.style.position = 'fixed';
        iframe.style.bottom = '20px';
        iframe.style.right = '20px';
        iframe.style.width = '50px';
        iframe.style.height = '50px';
        iframe.style.border = 'none';
        iframe.style.zIndex = '9999';
        iframe.style.transition = 'width 0.3s, height 0.3s';
        iframe.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
        iframe.style.borderRadius = `${borderRadius}px`;
        iframe.setAttribute('loading', 'lazy');
        iframe.setAttribute('title', 'AI Chat Widget');
        iframe.setAttribute('allow', 'microphone; camera');

        // Add data attributes
        iframe.setAttribute('data-allowed-domains', allowedDomains);
        iframe.setAttribute('data-guest-flow', guestFlow);
        if (webhookUrl) {
            iframe.setAttribute('data-webhook-url', webhookUrl);
        }

        // Apply custom primary color to iframe
        if (primaryColor) {
            iframe.style.cssText += `--primary-color: ${primaryColor};`;
        }

        return iframe;
    }

    // Listen for messages from iframe
    function setupMessageListener(iframe) {
        window.addEventListener('message', function (event) {
            // In production, validate the origin
            // if (event.origin !== expectedOrigin) return;

            // Handle resize messages
            if (event.data && event.data.type === 'resize_widget') {
                iframe.style.width = `${event.data.width}px`;
                iframe.style.height = `${event.data.height}px`;
            }

            // Handle widget state changes
            if (event.data && event.data.type === 'widget_opened') {
                // Could add custom behavior when widget is opened
                if (enableAnalytics) {
                    logWidgetEvent('widget_opened');
                }
            }

            if (event.data && event.data.type === 'widget_closed') {
                // Could add custom behavior when widget is closed
                if (enableAnalytics) {
                    logWidgetEvent('widget_closed');
                }
            }
        });
    }

    // Simple analytics logging
    function logWidgetEvent(eventType, eventData = {}) {
        if (!enableAnalytics) return;

        const data = {
            event: eventType,
            widget_id: widgetId,
            timestamp: new Date().toISOString(),
            page_url: window.location.href,
            referrer: document.referrer,
            ...eventData
        };

        // In production, this would send to your analytics endpoint
        console.log('Widget analytics:', data);

        try {
            fetch(`${baseUrl}/api/widget-analytics`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
                mode: 'cors',
                credentials: 'omit'
            }).catch(err => console.log('Analytics error:', err));
        } catch (e) {
            console.log('Failed to send analytics');
        }
    }

    // Initialize widget
    function initWidget() {
        // Don't initialize twice
        if (document.getElementById(`ai-chat-iframe-${widgetId}`)) return;

        // Create and add the iframe
        const iframe = createWidgetIframe();
        document.body.appendChild(iframe);

        // Setup communication
        setupMessageListener(iframe);

        // Log initialization
        if (enableAnalytics) {
            logWidgetEvent('widget_initialized');
        }

        // Export control methods to window
        window.AIChatWidget = window.AIChatWidget || {};
        window.AIChatWidget[widgetId] = {
            open: function () {
                iframe.contentWindow.postMessage({ type: 'open_widget' }, '*');
            },
            close: function () {
                iframe.contentWindow.postMessage({ type: 'close_widget' }, '*');
            },
            toggle: function () {
                iframe.contentWindow.postMessage({ type: 'toggle_widget' }, '*');
            },
            setTheme: function (theme) {
                iframe.contentWindow.postMessage({
                    type: 'set_theme',
                    theme: theme
                }, '*');
            }
        };

        return iframe;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(initWidget, 1);
    } else {
        document.addEventListener('DOMContentLoaded', initWidget);
    }
})();
