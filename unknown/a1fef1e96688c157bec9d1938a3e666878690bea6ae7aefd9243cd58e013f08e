<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('widget_logos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('widget_id')->constrained()->onDelete('cascade');
            
            // Logo data
            $table->longText('logo_data'); // Base64 encoded image or URL
            $table->enum('logo_type', ['url', 'base64'])->default('url');
            
            // Image metadata
            $table->string('original_filename')->nullable();
            $table->string('mime_type')->nullable();
            $table->integer('file_size')->nullable(); // in bytes
            $table->integer('width')->nullable(); // in pixels
            $table->integer('height')->nullable(); // in pixels
            
            // Display settings
            $table->integer('display_width')->nullable(); // Desired display width
            $table->integer('display_height')->nullable(); // Desired display height
            $table->enum('position', ['header', 'footer', 'sidebar', 'floating'])->default('header');
            $table->string('alt_text')->nullable();
            
            // Optimization settings
            $table->integer('quality')->default(92); // JPEG quality (1-100)
            $table->boolean('auto_optimize')->default(true);
            $table->string('format_preference')->default('auto'); // 'auto', 'jpeg', 'png', 'webp'
            
            // Status
            $table->boolean('is_active')->default(true);
            $table->timestamp('uploaded_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index('widget_id');
            $table->index(['widget_id', 'is_active']);
            $table->index('logo_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('widget_logos');
    }
};
