<?php

namespace App\Http\Controllers;

use App\Models\PostChatSurvey;
use App\Models\SurveyQuestion;
use App\Models\SurveyResponse;
use App\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PostChatController extends Controller
{
    /**
     * Get the post-chat surveys for a widget
     *
     * @param string $widgetId
     * @return JsonResponse
     */
    public function getSurveys(string $widgetId): JsonResponse
    {
        try {
            $widget = Widget::where('widget_id', $widgetId)->firstOrFail();

            $survey = PostChatSurvey::where('widget_id', $widget->id)
                ->where('is_active', true)
                ->with(['questions' => function ($query) {
                    $query->orderBy('order', 'asc');
                }])
                ->first();

            if (!$survey) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active post-chat survey found for this widget'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'survey' => $survey
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get post-chat surveys: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get post-chat survey'
            ], 500);
        }
    }

    /**
     * Submit a post-chat survey
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function submitSurvey(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'survey_id' => 'required|integer|exists:post_chat_surveys,id',
            'session_id' => 'required|string',
            'answers' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get the survey
            $survey = PostChatSurvey::with('questions')->findOrFail($request->survey_id);

            // Validate required questions
            $requiredQuestions = $survey->questions->where('is_required', true)->pluck('id')->toArray();
            foreach ($requiredQuestions as $questionId) {
                if (!isset($request->answers[$questionId]) ||
                    (is_string($request->answers[$questionId]) && empty(trim($request->answers[$questionId])))) {
                    return response()->json([
                        'success' => false,
                        'message' => "Answer for question #{$questionId} is required"
                    ], 422);
                }
            }

            // Create response
            $response = SurveyResponse::create([
                'survey_id' => $request->survey_id,
                'session_id' => $request->session_id,
                'answers' => $request->answers
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Survey submitted successfully',
                'response_id' => $response->id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to submit post-chat survey: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit survey'
            ], 500);
        }
    }
}
