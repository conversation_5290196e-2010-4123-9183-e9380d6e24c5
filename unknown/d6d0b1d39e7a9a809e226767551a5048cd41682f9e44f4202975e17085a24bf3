import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EndpointList } from "@/components/api-tester/endpoint-list";
import { RequestEditor } from "@/components/api-tester/request-editor";
import { ResponseViewer } from "@/components/api-tester/response-viewer";
import {
  ApiRoute,
  ApiTestRequest,
  ApiTestResponse,
  apiTestService,
} from "@/utils/api-test-service";
import { Spinner } from "@/components/ui/spinner";

export function ApiTesterModule() {
  const [activeTab, setActiveTab] = useState("test");
  const [routes, setRoutes] = useState<ApiRoute[]>([]);
  const [selectedRoute, setSelectedRoute] = useState<ApiRoute | undefined>();
  const [selectedMethod, setSelectedMethod] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<ApiTestResponse | undefined>();
  const [isLoadingRoutes, setIsLoadingRoutes] = useState(true);

  // Fetch available API routes on component mount
  useEffect(() => {
    const fetchRoutes = async () => {
      setIsLoadingRoutes(true);
      try {
        const apiRoutes = await apiTestService.getApiRoutes();
        setRoutes(apiRoutes);
      } catch (error) {
        console.error("Failed to load API routes:", error);
      } finally {
        setIsLoadingRoutes(false);
      }
    };

    fetchRoutes();
  }, []);

  const handleSelectEndpoint = (route: ApiRoute, method: string) => {
    setSelectedRoute(route);
    setSelectedMethod(method);
    setResponse(undefined); // Clear previous response
  };

  const handleExecuteRequest = async (request: ApiTestRequest) => {
    setIsLoading(true);
    try {
      const result = await apiTestService.executeTest(request);
      setResponse(result);
    } catch (error) {
      console.error("Error executing request:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>API Tester</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="test">Test API</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="test" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  {isLoadingRoutes ? (
                    <div className="flex justify-center items-center h-40">
                      <Spinner size="lg" />
                    </div>
                  ) : (
                    <EndpointList
                      routes={routes}
                      onSelectEndpoint={handleSelectEndpoint}
                    />
                  )}
                </div>

                <div className="md:col-span-2 space-y-6">
                  <RequestEditor
                    selectedRoute={selectedRoute}
                    selectedMethod={selectedMethod}
                    onExecute={handleExecuteRequest}
                    isLoading={isLoading}
                  />

                  <ResponseViewer response={response} isLoading={isLoading} />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="history">
              <div className="p-4 border rounded-md bg-muted/50">
                <p className="text-muted-foreground text-center">
                  Request history will be available in a future update.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="settings">
              <div className="p-4 border rounded-md bg-muted/50">
                <p className="text-muted-foreground text-center">
                  API testing settings will be available in a future update.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
