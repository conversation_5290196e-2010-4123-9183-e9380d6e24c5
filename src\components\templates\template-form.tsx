"use client"

import React, { useState, useEffect } from "react"
import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
    CardDescription
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { templateService, generateSlug } from "@/utils/template-service"
import { Loader2, Code, Settings, Wand2, ListChecks, Save } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TemplateTester } from "./template-tester"
import templateProcessingService, { extractTemplateVariables } from "@/services/template-processing-service"

interface TemplateFormProps {
    templateId?: number
    onSuccess?: () => void
    onCancel?: () => void
}

export function TemplateForm({ templateId, onSuccess, onCancel }: TemplateFormProps) {
    const { toast } = useToast()

    const [loading, setLoading] = useState<boolean>(false)
    const [name, setName] = useState<string>("")
    const [description, setDescription] = useState<string>("")
    const [category, setCategory] = useState<string>("general")
    const [content, setContent] = useState<string>("")
    const [isDefault, setIsDefault] = useState<boolean>(false)
    const [status, setStatus] = useState<string>("active")
    const [variables, setVariables] = useState<string[]>([])
    const [newVariable, setNewVariable] = useState<string>("")
    const [activeTab, setActiveTab] = useState<string>("content")
    const [slug, setSlug] = useState<string>("")
    const [autoSlug, setAutoSlug] = useState<boolean>(true)

    const categories = [
        { value: "general", label: "General" },
        { value: "customer-service", label: "Customer Service" },
        { value: "technical-support", label: "Technical Support" },
        { value: "knowledge-base", label: "Knowledge Base" },
        { value: "followup", label: "Follow-up Responses" },
        { value: "marketing", label: "Marketing" },
    ]

    useEffect(() => {
        if (templateId) {
            loadTemplate(templateId)
        }
    }, [templateId])

    const loadTemplate = async (id: number) => {
        setLoading(true)
        try {
            const response = await templateService.getTemplate(id)
            if (response) {
                setName(response.name || "")
                setDescription(response.description || "")
                setCategory(response.category || "general")
                setContent(response.content || "")
                setIsDefault(response.is_default || false)
                setStatus(response.status || "active")
                setVariables(response.variables || [])
                setSlug(response.slug || "")
                setAutoSlug(false)
            }
        } catch (error) {
            console.error("Error loading template:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to load template. Please try again."
            })
        } finally {
            setLoading(false)
        }
    }

    const extractVariablesFromContent = () => {
        const extractedVars = extractTemplateVariables(content);
        setVariables(extractedVars);
        toast({
            title: "Variables Extracted",
            description: `Found ${extractedVars.length} variables in your template.`
        });
    }

    const addVariable = () => {
        if (newVariable && !variables.includes(newVariable)) {
            setVariables([...variables, newVariable])
            setNewVariable("")
        }
    }

    const removeVariable = (variable: string) => {
        setVariables(variables.filter(v => v !== variable))
    }

    const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setContent(e.target.value)
    }

    // Handle name change and update slug if autoSlug is enabled
    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newName = e.target.value
        setName(newName)

        if (autoSlug) {
            setSlug(generateSlug(newName))
        }
    }

    // Handle slug change
    const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSlug(e.target.value)
    }

    // Toggle auto slug generation
    const toggleAutoSlug = (checked: boolean) => {
        setAutoSlug(checked)
        if (checked && name) {
            setSlug(generateSlug(name))
        }
    }

    const handleSave = async () => {
        if (!name || !content) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Name and content are required fields."
            })
            return
        }

        // Ensure slug is set
        if (!slug && name) {
            setSlug(generateSlug(name))
        }

        // Extract variables from content before saving
        const extractedVars = extractTemplateVariables(content);
        setVariables(extractedVars);

        setLoading(true)

        const templateData = {
            name,
            description,
            category,
            content,
            is_default: isDefault,
            status,
            variables: extractedVars,
            metadata: {},
            slug: slug || generateSlug(name) // Ensure slug is set
        }

        try {
            if (templateId) {
                await templateService.updateTemplate(templateId, templateData)
                toast({
                    title: "Success",
                    description: "Template updated successfully."
                })
            } else {
                await templateService.createTemplate(templateData)
                toast({
                    title: "Success",
                    description: "Template created successfully."
                })
            }

            if (onSuccess) {
                onSuccess()
            }
        } catch (error) {
            console.error("Error saving template:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to save template. Please try again."
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle>{templateId ? "Edit Template" : "Create Template"}</CardTitle>
                <CardDescription>
                    Create templates with variables to customize AI responses.
                    Use {"{{variable_name}}"} syntax in your template content.
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {loading && !templateId ? (
                    <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                ) : (
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList className="mb-6">
                            <TabsTrigger value="basics">
                                <Settings className="h-4 w-4 mr-2" />
                                Basic Info
                            </TabsTrigger>
                            <TabsTrigger value="content" className="border-b-2 border-primary font-medium">
                                <Code className="h-4 w-4 mr-2" />
                                Template Content *
                            </TabsTrigger>
                            <TabsTrigger value="variables">
                                <ListChecks className="h-4 w-4 mr-2" />
                                Variables
                            </TabsTrigger>
                            <TabsTrigger value="test">
                                <Wand2 className="h-4 w-4 mr-2" />
                                Test Template
                            </TabsTrigger>
                        </TabsList>

                        {!content && (
                            <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md text-amber-800 text-sm">
                                Please add template content in the "Template Content" tab. This is required to save the template.
                            </div>
                        )}

                        <TabsContent value="basics" className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Template Name *</Label>
                                    <Input
                                        id="name"
                                        value={name}
                                        onChange={handleNameChange}
                                        placeholder="Enter template name"
                                        required
                                    />
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <Label htmlFor="slug">Slug</Label>
                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                id="auto-slug"
                                                checked={autoSlug}
                                                onCheckedChange={toggleAutoSlug}
                                                className="scale-75"
                                            />
                                            <Label htmlFor="auto-slug" className="text-xs text-muted-foreground">Auto-generate</Label>
                                        </div>
                                    </div>
                                    <Input
                                        id="slug"
                                        value={slug}
                                        onChange={handleSlugChange}
                                        placeholder="template-slug"
                                        disabled={autoSlug}
                                        className={autoSlug ? "bg-muted" : ""}
                                    />
                                    {!slug && !autoSlug && (
                                        <p className="text-xs text-amber-500">
                                            A unique slug is required. It will be used in URLs and API calls.
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="category">Category</Label>
                                    <Select
                                        value={category}
                                        onValueChange={setCategory}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {categories.map((cat) => (
                                                <SelectItem key={cat.value} value={cat.value}>
                                                    {cat.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={description}
                                    onChange={(e) => setDescription(e.target.value)}
                                    placeholder="Enter template description"
                                    rows={3}
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={status}
                                        onValueChange={setStatus}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                            <SelectItem value="draft">Draft</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-center space-x-2 h-full pt-6">
                                    <Switch
                                        id="is-default"
                                        checked={isDefault}
                                        onCheckedChange={setIsDefault}
                                    />
                                    <Label htmlFor="is-default">Set as default template</Label>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="content" className="space-y-6">
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <Label htmlFor="content">Template Content *</Label>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={extractVariablesFromContent}
                                    >
                                        Extract Variables
                                    </Button>
                                </div>
                                <Textarea
                                    id="content"
                                    value={content}
                                    onChange={handleContentChange}
                                    placeholder="Enter template content. Use {{variable_name}} for variables."
                                    rows={15}
                                    className="font-mono text-sm"
                                    required
                                />
                                <p className="text-xs text-muted-foreground">
                                    Example: "Hello {"{{user_name}}"}, welcome to {"{{company_name}}"}"
                                </p>
                            </div>

                            <div className="p-4 border rounded-md bg-muted">
                                <h3 className="text-sm font-medium mb-2">Detected Variables</h3>
                                <div className="flex flex-wrap gap-2">
                                    {variables.length > 0 ? (
                                        variables.map((variable) => (
                                            <Badge
                                                key={variable}
                                                variant="secondary"
                                            >
                                                {`{{${variable}}}`}
                                            </Badge>
                                        ))
                                    ) : (
                                        <p className="text-sm text-muted-foreground">
                                            No variables detected. Use "&#123;&#123;variable_name&#125;&#125;" syntax in your template.
                                        </p>
                                    )}
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="variables" className="space-y-6">
                            <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <Label htmlFor="variables">Template Variables</Label>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={extractVariablesFromContent}
                                    >
                                        Extract From Content
                                    </Button>
                                </div>

                                <div className="flex flex-wrap gap-2">
                                    {variables.map((variable) => (
                                        <Badge
                                            key={variable}
                                            variant="secondary"
                                            className="flex items-center gap-1"
                                        >
                                            {variable}
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="h-4 w-4"
                                                onClick={() => removeVariable(variable)}
                                            >
                                                ×
                                            </Button>
                                        </Badge>
                                    ))}
                                    {variables.length === 0 && (
                                        <p className="text-sm text-muted-foreground">
                                            No variables added yet. Variables will be automatically extracted from the content.
                                        </p>
                                    )}
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Input
                                        value={newVariable}
                                        onChange={(e) => setNewVariable(e.target.value)}
                                        placeholder="Add variable manually"
                                    />
                                    <Button
                                        variant="outline"
                                        onClick={addVariable}
                                        disabled={!newVariable}
                                    >
                                        Add
                                    </Button>
                                </div>
                            </div>

                            <div className="border-t pt-6">
                                <h3 className="text-lg font-medium mb-4">Common Variables</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="p-3 border rounded-md">
                                        <span className="font-mono text-sm">&#123;&#123;user_name&#125;&#125;</span>
                                        <p className="text-sm text-muted-foreground mt-1">The name of the user</p>
                                    </div>
                                    <div className="p-3 border rounded-md">
                                        <span className="font-mono text-sm">&#123;&#123;user_query&#125;&#125;</span>
                                        <p className="text-sm text-muted-foreground mt-1">The user's question</p>
                                    </div>
                                    <div className="p-3 border rounded-md">
                                        <span className="font-mono text-sm">&#123;&#123;company_name&#125;&#125;</span>
                                        <p className="text-sm text-muted-foreground mt-1">Your company name</p>
                                    </div>
                                    <div className="p-3 border rounded-md">
                                        <span className="font-mono text-sm">&#123;&#123;product_name&#125;&#125;</span>
                                        <p className="text-sm text-muted-foreground mt-1">The product name</p>
                                    </div>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="test" className="space-y-6">
                            {templateId ? (
                                <TemplateTester templateId={templateId} templateContent={content} variables={variables} />
                            ) : (
                                <div className="py-8 text-center">
                                    <p className="text-muted-foreground">
                                        Save your template first to enable testing.
                                    </p>
                                </div>
                            )}
                        </TabsContent>
                    </Tabs>
                )}
            </CardContent>
            <CardFooter className="flex justify-between">
                <Button
                    variant="outline"
                    onClick={onCancel}
                >
                    Cancel
                </Button>
                {content ? (
                    <Button
                        onClick={handleSave}
                        disabled={loading || !name || !content}
                        className="flex items-center gap-1"
                    >
                        {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="h-4 w-4 mr-1" />}
                        {templateId ? "Update Template" : "Create Template"}
                    </Button>
                ) : (
                    <p className="text-sm text-muted-foreground">
                        Add template content to enable saving
                    </p>
                )}
            </CardFooter>
        </Card>
    )
} 