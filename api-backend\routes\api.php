<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AIModelController;
use App\Http\Controllers\ModelActivationRuleController;
use App\Http\Controllers\ModelAnalyticsController;
use App\Http\Controllers\WidgetController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\EmbedCodeController;
use App\Http\Controllers\WidgetAnalyticsController;
use App\Http\Controllers\ApiTestController;
use App\Http\Controllers\GuestUserController;
use App\Http\Controllers\GuestUserAdminController;
use App\Http\Controllers\FollowUpController;
use App\Http\Controllers\BrandingController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\KnowledgeBaseController;
use App\Http\Controllers\TestTemplateController;
use App\Http\Controllers\PreChatFormController;
use App\Http\Controllers\PostChatSurveyController;
use App\Http\Controllers\WidgetStatisticsController;
use App\Http\Controllers\PreChatController;
use App\Http\Controllers\PostChatController;
use App\Http\Controllers\IntegrationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::get('/auth/check', [AuthController::class, 'check']);

// Widget public routes (for embeddable widget) with rate limiting
Route::middleware(['throttle:widget_public'])->group(function () {
    // Widget public endpoint
    Route::get('/widgets/public/{widgetId}', [WidgetController::class, 'getByWidgetId']);

    // Chat session endpoints
    Route::post('/chat/session/init', [ChatController::class, 'initSession']);
    Route::post('/chat/message', [ChatController::class, 'sendMessage']);
    Route::get('/chat/history', [ChatController::class, 'getHistory']);

    // Chat processing with template support
    Route::post('/chat/process', [ChatController::class, 'processMessage']);

    // Pre-chat form and post-chat survey endpoints
    Route::get('/pre-chat-form', [PreChatFormController::class, 'getForm']);
    Route::post('/pre-chat-form/submit', [PreChatFormController::class, 'submitForm']);
    Route::get('/post-chat-survey', [PostChatSurveyController::class, 'getSurvey']);
    Route::post('/post-chat-survey/submit', [PostChatSurveyController::class, 'submitSurvey']);

    // Widget embed validation route
    Route::post('/widget/validate-domain', [EmbedCodeController::class, 'validateDomain']);

    // Widget analytics endpoint - with more restrictive rate limiting
    Route::middleware(['throttle:widget_analytics'])->group(function () {
        Route::post('/widget/analytics/view', [WidgetAnalyticsController::class, 'trackEvent']);
        Route::get('/widget/analytics/view', [WidgetAnalyticsController::class, 'trackEvent']);
    });
});

// Guest user routes for widget
Route::middleware(['throttle:widget_guest'])->group(function () {
    Route::post('/guest/register', [GuestUserController::class, 'register']);
    Route::post('/guest/validate', [GuestUserController::class, 'validateSession']);
    Route::post('/guest/details', [GuestUserController::class, 'getDetails']);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'getCurrentUser']);

    // User management routes
    Route::apiResource('users', App\Http\Controllers\UserController::class);
    Route::apiResource('roles', App\Http\Controllers\RoleController::class);
    Route::apiResource('permissions', App\Http\Controllers\PermissionController::class);
    Route::apiResource('projects', App\Http\Controllers\ProjectController::class);
    Route::get('projects/{projectId}/tables', [App\Http\Controllers\ProjectController::class, 'getTables']);
    Route::post('/users/{user}/roles', [App\Http\Controllers\UserController::class, 'assignRoles']);
    Route::post('/roles/{role}/permissions', [App\Http\Controllers\RoleController::class, 'assignPermissions']);

    // AI Model routes
    Route::apiResource('ai-models', AIModelController::class);
    Route::post('/ai-models/{id}/test', [AIModelController::class, 'testConnection']);
    Route::post('/ai-models/test-connection', [AIModelController::class, 'testConnectionDirect']);
    Route::post('/ai-models/{id}/test-chat', [AIModelController::class, 'testChat']);
    Route::post('/ai-models/test-chat-direct', [AIModelController::class, 'testChatDirect']);
    Route::get('/ai-models/{id}/fallback-options', [AIModelController::class, 'getFallbackOptions']);
    Route::post('/ai-models/{id}/toggle-activation', [AIModelController::class, 'toggleActivation']);
    Route::get('/ai-models/{id}/available-models', [AIModelController::class, 'getAvailableModels']);
    Route::post('/ai-models/{id}/discover-models', [AIModelController::class, 'discoverModels']);
    Route::post('/ai-models/discover-models-direct', [AIModelController::class, 'discoverModelsDirect']);

    // Template routes
    Route::get('/templates', [TemplateController::class, 'index']);
    Route::get('/templates/categories', [TemplateController::class, 'getCategories']);
    Route::post('/templates', [TemplateController::class, 'store']);
    Route::get('/templates/{id}', [TemplateController::class, 'show']);
    Route::put('/templates/{id}', [TemplateController::class, 'update']);
    Route::delete('/templates/{id}', [TemplateController::class, 'destroy']);
    Route::post('/templates/{id}/set-default', [TemplateController::class, 'setDefault']);
    Route::post('/templates/{id}/test', [TemplateController::class, 'testTemplate']);

    // AI Model and Template integration
    Route::get('/ai-models/{modelId}/templates', [TemplateController::class, 'getTemplatesForModel']);
    Route::post('/ai-models/{modelId}/templates', [TemplateController::class, 'assignTemplateToModel']);

    // Model activation rules routes
    Route::get('/ai-models/{modelId}/rules', [ModelActivationRuleController::class, 'index']);
    Route::post('/ai-models/{modelId}/rules', [ModelActivationRuleController::class, 'store']);
    Route::put('/ai-models/{modelId}/rules/{ruleId}', [ModelActivationRuleController::class, 'update']);
    Route::delete('/ai-models/{modelId}/rules/{ruleId}', [ModelActivationRuleController::class, 'destroy']);

    // Model analytics routes
    Route::get('/analytics/models', [ModelAnalyticsController::class, 'getModelAnalytics']);
    Route::get('/analytics/models/{modelId}', [ModelAnalyticsController::class, 'getModelDetailedAnalytics']);
    Route::get('/analytics/models/{modelId}/errors', [ModelAnalyticsController::class, 'getModelErrorLogs']);

    // Widget routes
    Route::apiResource('widgets', WidgetController::class);

    // Widget logo management routes
    Route::post('/widgets/{id}/logo/upload', [WidgetController::class, 'uploadLogo']);
    Route::post('/widgets/{id}/logo/url', [WidgetController::class, 'updateLogoUrl']);
    Route::get('/widgets/{id}/logo', [WidgetController::class, 'getLogo']);
    Route::delete('/widgets/{id}/logo', [WidgetController::class, 'deleteLogo']);

    // Widget webhook testing route
    Route::post('/widgets/{id}/test-webhook', [WidgetController::class, 'testWebhook']);

    // Widget configuration export route
    Route::get('/widgets/{id}/export-config', [WidgetController::class, 'exportConfig']);

    // Widget version management routes
    Route::get('/widgets/{id}/versions', [WidgetController::class, 'getVersionHistory']);
    Route::post('/widgets/{id}/versions', [WidgetController::class, 'createVersion']);
    Route::post('/widgets/{id}/versions/{version}/restore', [WidgetController::class, 'restoreVersion']);

    // Widget bulk operations routes
    Route::post('/widgets/bulk-update', [WidgetController::class, 'bulkUpdate']);
    Route::post('/widgets/bulk-delete', [WidgetController::class, 'bulkDelete']);
    Route::post('/widgets/bulk-activate', [WidgetController::class, 'bulkActivate']);

    // Guest user management routes (admin)
    Route::get('/guest-users', [GuestUserAdminController::class, 'index']);
    Route::get('/guest-users/{id}', [GuestUserAdminController::class, 'show']);
    Route::delete('/guest-users/{id}', [GuestUserAdminController::class, 'destroy']);
    Route::get('/chat/history', [GuestUserAdminController::class, 'getChatHistory'])->name('admin.chat.history');

    // Chat management routes (admin)
    Route::get('/chat/sessions', [ChatController::class, 'listSessions']);

    // Embed code generator
    Route::post('/embed-code/generate', [EmbedCodeController::class, 'generate']);

    // Analytics routes
    Route::get('/widgets/{widget_id}/analytics', [WidgetAnalyticsController::class, 'getAnalytics']);
    Route::get('/widgets/{widget_id}/analytics/summary', [WidgetAnalyticsController::class, 'getSummary']);

    // Widget statistics routes
    Route::get('/widgets/{widgetId}/statistics', [WidgetStatisticsController::class, 'getStats']);
    Route::get('/widgets/{widgetId}/statistics/realtime', [WidgetStatisticsController::class, 'getRealTimeStats']);

    // Pre-chat form template management routes
    Route::get('/widgets/{widgetId}/pre-chat-templates', [PreChatFormController::class, 'getTemplates']);
    Route::post('/widgets/{widgetId}/pre-chat-templates', [PreChatFormController::class, 'createTemplate']);
    Route::put('/widgets/{widgetId}/pre-chat-templates/{templateId}', [PreChatFormController::class, 'updateTemplate']);
    Route::delete('/widgets/{widgetId}/pre-chat-templates/{templateId}', [PreChatFormController::class, 'deleteTemplate']);
    Route::post('/widgets/{widgetId}/pre-chat-templates/{templateId}/activate', [PreChatFormController::class, 'activateTemplate']);

    // Post-chat survey template management routes
    Route::get('/widgets/{widgetId}/post-chat-surveys', [PostChatSurveyController::class, 'getSurveys']);
    Route::post('/widgets/{widgetId}/post-chat-surveys', [PostChatSurveyController::class, 'createSurvey']);
    Route::put('/widgets/{widgetId}/post-chat-surveys/{surveyId}', [PostChatSurveyController::class, 'updateSurvey']);
    Route::delete('/widgets/{widgetId}/post-chat-surveys/{surveyId}', [PostChatSurveyController::class, 'deleteSurvey']);
    Route::post('/widgets/{widgetId}/post-chat-surveys/{surveyId}/activate', [PostChatSurveyController::class, 'activateSurvey']);

    // API Testing routes - should be disabled in production
    Route::prefix('test')->group(function () {
        Route::get('/routes', [ApiTestController::class, 'listRoutes']);
        Route::get('/ai-models', [ApiTestController::class, 'testAIModelEndpoints']);
        Route::get('/widgets', [ApiTestController::class, 'testWidgetEndpoints']);
        Route::get('/all', [ApiTestController::class, 'testAllEndpoints']);
        Route::get('/template', [TestTemplateController::class, 'testTemplate']);
        Route::post('/ai-with-template', [TestTemplateController::class, 'testAIWithTemplate']);
    });

    // Follow-up Engine routes
    Route::get('/widgets/{widgetId}/follow-up', [FollowUpController::class, 'getSettings']);
    Route::put('/widgets/{widgetId}/follow-up', [FollowUpController::class, 'updateSettings']);
    Route::get('/widgets/{widgetId}/suggestions', [FollowUpController::class, 'getSuggestions']);
    Route::post('/widgets/{widgetId}/suggestions', [FollowUpController::class, 'addSuggestion']);
    Route::put('/widgets/{widgetId}/suggestions/{suggestionId}', [FollowUpController::class, 'updateSuggestion']);
    Route::delete('/widgets/{widgetId}/suggestions/{suggestionId}', [FollowUpController::class, 'deleteSuggestion']);
    Route::get('/widgets/{widgetId}/follow-up/stats', [FollowUpController::class, 'getStats']);

    // Branding Engine routes
    Route::get('/widgets/{widgetId}/branding', [BrandingController::class, 'getBrandingSettings']);
    Route::put('/widgets/{widgetId}/branding', [BrandingController::class, 'updateBrandingSettings']);
    Route::post('/widgets/{widgetId}/branding/preview', [BrandingController::class, 'generatePreview']);
    Route::get('/branding-templates', [BrandingController::class, 'getBrandingTemplates']);

    // Module Configuration routes
    Route::get('/module-configurations', [App\Http\Controllers\ModuleConfigurationController::class, 'index']);
    Route::get('/module-configurations/{moduleId}', [App\Http\Controllers\ModuleConfigurationController::class, 'show']);
    Route::put('/module-configurations/{moduleId}', [App\Http\Controllers\ModuleConfigurationController::class, 'update']);
    Route::post('/module-configurations/batch', [App\Http\Controllers\ModuleConfigurationController::class, 'batchUpdate']);

    // Knowledge Base routes
    Route::prefix('knowledge-base')->group(function () {
        Route::post('upload', [KnowledgeBaseController::class, 'uploadDocument']);
        Route::get('documents', [KnowledgeBaseController::class, 'listAllDocuments']);
        Route::get('documents/{sourceId}', [KnowledgeBaseController::class, 'listDocuments']);
        Route::delete('documents/{docId}', [KnowledgeBaseController::class, 'deleteDocument']);
        Route::put('documents/{docId}/toggle-source', [KnowledgeBaseController::class, 'toggleDocumentAsAISource']);
        Route::post('documents/{docId}/embeddings', [KnowledgeBaseController::class, 'generateEmbeddings']);
        Route::post('documents/batch-embeddings', [KnowledgeBaseController::class, 'batchGenerateEmbeddings']);
        Route::get('embedding-status', [KnowledgeBaseController::class, 'getEmbeddingStatus']);
        Route::get('embedding-models', [KnowledgeBaseController::class, 'getEmbeddingModels']);
        Route::post('search', [KnowledgeBaseController::class, 'searchSimilarDocuments']);
        Route::post('scrape', [KnowledgeBaseController::class, 'scrapeUrl']);
        Route::post('save-scrape', [KnowledgeBaseController::class, 'saveScrape']);
        Route::get('scraped-content-tables', [KnowledgeBaseController::class, 'getScrapedContentTables']);
        Route::get('scheduled-scrapes', [KnowledgeBaseController::class, 'getScheduledScrapes']);
        Route::post('scheduled-scrapes', [KnowledgeBaseController::class, 'saveScheduledScrape']);
        Route::delete('scheduled-scrapes/{id}', [KnowledgeBaseController::class, 'deleteScheduledScrape']);
        Route::post('scheduled-scrapes/{id}/run', [KnowledgeBaseController::class, 'runScheduledScrape']);
        Route::get('scraped-urls', [KnowledgeBaseController::class, 'listScrapedUrls']);
        Route::delete('scraped-urls/{id}', [KnowledgeBaseController::class, 'deleteScrapedUrl']);

        // Context settings and rules
        Route::get('context-settings/{projectId}', [KnowledgeBaseController::class, 'getContextSettings']);
        Route::post('context-settings/{projectId}', [KnowledgeBaseController::class, 'updateContextSettings']);
        Route::get('context-rules/{projectId}', [KnowledgeBaseController::class, 'getContextRules']);
        Route::post('context-rules/{projectId}', [KnowledgeBaseController::class, 'saveContextRule']);
        Route::put('context-rules/{projectId}/{ruleId}', [KnowledgeBaseController::class, 'updateContextRule']);
        Route::delete('context-rules/{projectId}/{ruleId}', [KnowledgeBaseController::class, 'deleteContextRule']);
        Route::post('context-rules/{projectId}/test', [KnowledgeBaseController::class, 'testContextRule']);

        // Database connections
        Route::get('project-tables/{projectId}', [KnowledgeBaseController::class, 'getProjectTables']);
        Route::get('table-details/{projectId}/{tableName}', [KnowledgeBaseController::class, 'getTableDetails']);
        Route::post('execute-query', [KnowledgeBaseController::class, 'executeQuery']);
        Route::post('add-table/{projectId}/{tableName}', [KnowledgeBaseController::class, 'addTableToKnowledgeBase']);
        Route::post('database-connections', [KnowledgeBaseController::class, 'saveDatabaseConnection']);
        Route::get('database-connections/{projectId}', [KnowledgeBaseController::class, 'getDatabaseConnections']);
        Route::delete('database-connections/{connectionId}', [KnowledgeBaseController::class, 'deleteDatabaseConnection']);
        Route::post('test-database-connection', [KnowledgeBaseController::class, 'testDatabaseConnection']);
    });

    // Integration routes
    Route::prefix('integrations')->group(function () {
        Route::get('types', [App\Http\Controllers\IntegrationController::class, 'getIntegrationTypes']);
        Route::post('test', [App\Http\Controllers\IntegrationController::class, 'testIntegration']);
        Route::post('notify', [App\Http\Controllers\IntegrationController::class, 'sendNotification']);
    });

    // Chat message ratings
    Route::post('/chat/rate', [ChatController::class, 'rateMessage']);

    // Pre-chat forms
    Route::prefix('pre-chat')->group(function () {
        Route::get('/templates/{widget_id}', [PreChatController::class, 'getTemplates']);
        Route::post('/submit', [PreChatController::class, 'submitForm']);
    });

    // Post-chat surveys
    Route::prefix('post-chat')->group(function () {
        Route::get('/surveys/{widget_id}', [PostChatController::class, 'getSurveys']);
        Route::post('/submit', [PostChatController::class, 'submitSurvey']);
    });
});
