'use client'

import { useState, useEffect } from 'react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { useToast } from '@/components/ui/use-toast'
import { Spinner } from '@/components/ui/spinner'
import { Database } from 'lucide-react'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import api from '@/utils/api'

interface Project {
    id: number
    name: string
}

interface ModelKnowledgeIntegrationProps {
    modelId: number
}

export function ModelKnowledgeIntegration({ modelId }: ModelKnowledgeIntegrationProps) {
    const { toast } = useToast()
    const [isLoading, setIsLoading] = useState(true)
    const [isSaving, setIsSaving] = useState(false)
    const [projects, setProjects] = useState<Project[]>([])
    const [selectedProject, setSelectedProject] = useState<number | null>(null)
    const [useKnowledgeBase, setUseKnowledgeBase] = useState(true)
    const [relevanceThreshold, setRelevanceThreshold] = useState(0.7)

    // Load model settings and projects
    useEffect(() => {
        const fetchData = async () => {
            try {
                setIsLoading(true)

                // Fetch AI model to get current settings
                const modelResponse = await api.get(`/ai-models/${modelId}`)
                if (modelResponse.data && modelResponse.data.data) {
                    const model = modelResponse.data.data

                    // Extract knowledge base settings
                    const kbSettings = model.settings?.knowledge_base || {}
                    setUseKnowledgeBase(kbSettings.enabled !== false)
                    setRelevanceThreshold(kbSettings.relevance_threshold || 0.7)

                    if (kbSettings.project_id) {
                        setSelectedProject(kbSettings.project_id)
                    }
                }

                // Fetch available projects
                const projectsResponse = await api.get('/projects')
                if (projectsResponse.data?.success && projectsResponse.data?.data) {
                    setProjects(projectsResponse.data.data)

                    // If no project is selected but we have projects, select the first one
                    if (!selectedProject && projectsResponse.data.data.length > 0) {
                        setSelectedProject(projectsResponse.data.data[0].id)
                    }
                }
            } catch (error) {
                console.error('Error fetching data:', error)
                toast({
                    title: 'Error',
                    description: 'Failed to load model settings',
                    variant: 'destructive'
                })
            } finally {
                setIsLoading(false)
            }
        }

        fetchData()
    }, [modelId, toast])

    // Save knowledge base settings
    const saveSettings = async () => {
        if (!selectedProject && useKnowledgeBase) {
            toast({
                title: 'Error',
                description: 'Please select a project',
                variant: 'destructive'
            })
            return
        }

        try {
            setIsSaving(true)

            const payload = {
                settings: {
                    knowledge_base: {
                        enabled: useKnowledgeBase,
                        project_id: selectedProject,
                        relevance_threshold: relevanceThreshold
                    }
                }
            }

            await api.patch(`/ai-models/${modelId}`, payload)

            toast({
                title: 'Success',
                description: 'Knowledge base settings saved successfully'
            })
        } catch (error) {
            console.error('Error saving settings:', error)
            toast({
                title: 'Error',
                description: 'Failed to save knowledge base settings',
                variant: 'destructive'
            })
        } finally {
            setIsSaving(false)
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Knowledge Base Integration
                </CardTitle>
                <CardDescription>
                    Enable this AI model to use your knowledge base
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                {isLoading ? (
                    <div className="flex justify-center p-4">
                        <Spinner />
                    </div>
                ) : (
                    <>
                        <div className="flex items-center justify-between">
                            <div>
                                <Label htmlFor="use-knowledge-base">Enable Knowledge Base</Label>
                                <p className="text-sm text-muted-foreground">
                                    AI will use your knowledge base to generate responses
                                </p>
                            </div>
                            <Switch
                                id="use-knowledge-base"
                                checked={useKnowledgeBase}
                                onCheckedChange={setUseKnowledgeBase}
                            />
                        </div>

                        {useKnowledgeBase && (
                            <>
                                <div className="space-y-2">
                                    <Label htmlFor="project">Knowledge Base Project</Label>
                                    <Select
                                        value={selectedProject?.toString() || ''}
                                        onValueChange={(value) => setSelectedProject(parseInt(value, 10))}
                                    >
                                        <SelectTrigger id="project">
                                            <SelectValue placeholder="Select a project" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {projects.map((project) => (
                                                <SelectItem key={project.id} value={project.id.toString()}>
                                                    {project.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {projects.length === 0 && (
                                        <p className="text-sm text-amber-600">
                                            No projects available. Please create one in the Knowledge Base section.
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <Label htmlFor="relevance">Relevance Threshold: {relevanceThreshold.toFixed(2)}</Label>
                                    </div>
                                    <Slider
                                        id="relevance"
                                        min={0}
                                        max={1}
                                        step={0.05}
                                        value={[relevanceThreshold]}
                                        onValueChange={(value) => setRelevanceThreshold(value[0])}
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Higher values ensure more relevant context but may return fewer results
                                    </p>
                                </div>
                            </>
                        )}

                        <Button
                            onClick={saveSettings}
                            disabled={isLoading || isSaving || (useKnowledgeBase && !selectedProject)}
                            className="mt-2"
                        >
                            {isSaving && <Spinner className="mr-2 h-4 w-4" />}
                            Save Settings
                        </Button>
                    </>
                )}
            </CardContent>
        </Card>
    )
}
