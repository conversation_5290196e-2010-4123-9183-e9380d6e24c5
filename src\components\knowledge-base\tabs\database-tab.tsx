import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import {
    Database, Table, RefreshCw, Play,
    Code, AlignJustify, FileJson, ArrowDownWideNarrow
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { AIModelSelector } from '@/components/ai-models/ai-model-selector'

export default function DatabaseTab({
    projects,
    selectedProjectId,
    setSelectedProjectId,
    refreshTrigger,
    onRefresh
}) {
    // State
    const [isLoading, setIsLoading] = useState(false)
    const [tables, setTables] = useState([])
    const [selectedTable, setSelectedTable] = useState('')
    const [tableSchema, setTableSchema] = useState(null)
    const [query, setQuery] = useState('')
    const [queryResults, setQueryResults] = useState(null)
    const [isQueryRunning, setIsQueryRunning] = useState(false)
    const [isAddingTable, setIsAddingTable] = useState(false)
    const [embedModel, setEmbedModel] = useState(null)
    const [isAutoSync, setIsAutoSync] = useState(false)
    const [syncFrequency, setSyncFrequency] = useState('daily')
    const [isEmbedAll, setIsEmbedAll] = useState(false)

    // Fetch tables when project changes or refresh is triggered
    useEffect(() => {
        if (!selectedProjectId) return

        fetchTables()
    }, [selectedProjectId, refreshTrigger])

    // Fetch tables from API
    const fetchTables = async () => {
        if (!selectedProjectId) return

        setIsLoading(true)

        try {
            const response = await knowledgeBaseService.getProjectTables(selectedProjectId)

            if (response.data?.tables) {
                setTables(response.data.tables)
                if (response.data.tables.length > 0 && !selectedTable) {
                    setSelectedTable(response.data.tables[0].name)
                }
            } else {
                setTables([])
            }
        } catch (error) {
            console.error('Failed to fetch tables:', error)
            toast.error('Failed to fetch database tables')
            setTables([])
        } finally {
            setIsLoading(false)
        }
    }

    // Fetch table schema when a table is selected
    useEffect(() => {
        if (!selectedProjectId || !selectedTable) return

        const fetchTableSchema = async () => {
            setIsLoading(true)

            try {
                const response = await knowledgeBaseService.getTableDetails(selectedProjectId, selectedTable)

                if (response?.data) {
                    const tableDetails = response.data

                    // Format the schema data
                    const schema = tableDetails.columns.map(col => ({
                        name: col.name,
                        type: col.type,
                        nullable: col.nullable,
                        key: col.name === tableDetails.primary_key ? 'PRI' : ''
                    }))

                    setTableSchema(schema)

                    // Create a default query
                    setQuery(`SELECT * FROM ${selectedTable} LIMIT 10`)
                }
            } catch (error) {
                console.error('Failed to fetch table schema:', error)
                toast.error('Failed to fetch table schema')
                setTableSchema(null)
            } finally {
                setIsLoading(false)
            }
        }

        fetchTableSchema()
    }, [selectedProjectId, selectedTable])

    // Run SQL query
    const handleRunQuery = async () => {
        if (!query || !selectedProjectId) return

        setIsQueryRunning(true)

        try {
            const response = await knowledgeBaseService.executeQuery(selectedProjectId, query)

            if (response?.data) {
                setQueryResults(response.data)
                toast.success('Query executed successfully')
            } else if (response?.error) {
                toast.error(`Query failed: ${response.error}`)
            }
        } catch (error) {
            console.error('Query execution failed:', error)
            toast.error('Failed to execute query')
        } finally {
            setIsQueryRunning(false)
        }
    }

    // Add table to knowledge base
    const handleAddTable = async () => {
        if (!selectedTable || !selectedProjectId || !embedModel) return

        setIsAddingTable(true)

        try {
            const response = await knowledgeBaseService.addTableToKnowledgeBase(
                selectedProjectId,
                selectedTable,
                {
                    embedModel,
                    autoSync: isAutoSync,
                    syncFrequency,
                    embedAll: isEmbedAll
                }
            )

            if (response?.success) {
                toast.success(`Added ${selectedTable} to knowledge base`)
                // Refresh tables to show updated status
                fetchTables()
            } else {
                toast.error('Failed to add table to knowledge base')
            }
        } catch (error) {
            console.error('Failed to add table:', error)
            toast.error('Failed to add table to knowledge base')
        } finally {
            setIsAddingTable(false)
        }
    }

    return (
        <div className="space-y-6">
            {/* Project selector and controls */}
            <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                    <ProjectSelector
                        projects={projects}
                        selectedProjectId={selectedProjectId}
                        setSelectedProjectId={setSelectedProjectId}
                    />

                    <Button
                        variant="outline"
                        size="icon"
                        onClick={onRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                </div>
            </div>

            {/* Main content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left panel - Table browser */}
                <Card className="lg:col-span-1">
                    <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                            <Database className="h-5 w-5" /> Tables
                        </CardTitle>
                        <CardDescription>
                            Select a database table to use as a knowledge source
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="text-center py-8">
                                <RefreshCw className="h-8 w-8 mx-auto mb-3 animate-spin text-muted-foreground" />
                                <p className="text-muted-foreground">Loading tables...</p>
                            </div>
                        ) : tables.length === 0 ? (
                            <div className="text-center py-8">
                                <Database className="h-8 w-8 mx-auto mb-3 text-muted-foreground/30" />
                                <p className="text-muted-foreground">No tables found</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Connect to a database or create tables first
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                <div className="border rounded-md divide-y">
                                    {tables.map(table => (
                                        <div
                                            key={table.name}
                                            className={`p-3 cursor-pointer hover:bg-muted/30 ${selectedTable === table.name ? 'bg-primary/5' : ''
                                                }`}
                                            onClick={() => setSelectedTable(table.name)}
                                        >
                                            <div className="flex items-center justify-between mb-1">
                                                <h3 className="font-medium flex items-center gap-2">
                                                    <Table className="h-4 w-4 text-primary" />
                                                    {table.name}
                                                </h3>
                                                {table.is_embedded && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        Embedded
                                                    </Badge>
                                                )}
                                            </div>
                                            {table.description && (
                                                <p className="text-xs text-muted-foreground">
                                                    {table.description}
                                                </p>
                                            )}
                                            <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                                                <span>{table.row_count || 0} rows</span>
                                                <span>{table.column_count || 0} columns</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="space-y-4 border rounded-md p-4">
                                    <h3 className="font-medium mb-2">Add to Knowledge Base</h3>

                                    <div className="space-y-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="embedding-model">Embedding Model</Label>
                                            <AIModelSelector
                                                id="embedding-model"
                                                selectedModelId={embedModel}
                                                onModelChange={(value) => {
                                                    console.log("Model changed:", value);
                                                    setEmbedModel(value);
                                                }}
                                                placeholder="Select embedding model"
                                                className="w-full"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label htmlFor="auto-sync-switch">Auto-Sync</Label>
                                                <Switch
                                                    id="auto-sync-switch"
                                                    checked={isAutoSync}
                                                    onCheckedChange={setIsAutoSync}
                                                />
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Automatically sync changes to the knowledge base
                                            </p>
                                        </div>

                                        {isAutoSync && (
                                            <div className="space-y-2">
                                                <Label htmlFor="sync-frequency">Sync Frequency</Label>
                                                <Select
                                                    value={syncFrequency}
                                                    onValueChange={setSyncFrequency}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select frequency" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="hourly">Hourly</SelectItem>
                                                        <SelectItem value="daily">Daily</SelectItem>
                                                        <SelectItem value="weekly">Weekly</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        )}

                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label htmlFor="embed-all-switch">Embed all rows</Label>
                                                <Switch
                                                    id="embed-all-switch"
                                                    checked={isEmbedAll}
                                                    onCheckedChange={setIsEmbedAll}
                                                />
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Generate embeddings for all rows (can be resource intensive)
                                            </p>
                                        </div>

                                        <Button
                                            className="w-full"
                                            onClick={handleAddTable}
                                            disabled={!selectedTable || isAddingTable}
                                        >
                                            {isAddingTable ? (
                                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                            ) : (
                                                <Database className="h-4 w-4 mr-2" />
                                            )}
                                            Add Table to Knowledge Base
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Right panel - Table explorer */}
                <Card className="lg:col-span-2">
                    <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                            <Table className="h-5 w-5" /> Table Explorer
                        </CardTitle>
                        <CardDescription>
                            Explore the table structure and data
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {!selectedTable ? (
                            <div className="text-center py-8">
                                <Table className="h-8 w-8 mx-auto mb-3 text-muted-foreground/30" />
                                <p className="text-muted-foreground">Select a table to explore</p>
                            </div>
                        ) : (
                            <div className="space-y-6">
                                {/* Table schema */}
                                <div className="border rounded-md p-4">
                                    <h3 className="font-medium mb-3 flex items-center gap-2">
                                        <AlignJustify className="h-4 w-4" /> Schema: {selectedTable}
                                    </h3>

                                    {isLoading ? (
                                        <div className="text-center py-4">
                                            <RefreshCw className="h-5 w-5 mx-auto mb-2 animate-spin text-muted-foreground" />
                                            <p className="text-sm text-muted-foreground">Loading schema...</p>
                                        </div>
                                    ) : !tableSchema ? (
                                        <p className="text-sm text-muted-foreground">No schema available</p>
                                    ) : (
                                        <div className="border rounded-md overflow-hidden">
                                            <table className="w-full text-sm">
                                                <thead>
                                                    <tr className="bg-muted/50">
                                                        <th className="px-4 py-2 text-left font-medium">Column</th>
                                                        <th className="px-4 py-2 text-left font-medium">Type</th>
                                                        <th className="px-4 py-2 text-left font-medium">Nullable</th>
                                                        <th className="px-4 py-2 text-left font-medium">Key</th>
                                                    </tr>
                                                </thead>
                                                <tbody className="divide-y">
                                                    {tableSchema.map((column, index) => (
                                                        <tr key={index} className={index % 2 === 0 ? 'bg-muted/20' : ''}>
                                                            <td className="px-4 py-2 font-medium">{column.name}</td>
                                                            <td className="px-4 py-2">{column.type}</td>
                                                            <td className="px-4 py-2">{column.nullable ? 'Yes' : 'No'}</td>
                                                            <td className="px-4 py-2">
                                                                {column.key === 'PRI' && (
                                                                    <Badge variant="default" className="text-xs">Primary</Badge>
                                                                )}
                                                                {column.key === 'UNI' && (
                                                                    <Badge variant="secondary" className="text-xs">Unique</Badge>
                                                                )}
                                                                {column.key === 'MUL' && (
                                                                    <Badge variant="outline" className="text-xs">Index</Badge>
                                                                )}
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    )}
                                </div>

                                {/* SQL editor */}
                                <div className="border rounded-md p-4">
                                    <h3 className="font-medium mb-3 flex items-center gap-2">
                                        <Code className="h-4 w-4" /> Query {selectedTable}
                                    </h3>

                                    <div className="space-y-3">
                                        <Textarea
                                            value={query}
                                            onChange={e => setQuery(e.target.value)}
                                            placeholder="Enter SQL query..."
                                            className="font-mono text-sm h-24 resize-none"
                                        />

                                        <div className="flex justify-end">
                                            <Button
                                                onClick={handleRunQuery}
                                                disabled={isQueryRunning || !query}
                                                className="gap-2"
                                            >
                                                {isQueryRunning ? (
                                                    <RefreshCw className="h-4 w-4 animate-spin" />
                                                ) : (
                                                    <Play className="h-4 w-4" />
                                                )}
                                                Run Query
                                            </Button>
                                        </div>
                                    </div>
                                </div>

                                {/* Query results */}
                                {queryResults && (
                                    <div className="border rounded-md p-4">
                                        <h3 className="font-medium mb-3 flex items-center gap-2">
                                            <ArrowDownWideNarrow className="h-4 w-4" /> Query Results
                                        </h3>

                                        <div className="border rounded-md overflow-auto">
                                            <table className="w-full text-sm border-collapse">
                                                <thead>
                                                    <tr className="bg-muted/50">
                                                        {queryResults.columns.map((column, index) => (
                                                            <th key={index} className="px-4 py-2 text-left font-medium border">
                                                                {column}
                                                            </th>
                                                        ))}
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {queryResults.rows.map((row, rowIndex) => (
                                                        <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-muted/20' : ''}>
                                                            {queryResults.columns.map((column, colIndex) => (
                                                                <td key={`${rowIndex}-${colIndex}`} className="px-4 py-2 border">
                                                                    {row[column] !== null && row[column] !== undefined
                                                                        ? String(row[column])
                                                                        : <span className="text-muted-foreground italic">null</span>
                                                                    }
                                                                </td>
                                                            ))}
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>

                                        <div className="flex justify-between mt-3">
                                            <p className="text-xs text-muted-foreground">
                                                {queryResults.rows.length} rows returned
                                            </p>
                                            <Button variant="outline" size="sm" className="h-8 gap-1">
                                                <FileJson className="h-3 w-3" />
                                                Export JSON
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}