import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  FileText,
  Link,
  Shield,
  Database,
  Smartphone,
  MessageSquare,
  Code,
  Brain,
  Plus,
  CheckCircle2,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { getTooltipContent } from './tooltipContent';

interface FeatureCardsProps {
  form: UseFormReturn<any>;
  onFeatureToggle: (featureName: string, enabled: boolean) => void;
  onTooltipChange?: (tooltip: any) => void;
}

interface FeatureCard {
  id: string;
  name: string;
  description: string;
  benefit: string;
  icon: React.ComponentType<any>;
  category: 'essential' | 'growth' | 'advanced';
  popularity: number; // percentage of users who use this feature
  impact: 'low' | 'medium' | 'high';
  setupComplexity: 'simple' | 'moderate' | 'advanced';
}

const features: FeatureCard[] = [
  {
    id: 'preChat',
    name: 'Collect Visitor Info',
    description: 'Get name, email, and custom details before chat starts',
    benefit: 'Increases lead quality by 40% and helps personalize conversations',
    icon: FileText,
    category: 'essential',
    popularity: 78,
    impact: 'high',
    setupComplexity: 'simple',
  },
  {
    id: 'webhooks',
    name: 'Connect Your Tools',
    description: 'Send chat data to Slack, email, CRM, or any webhook URL',
    benefit: 'Automates workflow and ensures no messages are missed',
    icon: Link,
    category: 'growth',
    popularity: 65,
    impact: 'high',
    setupComplexity: 'moderate',
  },
  {
    id: 'domainRestriction',
    name: 'Control Access',
    description: 'Choose which websites can display your widget',
    benefit: 'Prevents unauthorized use and protects your resources',
    icon: Shield,
    category: 'advanced',
    popularity: 45,
    impact: 'medium',
    setupComplexity: 'simple',
  },
  {
    id: 'conversationPersistence',
    name: 'Remember Conversations',
    description: 'Keep chat history when visitors return to your site',
    benefit: 'Improves user experience and reduces repeated questions',
    icon: Database,
    category: 'growth',
    popularity: 55,
    impact: 'medium',
    setupComplexity: 'simple',
  },
  {
    id: 'mobileOptimization',
    name: 'Mobile Experience',
    description: 'Optimize widget display and behavior for mobile devices',
    benefit: 'Essential for mobile users (60%+ of web traffic)',
    icon: Smartphone,
    category: 'essential',
    popularity: 85,
    impact: 'high',
    setupComplexity: 'simple',
  },
  {
    id: 'postChat',
    name: 'Post-Chat Survey',
    description: 'Collect feedback and ratings after chat sessions',
    benefit: 'Improves service quality and customer satisfaction by 35%',
    icon: MessageSquare,
    category: 'growth',
    popularity: 42,
    impact: 'medium',
    setupComplexity: 'simple',
  },
  {
    id: 'customCSS',
    name: 'Custom Styling',
    description: 'Add custom CSS to completely customize your widget appearance',
    benefit: 'Perfect brand matching and professional appearance',
    icon: Code,
    category: 'advanced',
    popularity: 28,
    impact: 'medium',
    setupComplexity: 'advanced',
  },
  {
    id: 'aiModelSelection',
    name: 'AI Model Selection',
    description: 'Choose specific AI models (GPT-4, Gemini, etc.) for responses',
    benefit: 'Optimize response quality and cost for your use case',
    icon: Brain,
    category: 'advanced',
    popularity: 35,
    impact: 'high',
    setupComplexity: 'moderate',
  },

];

const getImpactColor = (impact: string) => {
  switch (impact) {
    case 'high': return 'text-green-600 bg-green-50 border-green-200';
    case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low': return 'text-gray-600 bg-gray-50 border-gray-200';
    default: return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'essential': return Users;
    case 'growth': return TrendingUp;
    case 'advanced': return Zap;
    default: return Users;
  }
};

/**
 * Feature Cards Component
 *
 * Displays available features as visual cards with clear benefits,
 * usage statistics, and easy toggle/configuration options.
 */
const FeatureCards = ({ form, onFeatureToggle, onTooltipChange }: FeatureCardsProps) => {
  const watchedFeatures = form.watch('features');
  const watchedAdvanced = form.watch('advanced');
  const watchedBehavior = form.watch('behavior');

  const handleFeatureChange = (featureId: string, enabled: boolean) => {
    form.setValue(`features.${featureId}`, enabled);
    onFeatureToggle(featureId, enabled);
  };

  const getFeatureStatus = (featureId: string) => {
    return watchedFeatures?.[featureId] || false;
  };

  // Get configuration details for each feature
  const getFeatureConfigDetails = (featureId: string) => {
    const isActive = getFeatureStatus(featureId);
    if (!isActive) return null;

    switch (featureId) {
      case 'preChat':
        // Check if pre-chat form has been configured
        const hasPreChatConfig = watchedBehavior?.collectUserData;
        return hasPreChatConfig ? {
          status: 'configured',
          details: 'Form fields configured',
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to set up form',
          icon: '⚙️'
        };

      case 'webhooks':
        // Check if webhooks have been configured
        const webhooks = watchedAdvanced?.integrations || [];
        const activeWebhooks = webhooks.filter((w: any) => w.active);
        return activeWebhooks.length > 0 ? {
          status: 'configured',
          details: `${activeWebhooks.length} webhook${activeWebhooks.length > 1 ? 's' : ''} active`,
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to add webhooks',
          icon: '⚙️'
        };

      case 'domainRestriction':
        // Check if domains have been configured
        const allowedDomains = watchedAdvanced?.allowedDomains || [];
        return allowedDomains.length > 0 ? {
          status: 'configured',
          details: `${allowedDomains.length} domain${allowedDomains.length > 1 ? 's' : ''} allowed`,
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to set domains',
          icon: '⚙️'
        };

      case 'conversationPersistence':
        // Check context retention setting
        const contextRetention = watchedAdvanced?.contextRetention;
        return contextRetention && contextRetention !== 'none' ? {
          status: 'configured',
          details: `${contextRetention} retention`,
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to set retention',
          icon: '⚙️'
        };

      case 'postChat':
        // Check if post-chat survey has been configured
        const hasPostChatConfig = watchedBehavior?.enableUserRatings;
        return hasPostChatConfig ? {
          status: 'configured',
          details: 'Survey questions configured',
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to set up survey',
          icon: '⚙️'
        };

      case 'customCSS':
        // Check if custom CSS has been added
        const customCSS = watchedAdvanced?.customCSS;
        return customCSS && customCSS.trim() ? {
          status: 'configured',
          details: `${customCSS.split('\n').length} lines of CSS`,
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to add CSS',
          icon: '⚙️'
        };

      case 'aiModelSelection':
        // Check if specific AI model has been selected
        const modelSelection = watchedAdvanced?.modelSelection;
        return modelSelection && modelSelection !== 'auto' ? {
          status: 'configured',
          details: `Using ${modelSelection}`,
          icon: '✅'
        } : {
          status: 'enabled',
          details: 'Click Configure to select model',
          icon: '⚙️'
        };

      case 'mobileOptimization':
        // This is always configured when enabled
        return {
          status: 'configured',
          details: 'Mobile-optimized layout',
          icon: '✅'
        };

      default:
        return {
          status: 'enabled',
          details: 'Feature enabled',
          icon: '✅'
        };
    }
  };

  const groupedFeatures = features.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as Record<string, FeatureCard[]>);

  const categoryOrder = ['essential', 'growth', 'advanced'];
  const categoryLabels = {
    essential: 'Essential Features',
    growth: 'Growth Features',
    advanced: 'Advanced Features',
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Smart Features</h2>
          <p className="text-muted-foreground text-sm mt-1">
            Add powerful features to make your widget more effective
          </p>
        </div>
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
          {Object.values(watchedFeatures || {}).filter(Boolean).length} active
        </Badge>
      </div>

      {categoryOrder.map((category) => {
        const categoryFeatures = groupedFeatures[category] || [];
        const CategoryIcon = getCategoryIcon(category);

        return (
          <div key={category} className="space-y-4">
            <div className="flex items-center space-x-2">
              <CategoryIcon className="w-5 h-5 text-muted-foreground" />
              <h3 className="text-lg font-medium text-foreground">
                {categoryLabels[category as keyof typeof categoryLabels]}
              </h3>
              <Badge variant="outline" className="text-xs">
                {categoryFeatures.length} features
              </Badge>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {categoryFeatures.map((feature) => {
                const isActive = getFeatureStatus(feature.id);
                const configDetails = getFeatureConfigDetails(feature.id);
                const Icon = feature.icon;

                const tooltipContent = getTooltipContent(feature.id);
                const updatedTooltipContent = {
                  ...tooltipContent,
                  status: (isActive ? (configDetails?.status === 'configured' ? 'configured' : 'enabled') : 'disabled') as 'enabled' | 'disabled' | 'configured'
                };

                return (
                  <Card
                    key={feature.id}
                    className={`
                      transition-all duration-200 hover:shadow-md cursor-pointer
                      ${isActive ? 'ring-2 ring-blue-500 bg-blue-50/30 dark:bg-blue-950/30' : 'hover:border-border'}
                    `}
                    onClick={() => handleFeatureChange(feature.id, !isActive)}
                    onMouseEnter={() => onTooltipChange?.(updatedTooltipContent)}
                    onMouseLeave={() => onTooltipChange?.(null)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`
                            p-2 rounded-lg
                            ${isActive ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' : 'bg-muted text-muted-foreground'}
                          `}>
                            <Icon className="w-5 h-5" />
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-base flex items-center space-x-2">
                              <span>{feature.name}</span>
                              {isActive && (
                                <CheckCircle2 className="w-4 h-4 text-green-600" />
                              )}
                            </CardTitle>
                            <CardDescription className="text-sm">
                              {feature.description}
                            </CardDescription>

                            {/* Configuration Status */}
                            {configDetails && (
                              <div className={`
                                mt-2 px-2 py-1 rounded-md text-xs flex items-center space-x-1
                                ${configDetails.status === 'configured'
                                  ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-950/50 dark:text-green-400 dark:border-green-800'
                                  : 'bg-amber-50 text-amber-700 border border-amber-200 dark:bg-amber-950/50 dark:text-amber-400 dark:border-amber-800'
                                }
                              `}>
                                <span>{configDetails.icon}</span>
                                <span>{configDetails.details}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <Switch
                          checked={isActive}
                          onCheckedChange={(checked) => handleFeatureChange(feature.id, checked)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      {/* Benefit */}
                      <div className="mb-3">
                        <p className="text-sm text-green-700 bg-green-50 p-2 rounded-md border border-green-200 dark:text-green-400 dark:bg-green-950/50 dark:border-green-800">
                          💡 {feature.benefit}
                        </p>
                      </div>

                      {/* Stats and Badges */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {feature.popularity}% use this
                          </Badge>
                          <Badge
                            variant="outline"
                            className={`text-xs ${getImpactColor(feature.impact)}`}
                          >
                            {feature.impact} impact
                          </Badge>
                        </div>

                        {isActive ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onFeatureToggle(feature.id, true);
                            }}
                            className={`text-xs ${configDetails?.status === 'configured'
                              ? 'border-green-300 text-green-700 hover:bg-green-50'
                              : 'border-amber-300 text-amber-700 hover:bg-amber-50'
                              }`}
                          >
                            {configDetails?.status === 'configured' ? 'Edit' : 'Configure'}
                          </Button>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFeatureChange(feature.id, true);
                            }}
                            className="text-xs"
                          >
                            <Plus className="w-3 h-3 mr-1" />
                            Add
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        );
      })}

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-950/50 dark:border-blue-800">
        <div className="flex items-start space-x-3">
          <div className="bg-blue-100 p-1 rounded-full dark:bg-blue-900">
            <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1 dark:text-blue-100">
              Smart Recommendations
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Features marked as "high impact" are used by successful businesses to increase
              conversions and improve customer experience. Start with essential features,
              then add growth features as your needs evolve.
            </p>
          </div>
        </div>
      </div>
    </div >
  );
};

export default FeatureCards;
