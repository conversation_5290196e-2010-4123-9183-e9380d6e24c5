<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('widgets', function (Blueprint $table) {
            // Change logo_url from VARCHAR(255) to LONGTEXT to support base64 encoded images
            $table->longText('logo_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('widgets', function (Blueprint $table) {
            // Revert back to string (VARCHAR(255))
            $table->string('logo_url')->nullable()->change();
        });
    }
};
