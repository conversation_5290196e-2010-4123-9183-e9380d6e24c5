"use client"

import React from "react"
import { UseFormReturn } from "react-hook-form"
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface SelectFieldProps {
  form: UseFormReturn<any>
  name: string
  label: string
  placeholder?: string
  description?: string
  required?: boolean
  options: string[]
  className?: string
  disabled?: boolean
}

/**
 * Select Field Component
 * 
 * A form field for selecting from a dropdown list of options.
 */
export function SelectField({
  form,
  name,
  label,
  placeholder,
  description,
  required = false,
  options,
  className,
  disabled = false
}: SelectFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel className="flex items-center gap-1 text-sm font-medium">
            {label}
            {required && <span className="text-red-500">*</span>}
          </FormLabel>
          <Select
            disabled={disabled}
            onValueChange={field.onChange}
            defaultValue={field.value}
            value={field.value}
          >
            <FormControl>
              <SelectTrigger className="bg-background transition-colors focus-visible:ring-1">
                <SelectValue placeholder={placeholder || "Select an option"} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {options.map((option, index) => (
                <SelectItem key={`${option}-${index}`} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && <FormDescription className="text-xs">{description}</FormDescription>}
          <FormMessage className="text-xs font-medium" />
        </FormItem>
      )}
    />
  )
}
