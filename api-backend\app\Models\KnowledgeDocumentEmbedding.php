<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KnowledgeDocumentEmbedding extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_id',
        'chunk_index',
        'chunk_text',
        'embedding',
        'provider',
        'model',
        'dimensions',
        'metadata',
    ];

    protected $casts = [
        'embedding' => 'array',
        'metadata' => 'array',
        'chunk_index' => 'integer',
        'dimensions' => 'integer',
    ];

    public function document()
    {
        return $this->belongsTo(KnowledgeDocument::class, 'document_id');
    }
}
