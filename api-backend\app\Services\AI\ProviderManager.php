<?php

declare(strict_types=1);

namespace App\Services\AI;

use App\Models\AIModel;
use Illuminate\Support\Facades\Log;

class ProviderManager
{
    protected $providerRegistry;

    public function __construct(ProviderRegistry $providerRegistry)
    {
        $this->providerRegistry = $providerRegistry;
    }

    /**
     * Get the provider instance for a model
     *
     * @param AIModel $model
     * @return ProviderInterface|null
     */
    public function getProvider(AIModel $model): ?ProviderInterface
    {
        try {
            $providerClass = $this->providerRegistry->getProviderClass($model->provider);

            if (!$providerClass) {
                Log::error('Provider not registered', [
                    'provider' => $model->provider,
                    'model' => $model->name
                ]);
                return null;
            }

            $provider = new $providerClass($model);
            return $provider;
        } catch (\Exception $e) {
            Log::error('Error getting provider: ' . $e->getMessage(), [
                'model' => $model->name,
                'provider' => $model->provider
            ]);

            return null;
        }
    }

    /**
     * Get all available providers
     *
     * @return array
     */
    public function getAvailableProviders(): array
    {
        return $this->providerRegistry->getAvailableProviders();
    }
}
