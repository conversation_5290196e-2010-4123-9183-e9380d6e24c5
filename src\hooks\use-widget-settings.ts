import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { widgetService } from "@/services/widget-service";
import { Widget, WidgetSettings } from "@/types/widget";
import { getDefaultWidgetSettings } from "@/utils/widget-utils";

// Define a more complete type for our internal widget representation
interface WidgetListItem {
  id: string;
  name: string;
  numericId?: number;
}

export function useWidgetSettings() {
  const { toast } = useToast();
  const [widgets, setWidgets] = useState<WidgetListItem[]>([]);
  const [selectedWidget, setSelectedWidget] = useState<string>("");
  const [selectedWidgetId, setSelectedWidgetId] = useState<number | null>(null);
  const [widgetSettings, setWidgetSettings] = useState<WidgetSettings>(getDefaultWidgetSettings());
  const [originalSettings, setOriginalSettings] = useState<WidgetSettings>(getDefaultWidgetSettings());
  const [isLoading, setIsLoading] = useState(true);

  // Fetch widgets on mount
  useEffect(() => {
    fetchWidgets();
  }, []);

  // Fetch widgets from API
  const fetchWidgets = async () => {
    setIsLoading(true);
    try {
      const response = await widgetService.getAllWidgets();
      const widgetData = response.data.map((widget: Widget) => ({
        id: widget.widget_id || (widget.id ? widget.id.toString() : ''),
        name: widget.name,
        numericId: widget.id // Store the numeric ID for API calls
      }));

      setWidgets(widgetData);

      // Select first widget if available
      if (widgetData.length > 0 && !selectedWidget) {
        handleWidgetChange(widgetData[0].id);
      } else if (widgetData.length === 0) {
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error fetching widgets:", error);
      toast({
        title: "Error",
        description: "Failed to load widgets",
        variant: "destructive"
      });
      setIsLoading(false);
    }
  };

  // Update widget settings when selecting a different widget
  const handleWidgetChange = async (widgetId: string) => {
    setSelectedWidget(widgetId);
    setIsLoading(true);

    try {
      // Find the numeric ID for this widget
      const widget = widgets.find(w => w.id === widgetId);
      const numericId = widget?.numericId;
      setSelectedWidgetId(numericId || null);

      let response;
      if (numericId) {
        response = await widgetService.getWidget(numericId);
      } else {
        response = await widgetService.getWidgetByPublicId(widgetId);
      }

      const data = response.data;

      if (data && data.settings) {
        setWidgetSettings(data.settings);
        setOriginalSettings(data.settings);
      } else {
        // Fall back to default settings if none available
        const defaultSettings = getDefaultWidgetSettings();
        setWidgetSettings(defaultSettings);
        setOriginalSettings(defaultSettings);
      }
    } catch (error) {
      console.error("Error fetching widget settings:", error);
      toast({
        title: "Error",
        description: "Failed to load widget settings",
        variant: "destructive"
      });

      // Use default settings on error
      const defaultSettings = getDefaultWidgetSettings();
      setWidgetSettings(defaultSettings);
      setOriginalSettings(defaultSettings);
    } finally {
      setIsLoading(false);
    }
  };

  // Update widget settings in the API
  const updateWidgetSettings = async (newSettings: Partial<WidgetSettings>) => {
    if (!selectedWidgetId) {
      console.error("No widget selected");
      return Promise.reject(new Error("No widget selected"));
    }

    try {
      // Merge new settings with existing settings
      const updatedSettings = { ...widgetSettings, ...newSettings };
      setWidgetSettings(updatedSettings);

      // Update API
      await widgetService.updateWidget(selectedWidgetId, {
        settings: updatedSettings
      });

      // Update original settings after successful save
      setOriginalSettings(updatedSettings);

      toast({
        title: "Success",
        description: "Widget settings updated successfully",
        variant: "default"
      });

      return Promise.resolve();
    } catch (error) {
      console.error("Error updating widget settings:", error);
      toast({
        title: "Error",
        description: "Failed to update widget settings",
        variant: "destructive"
      });
      return Promise.reject(error);
    }
  };

  // Reset widget settings to original values
  const resetWidgetSettings = useCallback(() => {
    setWidgetSettings(originalSettings);
  }, [originalSettings]);

  // Get widget configuration for embed code generation
  const getWidgetConfig = useCallback(() => {
    return {
      id: selectedWidget,
      numericId: selectedWidgetId ?? undefined,
      name: widgets.find(w => w.id === selectedWidget)?.name || "Widget",
      primaryColor: widgetSettings.primaryColor || "#4f46e5",
      borderRadius: widgetSettings.borderRadius || 8,
      position: widgetSettings.position || "bottom-right",
      initialMessage: widgetSettings.welcomeMessage || "Hello! How can I help you today?",
      headerTitle: widgetSettings.headerTitle || "AI Assistant",
      persistConversation: widgetSettings.persistConversation || false
    };
  }, [selectedWidget, selectedWidgetId, widgets, widgetSettings]);

  return {
    widgets,
    selectedWidget,
    widgetSettings,
    isLoading,
    error: null, // Add error state for consistency
    setSelectedWidget: handleWidgetChange,
    updateWidgetSettings,
    saveWidgetSettings: updateWidgetSettings, // Alias for updateWidgetSettings
    resetWidgetSettings,
    getWidgetConfig,
    refreshWidgets: fetchWidgets
  };
}
