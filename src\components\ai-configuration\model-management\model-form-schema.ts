import { z } from "zod";

// Schema for model settings
const modelSettingsSchema = z.object({
  model_name: z.string().optional(),
  temperature: z.number().min(0).max(2).default(0.7),
  max_tokens: z.number().min(1).max(32000).default(2048),
  system_message: z.string().optional(),
  knowledge_base: z.object({
    enabled: z.boolean().default(false),
    project_id: z.number().nullable().optional(),
    relevance_threshold: z.number().min(0).max(1).default(0.7),
    max_sources: z.number().min(1).max(10).default(3),
  }).optional().default({
    enabled: false,
    project_id: null,
    relevance_threshold: 0.7,
    max_sources: 3
  }),
});

// Schema for the entire model form
export const modelFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  provider: z.string().min(1, "Provider is required"),
  description: z.string().optional(),
  api_key: z.string().min(1, "API key is required"),
  is_default: z.boolean().default(false),
  active: z.boolean().default(true),
  is_free: z.boolean().default(false),
  fallback_model_id: z.number().nullable().optional(),
  confidence_threshold: z.number().min(0).max(1).default(0.7),
  template_id: z.number().nullable().optional(),
  settings: modelSettingsSchema,
});

export type ModelFormValues = z.infer<typeof modelFormSchema>;
