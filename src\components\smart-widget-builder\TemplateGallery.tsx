import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Sparkles, Users, Zap, Shield } from 'lucide-react';
import { useLocalTheme } from './LocalThemeProvider';
import ImageWithFallback from '../widget-builder/ImageWithFallback';

interface Template {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  category: 'popular' | 'business' | 'creative' | 'minimal';
  features: string[];
  usageStats?: string;
  recommended?: boolean;
}

interface TemplateGalleryProps {
  onTemplateSelect: (templateId: string) => void;
}

const templates: Template[] = [
  {
    id: "modern",
    name: "Modern Support",
    description: "Perfect for customer support with clean design and professional appearance",
    thumbnail: "/images/templates/modern.svg",
    category: "popular",
    features: ["Clean Design", "Professional", "Mobile Optimized"],
    usageStats: "Used by 2.3k+ businesses",
    recommended: true,
  },
  {
    id: "glass",
    name: "Glass Effect",
    description: "Trendy glass morphism design that stands out on any website",
    thumbnail: "/images/templates/glass.svg",
    category: "creative",
    features: ["Glass Effect", "Modern", "Eye-catching"],
    usageStats: "Used by 1.8k+ businesses",
  },
  {
    id: "dark",
    name: "Dark Professional",
    description: "Sleek dark theme perfect for tech companies and modern brands",
    thumbnail: "/images/templates/dark.svg",
    category: "business",
    features: ["Dark Theme", "Tech-focused", "Premium Feel"],
    usageStats: "Used by 1.5k+ businesses",
  },
  {
    id: "rounded",
    name: "Friendly Rounded",
    description: "Warm and approachable design with soft rounded corners",
    thumbnail: "/images/templates/rounded.svg",
    category: "popular",
    features: ["Friendly", "Approachable", "Soft Design"],
    usageStats: "Used by 2.1k+ businesses",
  },
  {
    id: "minimal",
    name: "Clean Minimal",
    description: "Distraction-free design that focuses purely on conversation",
    thumbnail: "/images/templates/minimal.svg",
    category: "minimal",
    features: ["Minimal", "Clean", "Distraction-free"],
    usageStats: "Used by 1.2k+ businesses",
  },
];

const categories = [
  { id: 'all', name: 'All Templates', icon: Sparkles },
  { id: 'popular', name: 'Popular', icon: Users },
  { id: 'business', name: 'Business', icon: Shield },
  { id: 'creative', name: 'Creative', icon: Zap },
  { id: 'minimal', name: 'Minimal', icon: Sparkles },
];

/**
 * Template Gallery Component
 *
 * Displays a visual gallery of widget templates with categories,
 * usage statistics, and clear benefits for each template.
 */
const TemplateGallery = ({ onTemplateSelect }: TemplateGalleryProps) => {
  const { theme } = useLocalTheme();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [hoveredTemplate, setHoveredTemplate] = useState<string | null>(null);

  const filteredTemplates = selectedCategory === 'all'
    ? templates
    : templates.filter(template => template.category === selectedCategory);

  const getCategoryIcon = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.icon || Sparkles;
  };

  return (
    <div className="space-y-8">
      {/* Category Filter */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2 bg-background rounded-lg p-1 shadow-sm border">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "secondary" : "ghost"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center space-x-2"
              >
                <Icon className="w-4 h-4" />
                <span>{category.name}</span>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {filteredTemplates.map((template) => (
          <Card
            key={template.id}
            className={`
              relative cursor-pointer transition-all duration-200 hover:shadow-lg
              ${hoveredTemplate === template.id ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
              ${template.recommended ? 'ring-2 ring-amber-400 ring-opacity-30' : ''}
            `}
            onMouseEnter={() => setHoveredTemplate(template.id)}
            onMouseLeave={() => setHoveredTemplate(null)}
            onClick={() => onTemplateSelect(template.id)}
          >
            {/* Recommended Badge */}
            {template.recommended && (
              <div className="absolute top-3 right-3 z-10">
                <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Recommended
                </Badge>
              </div>
            )}

            <CardHeader className="pb-3">
              {/* Template Preview */}
              <div className="aspect-[4/3] rounded-lg overflow-hidden bg-muted mb-4">
                <ImageWithFallback
                  src={template.thumbnail}
                  fallbackSrc="/images/templates/fallback.svg"
                  alt={template.name}
                  className="w-full h-full object-cover"
                />

                {/* Hover Overlay */}
                {hoveredTemplate === template.id && (
                  <div className="absolute inset-0 bg-blue-600/90 flex items-center justify-center text-white font-medium">
                    <div className="text-center">
                      <Check className="w-8 h-8 mx-auto mb-2" />
                      <span>Use This Template</span>
                    </div>
                  </div>
                )}
              </div>

              <CardTitle className="text-lg">{template.name}</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                {template.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Features */}
              <div className="flex flex-wrap gap-1 mb-3">
                {template.features.map((feature) => (
                  <Badge
                    key={feature}
                    variant="outline"
                    className="text-xs bg-gray-50 text-gray-700 border-gray-200"
                  >
                    {feature}
                  </Badge>
                ))}
              </div>

              {/* Usage Stats */}
              {template.usageStats && (
                <p className="text-xs text-gray-500 mb-4">
                  {template.usageStats}
                </p>
              )}

              {/* Action Button */}
              <Button
                className="w-full"
                variant={template.recommended ? "default" : "outline"}
                onClick={(e) => {
                  e.stopPropagation();
                  onTemplateSelect(template.id);
                }}
              >
                {template.recommended ? "Use Recommended" : "Use Template"}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Sparkles className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates found
          </h3>
          <p className="text-gray-600">
            Try selecting a different category or view all templates.
          </p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => setSelectedCategory('all')}
          >
            View All Templates
          </Button>
        </div>
      )}

      {/* Help Text */}
      <div className="text-center text-sm text-gray-500 max-w-2xl mx-auto">
        <p>
          Choose a template that matches your brand and use case. You can customize colors,
          messages, and features after selection. Don't worry - you can always change the
          template later!
        </p>
      </div>
    </div>
  );
};

export default TemplateGallery;
