<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scraped_urls', function (Blueprint $table) {
            $table->id();
            $table->string('url');
            $table->string('title')->nullable();
            $table->longText('content');
            $table->string('format')->default('text'); // text, raw, table, json
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->foreignId('source_id')->constrained('knowledge_sources')->onDelete('cascade');
            $table->string('table_name')->nullable();
            $table->foreignId('model_id')->nullable()->constrained('ai_models')->nullOnDelete();
            $table->boolean('is_stored_in_db')->default(false);
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scraped_urls');
    }
};
