<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WidgetBehavior extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'widget_id',
        'auto_open',
        'auto_open_delay',
        'start_minimized',
        'show_typing_indicator',
        'enable_user_ratings',
        'collect_user_data',
        'persist_conversation',
        'pre_chat_enabled',
        'post_chat_enabled',
        'close_after_inactivity',
        'inactivity_timeout',
        'show_logo',
        'show_close_button',
        'advanced_settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'auto_open' => 'boolean',
        'auto_open_delay' => 'integer',
        'start_minimized' => 'boolean',
        'show_typing_indicator' => 'boolean',
        'enable_user_ratings' => 'boolean',
        'collect_user_data' => 'boolean',
        'persist_conversation' => 'boolean',
        'pre_chat_enabled' => 'boolean',
        'post_chat_enabled' => 'boolean',
        'close_after_inactivity' => 'boolean',
        'inactivity_timeout' => 'integer',
        'show_logo' => 'boolean',
        'show_close_button' => 'boolean',
        'advanced_settings' => 'array',
    ];

    /**
     * Get the widget that owns this behavior configuration.
     */
    public function widget()
    {
        return $this->belongsTo(Widget::class);
    }

    /**
     * Get behavior settings as an array for API responses.
     */
    public function toSettingsArray(): array
    {
        return [
            'autoOpen' => $this->auto_open,
            'autoOpenDelay' => $this->auto_open_delay,
            'startMinimized' => $this->start_minimized,
            'showTypingIndicator' => $this->show_typing_indicator,
            'enableUserRatings' => $this->enable_user_ratings,
            'collectUserData' => $this->collect_user_data,
            'persistConversation' => $this->persist_conversation,
            'preChat' => $this->pre_chat_enabled,
            'postChat' => $this->post_chat_enabled,
            'closeAfterInactivity' => $this->close_after_inactivity,
            'inactivityTimeout' => $this->inactivity_timeout,
            'showLogo' => $this->show_logo,
            'showCloseButton' => $this->show_close_button,
            'advancedSettings' => $this->advanced_settings,
        ];
    }

    /**
     * Create or update behavior from settings array.
     */
    public static function createFromSettings(int $widgetId, array $settings): self
    {
        return static::updateOrCreate(
            ['widget_id' => $widgetId],
            [
                'auto_open' => $settings['autoOpen'] ?? false,
                'auto_open_delay' => $settings['autoOpenDelay'] ?? 5,
                'start_minimized' => $settings['startMinimized'] ?? false,
                'show_typing_indicator' => $settings['showTypingIndicator'] ?? true,
                'enable_user_ratings' => $settings['enableUserRatings'] ?? false,
                'collect_user_data' => $settings['collectUserData'] ?? false,
                'persist_conversation' => $settings['persistConversation'] ?? false,
                'pre_chat_enabled' => $settings['preChat'] ?? false,
                'post_chat_enabled' => $settings['postChat'] ?? false,
                'close_after_inactivity' => $settings['closeAfterInactivity'] ?? false,
                'inactivity_timeout' => $settings['inactivityTimeout'] ?? 5,
                'show_logo' => $settings['showLogo'] ?? true,
                'show_close_button' => $settings['showCloseButton'] ?? true,
                'advanced_settings' => $settings['advancedSettings'] ?? null,
            ]
        );
    }
}
