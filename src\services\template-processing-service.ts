import { Template } from "@/utils/template-service"
import { templateService } from "@/utils/template-service"

/**
 * TemplateProcessingService
 * 
 * Handles the processing of templates including variable substitution
 * and any other template-related processing logic
 */
export class TemplateProcessingService {
    /**
     * Process a template by substituting variables with their values
     * 
     * @param template The template object or template content string
     * @param variables An object mapping variable names to their values
     * @returns The processed template content
     */
    public static process(
        template: Template | string,
        variables: Record<string, string> = {}
    ): string {
        // If template is a string, use it directly
        // If it's a Template object, use its content
        const content = typeof template === "string" ? template : template.content

        // Perform variable substitution
        return this.substituteVariables(content, variables)
    }

    /**
     * Extract variables from a template content
     * Variables are expected in the format {{variable_name}}
     * 
     * @param content The template content
     * @returns Array of variable names
     */
    public static extractVariables(content: string): string[] {
        const regex = /\{\{([a-zA-Z0-9_]+)\}\}/g
        const matches = content.matchAll(regex)
        const variables = new Set<string>()

        for (const match of matches) {
            if (match[1]) {
                variables.add(match[1])
            }
        }

        return Array.from(variables)
    }

    /**
     * Substitute variables in the template content with their values
     * 
     * @param content The template content
     * @param variables An object mapping variable names to their values
     * @returns The processed content with variables substituted
     */
    private static substituteVariables(
        content: string,
        variables: Record<string, string>
    ): string {
        let result = content

        // Replace variables in the format {{variable_name}}
        for (const [key, value] of Object.entries(variables)) {
            const regex = new RegExp(`\\{\\{${key}\\}\\}`, "g")
            result = result.replace(regex, value)
        }

        return result
    }

    /**
     * Generate placeholder values for variables based on variable names
     * This is useful for testing templates
     * 
     * @param variables Array of variable names
     * @returns An object mapping variable names to placeholder values
     */
    public static generatePlaceholders(variables: string[]): Record<string, string> {
        const placeholders: Record<string, string> = {}

        // Common placeholders for specific variable names
        const commonValues: Record<string, string> = {
            user_name: "John Doe",
            user_query: "How can I reset my password?",
            company_name: "Acme Corporation",
            product_name: "Widget Pro",
            date: new Date().toLocaleDateString(),
            time: new Date().toLocaleTimeString(),
            timestamp: new Date().toISOString(),
            email: "<EMAIL>",
            phone: "+****************"
        }

        // Assign placeholder values for each variable
        for (const variable of variables) {
            placeholders[variable] = commonValues[variable] || `[${variable}]`
        }

        return placeholders
    }
}

/**
 * Utility to check if a value is not a function
 */
export function isNotFunction(value: unknown): boolean {
    return typeof value !== 'function';
}

/**
 * Extract variables from template content
 */
export function extractTemplateVariables(content: string): string[] {
    const regex = /\{\{([a-zA-Z0-9_]+)\}\}/g;
    const matches = content.matchAll(regex);
    const extractedVars = new Set<string>();

    for (const match of matches) {
        if (match[1]) {
            extractedVars.add(match[1]);
        }
    }

    return [...extractedVars];
}

/**
 * Process a template by replacing variables with values
 */
export function processTemplateContent(
    content: string,
    variables: Record<string, unknown>
): string {
    let result = content;

    // Replace variables in the format {{variable_name}}
    for (const [key, value] of Object.entries(variables)) {
        if (isNotFunction(value)) {
            const regex = new RegExp(`\\{\\{${key}\\}\\}`, "g");
            result = result.replace(regex, String(value));
        }
    }

    return result;
}

/**
 * Get sample values for common variable names
 */
export function getSampleValue(variable: string): string {
    // Default sample values based on common variable names
    const sampleValues: Record<string, string> = {
        user_name: "John Doe",
        user_query: "How can I reset my password?",
        company_name: "Acme Corporation",
        product_name: "Widget Pro",
        date: new Date().toISOString().split("T")[0],
        time: new Date().toTimeString().split(" ")[0],
        timestamp: new Date().toISOString(),
        email: "<EMAIL>",
        phone: "+****************"
    };

    return sampleValues[variable] || `sample_${variable}`;
}

/**
 * Process variable values, ensuring they're not functions
 */
export function processVariableValues(values: Record<string, unknown>): Record<string, string> {
    const processed: Record<string, string> = {};

    for (const [key, value] of Object.entries(values)) {
        if (isNotFunction(value)) {
            processed[key] = String(value);
        }
    }

    return processed;
}

/**
 * Process a template with the given variable values
 */
export async function processTemplate(
    templateIdOrContent: number | string,
    variableValues: Record<string, unknown>
): Promise<string> {
    try {
        // Process variable values
        const processedValues = processVariableValues(variableValues);

        // If templateIdOrContent is a number, fetch the template from the API
        if (typeof templateIdOrContent === 'number') {
            const response = await templateService.testTemplate(templateIdOrContent, processedValues);
            return response.content;
        }

        // If templateIdOrContent is a string, process it directly
        return processTemplateContent(templateIdOrContent, processedValues);
    } catch (error) {
        console.error('Error processing template:', error);
        throw error;
    }
}

/**
 * Initialize variable values with defaults
 */
export function initializeVariableValues(
    variables: string[],
    existingValues: Record<string, string> = {},
    predefinedValues: Record<string, unknown> = {}
): Record<string, string> {
    const initialValues: Record<string, string> = {};

    variables.forEach(variable => {
        if (isNotFunction(predefinedValues[variable])) {
            initialValues[variable] = String(predefinedValues[variable]);
        } else {
            initialValues[variable] = existingValues[variable] || getSampleValue(variable);
        }
    });

    return initialValues;
}

// Export the template processing service
const templateProcessingService = {
    extractTemplateVariables,
    processTemplateContent,
    processTemplate,
    getSampleValue,
    processVariableValues,
    initializeVariableValues,
    isNotFunction
};

export default templateProcessingService; 