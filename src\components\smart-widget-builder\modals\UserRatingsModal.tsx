import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, CheckCircle, TrendingUp, Users } from 'lucide-react';
import { FeatureModalProps } from '../index';

const UserRatingsModal = ({ form, onClose }: FeatureModalProps) => {
  const [showAfterResponse, setShowAfterResponse] = useState(true);
  const [collectFeedback, setCollectFeedback] = useState(true);
  const [requireRating, setRequireRating] = useState(false);

  const handleSave = () => {
    form.setValue('features.userRatings', true);
    form.setValue('behavior.enableUserRatings', true);
    onClose();
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Star className="w-5 h-5 mr-2 text-yellow-600" />
            User Ratings & Feedback
          </DialogTitle>
          <DialogDescription>
            Allow users to rate AI responses and collect valuable feedback
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Rating Settings</CardTitle>
                <CardDescription>Configure when and how ratings are collected</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enable-ratings">Enable User Ratings</Label>
                  <Switch id="enable-ratings" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="show-after-response">Show After Each Response</Label>
                    <p className="text-sm text-gray-500">Display rating option after AI responses</p>
                  </div>
                  <Switch 
                    id="show-after-response" 
                    checked={showAfterResponse}
                    onCheckedChange={setShowAfterResponse}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="collect-feedback">Collect Text Feedback</Label>
                    <p className="text-sm text-gray-500">Allow users to leave comments</p>
                  </div>
                  <Switch 
                    id="collect-feedback" 
                    checked={collectFeedback}
                    onCheckedChange={setCollectFeedback}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="require-rating">Require Rating</Label>
                    <p className="text-sm text-gray-500">Make rating mandatory</p>
                  </div>
                  <Switch 
                    id="require-rating" 
                    checked={requireRating}
                    onCheckedChange={setRequireRating}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Rating Display</CardTitle>
                <CardDescription>Customize how ratings appear to users</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Rating Style</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" className="justify-start">
                      <Star className="w-4 h-4 mr-2" />
                      Stars (1-5)
                    </Button>
                    <Button variant="ghost" className="justify-start">
                      👍👎 Thumbs
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Rating Text</Label>
                  <div className="text-sm space-y-1">
                    <div className="p-2 bg-gray-50 rounded">
                      <strong>Prompt:</strong> "How helpful was this response?"
                    </div>
                    <div className="p-2 bg-gray-50 rounded">
                      <strong>Thank you:</strong> "Thanks for your feedback!"
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Rating Preview</CardTitle>
                <CardDescription>How ratings will appear in your widget</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-4">
                  {/* Sample AI Response */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border">
                    <div className="flex items-start space-x-2">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-xs">🤖</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm">I can help you with that! Here's the information you requested...</p>
                      </div>
                    </div>
                  </div>

                  {/* Rating Interface */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                    <p className="text-sm font-medium mb-2">How helpful was this response?</p>
                    <div className="flex items-center space-x-1 mb-3">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star 
                          key={star} 
                          className={`w-5 h-5 cursor-pointer transition-colors ${
                            star <= 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`} 
                        />
                      ))}
                    </div>
                    {collectFeedback && (
                      <div className="space-y-2">
                        <textarea 
                          className="w-full p-2 text-xs border rounded resize-none"
                          placeholder="Any additional feedback? (optional)"
                          rows={2}
                        />
                      </div>
                    )}
                    <div className="flex justify-end mt-2">
                      <Button size="sm" className="text-xs">Submit</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-medium text-sm">Improve Quality</p>
                      <p className="text-xs text-gray-500">AI learns from ratings</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-sm">User Engagement</p>
                      <p className="text-xs text-gray-500">Increases interaction</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">Benefits</h4>
                  <ul className="text-sm text-green-700 dark:text-green-200 mt-1 space-y-1">
                    <li>• Continuous AI improvement</li>
                    <li>• User satisfaction insights</li>
                    <li>• Quality assurance metrics</li>
                    <li>• Enhanced user engagement</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-yellow-600 hover:bg-yellow-700">
            Enable User Ratings
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserRatingsModal;
