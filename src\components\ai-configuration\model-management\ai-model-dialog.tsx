import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Spinner } from "@/components/ui/spinner";
import { AIModelData } from "@/utils/ai-model-service";
import { modelFormSchema, ModelFormValues } from "./model-form-schema";
import { ModelBasicInfoFields } from "./model-basic-info-fields";
import { ModelApiKeyField } from "./model-api-key-field";
import { ModelSettingsFields } from "./model-settings-fields";
import { ModelDefaultToggle } from "./model-default-toggle";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ModelKnowledgeTab } from "./model-knowledge-tab";
import { Settings, Database, Zap } from "lucide-react";

interface AIModelDialogProps {
  model?: AIModelData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AIModelData) => Promise<void>;
  isLoading?: boolean;
}

export function AIModelDialog({
  model,
  open,
  onOpenChange,
  onSubmit,
  isLoading = false
}: AIModelDialogProps) {
  const [activeTab, setActiveTab] = useState("basic");

  // Initialize form with default values or existing model data
  const form = useForm<ModelFormValues>({
    resolver: zodResolver(modelFormSchema),
    defaultValues: {
      name: model?.name || "",
      provider: model?.provider || "",
      description: model?.description || "",
      api_key: model?.api_key || "",
      is_default: model?.is_default || false,
      active: model?.active !== false,
      is_free: model?.is_free || false,
      fallback_model_id: model?.fallback_model_id || null,
      confidence_threshold: model?.confidence_threshold || 0.7,
      template_id: model?.template_id || null,
      settings: {
        model_name: model?.settings?.model_name || "",
        temperature: model?.settings?.temperature || 0.7,
        max_tokens: model?.settings?.max_tokens || 2048,
        system_message: model?.settings?.system_message || "",
        knowledge_base: {
          enabled: model?.settings?.knowledge_base?.enabled || false,
          project_id: model?.settings?.knowledge_base?.project_id || null,
          relevance_threshold: model?.settings?.knowledge_base?.relevance_threshold || 0.7,
          max_sources: model?.settings?.knowledge_base?.max_sources || 3
        }
      }
    },
  });

  const handleFormSubmit = async (data: ModelFormValues) => {
    await onSubmit(data as AIModelData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[650px]">
        <DialogHeader>
          <DialogTitle>
            {model ? "Edit AI Model" : "Add New AI Model"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span>Basic Settings</span>
                </TabsTrigger>
                <TabsTrigger value="advanced" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  <span>Advanced</span>
                </TabsTrigger>
                <TabsTrigger value="knowledge" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span>Knowledge Base</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4 pt-4">
                {/* Basic information */}
                <ModelBasicInfoFields />

                {/* API Key */}
                <ModelApiKeyField />

                {/* Default model toggle */}
                <ModelDefaultToggle />
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4 pt-4">
                {/* Model settings */}
                <ModelSettingsFields />
              </TabsContent>

              <TabsContent value="knowledge" className="pt-4">
                {/* Knowledge base integration */}
                <ModelKnowledgeTab />
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Spinner className="mr-2" size="sm" />}
                {model ? "Save Changes" : "Create Model"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
