<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix inconsistencies in knowledge_documents table
        $this->fixKnowledgeDocumentsTable();
        
        // Fix inconsistencies in knowledge_embeddings table
        $this->fixKnowledgeEmbeddingsTable();
        
        // Fix inconsistencies in knowledge_sources table
        $this->fixKnowledgeSourcesTable();
        
        // Fix inconsistencies in knowledge_scraped_urls table
        $this->fixKnowledgeScrapedUrlsTable();
        
        // Log completion
        Log::info('Knowledge base schema issues fixed successfully');
    }

    /**
     * Fix knowledge_documents table
     */
    private function fixKnowledgeDocumentsTable(): void
    {
        if (Schema::hasTable('knowledge_documents')) {
            // Ensure all required columns exist
            Schema::table('knowledge_documents', function (Blueprint $table) {
                if (!Schema::hasColumn('knowledge_documents', 'is_active_source')) {
                    $table->boolean('is_active_source')->default(false)->after('status');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'category')) {
                    $table->string('category')->nullable()->after('is_active_source');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'project_id')) {
                    $table->foreignId('project_id')->nullable()->after('category');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'extracted_text')) {
                    $table->text('extracted_text')->nullable()->after('project_id');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'size')) {
                    $table->integer('size')->nullable()->after('file_type');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'has_embeddings')) {
                    $table->boolean('has_embeddings')->default(false)->after('extracted_text');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'embeddings_count')) {
                    $table->integer('embeddings_count')->nullable()->after('has_embeddings');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'embeddings_provider')) {
                    $table->string('embeddings_provider')->nullable()->after('embeddings_count');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'embeddings_model')) {
                    $table->string('embeddings_model')->nullable()->after('embeddings_provider');
                }
                
                if (!Schema::hasColumn('knowledge_documents', 'embeddings_generated_at')) {
                    $table->timestamp('embeddings_generated_at')->nullable()->after('embeddings_model');
                }
            });
            
            // Ensure all required indexes exist
            Schema::table('knowledge_documents', function (Blueprint $table) {
                if (!Schema::hasIndex('knowledge_documents', 'knowledge_documents_is_active_source_index')) {
                    $table->index(['is_active_source']);
                }
                
                if (!Schema::hasIndex('knowledge_documents', 'knowledge_documents_category_index')) {
                    $table->index(['category']);
                }
                
                if (!Schema::hasIndex('knowledge_documents', 'knowledge_documents_project_id_index')) {
                    $table->index(['project_id']);
                }
            });
        }
    }

    /**
     * Fix knowledge_embeddings table
     */
    private function fixKnowledgeEmbeddingsTable(): void
    {
        // Check if both tables exist (knowledge_embeddings and knowledge_document_embeddings)
        if (Schema::hasTable('knowledge_embeddings') && Schema::hasTable('knowledge_document_embeddings')) {
            // Migrate data from knowledge_document_embeddings to knowledge_embeddings if needed
            $documentEmbeddingsCount = DB::table('knowledge_document_embeddings')->count();
            
            if ($documentEmbeddingsCount > 0) {
                $embeddingsCount = DB::table('knowledge_embeddings')->count();
                
                if ($embeddingsCount === 0) {
                    // Copy data from knowledge_document_embeddings to knowledge_embeddings
                    DB::statement('
                        INSERT INTO knowledge_embeddings 
                        (document_id, chunk_index, chunk_text, embedding, provider, model, dimensions, metadata, created_at, updated_at)
                        SELECT 
                            document_id, chunk_index, chunk_text, embedding, provider, model, dimensions, metadata, created_at, updated_at
                        FROM knowledge_document_embeddings
                    ');
                    
                    Log::info("Migrated {$documentEmbeddingsCount} embeddings from knowledge_document_embeddings to knowledge_embeddings");
                }
            }
            
            // Drop the duplicate table
            Schema::dropIfExists('knowledge_document_embeddings');
            Log::info('Dropped duplicate table knowledge_document_embeddings');
        }
        
        // Ensure knowledge_embeddings table exists with correct structure
        if (!Schema::hasTable('knowledge_embeddings')) {
            Schema::create('knowledge_embeddings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('document_id')->constrained('knowledge_documents')->onDelete('cascade');
                $table->integer('chunk_index');
                $table->text('chunk_text');
                $table->json('embedding');
                $table->string('provider', 50);
                $table->string('model', 100);
                $table->integer('dimensions')->default(0);
                $table->json('metadata')->nullable();
                $table->timestamps();
                
                // Add index for faster lookups
                $table->index(['document_id', 'chunk_index']);
            });
            
            Log::info('Created knowledge_embeddings table');
        }
    }

    /**
     * Fix knowledge_sources table
     */
    private function fixKnowledgeSourcesTable(): void
    {
        if (Schema::hasTable('knowledge_sources')) {
            // Ensure all required columns exist
            Schema::table('knowledge_sources', function (Blueprint $table) {
                if (!Schema::hasColumn('knowledge_sources', 'project_id')) {
                    $table->foreignId('project_id')->nullable()->after('description');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'table_name')) {
                    $table->string('table_name')->nullable()->after('project_id');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'query')) {
                    $table->text('query')->nullable()->after('table_name');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'sync_frequency')) {
                    $table->string('sync_frequency')->nullable()->after('status');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'incremental_sync')) {
                    $table->boolean('incremental_sync')->default(true)->after('sync_frequency');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'config')) {
                    $table->json('config')->nullable()->after('incremental_sync');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'last_synced_at')) {
                    $table->timestamp('last_synced_at')->nullable()->after('config');
                }
                
                if (!Schema::hasColumn('knowledge_sources', 'embedding_model_id')) {
                    $table->foreignId('embedding_model_id')->nullable()->after('last_synced_at');
                }
            });
        }
    }

    /**
     * Fix knowledge_scraped_urls table
     */
    private function fixKnowledgeScrapedUrlsTable(): void
    {
        // Check if both tables exist (knowledge_scraped_urls and scraped_urls)
        if (Schema::hasTable('knowledge_scraped_urls') && Schema::hasTable('scraped_urls')) {
            // Migrate data from scraped_urls to knowledge_scraped_urls if needed
            $scrapedUrlsCount = DB::table('scraped_urls')->count();
            
            if ($scrapedUrlsCount > 0) {
                $knowledgeScrapedUrlsCount = DB::table('knowledge_scraped_urls')->count();
                
                if ($knowledgeScrapedUrlsCount === 0) {
                    // Copy data from scraped_urls to knowledge_scraped_urls
                    // Note: Column names might differ, adjust as needed
                    DB::statement('
                        INSERT INTO knowledge_scraped_urls 
                        (source_id, url, title, raw_content, text_content, project_id, created_at, updated_at)
                        SELECT 
                            source_id, url, title, content as raw_content, content as text_content, project_id, created_at, updated_at
                        FROM scraped_urls
                    ');
                    
                    Log::info("Migrated {$scrapedUrlsCount} records from scraped_urls to knowledge_scraped_urls");
                }
            }
            
            // Drop the duplicate table
            Schema::dropIfExists('scraped_urls');
            Log::info('Dropped duplicate table scraped_urls');
        }
        
        // Ensure knowledge_scraped_urls table exists with correct structure
        if (!Schema::hasTable('knowledge_scraped_urls')) {
            Schema::create('knowledge_scraped_urls', function (Blueprint $table) {
                $table->id();
                $table->foreignId('source_id')->nullable()->constrained('knowledge_sources')->nullOnDelete();
                $table->string('url');
                $table->text('title')->nullable();
                $table->longText('raw_content')->nullable();
                $table->longText('text_content')->nullable();
                $table->longText('table_content')->nullable();
                $table->json('json_content')->nullable();
                $table->string('status')->default('active');
                $table->json('metadata')->nullable();
                $table->foreignId('project_id')->nullable();
                $table->string('table_name')->nullable();
                $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
                $table->timestamps();
                
                // Add indexes for faster lookups
                $table->index(['source_id', 'status']);
                $table->index(['url']);
                $table->index(['project_id']);
            });
            
            Log::info('Created knowledge_scraped_urls table');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration only fixes inconsistencies, no need for down method
    }
};
