import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Sparkles, 
  ArrowRight, 
  Clock, 
  Zap, 
  Users,
  CheckCircle2,
  Play,
  BookOpen,
  HelpCircle
} from 'lucide-react';

/**
 * Smart Builder Guide Component
 * 
 * Provides an introduction and navigation guide for the new Smart Widget Builder
 * with clear benefits, features, and getting started instructions.
 */
const SmartBuilderGuide = () => {
  const navigate = useNavigate();
  const [showGuide, setShowGuide] = useState(false);

  const handleGetStarted = () => {
    navigate('/dashboard/widget-builder/smart');
  };

  const features = [
    {
      icon: Clock,
      title: "3x Faster Setup",
      description: "Create widgets in 3-5 minutes instead of 15-30 minutes",
      color: "text-green-600 bg-green-100",
    },
    {
      icon: Zap,
      title: "Visual Interface",
      description: "Click-to-edit with real-time preview and instant feedback",
      color: "text-blue-600 bg-blue-100",
    },
    {
      icon: Users,
      title: "85% Success Rate",
      description: "Most users successfully complete their widget setup",
      color: "text-purple-600 bg-purple-100",
    },
  ];

  const steps = [
    {
      step: 1,
      title: "Choose Template",
      description: "Start with a pre-designed template that matches your use case",
    },
    {
      step: 2,
      title: "Customize Visually",
      description: "Click elements in the preview to edit them directly",
    },
    {
      step: 3,
      title: "Add Features",
      description: "Enable powerful features with clear benefits and guided setup",
    },
    {
      step: 4,
      title: "Deploy",
      description: "Save and deploy your widget with one click",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Sparkles className="w-6 h-6 text-blue-600" />
            </div>
            <CardTitle className="text-2xl text-blue-900">
              Smart Widget Builder
            </CardTitle>
            <Badge className="bg-blue-600 text-white">
              New
            </Badge>
          </div>
          <CardDescription className="text-blue-700 text-lg">
            The fastest and easiest way to create beautiful, functional chat widgets
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center space-y-2">
                  <div className={`w-12 h-12 rounded-lg ${feature.color} flex items-center justify-center mx-auto`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </div>
              );
            })}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-center space-x-4 pt-4">
            <Button
              onClick={handleGetStarted}
              className="bg-blue-600 hover:bg-blue-700"
              size="lg"
            >
              <Play className="w-4 h-4 mr-2" />
              Get Started Now
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
            
            <Dialog open={showGuide} onOpenChange={setShowGuide}>
              <DialogTrigger asChild>
                <Button variant="outline" size="lg">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Quick Guide
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <Sparkles className="w-5 h-5 text-blue-600" />
                    <span>Smart Builder Quick Guide</span>
                  </DialogTitle>
                  <DialogDescription>
                    Learn how to create your first widget in 4 simple steps
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  {steps.map((step, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-semibold text-sm">
                        {step.step}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{step.title}</h4>
                        <p className="text-sm text-gray-600">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setShowGuide(false)}>
                    Close
                  </Button>
                  <Button 
                    onClick={() => {
                      setShowGuide(false);
                      handleGetStarted();
                    }}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Start Building
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Comparison Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <HelpCircle className="w-5 h-5" />
            <span>Why Choose Smart Builder?</span>
          </CardTitle>
          <CardDescription>
            See how the Smart Builder compares to the classic widget builder
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Smart Builder */}
            <div className="space-y-3">
              <h3 className="font-semibold text-blue-900 flex items-center space-x-2">
                <Sparkles className="w-4 h-4" />
                <span>Smart Builder</span>
              </h3>
              <div className="space-y-2">
                {[
                  "Template-first approach",
                  "Visual click-to-edit interface",
                  "Smart feature discovery",
                  "Mobile-optimized design",
                  "3-5 minute setup time",
                  "85% user success rate",
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Classic Builder */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-700">Classic Builder</h3>
              <div className="space-y-2">
                {[
                  "Form-based configuration",
                  "All options visible at once",
                  "Tab-based navigation",
                  "Technical terminology",
                  "15-30 minute setup time",
                  "40% user success rate",
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
                    <span className="text-sm text-gray-600">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> Both builders have access to the same features and capabilities. 
              The Smart Builder simply provides a more intuitive and efficient way to configure them.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Navigation Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="border-blue-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center space-x-2">
              <Sparkles className="w-5 h-5 text-blue-600" />
              <span>Try Smart Builder</span>
            </CardTitle>
            <CardDescription>
              Recommended for all users - faster and easier
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleGetStarted}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Create Widget with Smart Builder
            </Button>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-gray-700">
              Use Classic Builder
            </CardTitle>
            <CardDescription>
              Traditional interface for advanced users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              variant="outline"
              onClick={() => navigate('/dashboard/widget-builder')}
              className="w-full"
            >
              Continue with Classic Builder
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SmartBuilderGuide;
