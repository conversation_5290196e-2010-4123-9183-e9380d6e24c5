/**
 * Component Library Index
 * 
 * This file organizes exports from various component groups for easier importing
 */

// Widget Builder Component - we've consolidated to a single implementation
// SmartWidgetBuilder is exported as both WidgetBuilder (for backward compatibility) 
// and SmartWidgetBuilder (for new code)
export { default as WidgetBuilder } from './SmartWidgetBuilder';
export { default as SmartWidgetBuilder } from './SmartWidgetBuilder';

// Widget Builder Sub-components - use these for specific parts
export * from './widget-builder';

// Note: UI components should be imported directly from their source
// export * from './ui';  // Remove since it doesn't exist 