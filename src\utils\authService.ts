import api from './api';

export interface User {
  id: number;
  name: string;
  email: string;
  // Add other user fields as needed
}

// Authenticated login
export const login = async (
  email: string,
  password: string,
  remember_me: boolean = false
): Promise<User> => {
  await getCsrfToken();
  const response = await api.post('/login', { email, password, remember_me });

  // The backend returns { success, message, user }
  // The token is stored in an HTTP-only cookie by the backend
  return response.data.user;
};

// Register a new user
export const register = async (
  name: string,
  email: string,
  password: string,
  password_confirmation: string
): Promise<User> => {
  await getCsrfToken();
  const response = await api.post('/register', { name, email, password, password_confirmation });
  return response.data.user;
};

// Logout and clear cookies
export const logout = async (): Promise<void> => {
  await api.post('/logout');
};

// Fetch current user info
export const getUser = async (): Promise<User> => {
  const response = await api.get('/user');
  return response.data.user;
};

// Check if the user is authenticated
export const checkAuth = async (): Promise<boolean> => {
  try {
    const response = await api.get('/auth/check');
    return response.data.authenticated;
  } catch (error) {
    return false;
  }
};

/**
 * Fetch CSRF cookie from Sanctum.
 * Should be called before login or registration.
 */
export const getCsrfToken = async (): Promise<void> => {
  try {
    // Extract the base URL from the API URL (removing /api if present)
    const envUrl = import.meta.env.VITE_API_URL || '';
    const baseUrl = envUrl.replace(/\/api\/?$/, '');

    // Use fetch with credentials to ensure cookies are sent and stored
    const response = await fetch(`${baseUrl}/sanctum/csrf-cookie`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch CSRF cookie: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    throw error;
  }
};
