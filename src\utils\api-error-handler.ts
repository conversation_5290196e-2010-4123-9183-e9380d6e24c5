import { AxiosError } from 'axios';

// Error types
export interface ApiError {
    code: number;
    message: string;
    path?: string;
    handled: boolean;
    timestamp: number;
    details?: unknown;
}

// Global event bus for API errors with better error tracking
export class ApiErrorBus {
    private static listeners: Map<string, ((error: ApiError) => void)[]> = new Map();
    private static recentErrors: ApiError[] = [];
    private static maxRecentErrors = 10;

    // Register a listener for specific error paths
    static subscribe(path: string, callback: (error: ApiError) => void): () => void {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, []);
        }

        this.listeners.get(path)?.push(callback);

        // Return unsubscribe function
        return () => {
            const listeners = this.listeners.get(path);
            if (listeners) {
                const index = listeners.indexOf(callback);
                if (index !== -1) {
                    listeners.splice(index, 1);
                }
            }
        };
    }

    // Publish an error to listeners
    static publish(error: ApiError): boolean {
        let handled = false;

        // Add to recent errors for debugging
        this.trackError(error);

        // Notify exact path subscribers
        if (error.path) {
            const pathListeners = this.listeners.get(error.path);
            if (pathListeners?.length) {
                pathListeners.forEach(listener => listener(error));
                handled = true;
            }
        }

        // Notify global error subscribers
        const globalListeners = this.listeners.get('*');
        if (globalListeners?.length) {
            globalListeners.forEach(listener => listener(error));
            handled = handled || !!globalListeners.length;
        }

        return handled;
    }

    // Track recent errors for debugging
    private static trackError(error: ApiError): void {
        this.recentErrors.unshift(error);
        if (this.recentErrors.length > this.maxRecentErrors) {
            this.recentErrors.pop();
        }
    }

    // Get recent errors for debugging
    static getRecentErrors(): ApiError[] {
        return [...this.recentErrors];
    }

    // Clear tracked errors
    static clearErrors(): void {
        this.recentErrors = [];
    }
}

// Utility to normalize error structure
export function normalizeApiError(error: any): ApiError {
    // Handle the specific error format from the error in the ticket
    if (error && error.code === 403 && error.reqInfo?.pathPrefix && error.reqInfo?.path) {
        return {
            code: error.code,
            message: error.message || error.data?.msg || 'Permission error',
            path: `${error.reqInfo.pathPrefix}${error.reqInfo.path}`,
            handled: false,
            timestamp: Date.now(),
            details: error.data || error.reqInfo
        };
    }

    // Generic error case
    return {
        code: error.code || error.status || error.response?.status || 500,
        message: error.message || 'Unknown error',
        path: error.path || error.url || error.config?.url,
        handled: false,
        timestamp: Date.now(),
        details: error.data || error.response?.data
    };
}

// Handle API errors globally
export function setupGlobalErrorHandler() {
    window.addEventListener('unhandledrejection', (event) => {
        const error = event.reason;

        // Only handle API-related errors
        if (error && typeof error === 'object' && ('code' in error || 'response' in error)) {
            const apiError = normalizeApiError(error);

            // Special handling for permission errors
            if (apiError.code === 403) {
                // Publish to error bus and mark as handled if listeners exist
                const handled = ApiErrorBus.publish(apiError);
                if (handled) {
                    apiError.handled = true;
                    event.preventDefault();
                }
            }
        }
    });

    // Also add window error handler for non-promise errors
    window.addEventListener('error', (event) => {
        const error = event.error;

        // Only handle API-related errors with proper structure
        if (error && typeof error === 'object' && ('code' in error || 'response' in error)) {
            const apiError = normalizeApiError(error);

            // Handle permission errors or API errors
            if (apiError.code === 403 || (apiError.path && apiError.path.includes('/api/'))) {
                // Publish to error bus and mark as handled if listeners exist
                const handled = ApiErrorBus.publish(apiError);
                if (handled) {
                    apiError.handled = true;
                    event.preventDefault();
                }
            }
        }
    });
}

// Enhanced Axios error interceptor
export function setupAxiosErrorInterceptor(axiosInstance: any) {
    axiosInstance.interceptors.response.use(
        (response: any) => response,
        (error: AxiosError) => {
            // Process axios errors into our standard format
            if (error.response) {
                const status = error.response.status;
                const path = error.config?.url;

                if (status === 403) {
                    const apiError: ApiError = {
                        code: status,
                        message: error.response.data?.message || error.response.data?.msg || 'Permission denied',
                        path,
                        handled: false,
                        timestamp: Date.now(),
                        details: error.response.data
                    };

                    // Publish to error bus
                    ApiErrorBus.publish(apiError);
                }
            }

            // Always reject the promise to allow component-level handling
            return Promise.reject(error);
        }
    );
}

export default {
    ApiErrorBus,
    setupGlobalErrorHandler,
    setupAxiosErrorInterceptor,
    normalizeApiError
}; 