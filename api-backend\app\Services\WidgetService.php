<?php

namespace App\Services;

use App\Models\Widget;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class WidgetService
{
    protected WidgetBehaviorService $behaviorService;
    protected WidgetLogoService $logoService;
    protected WebhookService $webhookService;

    public function __construct(
        WidgetBehaviorService $behaviorService,
        WidgetLogoService $logoService,
        WebhookService $webhookService
    ) {
        $this->behaviorService = $behaviorService;
        $this->logoService = $logoService;
        $this->webhookService = $webhookService;
    }

    /**
     * Get all widgets for a user.
     */
    public function getUserWidgets(User $user): Collection
    {
        return Widget::where('user_id', $user->id)
            ->where('is_active', true)
            ->with(['aiModel', 'behavior', 'logo', 'webhooks'])
            ->get();
    }

    /**
     * Get a specific widget by ID for a user.
     */
    public function getUserWidget(User $user, int $widgetId): Widget
    {
        return Widget::where('id', $widgetId)
            ->where('user_id', $user->id)
            ->with(['aiModel', 'behavior', 'logo', 'webhooks'])
            ->firstOrFail();
    }

    /**
     * Get a widget by its public widget_id.
     */
    public function getPublicWidget(string $widgetId): Widget
    {
        return cache()->remember('widget_public_'.$widgetId, 3600, function () use ($widgetId) {
            return Widget::where('widget_id', $widgetId)
                ->where('is_active', true)
                ->with(['behavior', 'logo', 'activeWebhooks'])
                ->firstOrFail();
        });
    }

    /**
     * Create a new widget.
     */
    public function createWidget(User $user, array $data): Widget
    {
        // Extract core widget data
        $widgetData = $this->extractCoreWidgetData($data);
        $widgetData['user_id'] = $user->id;

        // Create the widget
        $widget = Widget::create($widgetData);

        // Handle related data
        $this->handleRelatedData($widget, $data);

        // Load relationships for response
        $widget->load(['aiModel', 'behavior', 'logo', 'webhooks']);

        // Clear cache
        $this->clearWidgetCache($widget->widget_id);

        return $widget;
    }

    /**
     * Update an existing widget.
     */
    public function updateWidget(User $user, int $widgetId, array $data): Widget
    {
        $widget = $this->getUserWidget($user, $widgetId);

        // Update core widget data
        $coreData = $this->extractCoreWidgetData($data);
        $widget->update($coreData);

        // Handle related data
        $this->handleRelatedData($widget, $data);

        // Load relationships for response
        $widget->load(['aiModel', 'behavior', 'logo', 'webhooks']);

        // Clear cache
        $this->clearWidgetCache($widget->widget_id);

        return $widget;
    }

    /**
     * Delete a widget.
     */
    public function deleteWidget(User $user, int $widgetId): void
    {
        $widget = $this->getUserWidget($user, $widgetId);

        // Clear cache before deletion
        $this->clearWidgetCache($widget->widget_id);

        $widget->delete();
    }

    /**
     * Extract core widget data from request data.
     */
    protected function extractCoreWidgetData(array $data): array
    {
        return [
            'name' => $data['name'] ?? 'My Chat Widget',
            'ai_model_id' => $data['ai_model_id'] ?? null,
            'settings' => $data['settings'] ?? [],
            'allowed_domains' => $data['allowed_domains'] ?? [],
            'position_type' => $data['position_type'] ?? 'fixed',
            'position_settings' => $data['position_settings'] ?? [],
            'custom_css' => $data['custom_css'] ?? '',
            'typography' => $data['typography'] ?? [],
            'button_customization' => $data['button_customization'] ?? [],
            'mobile_settings' => $data['mobile_settings'] ?? [],
            'is_active' => $data['is_active'] ?? true,
        ];
    }

    /**
     * Handle related data (behavior, logo, webhooks).
     */
    protected function handleRelatedData(Widget $widget, array $data): void
    {
        // Handle behavior settings
        if (isset($data['settings']['behavior'])) {
            $this->behaviorService->updateBehavior($widget->id, $data['settings']['behavior']);
        }

        // Handle logo
        if (isset($data['logo_url']) && !empty($data['logo_url'])) {
            $logoData = [
                'logoData' => $data['logo_url'],
                'logoUrl' => $data['logo_url'],
            ];
            $this->logoService->updateLogo($widget->id, $logoData);
        }

        // Handle webhooks
        if (isset($data['settings']['advanced']['webhookUrl']) && !empty($data['settings']['advanced']['webhookUrl'])) {
            $webhookData = [
                'url' => $data['settings']['advanced']['webhookUrl'],
                'name' => 'Default Webhook',
                'on_chat_start' => true,
                'on_chat_end' => true,
                'on_message_sent' => true,
                'on_message_received' => true,
            ];
            $this->webhookService->createOrUpdateWebhook($widget->id, $webhookData);
        }
    }

    /**
     * Transform widget data for API response.
     */
    public function transformForApi(Widget $widget): array
    {
        $data = $widget->toArray();

        // Add behavior settings to the main settings array
        if ($widget->behavior) {
            $data['settings']['behavior'] = $widget->behavior->toSettingsArray();
        }

        // Add logo URL for backward compatibility
        if ($widget->logo) {
            $data['logo_url'] = $widget->logo->getDisplayData();
        }

        // Add webhook URL for backward compatibility
        $activeWebhook = $widget->activeWebhooks->first();
        if ($activeWebhook) {
            $data['settings']['advanced']['webhookUrl'] = $activeWebhook->url;
        }

        return $data;
    }

    /**
     * Clear widget cache.
     */
    protected function clearWidgetCache(string $widgetId): void
    {
        cache()->forget('widget_public_'.$widgetId);
    }

    /**
     * Check if a domain is allowed for a widget.
     */
    public function isDomainAllowed(Widget $widget, string $domain): bool
    {
        if (empty($widget->allowed_domains)) {
            return true; // No restrictions
        }

        foreach ($widget->allowed_domains as $allowedDomain) {
            if ($this->matchesDomain($domain, $allowedDomain)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a domain matches an allowed domain pattern.
     */
    protected function matchesDomain(string $domain, string $pattern): bool
    {
        // Remove protocol if present
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $pattern = preg_replace('/^https?:\/\//', '', $pattern);

        // Handle wildcard patterns
        if (str_contains($pattern, '*')) {
            $pattern = str_replace('*', '.*', $pattern);
            return preg_match('/^' . $pattern . '$/i', $domain);
        }

        return strcasecmp($domain, $pattern) === 0;
    }

    /**
     * Get version history for a widget.
     */
    public function getVersionHistory(int $widgetId): array
    {
        // For now, return mock data since we don't have a versions table
        // In a real implementation, you would query a widget_versions table
        return [
            [
                'id' => 1,
                'version' => '1.0',
                'description' => 'Initial version',
                'created_at' => now()->subDays(7)->toISOString(),
                'created_by' => '<EMAIL>',
            ],
            [
                'id' => 2,
                'version' => '1.1',
                'description' => 'Updated styling and behavior',
                'created_at' => now()->subDays(3)->toISOString(),
                'created_by' => '<EMAIL>',
            ],
        ];
    }

    /**
     * Create a new version of a widget.
     */
    public function createVersion(int $widgetId, string $description): array
    {
        // In a real implementation, you would:
        // 1. Get current widget state
        // 2. Save it as a new version in widget_versions table
        // 3. Increment version number

        $newVersion = '1.' . (time() % 100); // Simple version numbering

        return [
            'id' => time(),
            'version' => $newVersion,
            'description' => $description,
            'created_at' => now()->toISOString(),
            'created_by' => auth()->user()->email ?? 'system',
        ];
    }

    /**
     * Restore a specific version of a widget.
     */
    public function restoreVersion(int $widgetId, string $version): Widget
    {
        // In a real implementation, you would:
        // 1. Find the version record
        // 2. Restore widget data from that version
        // 3. Update the current widget

        $widget = Widget::findOrFail($widgetId);

        // For now, just update the version field
        $widget->update(['version' => $version]);

        return $widget->fresh(['aiModel', 'behavior', 'logo', 'webhooks']);
    }

    /**
     * Bulk update widgets.
     */
    public function bulkUpdate(User $user, array $widgetIds, array $updateData): array
    {
        $updated = 0;
        $failed = 0;
        $errors = [];

        foreach ($widgetIds as $widgetId) {
            try {
                $widget = $this->getUserWidget($user, $widgetId);
                $coreData = $this->extractCoreWidgetData($updateData);
                $widget->update($coreData);

                // Handle related data
                $this->handleRelatedData($widget, $updateData);

                // Clear cache
                $this->clearWidgetCache($widget->widget_id);

                $updated++;
            } catch (\Exception $e) {
                $failed++;
                $errors[] = "Widget {$widgetId}: " . $e->getMessage();
            }
        }

        return [
            'updated_count' => $updated,
            'failed_count' => $failed,
            'errors' => $errors,
        ];
    }

    /**
     * Bulk delete widgets.
     */
    public function bulkDelete(User $user, array $widgetIds): array
    {
        $deleted = 0;
        $failed = 0;
        $errors = [];

        foreach ($widgetIds as $widgetId) {
            try {
                $widget = $this->getUserWidget($user, $widgetId);

                // Clear cache before deletion
                $this->clearWidgetCache($widget->widget_id);

                $widget->delete();
                $deleted++;
            } catch (\Exception $e) {
                $failed++;
                $errors[] = "Widget {$widgetId}: " . $e->getMessage();
            }
        }

        return [
            'deleted_count' => $deleted,
            'failed_count' => $failed,
            'errors' => $errors,
        ];
    }

    /**
     * Bulk activate/deactivate widgets.
     */
    public function bulkActivate(User $user, array $widgetIds, bool $isActive): array
    {
        $updated = 0;
        $failed = 0;
        $errors = [];

        foreach ($widgetIds as $widgetId) {
            try {
                $widget = $this->getUserWidget($user, $widgetId);
                $widget->update(['is_active' => $isActive]);

                // Clear cache
                $this->clearWidgetCache($widget->widget_id);

                $updated++;
            } catch (\Exception $e) {
                $failed++;
                $errors[] = "Widget {$widgetId}: " . $e->getMessage();
            }
        }

        return [
            'updated_count' => $updated,
            'failed_count' => $failed,
            'errors' => $errors,
        ];
    }
}
