// Add this method to the AIModelController class

/**
 * Test chat with a temporary AI model (not saved in database).
 *
 * @param  \Illuminate\Http\Request  $request
 * @return \Illuminate\Http\Response
 */
public function testChatDirect(Request $request)
{
    $validator = Validator::make($request->all(), [
        'message' => 'required|string|max:2000',
        'provider' => 'required|string',
        'api_key' => 'required|string',
        'model_name' => 'nullable|string',
        'temperature' => 'nullable|numeric|min:0|max:2',
        'max_tokens' => 'nullable|integer|min:1|max:32000',
        'system_prompt' => 'nullable|string|max:4000',
    ]);

    if ($validator->fails()) {
        return response()->json(['errors' => $validator->errors(), 'success' => false], 422);
    }

    try {
        // Create a temporary model object
        $tempModel = new \App\Models\AIModel();
        $tempModel->provider = $request->provider;
        $tempModel->api_key = $request->api_key;
        $tempModel->settings = [
            'model_name' => $request->model_name,
            'temperature' => $request->temperature ?? 0.7,
            'max_tokens' => $request->max_tokens ?? 2048,
        ];

        // Get the appropriate provider for this model
        $provider = $this->getProviderForModel($tempModel);

        // Start timing the response
        $startTime = microtime(true);

        // Get the response from the provider
        $response = $provider->generateChatResponse(
            $tempModel,
            $request->message,
            $request->system_prompt ?? null
        );

        // Calculate response time
        $responseTime = round((microtime(true) - $startTime) * 1000);

        // Calculate tokens (if available in response)
        $tokensInput = $response['metadata']['tokens_input'] ?? strlen($request->message) / 4;
        $tokensOutput = $response['metadata']['tokens_output'] ?? strlen($response['content']) / 4;

        return response()->json([
            'success' => !isset($response['metadata']['error']),
            'response' => $response['content'],
            'metadata' => [
                'model' => $request->model_name ?? 'unknown',
                'provider' => $tempModel->provider,
                'response_time' => $responseTime,
                'tokens_input' => $tokensInput,
                'tokens_output' => $tokensOutput,
                'error' => $response['metadata']['error'] ?? null
            ]
        ]);
    } catch (\Exception $e) {
        \Log::error('Direct chat test failed: ' . $e->getMessage());
        return response()->json([
            'success' => false,
            'response' => 'Error: ' . $e->getMessage(),
            'metadata' => [
                'model' => $request->model_name ?? 'unknown',
                'provider' => $request->provider,
                'response_time' => 0,
                'tokens_input' => 0,
                'tokens_output' => 0,
                'error' => $e->getMessage()
            ]
        ]);
    }
}
