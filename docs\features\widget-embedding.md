# Widget Embedding System

This document outlines how to set up and use the widget embedding system, which allows your AI chat widget to be embedded on third-party websites.

## Overview

The widget embedding system provides a way to:

1. Generate embed code for widgets that can be placed on third-party websites
2. Handle cross-origin requests securely using CORS
3. Manage guest users with different flow options (auto, required, none)
4. Provide a webhook system for integrating with external systems
5. Track widget usage through analytics

## Setup Instructions

### 1. Environment Configuration

Add the following variables to your `.env` file:

```
# Widget Configuration
WIDGET_ASSET_URL="https://your-api-domain.com"
CORS_ALLOWED_ORIGINS="*"
SANCTUM_STATEFUL_DOMAINS="localhost:3000,127.0.0.1:3000,your-frontend-domain.com"
```

- `WIDGET_ASSET_URL`: The base URL where your widget assets are hosted (typically your API domain)
- `CORS_ALLOWED_ORIGINS`: Domains allowed to access your API (use * for public widgets)
- `SANCTUM_STATEFUL_DOMAINS`: Frontend domains for authentication

### 2. Database Migration

Run the migration to add the guest_user_flow field to the widgets table:

```bash
php artisan migrate
```

This adds a `guest_user_flow` column to the widgets table with options:
- `auto`: Automatically create guest users (default)
- `required`: Require guest information before chat starts
- `none`: No guest user tracking

### 3. Routes Setup

Ensure the following routes are properly set up:

```php
// API routes for widget embedding
Route::middleware(['throttle:widget_embed'])->group(function () {
    Route::get('/widgets/public/{widgetId}', [WidgetController::class, 'getByWidgetId']);
    Route::options('/widget/cors', [EmbedCodeController::class, 'getCorsHeaders']);
    Route::get('/widget/preview/{widget_id}', [EmbedCodeController::class, 'preview'])
        ->name('widget.preview')
        ->middleware('signed');
    Route::post('/widget/validate-domain', [EmbedCodeController::class, 'validateDomain']);
});

// Guest user routes
Route::middleware(['throttle:widget_guest'])->group(function () {
    Route::post('/guest/register', [GuestUserController::class, 'register']);
    Route::post('/guest/auto-register', [GuestUserController::class, 'autoRegister']);
    Route::post('/guest/validate', [GuestUserController::class, 'validateSession']);
    Route::post('/guest/details', [GuestUserController::class, 'getDetails']);
    Route::get('/guest/flow', [GuestUserController::class, 'checkGuestFlow']);
});

// Web routes for embed code generation (authenticated)
Route::middleware(['auth'])->group(function () {
    Route::get('/widget/{widgetId}/embed', [EmbedCodeController::class, 'showEmbedCode'])
        ->name('widget.embed');
});
```

### 4. Frontend Files

Ensure the following frontend files are in place:

1. `public/widget/v1/script.js`: The main widget loader script
2. `api-backend/resources/views/widget/embed.blade.php`: The embed code generation page
3. `api-backend/resources/views/widget/preview.blade.php`: The widget preview page

## Guest User Flow Options

The widget supports three guest user flow options:

### Auto (Default)

- Automatically creates a guest user with a generated name
- No registration form is shown to the end user
- Session is stored in localStorage
- User information collected: IP, user agent, page URL, referrer

### Required

- Shows a registration form before starting the chat
- Collects user information: name, email, phone number
- Session is stored in localStorage
- Good for customer service or lead generation

### None

- No guest user tracking
- No user information is collected or stored
- Chat is completely anonymous
- Good for simple information widgets

## Widget JavaScript API

The embedded widget exposes a JavaScript API that can be used to control the widget:

```javascript
// Open the widget
window.AIChatWidget["widget_id"].open();

// Close the widget
window.AIChatWidget["widget_id"].close();

// Toggle widget visibility
window.AIChatWidget["widget_id"].toggle();

// Clear the current session
window.AIChatWidget["widget_id"].clearSession();
```

## Webhook Integration

The widget can send events to a webhook URL, which can be used to integrate with external systems:

1. Set the webhook URL using the `data-webhook-url` attribute in the embed code
2. Events are sent as JSON POST requests with the following structure:

```json
{
  "event": "message_sent",
  "widget_id": "abc123",
  "session_id": "session-uuid",
  "timestamp": "2023-08-21T14:30:00Z",
  "data": {
    "message": "Hello, how can I help you?",
    "sender": "ai"
  },
  "url": "https://customer-website.com/contact"
}
```

Common events:
- `widget_loaded`: Widget script loaded on page
- `widget_opened`: User opened the widget
- `widget_closed`: User closed the widget
- `message_sent`: User or AI sent a message
- `guest_registered`: New guest user registered
- `session_started`: New chat session started
- `session_ended`: Chat session ended

## Domain Restrictions

You can restrict which domains can embed your widget:

1. In the widget settings, specify allowed domains
2. Wildcards are supported (e.g., `*.example.com`)
3. Empty list or `*` allows all domains

## Testing Locally

To test the widget locally:

1. Run your Laravel backend
2. Run your frontend application
3. Navigate to `/widget/{widgetId}/embed` to view and copy the embed code
4. Create a test HTML page and paste the embed code
5. Open the test HTML page in a browser

## Troubleshooting

### CORS Issues

If you're experiencing CORS issues:

1. Check your `config/cors.php` configuration
2. Ensure `supports_credentials` is set to `true`
3. Make sure your widget domains are included in allowed origins
4. Check that all API routes the widget uses are in the cors.php paths array

### Session Issues

If sessions aren't persisting:

1. Check that localStorage is available in the user's browser
2. Verify that the guest user flow is properly configured
3. Check for any JavaScript errors in the console

### Performance Optimization

For better performance:

1. Ensure the script is loaded with `async` attribute
2. Place the script just before the closing `</body>` tag
3. Use the appropriate guest flow for your needs (auto is fastest)
4. Consider using a CDN for serving widget assets 