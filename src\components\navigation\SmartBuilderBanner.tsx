import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Sparkles, 
  ArrowRight, 
  X, 
  Zap, 
  Clock, 
  Users,
  ChevronRight
} from 'lucide-react';

interface SmartBuilderBannerProps {
  onDismiss?: () => void;
  showDismiss?: boolean;
  variant?: 'full' | 'compact';
}

/**
 * Smart Builder Banner Component
 * 
 * Promotional banner to introduce users to the new Smart Widget Builder
 * with clear benefits and call-to-action.
 */
const SmartBuilderBanner = ({ 
  onDismiss, 
  showDismiss = true, 
  variant = 'full' 
}: SmartBuilderBannerProps) => {
  const navigate = useNavigate();
  const [isDismissed, setIsDismissed] = useState(false);

  const handleTrySmartBuilder = () => {
    navigate('/dashboard/widget-builder/smart');
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  if (isDismissed) {
    return null;
  }

  if (variant === 'compact') {
    return (
      <Alert className="border-blue-200 bg-blue-50">
        <Sparkles className="h-4 w-4 text-blue-600" />
        <AlertDescription className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-blue-800">
              <strong>New:</strong> Smart Widget Builder is now available!
            </span>
            <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
              3x Faster
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              onClick={handleTrySmartBuilder}
              className="bg-blue-600 hover:bg-blue-700 h-7"
            >
              Try Now
              <ArrowRight className="w-3 h-3 ml-1" />
            </Button>
            {showDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-7 w-7 p-0 text-blue-600 hover:bg-blue-100"
              >
                <X className="w-3 h-3" />
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 relative overflow-hidden">
      {showDismiss && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="absolute top-2 right-2 h-8 w-8 p-0 text-blue-600 hover:bg-blue-100 z-10"
        >
          <X className="w-4 h-4" />
        </Button>
      )}
      
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          {/* Icon */}
          <div className="flex-shrink-0">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Sparkles className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1 space-y-4">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="text-xl font-bold text-blue-900">
                  Introducing Smart Widget Builder
                </h3>
                <Badge className="bg-blue-600 text-white">
                  New
                </Badge>
              </div>
              <p className="text-blue-700 text-base">
                Create beautiful, functional chat widgets in minutes with our new visual, 
                template-first approach. Perfect for both beginners and experts.
              </p>
            </div>

            {/* Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <div className="p-1 bg-green-100 rounded">
                  <Clock className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">3x Faster Setup</p>
                  <p className="text-xs text-blue-600">3-5 minutes vs 15-30 minutes</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="p-1 bg-purple-100 rounded">
                  <Zap className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">Visual Interface</p>
                  <p className="text-xs text-blue-600">Click-to-edit with live preview</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="p-1 bg-orange-100 rounded">
                  <Users className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">85% Success Rate</p>
                  <p className="text-xs text-blue-600">vs 40% with classic builder</p>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-800">
              <div className="flex items-center space-x-2">
                <ChevronRight className="w-3 h-3" />
                <span>Template gallery with instant preview</span>
              </div>
              <div className="flex items-center space-x-2">
                <ChevronRight className="w-3 h-3" />
                <span>Smart feature discovery with benefits</span>
              </div>
              <div className="flex items-center space-x-2">
                <ChevronRight className="w-3 h-3" />
                <span>Mobile-optimized interface</span>
              </div>
              <div className="flex items-center space-x-2">
                <ChevronRight className="w-3 h-3" />
                <span>All existing features included</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3 pt-2">
              <Button
                onClick={handleTrySmartBuilder}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Try Smart Builder Now
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              
              <Button
                variant="outline"
                onClick={() => navigate('/dashboard/widget-builder')}
                className="border-blue-300 text-blue-700 hover:bg-blue-50"
              >
                Continue with Classic Builder
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-blue-200 rounded-full opacity-20 -translate-y-16 translate-x-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-200 rounded-full opacity-20 translate-y-12 -translate-x-12"></div>
    </Card>
  );
};

export default SmartBuilderBanner;
