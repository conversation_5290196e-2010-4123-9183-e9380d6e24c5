"use client"

import React, { ReactNode } from "react"
import { UseFormReturn } from "react-hook-form"
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface EmailFieldProps {
  form: UseFormReturn<any>
  name: string
  label: string
  placeholder?: string
  description?: string
  required?: boolean
  icon?: ReactNode
  className?: string
  disabled?: boolean
}

/**
 * Email Field Component
 * 
 * A form field for email input with validation and optional icon.
 */
export function EmailField({
  form,
  name,
  label,
  placeholder,
  description,
  required = false,
  icon,
  className,
  disabled = false
}: EmailFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel className="flex items-center gap-1 text-sm font-medium">
            {label}
            {required && <span className="text-red-500">*</span>}
          </FormLabel>
          <FormControl>
            <div className="relative">
              {icon && (
                <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
                  {icon}
                </div>
              )}
              <Input
                type="email"
                placeholder={placeholder}
                className={cn(
                  "bg-background transition-colors focus-visible:ring-1",
                  icon && "pl-10"
                )}
                disabled={disabled}
                {...field}
              />
            </div>
          </FormControl>
          {description && <FormDescription className="text-xs">{description}</FormDescription>}
          <FormMessage className="text-xs font-medium" />
        </FormItem>
      )}
    />
  )
}
