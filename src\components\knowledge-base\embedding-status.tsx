import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Database, AlertCircle, CheckCircle2, Clock } from "lucide-react";
import { formatDistanceToNow } from 'date-fns';

interface EmbeddingStatusProps {
  hasEmbeddings: boolean;
  embeddingsCount?: number;
  embeddingsProvider?: string;
  embeddingsModel?: string;
  embeddingsGeneratedAt?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function EmbeddingStatus({
  hasEmbeddings,
  embeddingsCount,
  embeddingsProvider,
  embeddingsModel,
  embeddingsGeneratedAt,
  size = 'md'
}: EmbeddingStatusProps) {
  const iconSize = size === 'sm' ? 14 : size === 'md' ? 16 : 18;
  
  if (!hasEmbeddings) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="text-muted-foreground border-muted-foreground/30 gap-1">
              <Database className={`h-${iconSize/4} w-${iconSize/4}`} />
              <span>No Embeddings</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>This document doesn't have vector embeddings yet.</p>
            <p>Generate embeddings to enable semantic search.</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant="outline" className="text-primary border-primary/30 gap-1">
            <CheckCircle2 className={`h-${iconSize/4} w-${iconSize/4}`} />
            <span>{embeddingsCount} {embeddingsCount === 1 ? 'Chunk' : 'Chunks'}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent className="space-y-1">
          <p className="font-medium">Vector Embeddings Available</p>
          <div className="text-xs text-muted-foreground">
            <p><span className="font-medium">Provider:</span> {embeddingsProvider}</p>
            <p><span className="font-medium">Model:</span> {embeddingsModel}</p>
            {embeddingsGeneratedAt && (
              <p><span className="font-medium">Generated:</span> {formatDistanceToNow(new Date(embeddingsGeneratedAt), { addSuffix: true })}</p>
            )}
            <p><span className="font-medium">Chunks:</span> {embeddingsCount}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
