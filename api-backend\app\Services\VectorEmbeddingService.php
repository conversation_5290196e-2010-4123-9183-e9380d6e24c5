<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\KnowledgeDocument;
use App\Models\KnowledgeEmbedding;
use App\Models\AIModel;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Exception;
use Throwable;
use App\Models\Document;
use App\Services\DocumentProcessingService;

class VectorEmbeddingService
{
    /**
     * @var DocumentProcessingService
     */
    protected $documentService;

    /**
     * @var array
     */
    protected $retryConfig = [
        'max_attempts' => 3,
        'initial_delay' => 2,
        'backoff_factor' => 2,
    ];

    /**
     * Configuration for embedding models
     */
    protected $embeddingModels = [
        'openai' => [
            'name' => 'OpenAI Ada',
            'endpoint' => 'https://api.openai.com/v1/embeddings',
            'model' => 'text-embedding-ada-002',
            'dimensions' => 1536,
            'max_tokens' => 8191,
        ],
        'huggingface' => [
            'name' => 'Hugging Face Sentence Transformers',
            'endpoint' => 'https://api-inference.huggingface.co/pipeline/feature-extraction',
            'model' => 'sentence-transformers/all-mpnet-base-v2',
            'dimensions' => 768,
            'max_tokens' => 512,
        ],
    ];

    /**
     * Constructor.
     */
    public function __construct(DocumentProcessingService $documentService)
    {
        $this->documentService = $documentService;
    }

    /**
     * Generate embeddings for a document
     *
     * @param KnowledgeDocument $document
     * @param string $provider
     * @param array $options
     * @return bool
     */
    public function generateEmbeddings(KnowledgeDocument $document, string $provider = 'openai', array $options = []): bool
    {
        $lockKey = "embedding_lock_doc_{$document->id}";
        $cacheKey = "embedding_status_doc_{$document->id}";

        // Check if there's already an embedding process running for this document
        if (Cache::has($lockKey)) {
            Log::info("Embedding generation already in progress for document {$document->id}");
            return false;
        }

        // Set a lock to prevent concurrent embedding generation
        Cache::put($lockKey, true, 3600); // 1 hour lock

        try {
            // Check if document has extracted text
            if (empty($document->extracted_text)) {
                Log::warning("Document {$document->id} has no extracted text for embedding generation");
                $this->updateDocumentStatus($document, 'failed_embeddings', 'No extracted text available');
                return false;
            }

            // Get model information if model_id is provided
            $modelName = $this->embeddingModels[$provider]['model'];
            if (!empty($options['model_id'])) {
                $model = AIModel::find($options['model_id']);
                if ($model) {
                    $provider = strtolower($model->provider);
                    $modelName = $model->name;
                    $options['api_key'] = $model->api_key;
                }
            }

            // Update document status to processing
            $this->updateDocumentStatus($document, 'processing_embeddings');

            // Check if we already have some embeddings for this document
            $existingCount = KnowledgeEmbedding::where('document_id', $document->id)->count();

            // Get text chunks
            $chunkSize = $options['chunk_size'] ?? 1000;
            $chunkOverlap = $options['chunk_overlap'] ?? 200;
            $chunks = $this->chunkText($document->extracted_text, $chunkSize, $chunkOverlap);

            // Store status in cache for monitoring
            Cache::put($cacheKey, [
                'status' => 'processing',
                'total_chunks' => count($chunks),
                'processed_chunks' => $existingCount,
                'success_count' => $existingCount,
                'error_count' => 0,
                'last_error' => null,
                'start_time' => now()->timestamp,
            ], 3600);

            // Generate embeddings for each chunk
            $embeddingsData = [];
            $successCount = $existingCount;
            $errorCount = 0;
            $totalChunks = count($chunks);
            $lastError = null;

            // Skip chunks that already have embeddings
            $startIndex = $existingCount;
            $chunksToProcess = array_slice($chunks, $startIndex);

            foreach ($chunksToProcess as $index => $chunk) {
                $actualIndex = $index + $startIndex;

                try {
                    $embedding = $this->generateEmbeddingWithRetry(
                        $chunk,
                        $provider,
                        $modelName,
                        $options['retry']
                    );

                    if ($embedding) {
                        $embeddingsData[] = [
                            'document_id' => $document->id,
                            'chunk_index' => $actualIndex,
                            'chunk_text' => $chunk,
                            'embedding' => json_encode($embedding),
                            'provider' => $provider,
                            'model' => $modelName,
                            'dimensions' => count($embedding),
                            'metadata' => json_encode([
                                'position' => $actualIndex,
                                'total_chunks' => $totalChunks,
                                'document_name' => $document->file_name,
                                'category' => $document->category,
                                'chunk_size' => $chunkSize,
                                'chunk_overlap' => $chunkOverlap,
                            ]),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                        $successCount++;

                        // Update progress periodically
                        if ($successCount % 5 === 0 || $successCount === $totalChunks) {
                            $progress = intval(($successCount / $totalChunks) * 100);
                            $document->update([
                                'embeddings_progress' => $progress
                            ]);

                            // Update cache status
                            Cache::put($cacheKey, [
                                'status' => 'processing',
                                'total_chunks' => $totalChunks,
                                'processed_chunks' => $successCount + $errorCount,
                                'success_count' => $successCount,
                                'error_count' => $errorCount,
                                'last_error' => $lastError,
                                'progress' => $progress,
                                'start_time' => Cache::get($cacheKey)['start_time'],
                            ], 3600);
                        }

                        // Store embeddings in batches to avoid memory issues
                        if (count($embeddingsData) >= 20) {
                            DB::beginTransaction();
                            try {
                                KnowledgeEmbedding::insert($embeddingsData);
                                DB::commit();
                                $embeddingsData = []; // Clear after successful insert
                            } catch (Throwable $e) {
                                DB::rollBack();
                                Log::error("Failed to insert embedding batch: " . $e->getMessage());
                                throw $e;
                            }
                        }
                    } else {
                        $errorCount++;
                        $lastError = "Failed to generate embedding for chunk {$actualIndex}";
                        Log::warning("Failed to generate embedding for document {$document->id}, chunk {$actualIndex}");
                    }
                } catch (Throwable $e) {
                    $errorCount++;
                    $lastError = $e->getMessage();
                    Log::error("Error processing chunk {$actualIndex} for document {$document->id}: " . $e->getMessage());

                    // Continue with next chunk instead of failing the entire process
                    continue;
                }
            }

            // Store any remaining embeddings
            if (!empty($embeddingsData)) {
                DB::beginTransaction();
                try {
                    KnowledgeEmbedding::insert($embeddingsData);
                    DB::commit();
                } catch (Throwable $e) {
                    DB::rollBack();
                    Log::error("Failed to insert final embedding batch: " . $e->getMessage());
                    throw $e;
                }
            }

            // Get total count of embeddings for this document
            $totalEmbeddings = KnowledgeEmbedding::where('document_id', $document->id)->count();

            // Update document status based on results
            if ($totalEmbeddings > 0) {
                $status = ($totalEmbeddings == $totalChunks) ? 'ready' : 'partial_embeddings';
                $document->update([
                    'has_embeddings' => true,
                    'embeddings_count' => $totalEmbeddings,
                    'embeddings_provider' => $provider,
                    'embeddings_model' => $modelName,
                    'embeddings_generated_at' => now(),
                    'embeddings_progress' => intval(($totalEmbeddings / $totalChunks) * 100),
                    'status' => $status
                ]);

                // Update cache status
                Cache::put($cacheKey, [
                    'status' => 'completed',
                    'total_chunks' => $totalChunks,
                    'processed_chunks' => $totalChunks,
                    'success_count' => $totalEmbeddings,
                    'error_count' => $errorCount,
                    'last_error' => $lastError,
                    'progress' => intval(($totalEmbeddings / $totalChunks) * 100),
                    'start_time' => Cache::get($cacheKey)['start_time'],
                    'end_time' => now()->timestamp,
                ], 3600);

                return true;
            }

            // If we got here, no embeddings were generated
            $this->updateDocumentStatus($document, 'failed_embeddings', $lastError ?? 'No embeddings were generated');
            return false;
        } catch (Throwable $e) {
            Log::error("Error generating embeddings for document {$document->id}: " . $e->getMessage());

            // Update cache status
            if (Cache::has($cacheKey)) {
                $status = Cache::get($cacheKey);
                $status['status'] = 'failed';
                $status['last_error'] = $e->getMessage();
                $status['end_time'] = now()->timestamp;
                Cache::put($cacheKey, $status, 3600);
            }

            // Update document status to failed
            $this->updateDocumentStatus($document, 'failed_embeddings', $e->getMessage());

            return false;
        } finally {
            // Always release the lock
            Cache::forget($lockKey);
        }
    }

    /**
     * Generate embedding for a single text chunk with retry logic.
     *
     * @param string $text
     * @param string $provider
     * @param string $modelName
     * @param bool $allowRetry
     * @return array|null
     * @throws Exception
     */
    protected function generateEmbeddingWithRetry(string $text, string $provider, string $modelName, bool $allowRetry = true): ?array
    {
        $attempts = 0;
        $delay = $this->retryConfig['initial_delay'];
        $maxAttempts = $allowRetry ? $this->retryConfig['max_attempts'] : 1;

        while ($attempts < $maxAttempts) {
            $attempts++;

            try {
                return $this->callEmbeddingAPI($text, $provider, $modelName);
            } catch (Exception $e) {
                $errorMessage = $e->getMessage();

                // Check for rate limit errors
                $isRateLimitError =
                    stripos($errorMessage, 'rate limit') !== false ||
                    stripos($errorMessage, 'too many requests') !== false ||
                    stripos($errorMessage, '429') !== false;

                // If last attempt or not a rate limit error and not allowing retries, throw
                if ($attempts >= $maxAttempts || (!$isRateLimitError && !$allowRetry)) {
                    throw $e;
                }

                // Exponential backoff
                Log::warning("Embedding API attempt {$attempts} failed: {$errorMessage}. Retrying in {$delay}s.");
                sleep($delay);
                $delay *= $this->retryConfig['backoff_factor'];
            }
        }

        // Should not reach here, but just in case
        throw new Exception("Failed to generate embedding after {$maxAttempts} attempts");
    }

    /**
     * Call the embedding API
     *
     * @param string $text
     * @param string $provider
     * @param string $modelName
     * @return array|null
     * @throws Exception
     */
    protected function callEmbeddingAPI(string $text, string $provider, string $modelName): ?array
    {
        $config = $this->embeddingModels[$provider] ?? null;

        if (!$config) {
            throw new Exception("Unknown embedding provider: {$provider}");
        }

        $apiKey = $this->embeddingModels[$provider]['api_key'] ?? env(strtoupper($provider) . '_API_KEY');

        if (!$apiKey) {
            throw new Exception("No API key found for provider: {$provider}");
        }

        // Check if text is empty
        if (empty(trim($text))) {
            Log::warning("Empty text provided for embedding generation");
            return [];
        }

        // Check if text exceeds maximum token limit
        $maxTokens = $this->embeddingModels[$provider]['max_tokens'];
        $estimatedTokens = intval(str_word_count($text) * 1.3); // Rough estimate

        if ($estimatedTokens > $maxTokens) {
            Log::warning("Text likely exceeds maximum token limit ({$estimatedTokens} > {$maxTokens})");
            // Truncate text to avoid API errors
            $text = substr($text, 0, intval($maxTokens / $estimatedTokens * strlen($text)));
        }

        // Retry logic with exponential backoff
        $maxRetries = $this->retryConfig['max_attempts'];
        $retryDelay = $this->retryConfig['initial_delay'];
        $timeout = 60; // seconds

        // Rate limiting tracking
        $rateLimitKey = "rate_limit_{$provider}";
        $rateLimitResetKey = "rate_limit_reset_{$provider}";

        // Check if we're currently rate limited
        if (Cache::has($rateLimitKey) && Cache::get($rateLimitKey) === true) {
            $resetTime = Cache::get($rateLimitResetKey, 0);
            $currentTime = now()->timestamp;

            if ($resetTime > $currentTime) {
                $waitTime = $resetTime - $currentTime;
                Log::info("Rate limit in effect for {$provider}. Waiting {$waitTime} seconds.");

                if ($waitTime > 30) {
                    throw new Exception("Rate limit exceeded for {$provider}. Try again later.");
                }

                // Wait for rate limit to reset
                sleep(min($waitTime, 10)); // Wait at most 10 seconds
            } else {
                // Reset has passed
                Cache::forget($rateLimitKey);
                Cache::forget($rateLimitResetKey);
            }
        }

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                if ($provider === 'openai') {
                    $response = Http::timeout($timeout)
                        ->withHeaders([
                            'Authorization' => "Bearer {$apiKey}",
                            'Content-Type' => 'application/json',
                            'OpenAI-Organization' => $this->embeddingModels[$provider]['organization_id'] ?? env('OPENAI_ORGANIZATION_ID', ''),
                        ])
                        ->post($this->embeddingModels[$provider]['endpoint'], [
                            'input' => $text,
                            'model' => $modelName,
                            'dimensions' => $this->embeddingModels[$provider]['dimensions'] ?? null,
                        ]);

                    if ($response->successful()) {
                        $data = $response->json();
                        return $data['data'][0]['embedding'] ?? null;
                    }

                    // Handle rate limiting
                    if ($response->status() === 429) {
                        $retryAfter = $response->header('Retry-After', 60);
                        $resetTime = now()->addSeconds((int)$retryAfter)->timestamp;

                        // Store rate limit info in cache
                        Cache::put($rateLimitKey, true, $retryAfter);
                        Cache::put($rateLimitResetKey, $resetTime, $retryAfter + 10);

                        Log::warning("OpenAI rate limit exceeded. Retry after {$retryAfter} seconds.");

                        if ($attempt < $maxRetries) {
                            sleep(min((int)$retryAfter, 30)); // Wait at most 30 seconds
                            continue;
                        } else {
                            throw new Exception("Rate limit exceeded after {$maxRetries} attempts");
                        }
                    }

                    // Handle other errors
                    $errorData = $response->json();
                    $errorMessage = $errorData['error']['message'] ?? "API error: {$response->status()}";
                    throw new Exception($errorMessage);

                } elseif ($provider === 'huggingface') {
                    $response = Http::timeout($timeout)
                        ->withHeaders([
                            'Authorization' => "Bearer {$apiKey}",
                            'Content-Type' => 'application/json',
                        ])
                        ->post($this->embeddingModels[$provider]['endpoint'] . '/' . $modelName, [
                            'inputs' => $text,
                        ]);

                    if ($response->successful()) {
                        return $response->json();
                    }

                    // Handle rate limiting
                    if ($response->status() === 429) {
                        $retryAfter = $response->header('Retry-After', 60);
                        $resetTime = now()->addSeconds((int)$retryAfter)->timestamp;

                        // Store rate limit info in cache
                        Cache::put($rateLimitKey, true, $retryAfter);
                        Cache::put($rateLimitResetKey, $resetTime, $retryAfter + 10);

                        Log::warning("Hugging Face rate limit exceeded. Retry after {$retryAfter} seconds.");

                        if ($attempt < $maxRetries) {
                            sleep(min((int)$retryAfter, 30)); // Wait at most 30 seconds
                            continue;
                        } else {
                            throw new Exception("Rate limit exceeded after {$maxRetries} attempts");
                        }
                    }

                    // Handle other errors
                    $errorData = $response->json();
                    $errorMessage = $errorData['error'] ?? "API error: {$response->status()}";
                    throw new Exception($errorMessage);
                } else {
                    throw new Exception("Unsupported provider: {$provider}");
                }
            } catch (Throwable $e) {
                Log::warning("Embedding API request exception (attempt {$attempt}/{$maxRetries}): " . $e->getMessage());

                if ($attempt < $maxRetries) {
                    // Exponential backoff with jitter
                    $jitter = rand(0, 1000) / 1000; // Random value between 0 and 1
                    $sleepTime = ($retryDelay * (2 ** ($attempt - 1))) * (1 + $jitter);
                    usleep(min($sleepTime, 30000) * 1000); // Cap at 30 seconds, convert to microseconds
                } else {
                    throw new Exception("Failed to generate embedding after {$maxRetries} attempts: " . $e->getMessage());
                }
            }
        }

        throw new Exception("Failed to generate embedding after {$maxRetries} attempts");
    }

    /**
     * Update document status with error tracking
     *
     * @param KnowledgeDocument $document
     * @param string $status
     * @param string|null $errorMessage
     * @return void
     */
    protected function updateDocumentStatus(KnowledgeDocument $document, string $status, ?string $errorMessage = null): void
    {
        $updateData = ['status' => $status];

        if ($errorMessage) {
            $metadata = $document->metadata ?? [];
            $metadata['last_error'] = $errorMessage;
            $metadata['error_timestamp'] = now()->toIso8601String();
            $updateData['metadata'] = $metadata;
        }

        $document->update($updateData);
    }

    /**
     * Generate embeddings for multiple documents
     *
     * @param array $documentIds
     * @param string $provider
     * @param array $options
     * @return array
     */
    public function batchGenerateEmbeddings(array $documentIds, string $provider = 'openai', array $options = []): array
    {
        $batchId = uniqid('batch_');
        $cacheKey = "embedding_batch_{$batchId}";

        // Initialize batch status
        $batchStatus = [
            'id' => $batchId,
            'total' => count($documentIds),
            'pending' => count($documentIds),
            'processing' => 0,
            'successful' => 0,
            'failed' => 0,
            'details' => [],
            'start_time' => now()->timestamp,
            'estimated_completion' => null,
        ];

        // Store initial status in cache
        Cache::put($cacheKey, $batchStatus, 24 * 3600); // 24 hours

        // Process documents in smaller batches to avoid memory issues
        $documentBatches = array_chunk($documentIds, 5);

        foreach ($documentBatches as $batchIndex => $batch) {
            foreach ($batch as $documentId) {
                try {
                    // Update document status to pending
                    $batchStatus = Cache::get($cacheKey);
                    $batchStatus['pending']--;
                    $batchStatus['processing']++;
                    $batchStatus['details'][$documentId] = [
                        'status' => 'processing',
                        'start_time' => now()->timestamp,
                    ];
                    Cache::put($cacheKey, $batchStatus, 24 * 3600);

                    // Get document
                    $document = KnowledgeDocument::find($documentId);

                    if (!$document) {
                        $this->updateBatchStatus($cacheKey, $documentId, 'error', 'Document not found');
                        continue;
                    }

                    // Generate embeddings
                    $success = $this->generateEmbeddings($document, $provider, $options);

                    if ($success) {
                        $this->updateBatchStatus($cacheKey, $documentId, 'success', null, [
                            'chunks' => $document->embeddings_count,
                            'provider' => $document->embeddings_provider,
                            'model' => $document->embeddings_model,
                        ]);
                    } else {
                        // Get error message from document metadata
                        $errorMessage = 'Failed to generate embeddings';
                        if (isset($document->metadata['last_error'])) {
                            $errorMessage = $document->metadata['last_error'];
                        }

                        $this->updateBatchStatus($cacheKey, $documentId, 'error', $errorMessage);
                    }
                } catch (Throwable $e) {
                    $this->updateBatchStatus($cacheKey, $documentId, 'error', $e->getMessage());
                    Log::error("Error in batch embedding for document {$documentId}: " . $e->getMessage());
                }
            }

            // Update estimated completion time after each batch
            $batchStatus = Cache::get($cacheKey);
            $elapsedTime = now()->timestamp - $batchStatus['start_time'];
            $processedCount = $batchStatus['successful'] + $batchStatus['failed'];

            if ($processedCount > 0 && $batchStatus['pending'] + $batchStatus['processing'] > 0) {
                $timePerDocument = $elapsedTime / $processedCount;
                $remainingDocuments = $batchStatus['pending'] + $batchStatus['processing'];
                $estimatedRemainingTime = $timePerDocument * $remainingDocuments;
                $batchStatus['estimated_completion'] = now()->addSeconds($estimatedRemainingTime)->timestamp;
                Cache::put($cacheKey, $batchStatus, 24 * 3600);
            }
        }

        // Get final status
        $finalStatus = Cache::get($cacheKey);
        $finalStatus['end_time'] = now()->timestamp;
        $finalStatus['duration'] = $finalStatus['end_time'] - $finalStatus['start_time'];
        $finalStatus['status'] = 'completed';
        Cache::put($cacheKey, $finalStatus, 24 * 3600);

        return [
            'batch_id' => $batchId,
            'total' => $finalStatus['total'],
            'successful' => $finalStatus['successful'],
            'failed' => $finalStatus['failed'],
            'duration' => $finalStatus['duration'],
            'details' => $finalStatus['details'],
        ];
    }

    /**
     * Update batch status in cache
     *
     * @param string $cacheKey
     * @param int $documentId
     * @param string $status
     * @param string|null $message
     * @param array $additionalData
     * @return void
     */
    protected function updateBatchStatus(string $cacheKey, int $documentId, string $status, ?string $message = null, array $additionalData = []): void
    {
        $batchStatus = Cache::get($cacheKey);

        if (!$batchStatus) {
            return;
        }

        // Update document status
        $batchStatus['processing']--;

        if ($status === 'success') {
            $batchStatus['successful']++;
        } else {
            $batchStatus['failed']++;
        }

        $batchStatus['details'][$documentId] = array_merge([
            'status' => $status,
            'end_time' => now()->timestamp,
            'duration' => now()->timestamp - ($batchStatus['details'][$documentId]['start_time'] ?? $batchStatus['start_time']),
        ], $additionalData);

        if ($message) {
            $batchStatus['details'][$documentId]['message'] = $message;
        }

        Cache::put($cacheKey, $batchStatus, 24 * 3600);
    }

    /**
     * Get embedding status for a document
     *
     * @param int $documentId
     * @return array
     */
    public function getEmbeddingStatus(int $documentId): array
    {
        $document = KnowledgeDocument::find($documentId);

        if (!$document) {
            return [
                'status' => 'error',
                'message' => 'Document not found',
            ];
        }

        $cacheKey = "embedding_status_doc_{$documentId}";
        $cachedStatus = Cache::get($cacheKey);

        if ($cachedStatus) {
            return $cachedStatus;
        }

        // If no cached status, return document status
        return [
            'status' => $document->status,
            'has_embeddings' => $document->has_embeddings,
            'embeddings_count' => $document->embeddings_count,
            'embeddings_provider' => $document->embeddings_provider,
            'embeddings_model' => $document->embeddings_model,
            'embeddings_generated_at' => $document->embeddings_generated_at ? $document->embeddings_generated_at->toIso8601String() : null,
            'progress' => $document->embeddings_progress ?? 0,
            'error' => $document->metadata['last_error'] ?? null,
        ];
    }

    /**
     * Chunk text into smaller pieces
     *
     * @param string $text
     * @param int $chunkSize
     * @param int $overlap
     * @return array
     */
    protected function chunkText(string $text, int $chunkSize = 1000, int $overlap = 200): array
    {
        // Simple chunking by character count
        $chunks = [];
        $textLength = strlen($text);

        if ($textLength <= $chunkSize) {
            return [$text];
        }

        $start = 0;
        while ($start < $textLength) {
            $end = min($start + $chunkSize, $textLength);

            // Try to end at a sentence or paragraph boundary
            if ($end < $textLength) {
                $possibleBoundaries = [
                    strrpos(substr($text, $start, $end - $start), "\n\n"),
                    strrpos(substr($text, $start, $end - $start), ".\n"),
                    strrpos(substr($text, $start, $end - $start), ". "),
                    strrpos(substr($text, $start, $end - $start), ".\n"),
                ];

                foreach ($possibleBoundaries as $boundary) {
                    if ($boundary !== false && $boundary > ($chunkSize * 0.5)) {
                        $end = $start + $boundary + 1;
                        break;
                    }
                }
            }

            $chunks[] = substr($text, $start, $end - $start);
            $start = $end - $overlap;
        }

        return $chunks;
    }

    /**
     * Get available embedding models
     *
     * @return array
     */
    public function getAvailableModels(): array
    {
        return $this->embeddingModels;
    }

    /**
     * Search for similar documents based on vector similarity.
     *
     * @param string|array $query
     * @param array $filters
     * @param int $limit
     * @param float $threshold
     * @param string $provider
     * @param array $options
     * @return array
     */
    public function searchSimilarDocuments($query, array $filters = [], int $limit = 10, float $threshold = 0.7, string $provider = 'openai', array $options = []): array
    {
        try {
            $startTime = microtime(true);

            // Handle both string queries and pre-computed embeddings
            $queryEmbedding = null;

            if (is_string($query)) {
                // Generate embedding for the query
                $queryEmbedding = $this->generateQueryEmbedding($query, $provider, $options);

                if (!$queryEmbedding) {
                    Log::error("Failed to generate embedding for query");
                    return [];
                }
            } elseif (is_array($query)) {
                // Use pre-computed embedding
                $queryEmbedding = $query;
            } else {
                throw new Exception("Query must be a string or an array of embeddings");
            }

            // Get document IDs based on filters
            $documentIds = [];

            if (isset($filters['document_ids']) && is_array($filters['document_ids'])) {
                // Use provided document IDs directly
                $documentIds = $filters['document_ids'];
            } else {
                // Build query to find matching documents
                $documentQuery = Document::where('has_embeddings', true)
                    ->where('is_active_source', true);

                // Apply project filter
                if (isset($filters['project_id'])) {
                    $documentQuery->where('project_id', $filters['project_id']);
                }

                // Apply category filter
                if (isset($filters['category'])) {
                    $documentQuery->where('category', $filters['category']);
                }

                // Apply source filter
                if (isset($filters['source_id'])) {
                    $documentQuery->where('source_id', $filters['source_id']);
                }

                // Apply file type filter
                if (isset($filters['file_type'])) {
                    $documentQuery->where('file_type', $filters['file_type']);
                }

                $documentIds = $documentQuery->pluck('id')->toArray();
            }

            if (empty($documentIds)) {
                Log::warning("No documents found matching the filters", [
                    'filters' => $filters,
                ]);
                return [];
            }

            // Get embeddings for all chunks from the specified documents
            $embeddings = KnowledgeEmbedding::whereIn('document_id', $documentIds)->get();

            if ($embeddings->isEmpty()) {
                Log::warning("No embeddings found for the specified documents", [
                    'document_count' => count($documentIds),
                ]);
                return [];
            }

            Log::info("Searching through embeddings", [
                'embedding_count' => $embeddings->count(),
                'document_count' => count($documentIds),
            ]);

            // Calculate similarity scores
            $results = [];
            foreach ($embeddings as $embedding) {
                $similarity = $this->calculateCosineSimilarity($queryEmbedding, $embedding->embedding);

                // Only include results above the threshold
                if ($similarity >= $threshold) {
                    $results[] = [
                        'document_id' => $embedding->document_id,
                        'chunk_index' => $embedding->chunk_index,
                        'chunk_text' => $embedding->chunk_text,
                        'similarity' => $similarity,
                    ];
                }
            }

            // Sort by similarity score (highest first)
            usort($results, function ($a, $b) {
                return $b['similarity'] <=> $a['similarity'];
            });

            // Limit the number of results
            $results = array_slice($results, 0, $limit);

            $duration = microtime(true) - $startTime;

            Log::info("Search completed", [
                'result_count' => count($results),
                'threshold' => $threshold,
                'duration_ms' => round($duration * 1000),
            ]);

            return $results;

        } catch (Exception $e) {
            Log::error("Error searching for similar documents: " . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [];
        }
    }

    /**
     * Calculate cosine similarity between two vectors.
     *
     * @param array $vectorA
     * @param array $vectorB
     * @return float
     */
    protected function calculateCosineSimilarity(array $vectorA, array $vectorB): float
    {
        // Ensure both vectors have the same dimensions
        if (count($vectorA) !== count($vectorB)) {
            throw new Exception("Vectors must have the same dimensions");
        }

        $dotProduct = 0;
        $normA = 0;
        $normB = 0;

        for ($i = 0; $i < count($vectorA); $i++) {
            $dotProduct += $vectorA[$i] * $vectorB[$i];
            $normA += $vectorA[$i] * $vectorA[$i];
            $normB += $vectorB[$i] * $vectorB[$i];
        }

        $normA = sqrt($normA);
        $normB = sqrt($normB);

        // Prevent division by zero
        if ($normA == 0 || $normB == 0) {
            return 0;
        }

        return $dotProduct / ($normA * $normB);
    }

    /**
     * Generate embeddings for a search query.
     *
     * @param string $query
     * @param string $provider
     * @param array $options
     * @return array|null
     */
    public function generateQueryEmbedding(string $query, string $provider = 'openai', array $options = []): ?array
    {
        try {
            // Get embedding model
            $modelId = $options['model_id'] ?? null;
            $embeddingModel = $modelId ? AIModel::find($modelId) : null;

            // If no specific model provided, get default embedding model
            if (!$embeddingModel) {
                $embeddingModel = AIModel::where('type', 'embedding')
                    ->where('status', 'active')
                    ->orderBy('is_default', 'desc')
                    ->first();
            }

            // If still no model, use default provider settings
            $modelName = $embeddingModel ? $embeddingModel->model_id : null;

            Log::info("Generating query embedding", [
                'query_length' => strlen($query),
                'provider' => $provider,
                'model' => $modelName,
            ]);

            // Generate embedding
            return $this->generateEmbeddingWithRetry(
                $query,
                $provider,
                $embeddingModel,
                $options['retry'] ?? true
            );

        } catch (Exception $e) {
            Log::error("Error generating query embedding: " . $e->getMessage(), [
                'query_length' => strlen($query),
                'provider' => $provider,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Delete embeddings for a document
     *
     * @param int $documentId
     * @return bool
     */
    public function deleteEmbeddings(int $documentId): bool
    {
        try {
            $count = \App\Models\KnowledgeEmbedding::where('document_id', $documentId)->delete();

            // Update document status
            $document = KnowledgeDocument::find($documentId);
            if ($document) {
                $document->update([
                    'has_embeddings' => false,
                    'embeddings_count' => 0,
                    'embeddings_provider' => null,
                    'embeddings_model' => null,
                    'embeddings_generated_at' => null,
                    'embeddings_progress' => 0
                ]);
            }

            return $count > 0;
        } catch (Exception $e) {
            Log::error("Error deleting embeddings for document {$documentId}: " . $e->getMessage());
            return false;
        }
    }
}
