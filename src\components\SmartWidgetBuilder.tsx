import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';

import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { LocalThemeProvider } from './smart-widget-builder/LocalThemeProvider';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Save,
  Sparkles,
  Eye,
  EyeOff,
  Monitor,
  Tablet,
  Smartphone as PhoneIcon,
  Power,
  Trash2,
  Download,
  History,
  GitBranch,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import axios from 'axios';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
// Import existing components
import { WidgetPreview, DevicePreview } from '@/components/widget-builder';
import { widgetService, Widget } from '@/utils/widgetService';

// Import new components we'll create
import TemplateGallery from './smart-widget-builder/TemplateGallery';
import QuickSettings from './smart-widget-builder/QuickSettings';
import FeatureCards from './smart-widget-builder/FeatureCards';
import FeatureModals from './smart-widget-builder/FeatureModals';
import Advanced3DTooltip from './smart-widget-builder/Advanced3DTooltip';

// Import transformer functions
import { transformApiToForm, transformFormToApi } from '@/utils/widgetTransformer';

// Widget configuration schema - ENHANCED with proper validation
const smartWidgetSchema = z.object({
  // General Settings - REQUIRED FIELDS
  name: z.string()
    .min(1, "Widget name is required")
    .min(3, "Widget name must be at least 3 characters")
    .max(50, "Widget name must be less than 50 characters")
    .regex(/^[a-zA-Z0-9\s\-_]+$/, "Widget name can only contain letters, numbers, spaces, hyphens, and underscores"),
  welcomeMessage: z.string()
    .min(1, "Welcome message is required")
    .min(10, "Welcome message must be at least 10 characters")
    .max(200, "Welcome message must be less than 200 characters"),
  botName: z.string()
    .min(1, "Bot name is required")
    .min(2, "Bot name must be at least 2 characters")
    .max(30, "Bot name must be less than 30 characters")
    .default("AI Assistant"),
  placeholderText: z.string()
    .min(1, "Placeholder text is required")
    .max(100, "Placeholder text must be less than 100 characters")
    .default("Type your message..."),
  presetTheme: z.string().default("modern"),

  // Appearance Settings - REQUIRED FIELDS WITH VALIDATION
  primaryColor: z.string()
    .min(1, "Primary color is required")
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Primary color must be a valid hex color (e.g., #7E69AB)")
    .default("#7E69AB"),
  secondaryColor: z.string()
    .min(1, "Secondary color is required")
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Secondary color must be a valid hex color (e.g., #ffffff)")
    .default("#ffffff"),
  headerBgColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Header background color must be a valid hex color")
    .default("#1f2937"),
  textColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Text color must be a valid hex color")
    .default("#111827"),
  theme: z.enum(["modern", "glass", "dark", "rounded", "minimal"], {
    errorMap: () => ({ message: "Please select a valid theme" })
  }).default("modern"),
  position: z.enum(["bottom-right", "bottom-left", "top-right", "top-left"], {
    errorMap: () => ({ message: "Please select a valid widget position" })
  }).default("bottom-right"),
  borderRadius: z.number()
    .min(0, "Border radius cannot be negative")
    .max(30, "Border radius cannot exceed 30px")
    .default(12),
  width: z.number()
    .min(280, "Widget width must be at least 280px")
    .max(500, "Widget width cannot exceed 500px")
    .default(350),
  height: z.number()
    .min(400, "Widget height must be at least 400px")
    .max(800, "Widget height cannot exceed 800px")
    .default(600),
  fontSize: z.number()
    .min(12, "Font size must be at least 12px")
    .max(20, "Font size cannot exceed 20px")
    .default(14),
  animation: z.enum(["none", "fade", "slide", "bounce"], {
    errorMap: () => ({ message: "Please select a valid animation type" })
  }).default("fade"),
  shadow: z.enum(["none", "sm", "md", "lg", "xl"], {
    errorMap: () => ({ message: "Please select a valid shadow type" })
  }).default("md"),

  // Features
  features: z.object({
    preChat: z.boolean().default(false),
    postChat: z.boolean().default(false),
    webhooks: z.boolean().default(false),
    domainRestriction: z.boolean().default(false),
    conversationPersistence: z.boolean().default(false),
    mobileOptimization: z.boolean().default(true),
    customCSS: z.boolean().default(false),
    aiModelSelection: z.boolean().default(false),
    logoUpload: z.boolean().default(false),
  }),

  // Behavior Settings
  behavior: z.object({
    autoOpen: z.boolean().default(false),
    autoOpenDelay: z.number().min(0).max(60).default(5),
    showLogo: z.boolean().default(true),
    showCloseButton: z.boolean().default(true),
    startMinimized: z.boolean().default(false),
    closeAfterInactivity: z.boolean().default(false),
    inactivityTimeout: z.number().min(1).max(60).default(10),
    darkMode: z.boolean().default(false),
    glassMorphism: z.boolean().default(false),
    showTypingIndicator: z.boolean().default(true),
    enableUserRatings: z.boolean().default(true),
    collectUserData: z.boolean().default(true),
    persistConversation: z.boolean().default(false),
  }),

  // Advanced Settings
  advanced: z.object({
    modelSelection: z.enum(["gemini", "gpt-4", "huggingface", "auto"]).default("auto"),
    contextRetention: z.enum(["session", "persistent", "none"]).default("session"),
    maxMessagesStored: z.number().min(10).max(1000).default(100),
    enableAnalytics: z.boolean().default(true),
    debugMode: z.boolean().default(false),
    loadTimeoutMs: z.number().min(1000).max(30000).default(5000),
    webhookUrl: z.string().optional(),
    allowedDomains: z.array(z.string()).default([]),
    customCSS: z.string().default(""),
    logoUrl: z.string().default(""),
    logoFile: z.any().optional(), // For temporary file storage during widget creation
    customParameters: z.record(z.any()).default({}),
    integrations: z.array(z.object({
      id: z.string(),
      type: z.string(),
      name: z.string(),
      url: z.string(),
      active: z.boolean(),
      events: z.array(z.string()),
      secret: z.string().optional(),
      created_at: z.string()
    })).default([]),
  }),
});

type SmartWidgetFormValues = z.infer<typeof smartWidgetSchema>;

interface SmartWidgetBuilderProps {
  widgetId?: string;
  onSave?: (widget: Widget) => void;
  onCancel?: () => void;
}

/**
 * Smart Widget Builder - New click-based progressive interface
 * Implements the planned architecture with template-first entry,
 * visual configuration, and smart feature discovery
 */
const SmartWidgetBuilderContent = ({ widgetId, onSave, onCancel }: SmartWidgetBuilderProps) => {
  const { toast } = useToast();

  // UI State
  const [currentStep, setCurrentStep] = useState<'template' | 'customize' | 'deploy'>('template');
  const [previewVisible, setPreviewVisible] = useState(true);
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [activeFeatureModal, setActiveFeatureModal] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [isActive, setIsActive] = useState(true);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Auto-save and real-time features
  const [autoSaving, setAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [previewConfig, setPreviewConfig] = useState<any>(null);

  // Version management
  const [currentVersion, setCurrentVersion] = useState('1.0');
  const [versionHistory, setVersionHistory] = useState<any[]>([]);
  const [showVersionDialog, setShowVersionDialog] = useState(false);

  // AI Model and Template selection
  const [selectedAiModel] = useState<any>(null);
  const [selectedPromptTemplate] = useState<any>(null);

  // Tooltip state
  const [selectedTooltip, setSelectedTooltip] = useState<any>(null);

  // Error highlighting state
  const [showErrorHighlight, setShowErrorHighlight] = useState(false);

  // Form setup
  const form = useForm<SmartWidgetFormValues>({
    resolver: zodResolver(smartWidgetSchema),
    defaultValues: {
      // General Settings
      name: "My Chat Widget",
      welcomeMessage: "Hello! How can I help you today?",
      botName: "AI Assistant",
      placeholderText: "Type your message...",
      presetTheme: "modern",

      // Appearance Settings
      primaryColor: "#7E69AB",
      secondaryColor: "#ffffff",
      headerBgColor: "#1f2937",
      textColor: "#111827",
      theme: "modern",
      position: "bottom-right",
      borderRadius: 12,
      width: 350,
      height: 600,
      fontSize: 14,
      animation: "fade",
      shadow: "md",

      // Features
      features: {
        preChat: false,
        postChat: false,
        webhooks: false,
        domainRestriction: false,
        conversationPersistence: false,
        mobileOptimization: true,
        customCSS: false,
        aiModelSelection: false,
        logoUpload: false,
      },

      // Behavior Settings
      behavior: {
        autoOpen: false,
        autoOpenDelay: 5,
        showLogo: true,
        showCloseButton: true,
        startMinimized: false,
        closeAfterInactivity: false,
        inactivityTimeout: 10,
        darkMode: false,
        glassMorphism: false,
        showTypingIndicator: true,
        enableUserRatings: true,
        collectUserData: true,
        persistConversation: false,
      },

      // Advanced Settings
      advanced: {
        modelSelection: "auto",
        contextRetention: "session",
        maxMessagesStored: 100,
        enableAnalytics: true,
        debugMode: false,
        loadTimeoutMs: 5000,
        webhookUrl: "",
        allowedDomains: [],
        customCSS: "",
        logoUrl: "",
        customParameters: {},
        integrations: [],
      },
    },
    mode: "onChange"
  });

  // Load existing widget data if editing
  useEffect(() => {
    if (widgetId) {
      loadWidgetData();
    }
  }, [widgetId]);

  // Real-time preview updates
  useEffect(() => {
    const subscription = form.watch((value) => {
      setPreviewConfig(value);
      setHasUnsavedChanges(true);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Auto-save functionality
  useEffect(() => {
    if (!hasUnsavedChanges || !widgetId) return;

    const autoSaveTimer = setTimeout(async () => {
      try {
        setAutoSaving(true);
        const formData = form.getValues();
        const widgetData = transformFormToApi(formData);
        widgetData.is_active = isActive;

        await widgetService.updateWidget(Number(widgetId), widgetData);
        setHasUnsavedChanges(false);
        setLastSaved(new Date());

        toast({
          title: "Auto-saved",
          description: "Your changes have been automatically saved.",
          className: "bg-blue-50 border-blue-200 text-blue-800",
        });
      } catch (error) {
        console.error('Auto-save failed:', error);
      } finally {
        setAutoSaving(false);
      }
    }, 3000); // Auto-save after 3 seconds of inactivity

    return () => clearTimeout(autoSaveTimer);
  }, [hasUnsavedChanges, form, widgetId, isActive]);

  // Version management
  useEffect(() => {
    if (widgetId) {
      loadVersionHistory();
    }
  }, [widgetId]);

  const loadWidgetData = async () => {
    try {
      const response = await widgetService.getWidget(Number(widgetId));
      const widget = response.data;

      if (widget) {
        // Use transformer to convert API data to form values
        const formValues = transformApiToForm(widget);
        form.reset(formValues);

        // Set active state from widget data
        setIsActive(widget.is_active);
        setCurrentVersion(widget.version || '1.0');
        setHasUnsavedChanges(false);
        setLastSaved(new Date(widget.updated_at || Date.now()));

        setCurrentStep('customize');
      }
    } catch (error) {
      console.error('Error loading widget:', error);
      toast({
        title: "Error Loading Widget",
        description: "Could not load widget data. Starting with defaults.",
        variant: "destructive",
      });
    }
  };

  const loadVersionHistory = async () => {
    if (!widgetId) return;

    try {
      const response = await widgetService.getVersionHistory(Number(widgetId));
      setVersionHistory(response.data || []);
    } catch (error) {
      console.error('Error loading version history:', error);
    }
  };

  // Helper function to count nested errors
  const countNestedErrors = (errors: any): number => {
    let count = 0;

    const countErrors = (obj: any) => {
      for (const key in obj) {
        if (obj[key] && typeof obj[key] === 'object') {
          if (obj[key].message) {
            count++;
          } else {
            countErrors(obj[key]);
          }
        }
      }
    };

    countErrors(errors);
    return count;
  };

  // Helper function to scroll to first error field
  const scrollToFirstError = () => {
    setTimeout(() => {
      const errorElement = document.querySelector('[data-invalid="true"], .border-red-500, .ring-red-500');
      if (errorElement) {
        errorElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Add visual emphasis
        errorElement.classList.add('animate-pulse');
        setTimeout(() => {
          errorElement.classList.remove('animate-pulse');
        }, 2000);
      }
    }, 100);
  };

  const createVersion = async (description: string) => {
    if (!widgetId) return;

    try {
      const formData = form.getValues();
      const widgetData = transformFormToApi(formData);

      const response = await widgetService.createVersion(Number(widgetId), {
        ...widgetData,
        version_description: description,
      });

      setCurrentVersion(response.data.version);
      setVersionHistory(prev => [response.data, ...prev]);

      toast({
        title: "Version Created",
        description: `Version ${response.data.version} has been created successfully.`,
        className: "bg-green-50 border-green-200 text-green-800",
      });
    } catch (error) {
      console.error('Error creating version:', error);
      toast({
        title: "Version Creation Failed",
        description: "Failed to create new version. Please try again.",
        variant: "destructive",
      });
    }
  };

  const restoreVersion = async (version: string) => {
    if (!widgetId) return;

    try {
      const response = await widgetService.restoreVersion(Number(widgetId), version);
      const formValues = transformApiToForm(response.data);

      form.reset(formValues);
      setCurrentVersion(version);
      setHasUnsavedChanges(false);

      toast({
        title: "Version Restored",
        description: `Widget has been restored to version ${version}.`,
        className: "bg-green-50 border-green-200 text-green-800",
      });
    } catch (error) {
      console.error('Error restoring version:', error);
      toast({
        title: "Version Restore Failed",
        description: "Failed to restore version. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    // Apply template settings
    const templateSettings = getTemplateSettings(templateId);
    form.reset({
      ...form.getValues(),
      ...templateSettings,
    });
    setCurrentStep('customize');
  };

  const getTemplateSettings = (templateId: string) => {
    const templates = {
      modern: {
        theme: "modern" as const,
        primaryColor: "#7E69AB",
        secondaryColor: "#ffffff",
      },
      glass: {
        theme: "glass" as const,
        primaryColor: "#6366f1",
        secondaryColor: "#f8fafc",
      },
      dark: {
        theme: "dark" as const,
        primaryColor: "#10b981",
        secondaryColor: "#1f2937",
      },
      rounded: {
        theme: "rounded" as const,
        primaryColor: "#f59e0b",
        secondaryColor: "#fef3c7",
      },
      minimal: {
        theme: "minimal" as const,
        primaryColor: "#6b7280",
        secondaryColor: "#f9fafb",
      },
    };

    return templates[templateId as keyof typeof templates] || templates.modern;
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Validate form data with detailed error reporting
      const result = await form.trigger();
      if (!result) {
        const errors = form.formState.errors;
        const errorFields = Object.keys(errors);

        // Count total errors
        const totalErrors = countNestedErrors(errors);

        // Create detailed error message
        let errorMessage = `Found ${totalErrors} validation error${totalErrors > 1 ? 's' : ''}. `;

        if (errorFields.length > 0) {
          const fieldNames = errorFields.slice(0, 3).map(field => {
            // Convert field names to readable format
            return field.replace(/([A-Z])/g, ' $1').toLowerCase().replace(/^./, str => str.toUpperCase());
          });

          errorMessage += `Please check: ${fieldNames.join(', ')}`;
          if (errorFields.length > 3) {
            errorMessage += ` and ${errorFields.length - 3} more field${errorFields.length - 3 > 1 ? 's' : ''}`;
          }
        }

        toast({
          title: "❌ Validation Failed",
          description: errorMessage,
          variant: "destructive",
          duration: 6000,
        });

        // Scroll to first error field
        scrollToFirstError();

        setSaving(false);
        return;
      }

      const formData = form.getValues();

      // Use transformer to convert form data to API format
      const widgetData = transformFormToApi(formData);

      // Set active state
      widgetData.is_active = isActive;

      let savedWidget: any;
      if (widgetId) {
        savedWidget = await widgetService.updateWidget(Number(widgetId), widgetData);
      } else {
        savedWidget = await widgetService.createWidget(widgetData);
      }

      // Handle logo file upload after widget creation
      if (savedWidget.data && formData.advanced?.logoFile && !widgetId) {
        try {
          const logoFile = formData.advanced.logoFile;
          const metadata = {
            position: 'header' as const,
            altText: `${logoFile.name} logo`,
            quality: 92,
          };

          const logoResponse = await widgetService.uploadLogo(savedWidget.data.id, logoFile, metadata);

          if (logoResponse.data.logoUrl) {
            // Update the saved widget with the logo URL
            savedWidget.data.settings = {
              ...savedWidget.data.settings,
              advanced: {
                ...savedWidget.data.settings.advanced,
                logoUrl: logoResponse.data.logoUrl
              }
            };
          }
        } catch (logoError) {
          console.error('Logo upload failed:', logoError);
          toast({
            title: "Widget Saved, Logo Upload Failed",
            description: "Widget was created successfully, but logo upload failed. You can upload it later.",
            variant: "destructive",
          });
        }
      }

      // Handle response
      if (savedWidget.data) {
        toast({
          title: "Widget Saved",
          description: widgetId ? "Widget updated successfully." : "New widget created successfully.",
          className: "bg-green-50 border-green-200 text-green-800",
        });

        onSave?.(savedWidget.data);
      } else {
        throw new Error('Failed to save widget');
      }
    } catch (error) {
      console.error('Error saving widget:', error);

      // Improved error handling
      let errorMessage = "There was a problem saving your widget. Please try again.";

      // Handle validation errors from the API
      if (axios.isAxiosError(error) && error.response?.status === 422) {
        const validationErrors = error.response.data.errors;
        if (validationErrors) {
          // Map validation errors to form fields
          Object.keys(validationErrors).forEach(fieldName => {
            const fieldErrors = validationErrors[fieldName];
            if (fieldErrors && fieldErrors.length > 0) {
              // Try to set error on form field
              try {
                form.setError(fieldName as any, {
                  type: 'server',
                  message: fieldErrors[0]
                });
              } catch (e) {
                // If field not found in form, do nothing
              }
            }
          });

          errorMessage = "Please fix the highlighted errors and try again.";
        }
      }

      toast({
        title: "Error Saving Widget",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleFeatureToggle = (featureName: string, enabled: boolean) => {
    if (enabled) {
      // Open feature configuration modal
      setActiveFeatureModal(featureName);
    } else {
      // Disable feature
      form.setValue(`features.${featureName}` as any, false);
    }
  };

  // Add delete widget function
  const handleDelete = async () => {
    if (!widgetId) return;

    setDeleting(true);
    try {
      await widgetService.deleteWidget(Number(widgetId));

      toast({
        title: "Widget Deleted",
        description: "The widget has been deleted successfully.",
        className: "bg-green-50 border-green-200 text-green-800",
      });

      // Call onCancel if provided (to navigate back to the widgets list)
      onCancel?.();
    } catch (error) {
      console.error('Error deleting widget:', error);
      toast({
        title: "Error Deleting Widget",
        description: "Failed to delete the widget. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Add export configuration function
  const handleExportConfig = async () => {
    if (!widgetId) {
      toast({
        title: "Export Not Available",
        description: "Please save the widget first before exporting configuration.",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await widgetService.exportConfig(Number(widgetId));
      const { config, filename } = response.data;

      // Create and download the file
      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename || `widget-${widgetId}-config.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Configuration Exported",
        description: "Widget configuration has been downloaded successfully.",
        className: "bg-green-50 border-green-200 text-green-800",
      });
    } catch (error) {
      console.error('Error exporting configuration:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export widget configuration. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Render template selection step
  if (currentStep === 'template') {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">Create Your Chat Widget</h1>
            <p className="text-muted-foreground">Choose a template to get started quickly</p>
          </div>

          <TemplateGallery onTemplateSelect={handleTemplateSelect} />

          <div className="text-center mt-8">
            <Button
              variant="outline"
              onClick={() => setCurrentStep('customize')}
              className="mr-4"
            >
              Start from Scratch
            </Button>
            {onCancel && (
              <Button variant="ghost" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Render main customization interface
  return (
    <div className="bg-background">
      {/* Header */}
      <div className="border-b border-border px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-foreground">Widget Configuration</h2>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              <Sparkles className="w-3 h-3 mr-1" />
              Smart Builder
            </Badge>
            {widgetId && (
              <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                <GitBranch className="w-3 h-3 mr-1" />
                v{currentVersion}
              </Badge>
            )}
            {autoSaving && (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                <Clock className="w-3 h-3 mr-1 animate-spin" />
                Auto-saving...
              </Badge>
            )}
            {hasUnsavedChanges && !autoSaving && (
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                <Clock className="w-3 h-3 mr-1" />
                Unsaved changes
              </Badge>
            )}
            {lastSaved && !hasUnsavedChanges && !autoSaving && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <Clock className="w-3 h-3 mr-1" />
                Saved {lastSaved.toLocaleTimeString()}
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 mr-2">
              <Switch
                id="widget-active"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="widget-active" className="text-sm font-medium cursor-pointer">
                {isActive ? (
                  <span className="text-green-600 flex items-center">
                    <Power className="w-3 h-3 mr-1" /> Active
                  </span>
                ) : (
                  <span className="text-muted-foreground flex items-center">
                    <Power className="w-3 h-3 mr-1" /> Inactive
                  </span>
                )}
              </Label>
            </div>

            {widgetId && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowVersionDialog(true)}
                  className="text-purple-600 border-purple-200 hover:bg-purple-50"
                >
                  <History className="w-4 h-4 mr-2" />
                  Version History
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportConfig}
                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export Config
                </Button>

                <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm" className="text-red-500 border-red-200 hover:bg-red-50">
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the widget
                        and remove it from our servers.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDelete}
                        className="bg-red-500 hover:bg-red-600"
                        disabled={deleting}
                      >
                        {deleting ? "Deleting..." : "Delete Widget"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => setPreviewVisible(!previewVisible)}
            >
              {previewVisible ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
              {previewVisible ? "Hide Preview" : "Show Preview"}
            </Button>

            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? "Saving..." : "Save Widget"}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex min-h-[800px]">
        {/* Preview Panel */}
        {previewVisible && (
          <div className="w-1/2 bg-background border-r border-border p-6">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-foreground">Live Preview</h3>
              <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
                <Button
                  variant={previewDevice === "desktop" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 px-2.5"
                  onClick={() => setPreviewDevice("desktop")}
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewDevice === "tablet" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 px-2.5"
                  onClick={() => setPreviewDevice("tablet")}
                >
                  <Tablet className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewDevice === "mobile" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 px-2.5"
                  onClick={() => setPreviewDevice("mobile")}
                >
                  <PhoneIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="bg-muted rounded-lg p-4 min-h-[600px] flex items-center justify-center">
              <DevicePreview device={previewDevice}>
                <WidgetPreview
                  config={{
                    general: {
                      name: form.watch('name'),
                      welcomeMessage: form.watch('welcomeMessage'),
                      botName: form.watch('botName'),
                      placeholderText: form.watch('placeholderText'),
                      widgetPosition: form.watch('position'),
                      presetTheme: form.watch('presetTheme'),
                    },
                    appearance: {
                      primaryColor: form.watch('primaryColor'),
                      secondaryColor: form.watch('secondaryColor'),
                      headerBgColor: form.watch('headerBgColor'),
                      textColor: form.watch('textColor'),
                      fontSize: form.watch('fontSize'),
                      borderRadius: form.watch('borderRadius'),
                      widgetWidth: form.watch('width'),
                      widgetHeight: form.watch('height'),
                      showLogo: form.watch('behavior.showLogo'),
                      showCloseButton: form.watch('behavior.showCloseButton'),
                      darkMode: form.watch('behavior.darkMode'),
                      glassMorphism: form.watch('behavior.glassMorphism'),
                      animation: form.watch('animation'),
                      shadow: form.watch('shadow'),
                      customCSS: form.watch('advanced.customCSS'),
                      theme: form.watch('theme'),
                    },
                    behavior: {
                      ...form.watch('behavior'),
                      autoOpen: form.watch('behavior.autoOpen'),
                      autoOpenDelay: form.watch('behavior.autoOpenDelay'),
                      enableUserRatings: form.watch('behavior.enableUserRatings'),
                      showTypingIndicator: form.watch('behavior.showTypingIndicator'),
                      collectUserData: form.watch('behavior.collectUserData'),
                      persistConversation: form.watch('behavior.persistConversation'),
                    },
                    features: form.watch('features'),
                    advanced: {
                      ...form.watch('advanced'),
                      modelSelection: form.watch('advanced.modelSelection'),
                      contextRetention: form.watch('advanced.contextRetention'),
                      enableAnalytics: form.watch('advanced.enableAnalytics'),
                      logoUrl: form.watch('advanced.logoUrl'),
                    },
                  }}
                  previewConfig={previewConfig}
                  aiModel={selectedAiModel}
                  promptTemplate={selectedPromptTemplate}
                />
              </DevicePreview>
            </div>
          </div>
        )}

        {/* Settings Panel */}
        <div className={`${previewVisible ? 'w-1/2' : 'w-full'} bg-muted/50 overflow-y-auto`}>
          <div className="p-6 space-y-6">
            {/* Quick Settings */}
            <QuickSettings
              form={form}
              widgetId={widgetId}
              onOpenFeatureModal={setActiveFeatureModal}
              showErrorHighlight={showErrorHighlight}
            />

            {/* Feature Cards */}
            <FeatureCards
              form={form}
              onFeatureToggle={handleFeatureToggle}
              onTooltipChange={setSelectedTooltip}
            />

            {/* Feature Details Tooltip Display */}
            <div id="feature-tooltip-display" className="min-h-[400px]">
              {selectedTooltip && (
                <Advanced3DTooltip content={selectedTooltip}>
                  <div className="w-full h-full" />
                </Advanced3DTooltip>
              )}
            </div>

            {/* Validation Summary */}
            {Object.keys(form.formState.errors).length > 0 && (
              <Alert variant="destructive" className="mb-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">Please fix the following errors before saving:</p>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {Object.entries(form.formState.errors).slice(0, 5).map(([field, error]) => (
                        <li key={field}>
                          <strong>{field}:</strong>{' '}
                          {error?.message || 'Invalid value'}
                        </li>
                      ))}
                      {Object.keys(form.formState.errors).length > 5 && (
                        <li className="text-gray-600">
                          ...and {Object.keys(form.formState.errors).length - 5} more errors
                        </li>
                      )}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-6 border-t">
              <div className="flex items-center space-x-4">
                {/* Widget Status Toggle */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="widget-active"
                    checked={isActive}
                    onCheckedChange={setIsActive}
                  />
                  <Label htmlFor="widget-active" className="text-sm">
                    Widget Active
                  </Label>
                </div>

                {/* Auto-save Status */}
                {autoSaving && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                    <span>Auto-saving...</span>
                  </div>
                )}

                {lastSaved && !hasUnsavedChanges && (
                  <div className="flex items-center space-x-2 text-sm text-green-600">
                    <Clock className="w-3 h-3" />
                    <span>Saved {lastSaved.toLocaleTimeString()}</span>
                  </div>
                )}

                {hasUnsavedChanges && (
                  <div className="flex items-center space-x-2 text-sm text-amber-600">
                    <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                    <span>Unsaved changes</span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-3">
                {/* Version Management */}
                {widgetId && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowVersionDialog(true)}
                    className="flex items-center space-x-2"
                  >
                    <GitBranch className="w-4 h-4" />
                    <span>v{currentVersion}</span>
                  </Button>
                )}

                {/* Export Configuration */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportConfig}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>

                {/* Delete Widget */}
                {widgetId && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDeleteDialog(true)}
                    className="flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </Button>
                )}

                {/* Save Button with Validation State */}
                <Button
                  onClick={Object.keys(form.formState.errors).length > 0 ? () => {
                    setShowErrorHighlight(true);
                    setTimeout(() => setShowErrorHighlight(false), 3000);
                    scrollToFirstError();
                  } : handleSave}
                  disabled={saving}
                  className={cn(
                    "flex items-center space-x-2 transition-all",
                    Object.keys(form.formState.errors).length > 0
                      ? "bg-red-500 hover:bg-red-600 text-white"
                      : "bg-blue-600 hover:bg-blue-700"
                  )}
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Saving...</span>
                    </>
                  ) : Object.keys(form.formState.errors).length > 0 ? (
                    <>
                      <AlertTriangle className="w-4 h-4" />
                      <span>Fix Errors First</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      <span>{widgetId ? 'Update Widget' : 'Create Widget'}</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Modals */}
      <FeatureModals
        activeModal={activeFeatureModal}
        onClose={() => setActiveFeatureModal(null)}
        form={form}
        widgetId={widgetId}
      />

      {/* Version History Dialog */}
      <Dialog open={showVersionDialog} onOpenChange={setShowVersionDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <History className="w-5 h-5" />
              <span>Version History</span>
            </DialogTitle>
            <DialogDescription>
              Manage widget versions and restore previous configurations
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Current Version Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-blue-900">Current Version: v{currentVersion}</h3>
                  <p className="text-sm text-blue-700">
                    {hasUnsavedChanges ? 'You have unsaved changes' : 'All changes saved'}
                  </p>
                </div>
                <Button
                  onClick={() => {
                    const description = prompt('Enter version description:');
                    if (description) {
                      createVersion(description);
                    }
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <GitBranch className="w-4 h-4 mr-2" />
                  Create New Version
                </Button>
              </div>
            </div>

            {/* Version History List */}
            <div className="space-y-2">
              <h4 className="font-medium">Previous Versions</h4>
              {versionHistory.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No previous versions available</p>
              ) : (
                versionHistory.map((version: any) => (
                  <div key={version.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">v{version.version}</Badge>
                          <span className="font-medium">{version.description || 'No description'}</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          Created {new Date(version.created_at).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => restoreVersion(version.version)}
                        >
                          Restore
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Main component with local theme provider
const SmartWidgetBuilder = (props: SmartWidgetBuilderProps) => {
  return (
    <LocalThemeProvider defaultTheme="system" storageKey="smart-widget-builder-theme">
      <SmartWidgetBuilderContent {...props} />
    </LocalThemeProvider>
  );
};

export default SmartWidgetBuilder;
