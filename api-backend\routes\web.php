<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\EmbedCodeController;

Route::get('/', function () {
    return view('welcome');
});

// Define a login route for web requests that redirects to the frontend
Route::get('/login', function () {
    return redirect(env('FRONTEND_URL', 'http://localhost:3000') . '/login');
})->name('login');

// Widget embedding routes
Route::middleware(['auth'])->group(function () {
    Route::get('/widget/{widgetId}/embed', [EmbedCodeController::class, 'showEmbedCode'])
        ->name('widget.embed');
});

// Widget asset routes - public access
Route::prefix('widget')->group(function () {
    Route::get('/v1.0/web-component.js', function () {
        return response()->file(public_path('widget/v1.0/web-component.js'))
            ->header('Content-Type', 'application/javascript; charset=utf-8')
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept');
    });

    Route::get('/v1.0/script.js', function () {
        return response()->file(public_path('widget/v1.0/script.js'))
            ->header('Content-Type', 'application/javascript; charset=utf-8')
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept');
    });

    Route::get('/v1.0/iframe/{widgetId}', function ($widgetId) {
        // Placeholder iframe response
        return response()->view('widget.iframe', ['widgetId' => $widgetId])
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept');
    });
});

// Route to serve widget test pages
Route::get('/widget-tests', function () {
    return redirect('/widget-tests/index.html');
});

