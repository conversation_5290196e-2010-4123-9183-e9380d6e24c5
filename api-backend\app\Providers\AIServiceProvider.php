<?php

namespace App\Providers;

use App\Services\AIService;
use App\Services\AIModelSelector;
use App\Services\AI\ProviderRegistry;
use App\Services\AI\ProviderManager;
use App\Services\AI\Providers\GrokProvider;
use App\Services\AI\Providers\GeminiProvider;
use App\Services\AI\Providers\OpenAIProvider;
use App\Services\AI\Providers\MistralProvider;
use App\Services\AI\Providers\CohereProvider;
use App\Services\AI\Providers\DeepSeekProvider;
use App\Services\AI\Providers\AnthropicProvider;
use App\Services\AI\Providers\OpenRouterProvider;
use App\Services\AI\Providers\HuggingFaceProvider;
use App\Services\TemplateProcessingService;
use App\Services\ContextRuleService;
use App\Services\VectorEmbeddingService;
use App\Services\KnowledgeBaseRetrievalService;
use App\Services\ScraperService;
use App\Services\DocumentProcessingService;
use App\Services\DatabaseConnectionService;
use App\Services\AuditLogService;
use Illuminate\Support\ServiceProvider;

class AIServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register Provider Registry
        $this->app->singleton(ProviderRegistry::class, function ($app) {
            $registry = new ProviderRegistry();

            // Register providers if they exist
            if (class_exists(OpenAIProvider::class)) {
                $registry->register('openai', OpenAIProvider::class);
            }

            if (class_exists(AnthropicProvider::class)) {
                $registry->register('anthropic', AnthropicProvider::class);
            }

            if (class_exists(GeminiProvider::class)) {
                $registry->register('gemini', GeminiProvider::class);
            }

            if (class_exists(GrokProvider::class)) {
                $registry->register('grok', GrokProvider::class);
            }

            if (class_exists(HuggingFaceProvider::class)) {
                $registry->register('huggingface', HuggingFaceProvider::class);
            }

            if (class_exists(OpenRouterProvider::class)) {
                $registry->register('openrouter', OpenRouterProvider::class);
            }

            if (class_exists(MistralProvider::class)) {
                $registry->register('mistral', MistralProvider::class);
            }

            if (class_exists(DeepSeekProvider::class)) {
                $registry->register('deepseek', DeepSeekProvider::class);
            }

            if (class_exists(CohereProvider::class)) {
                $registry->register('cohere', CohereProvider::class);
            }

            return $registry;
        });

        // Register ProviderManager
        $this->app->singleton(ProviderManager::class, function ($app) {
            return new ProviderManager(
                $app->make(ProviderRegistry::class)
            );
        });

        // Register AIModelSelector
        $this->app->singleton(AIModelSelector::class, function ($app) {
            return new AIModelSelector();
        });

        // Register ScraperService
        $this->app->singleton(ScraperService::class, function ($app) {
            return new ScraperService();
        });

        // Register DocumentProcessingService
        $this->app->singleton(DocumentProcessingService::class, function ($app) {
            return new DocumentProcessingService();
        });

        // Register DatabaseConnectionService
        $this->app->singleton(DatabaseConnectionService::class, function ($app) {
            return new DatabaseConnectionService();
        });

        // Register AuditLogService
        $this->app->singleton(AuditLogService::class, function ($app) {
            return new AuditLogService();
        });

        // Register ContextRuleService with VectorEmbeddingService dependency
        $this->app->singleton(ContextRuleService::class, function ($app) {
            return new ContextRuleService($app->make(VectorEmbeddingService::class));
        });

        // Register TemplateProcessingService
        $this->app->singleton(TemplateProcessingService::class, function ($app) {
            return new TemplateProcessingService();
        });

        // Register KnowledgeBaseRetrievalService with dependencies
        $this->app->singleton(KnowledgeBaseRetrievalService::class, function ($app) {
            return new KnowledgeBaseRetrievalService(
                $app->make(VectorEmbeddingService::class),
                $app->make(ContextRuleService::class),
                $app->make(ScraperService::class)
            );
        });

        // Register AIService with dependencies
        $this->app->singleton(AIService::class, function ($app) {
            return new AIService(
                $app->make(AIModelSelector::class),
                $app->make(ProviderRegistry::class),
                $app->make(ProviderManager::class),
                $app->make(ContextRuleService::class),
                $app->make(TemplateProcessingService::class),
                $app->make(KnowledgeBaseRetrievalService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Publish configuration
        $this->publishes([
            __DIR__.'/../../config/ai.php' => config_path('ai.php'),
        ], 'ai-config');
    }
}
