import React from 'react';
import { 
  FileText, 
  Link, 
  Shield, 
  Database, 
  Smartphone, 
  MessageSquare, 
  Code, 
  Brain 
} from 'lucide-react';

export interface TooltipContentData {
  title: string;
  description: string;
  features: string[];
  benefits: string[];
  status: 'enabled' | 'disabled' | 'configured';
  icon: React.ReactNode;
  gradient: string;
  examples?: string[];
  stats?: { label: string; value: string }[];
}

export const getTooltipContent = (featureId: string): TooltipContentData => {
  const tooltipData: Record<string, TooltipContentData> = {
    preChat: {
      title: 'Pre-Chat Form',
      description: 'Collect visitor information before starting the conversation to provide personalized assistance and generate qualified leads.',
      features: [
        'Custom form fields (name, email, phone)',
        'Required/optional field configuration',
        'Multi-step form support',
        'Real-time validation',
        'Mobile-optimized design'
      ],
      benefits: [
        'Increases lead quality by 40%',
        'Enables personalized conversations',
        'Reduces support workload',
        'Improves customer satisfaction'
      ],
      status: 'disabled',
      icon: <FileText className="w-5 h-5" />,
      gradient: 'from-blue-500 to-indigo-600',
      examples: ['Name + Email', 'Support Category', 'Company Size'],
      stats: [
        { label: 'Conversion Rate', value: '+40%' },
        { label: 'Lead Quality', value: '95%' }
      ]
    },
    webhooks: {
      title: 'Webhook Integration',
      description: 'Automatically send chat data to your favorite tools like Slack, email, CRM systems, or any custom webhook endpoint.',
      features: [
        'Real-time data transmission',
        'Multiple webhook endpoints',
        'Custom payload formatting',
        'Retry mechanism for failed requests',
        'Secure HTTPS delivery'
      ],
      benefits: [
        'Automates workflow processes',
        'Ensures no messages are missed',
        'Integrates with existing tools',
        'Reduces manual data entry'
      ],
      status: 'disabled',
      icon: <Link className="w-5 h-5" />,
      gradient: 'from-green-500 to-emerald-600',
      examples: ['Slack notifications', 'Email alerts', 'CRM updates'],
      stats: [
        { label: 'Delivery Rate', value: '99.9%' },
        { label: 'Response Time', value: '<100ms' }
      ]
    },
    domainRestriction: {
      title: 'Domain Restriction',
      description: 'Control which websites can display your widget to prevent unauthorized usage and protect your resources.',
      features: [
        'Whitelist specific domains',
        'Wildcard domain support',
        'Real-time domain validation',
        'Usage analytics per domain',
        'Easy domain management'
      ],
      benefits: [
        'Prevents unauthorized usage',
        'Protects API resources',
        'Controls widget distribution',
        'Maintains brand integrity'
      ],
      status: 'disabled',
      icon: <Shield className="w-5 h-5" />,
      gradient: 'from-red-500 to-pink-600',
      examples: ['yoursite.com', '*.company.com', 'app.domain.com'],
      stats: [
        { label: 'Security Level', value: 'High' },
        { label: 'Blocked Requests', value: '0' }
      ]
    },
    conversationPersistence: {
      title: 'Conversation Memory',
      description: 'Remember chat history when visitors return to your site, providing seamless conversation continuity.',
      features: [
        'Session-based memory',
        'Cross-device synchronization',
        'Configurable retention period',
        'Privacy-compliant storage',
        'Automatic cleanup'
      ],
      benefits: [
        'Improves user experience',
        'Reduces repeated questions',
        'Builds conversation context',
        'Increases engagement'
      ],
      status: 'disabled',
      icon: <Database className="w-5 h-5" />,
      gradient: 'from-purple-500 to-violet-600',
      examples: ['24 hours', '7 days', '30 days'],
      stats: [
        { label: 'Return Rate', value: '+25%' },
        { label: 'Satisfaction', value: '92%' }
      ]
    },
    mobileOptimization: {
      title: 'Mobile Optimization',
      description: 'Ensure your widget works perfectly on mobile devices with responsive design and touch-friendly interactions.',
      features: [
        'Responsive design',
        'Touch-optimized controls',
        'Mobile-first approach',
        'Gesture support',
        'Performance optimization'
      ],
      benefits: [
        'Essential for 60%+ mobile traffic',
        'Improves mobile conversions',
        'Better user experience',
        'Higher engagement rates'
      ],
      status: 'disabled',
      icon: <Smartphone className="w-5 h-5" />,
      gradient: 'from-orange-500 to-red-600',
      examples: ['Responsive layout', 'Touch gestures', 'Mobile keyboard'],
      stats: [
        { label: 'Mobile Traffic', value: '65%' },
        { label: 'Mobile Conversion', value: '+30%' }
      ]
    },
    postChat: {
      title: 'Post-Chat Survey',
      description: 'Collect valuable feedback and ratings after chat sessions to continuously improve your service quality.',
      features: [
        'Custom survey questions',
        'Star rating system',
        'Multiple choice options',
        'Optional feedback text',
        'Analytics dashboard'
      ],
      benefits: [
        'Improves service quality by 35%',
        'Identifies improvement areas',
        'Measures customer satisfaction',
        'Builds customer insights'
      ],
      status: 'disabled',
      icon: <MessageSquare className="w-5 h-5" />,
      gradient: 'from-teal-500 to-cyan-600',
      examples: ['5-star rating', 'Satisfaction survey', 'Feedback form'],
      stats: [
        { label: 'Response Rate', value: '78%' },
        { label: 'Avg Rating', value: '4.6/5' }
      ]
    },
    customCSS: {
      title: 'Custom CSS Styling',
      description: 'Add custom CSS to completely customize your widget appearance and match your brand perfectly.',
      features: [
        'Full CSS customization',
        'Real-time preview',
        'CSS validation',
        'Responsive design support',
        'Brand color integration'
      ],
      benefits: [
        'Perfect brand matching',
        'Professional appearance',
        'Unique design identity',
        'Enhanced user experience'
      ],
      status: 'disabled',
      icon: <Code className="w-5 h-5" />,
      gradient: 'from-gray-600 to-gray-800',
      examples: ['Custom colors', 'Font styles', 'Border radius'],
      stats: [
        { label: 'Customization', value: '100%' },
        { label: 'Brand Match', value: 'Perfect' }
      ]
    },
    aiModelSelection: {
      title: 'AI Model Selection',
      description: 'Choose specific AI models like GPT-4, Gemini, or Claude to optimize response quality and cost for your use case.',
      features: [
        'Multiple AI model options',
        'Performance optimization',
        'Cost-effective selection',
        'Model-specific features',
        'Automatic fallback'
      ],
      benefits: [
        'Optimizes response quality',
        'Controls operational costs',
        'Matches specific use cases',
        'Improves accuracy'
      ],
      status: 'disabled',
      icon: <Brain className="w-5 h-5" />,
      gradient: 'from-indigo-500 to-purple-600',
      examples: ['GPT-4', 'Claude-3', 'Gemini Pro'],
      stats: [
        { label: 'Accuracy', value: '96%' },
        { label: 'Response Time', value: '1.2s' }
      ]
    }
  };

  return tooltipData[featureId] || {
    title: 'Feature',
    description: 'Feature description not available.',
    features: [],
    benefits: [],
    status: 'disabled',
    icon: <div className="w-5 h-5" />,
    gradient: 'from-gray-400 to-gray-600'
  };
};
