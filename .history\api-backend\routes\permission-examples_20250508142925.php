<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\AIModelController;
use App\Http\Controllers\WidgetController;

/*
|--------------------------------------------------------------------------
| Permission-Protected API Routes Example
|--------------------------------------------------------------------------
|
| This file demonstrates how to use the permission middleware to protect
| routes based on specific permissions. Include this in your api.php file
| or use it as a reference for implementing permission checks.
|
*/

// All routes in this group require authentication
Route::middleware('auth:sanctum')->group(function () {

    // User Management Routes - Requires specific permissions
    Route::prefix('admin')->group(function () {
        // User management - requires user.view, user.create, etc. permissions
        Route::get('/users', [UserController::class, 'index'])
            ->middleware('permission:user.view');

        Route::post('/users', [UserController::class, 'store'])
            ->middleware('permission:user.create');

        Route::put('/users/{user}', [UserController::class, 'update'])
            ->middleware('permission:user.edit');

        Route::delete('/users/{user}', [UserController::class, 'destroy'])
            ->middleware('permission:user.delete');

        // Role management - requires role.view, role.create, etc. permissions
        Route::get('/roles', [RoleController::class, 'index'])
            ->middleware('permission:role.view');

        Route::post('/roles', [RoleController::class, 'store'])
            ->middleware('permission:role.create');

        Route::put('/roles/{role}', [RoleController::class, 'update'])
            ->middleware('permission:role.edit');

        Route::delete('/roles/{role}', [RoleController::class, 'destroy'])
            ->middleware('permission:role.delete');

        // Permission management
        Route::get('/permissions', [PermissionController::class, 'index'])
            ->middleware('permission:permission.view');
    });

    // AI Model Routes - Requires ai_model permissions
    Route::prefix('ai-models')->group(function () {
        Route::get('/', [AIModelController::class, 'index'])
            ->middleware('permission:ai_model.view');

        Route::post('/', [AIModelController::class, 'store'])
            ->middleware('permission:ai_model.create');

        Route::put('/{model}', [AIModelController::class, 'update'])
            ->middleware('permission:ai_model.edit');

        Route::delete('/{model}', [AIModelController::class, 'destroy'])
            ->middleware('permission:ai_model.delete');
    });

    // Widget Routes - Requires widget permissions
    Route::prefix('widgets')->group(function () {
        Route::get('/', [WidgetController::class, 'index'])
            ->middleware('permission:widget.view');

        Route::post('/', [WidgetController::class, 'store'])
            ->middleware('permission:widget.create');

        Route::put('/{widget}', [WidgetController::class, 'update'])
            ->middleware('permission:widget.edit');

        Route::delete('/{widget}', [WidgetController::class, 'destroy'])
            ->middleware('permission:widget.delete');
    });
});