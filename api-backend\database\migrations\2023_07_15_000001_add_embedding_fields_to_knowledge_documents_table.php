<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEmbeddingFieldsToKnowledgeDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('knowledge_documents', function (Blueprint $table) {
            $table->boolean('has_embeddings')->default(false)->after('extracted_text');
            $table->integer('embeddings_count')->nullable()->after('has_embeddings');
            $table->string('embeddings_provider')->nullable()->after('embeddings_count');
            $table->string('embeddings_model')->nullable()->after('embeddings_provider');
            $table->timestamp('embeddings_generated_at')->nullable()->after('embeddings_model');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('knowledge_documents', function (Blueprint $table) {
            $table->dropColumn([
                'has_embeddings',
                'embeddings_count',
                'embeddings_provider',
                'embeddings_model',
                'embeddings_generated_at',
            ]);
        });
    }
}
