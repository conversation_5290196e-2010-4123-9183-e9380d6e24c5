<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('knowledge_sources')) {
            Schema::create('knowledge_sources', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('type'); // 'file', 'database', 'scrape'
                $table->text('description')->nullable();
                $table->foreignId('project_id')->constrained()->onDelete('cascade');
                $table->string('table_name')->nullable();
                $table->text('query')->nullable();
                $table->integer('priority')->default(50);
                $table->string('status')->default('active');
                $table->string('sync_frequency')->nullable();
                $table->boolean('incremental_sync')->default(true);
                $table->json('config')->nullable();
                $table->timestamp('last_synced_at')->nullable();
                $table->foreignId('embedding_model_id')->nullable()->constrained('ai_models')->nullOnDelete();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('knowledge_sources');
    }
};
