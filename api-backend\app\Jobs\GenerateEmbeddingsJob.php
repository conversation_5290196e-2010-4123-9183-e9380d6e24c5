<?php

namespace App\Jobs;

use App\Models\Document;
use App\Services\VectorEmbeddingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class GenerateEmbeddingsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var array
     */
    public $backoff = [30, 60, 120];

    /**
     * The document ID to generate embeddings for.
     *
     * @var int
     */
    protected $documentId;

    /**
     * The options for embedding generation.
     *
     * @var array
     */
    protected $options;

    /**
     * Create a new job instance.
     *
     * @param int $documentId
     * @param array $options
     * @return void
     */
    public function __construct(int $documentId, array $options = [])
    {
        $this->documentId = $documentId;
        $this->options = $options;
        $this->onQueue('embeddings');
    }

    /**
     * Execute the job.
     *
     * @param VectorEmbeddingService $embeddingService
     * @return void
     */
    public function handle(VectorEmbeddingService $embeddingService)
    {
        try {
            $document = Document::findOrFail($this->documentId);

            // Update document status
            $document->update([
                'status' => 'processing',
                'embeddings_progress' => 0,
                'metadata' => array_merge($document->metadata ?? [], [
                    'embedding_job_started' => now()->toIso8601String(),
                ]),
            ]);

            Log::info("Starting embedding generation for document {$this->documentId}", [
                'document_name' => $document->file_name,
                'options' => $this->options,
            ]);

            // Generate embeddings
            $result = $embeddingService->generateEmbeddings($document, $this->options);

            Log::info("Completed embedding generation for document {$this->documentId}", [
                'document_name' => $document->file_name,
                'result' => $result,
            ]);

        } catch (Exception $e) {
            Log::error("Failed to generate embeddings for document {$this->documentId}: " . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'options' => $this->options,
            ]);

            // Update document status
            $document = Document::find($this->documentId);
            if ($document) {
                $document->update([
                    'status' => 'failed',
                    'metadata' => array_merge($document->metadata ?? [], [
                        'embedding_error' => $e->getMessage(),
                        'embedding_error_time' => now()->toIso8601String(),
                    ]),
                ]);
            }

            // Determine if the job should be retried
            $shouldRetry = $this->shouldRetry($e->getMessage());

            if ($shouldRetry && $this->attempts() < $this->tries) {
                // Release the job back to the queue
                $delay = $this->backoff[$this->attempts() - 1] ?? 30;
                $this->release($delay);

                Log::info("Job released for retry (attempt {$this->attempts()} of {$this->tries}) with {$delay}s delay");
            } else {
                // Fail the job permanently
                $this->fail($e);
            }
        }
    }

    /**
     * Determine if the job should be retried based on the error message.
     *
     * @param string $errorMessage
     * @return bool
     */
    protected function shouldRetry(string $errorMessage): bool
    {
        // Retry on rate limit errors
        if (
            stripos($errorMessage, 'rate limit') !== false ||
            stripos($errorMessage, 'too many requests') !== false ||
            stripos($errorMessage, '429') !== false
        ) {
            return true;
        }

        // Retry on temporary server errors
        if (
            stripos($errorMessage, 'server error') !== false ||
            stripos($errorMessage, '5xx') !== false ||
            stripos($errorMessage, 'timeout') !== false ||
            stripos($errorMessage, 'connection') !== false
        ) {
            return true;
        }

        // Don't retry on validation errors, authentication errors, etc.
        if (
            stripos($errorMessage, 'invalid') !== false ||
            stripos($errorMessage, 'not found') !== false ||
            stripos($errorMessage, 'unauthorized') !== false ||
            stripos($errorMessage, 'authentication') !== false
        ) {
            return false;
        }

        // Default to retry for other errors
        return true;
    }

    /**
     * The job failed to process.
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception)
    {
        Log::error("Job failed permanently: " . $exception->getMessage(), [
            'document_id' => $this->documentId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // Update document with permanent failure status
        $document = Document::find($this->documentId);
        if ($document) {
            $document->update([
                'status' => 'failed',
                'metadata' => array_merge($document->metadata ?? [], [
                    'embedding_error' => $exception->getMessage(),
                    'embedding_error_time' => now()->toIso8601String(),
                    'embedding_job_failed' => true,
                    'embedding_attempts' => $this->attempts(),
                ]),
            ]);
        }
    }
}
