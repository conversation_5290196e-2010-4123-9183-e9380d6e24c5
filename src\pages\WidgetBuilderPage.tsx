import React from 'react';
import { AdminLayout } from "@/components/admin-layout";
import WidgetBuilder from '@/components/WidgetBuilder';
import PermissionErrorBoundary from '@/components/error-handling/PermissionErrorBoundary';

const WidgetBuilderPage = () => {
  return (
    <AdminLayout>
      <PermissionErrorBoundary 
        feature="template management"
        paths={['/site_integration/template_list', '/writing/get_template_list']}
      >
        <WidgetBuilder />
      </PermissionErrorBoundary>
    </AdminLayout>
  );
};

export default WidgetBuilderPage;