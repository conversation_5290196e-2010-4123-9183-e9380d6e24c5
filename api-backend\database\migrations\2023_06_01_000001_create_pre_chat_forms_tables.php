<?php

/**
 * PLACEHOLDER MIGRATION FILE
 *
 * This is a conceptual migration file for the pre-chat form tables.
 * It outlines the schema but is not meant to be executed as-is.
 * The actual implementation will be added in a future release.
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Pre-chat form templates
        Schema::create('pre_chat_form_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('widget_id')->constrained('widgets')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Pre-chat form fields
        Schema::create('pre_chat_form_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('template_id')->constrained('pre_chat_form_templates')->onDelete('cascade');
            $table->string('name');
            $table->string('label');
            $table->enum('type', ['text', 'email', 'phone', 'select', 'checkbox']);
            $table->string('placeholder')->nullable();
            $table->json('options')->nullable(); // For select fields
            $table->boolean('is_required')->default(false);
            $table->string('validation_pattern')->nullable();
            $table->string('error_message')->nullable();
            $table->integer('order')->default(0);
            $table->timestamps();
        });

        // Pre-chat form submissions
        Schema::create('pre_chat_form_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('template_id')->constrained('pre_chat_form_templates');
            $table->string('session_id');
            $table->json('data'); // Field name to value mapping
            $table->timestamps();

            // Add index for faster lookups
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pre_chat_form_submissions');
        Schema::dropIfExists('pre_chat_form_fields');
        Schema::dropIfExists('pre_chat_form_templates');
    }
};
