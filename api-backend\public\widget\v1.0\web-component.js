/**
 * AI Chat Widget - Web Component
 * v1.0.0
 *
 * Usage:
 * <ai-chat-widget widget-id="your-widget-id"></ai-chat-widget>
 */

class AIChatWidget extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });

        // Initialize properties
        this._widgetId = this.getAttribute('widget-id');
        this._primaryColor = this.getAttribute('primary-color') || '#4f46e5';
        this._borderRadius = this.getAttribute('border-radius') || '8';
        this._allowedDomains = this.getAttribute('allowed-domains') || '*';
        this._guestFlow = this.getAttribute('guest-flow') || 'auto';
        this._webhookUrl = this.getAttribute('webhook-url') || '';
        this._analytics = this.getAttribute('analytics') === 'true';

        // Get base URL
        const scriptUrl = Array.from(document.querySelectorAll('script'))
            .find(script => script.src && script.src.includes('/web-component.js'))?.src;
        this._baseUrl = (window.CHAT_WIDGET_BASE_URL || (scriptUrl ? scriptUrl.split('/widget/')[0] : null)) || 'http://localhost:9090';

        // Create widget
        this._render();
    }

    static get observedAttributes() {
        return ['widget-id', 'primary-color', 'border-radius', 'allowed-domains',
            'guest-flow', 'webhook-url', 'analytics'];
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue === newValue) return;

        switch (name) {
            case 'widget-id':
                this._widgetId = newValue;
                break;
            case 'primary-color':
                this._primaryColor = newValue;
                this._updateStyles();
                break;
            case 'border-radius':
                this._borderRadius = newValue;
                this._updateStyles();
                break;
            case 'allowed-domains':
                this._allowedDomains = newValue;
                break;
            case 'guest-flow':
                this._guestFlow = newValue;
                break;
            case 'webhook-url':
                this._webhookUrl = newValue;
                break;
            case 'analytics':
                this._analytics = newValue === 'true';
                break;
        }

        if (this._iframe) {
            this._updateIframeAttributes();
        }
    }

    connectedCallback() {
        // Handle iframe messages
        this._setupMessageListener();

        // Log initialization if analytics enabled
        if (this._analytics) {
            this._logWidgetEvent('widget_initialized');
        }
    }

    disconnectedCallback() {
        window.removeEventListener('message', this._handleMessage);
        if (this._analytics) {
            this._logWidgetEvent('widget_removed');
        }
    }

    _render() {
        // Create styles
        const style = document.createElement('style');
        style.textContent = `
      :host {
        display: block;
        position: relative;
        z-index: 9999;
        width: 0;
        height: 0;
      }

      .widget-iframe {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border: none;
        z-index: 9999;
        transition: width 0.3s, height 0.3s;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-radius: ${this._borderRadius}px;
        background-color: transparent;
      }
    `;

        // Create iframe
        const iframe = document.createElement('iframe');
        iframe.className = 'widget-iframe';
        iframe.src = `${this._baseUrl}/widget/v1.0/iframe/${this._widgetId}`;
        iframe.setAttribute('loading', 'lazy');
        iframe.setAttribute('title', 'AI Chat Widget');
        iframe.setAttribute('allow', 'microphone; camera');

        // Set data attributes
        this._updateIframeAttributes(iframe);

        // Append to shadow DOM
        this.shadowRoot.appendChild(style);
        this.shadowRoot.appendChild(iframe);

        // Store reference
        this._iframe = iframe;
        this._style = style;
    }

    _updateStyles() {
        if (this._style) {
            this._style.textContent = `
        :host {
          display: block;
          position: relative;
          z-index: 9999;
          width: 0;
          height: 0;
        }

        .widget-iframe {
          position: fixed;
          bottom: 20px;
          right: 20px;
          width: 50px;
          height: 50px;
          border: none;
          z-index: 9999;
          transition: width 0.3s, height 0.3s;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border-radius: ${this._borderRadius}px;
          background-color: transparent;
        }
      `;
        }
    }

    _updateIframeAttributes(iframe = null) {
        const target = iframe || this._iframe;
        if (!target) return;

        // Update data attributes
        target.setAttribute('data-allowed-domains', this._allowedDomains);
        target.setAttribute('data-guest-flow', this._guestFlow);

        if (this._webhookUrl) {
            target.setAttribute('data-webhook-url', this._webhookUrl);
        } else if (target.hasAttribute('data-webhook-url')) {
            target.removeAttribute('data-webhook-url');
        }

        // Apply CSS variable for primary color
        if (this._primaryColor) {
            target.style.setProperty('--primary-color', this._primaryColor);
        }
    }

    _setupMessageListener() {
        // Use a bound function to maintain this context
        this._handleMessage = this._onMessage.bind(this);
        window.addEventListener('message', this._handleMessage);
    }

    _onMessage(event) {
        // In production, validate the origin
        // if (event.origin !== expectedOrigin) return;

        // Handle resize messages
        if (event.data && event.data.type === 'resize_widget') {
            this._iframe.style.width = `${event.data.width}px`;
            this._iframe.style.height = `${event.data.height}px`;
        }

        // Handle widget state changes
        if (event.data && event.data.type === 'widget_opened') {
            // Could add custom behavior when widget is opened
            if (this._analytics) {
                this._logWidgetEvent('widget_opened');
            }
            this.dispatchEvent(new CustomEvent('widgetOpened'));
        }

        if (event.data && event.data.type === 'widget_closed') {
            // Could add custom behavior when widget is closed
            if (this._analytics) {
                this._logWidgetEvent('widget_closed');
            }
            this.dispatchEvent(new CustomEvent('widgetClosed'));
        }
    }

    _logWidgetEvent(eventType, eventData = {}) {
        if (!this._analytics) return;

        const data = {
            event: eventType,
            widget_id: this._widgetId,
            timestamp: new Date().toISOString(),
            page_url: window.location.href,
            referrer: document.referrer,
            ...eventData
        };

        // In production, this would send to your analytics endpoint
        console.log('Widget analytics:', data);

        try {
            fetch(`${this._baseUrl}/api/widget-analytics`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
                mode: 'cors',
                credentials: 'omit'
            }).catch(err => console.log('Analytics error:', err));
        } catch (e) {
            console.log('Failed to send analytics');
        }
    }

    // Public API
    open() {
        this._iframe.contentWindow.postMessage({ type: 'open_widget' }, '*');
    }

    close() {
        this._iframe.contentWindow.postMessage({ type: 'close_widget' }, '*');
    }

    toggle() {
        this._iframe.contentWindow.postMessage({ type: 'toggle_widget' }, '*');
    }

    setTheme(theme) {
        this._iframe.contentWindow.postMessage({
            type: 'set_theme',
            theme: theme
        }, '*');
    }
}

// Register the custom element
customElements.define('ai-chat-widget', AIChatWidget);
