<?php

namespace App\Http\Controllers;

use App\Models\Widget;
use App\Http\Requests\WidgetRequest;
use App\Services\WidgetService;
use App\Services\WidgetBehaviorService;
use App\Services\WidgetLogoService;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\UploadedFile;

class WidgetController extends Controller
{
    protected WidgetService $widgetService;
    protected WidgetBehaviorService $behaviorService;
    protected WidgetLogoService $logoService;
    protected WebhookService $webhookService;

    public function __construct(
        WidgetService $widgetService,
        WidgetBehaviorService $behaviorService,
        WidgetLogoService $logoService,
        WebhookService $webhookService
    ) {
        $this->widgetService = $widgetService;
        $this->behaviorService = $behaviorService;
        $this->logoService = $logoService;
        $this->webhookService = $webhookService;
    }
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $widgets = $this->widgetService->getUserWidgets($request->user());

            // Transform widgets for API response
            $transformedWidgets = $widgets->map(function ($widget) {
                return $this->widgetService->transformForApi($widget);
            });

            return response()->json($transformedWidgets);
        } catch (\Exception $e) {
            \Log::error('Failed to fetch widgets: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch widgets'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\WidgetRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(WidgetRequest $request)
    {
        try {
            $widget = $this->widgetService->createWidget($request->user(), $request->validated());

            // Transform for API response
            $transformedWidget = $this->widgetService->transformForApi($widget);

            return response()->json($transformedWidget, 201);
        } catch (\Exception $e) {
            \Log::error('Failed to create widget: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create widget', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        try {
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            // Transform for API response
            $transformedWidget = $this->widgetService->transformForApi($widget);

            return response()->json($transformedWidget);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to fetch widget: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch widget'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\WidgetRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(WidgetRequest $request, $id)
    {
        try {
            $widget = $this->widgetService->updateWidget($request->user(), $id, $request->validated());

            // Transform for API response
            $transformedWidget = $this->widgetService->transformForApi($widget);

            return response()->json([
                'message' => 'Widget updated successfully',
                'widget' => $transformedWidget
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to update widget: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $this->widgetService->deleteWidget($request->user(), $id);

            return response()->json(null, 204);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to delete widget: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete widget'], 500);
        }
    }

    /**
     * Get a widget by its public widget_id.
     * This endpoint is public and used by the widget script.
     *
     * @param  string  $widgetId
     * @return \Illuminate\Http\Response
     */
    public function getByWidgetId($widgetId)
    {
        try {
            $widget = $this->widgetService->getPublicWidget($widgetId);

            // Perform domain validation if request has referrer
            $referer = request()->header('referer');
            if ($referer) {
                $refererHost = parse_url($referer, PHP_URL_HOST);
                if (!$this->widgetService->isDomainAllowed($widget, $refererHost)) {
                    \Log::warning("Domain access attempt blocked: {$refererHost} tried to access widget {$widgetId}");
                    return response()->json([
                        'error' => 'Access denied',
                        'message' => 'This domain is not authorized to load this widget'
                    ], 403);
                }
            }

            // Get behavior settings for public display
            $behaviorSettings = $this->behaviorService->getPublicBehaviorSettings($widget->id);

            // Get optimized logo for public display
            $logoSettings = $this->logoService->getOptimizedLogo($widget->id);

            // Return only the necessary public information
            $publicData = [
                'widget_id' => $widget->widget_id,
                'settings' => $widget->settings,
                'version' => $widget->version,
                'position_type' => $widget->position_type,
                'position_settings' => $widget->position_settings,
                'custom_css' => $widget->custom_css,
                'ai_model_id' => $widget->ai_model_id,
                'behavior' => $behaviorSettings,
            ];

            // Add logo if available
            if ($logoSettings) {
                $publicData['logo'] = $logoSettings;
            }

            return response()->json($publicData);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Error fetching widget by ID: ' . $e->getMessage());
            return response()->json(['error' => 'Error fetching widget'], 500);
        }
    }

    /**
     * Upload logo file for a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadLogo(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $validator = Validator::make($request->all(), [
                'logo' => 'required|image|mimes:jpeg,jpg,png,gif,svg,webp|max:5120', // 5MB max
                'position' => 'nullable|string|in:header,footer,sidebar,floating',
                'displayWidth' => 'nullable|integer|min:10|max:500',
                'displayHeight' => 'nullable|integer|min:10|max:500',
                'altText' => 'nullable|string|max:255',
                'quality' => 'nullable|integer|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            /** @var UploadedFile $file */
            $file = $request->file('logo');

            // Convert file to base64 for storage
            $base64Data = 'data:' . $file->getMimeType() . ';base64,' . base64_encode(file_get_contents($file->getPathname()));

            // Prepare metadata
            $metadata = [
                'originalFilename' => $file->getClientOriginalName(),
                'position' => $request->input('position', 'header'),
                'displayWidth' => $request->input('displayWidth'),
                'displayHeight' => $request->input('displayHeight'),
                'altText' => $request->input('altText'),
                'quality' => $request->input('quality', 92),
            ];

            // Process the upload
            $logo = $this->logoService->processImageUpload($widget->id, $base64Data, $metadata);

            return response()->json([
                'message' => 'Logo uploaded successfully',
                'logo' => $logo->toConfigArray(),
                'logoUrl' => $logo->getDisplayData(),
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to upload logo: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to upload logo', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update logo URL for a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLogoUrl(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $validator = Validator::make($request->all(), [
                'logoUrl' => 'required|url',
                'position' => 'nullable|string|in:header,footer,sidebar,floating',
                'displayWidth' => 'nullable|integer|min:10|max:500',
                'displayHeight' => 'nullable|integer|min:10|max:500',
                'altText' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            // Validate that the URL points to an image
            $logoUrl = $request->input('logoUrl');
            if (!preg_match('/\.(jpg|jpeg|png|gif|svg|webp)(\?.*)?$/i', $logoUrl)) {
                return response()->json(['error' => 'URL must point to a valid image file'], 422);
            }

            // Prepare metadata
            $metadata = [
                'position' => $request->input('position', 'header'),
                'displayWidth' => $request->input('displayWidth'),
                'displayHeight' => $request->input('displayHeight'),
                'altText' => $request->input('altText'),
            ];

            // Process the URL
            $logo = $this->logoService->processLogoUrl($widget->id, $logoUrl, $metadata);

            return response()->json([
                'message' => 'Logo URL updated successfully',
                'logo' => $logo->toConfigArray(),
                'logoUrl' => $logo->getDisplayData(),
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to update logo URL: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update logo URL', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Get logo for a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLogo(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $logo = $this->logoService->getLogo($widget->id);

            if (!$logo) {
                return response()->json(['message' => 'No logo found for this widget'], 404);
            }

            return response()->json([
                'logo' => $logo->toConfigArray(),
                'logoUrl' => $logo->getDisplayData(),
                'metadata' => $logo->getMetadata(),
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to get logo: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get logo'], 500);
        }
    }

    /**
     * Delete logo for a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteLogo(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $deleted = $this->logoService->deleteLogo($widget->id);

            if (!$deleted) {
                return response()->json(['message' => 'No logo found to delete'], 404);
            }

            return response()->json(['message' => 'Logo deleted successfully']);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to delete logo: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete logo'], 500);
        }
    }

    /**
     * Test webhook configuration for a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function testWebhook(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $validator = Validator::make($request->all(), [
                'url' => 'required|url',
                'method' => 'nullable|string|in:GET,POST,PUT,PATCH',
                'headers' => 'nullable|array',
                'secret' => 'nullable|string|max:255',
                'eventType' => 'nullable|string|in:chat_start,chat_end,message_sent,message_received,user_rating,form_submission',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            // Prepare test integration configuration
            $integration = [
                'id' => 'test-webhook-' . Str::random(8),
                'type' => 'generic',
                'url' => $request->input('url'),
                'method' => $request->input('method', 'POST'),
                'headers' => $request->input('headers', []),
                'secret' => $request->input('secret'),
                'active' => true,
                'events' => [$request->input('eventType', 'message_sent')],
            ];

            // Prepare test payload
            $testPayload = [
                'widget_id' => $widget->widget_id,
                'test' => true,
                'message' => 'This is a test webhook from your chat widget',
                'user_name' => 'Test User',
                'timestamp' => now()->toISOString(),
                'event_type' => $request->input('eventType', 'message_sent'),
            ];

            // Test the webhook
            $result = $this->webhookService->testIntegration($integration, $testPayload);

            return response()->json([
                'message' => 'Webhook test completed',
                'result' => $result,
                'test_payload' => $testPayload,
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to test webhook: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to test webhook', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Export widget configuration.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportConfig(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            // Get complete widget configuration
            $config = [
                'widget' => [
                    'name' => $widget->name,
                    'version' => $widget->version,
                    'settings' => $widget->settings,
                    'allowed_domains' => $widget->allowed_domains,
                    'position_type' => $widget->position_type,
                    'position_settings' => $widget->position_settings,
                    'custom_css' => $widget->custom_css,
                    'typography' => $widget->typography,
                    'button_customization' => $widget->button_customization,
                    'mobile_settings' => $widget->mobile_settings,
                ],
                'behavior' => null,
                'logo' => null,
                'webhooks' => [],
                'export_info' => [
                    'exported_at' => now()->toISOString(),
                    'exported_by' => $request->user()->email,
                    'version' => '1.0',
                    'format' => 'json',
                ],
            ];

            // Get behavior settings
            $behavior = $this->behaviorService->getBehavior($widget->id);
            if ($behavior) {
                $config['behavior'] = $behavior->toSettingsArray();
            }

            // Get logo settings
            $logo = $this->logoService->getLogo($widget->id);
            if ($logo) {
                $logoConfig = $logo->toConfigArray();
                // Remove sensitive data for export
                unset($logoConfig['logo_data']); // Don't export base64 data
                $config['logo'] = $logoConfig;
            }

            // Get webhook settings
            $webhooks = $this->webhookService->getWebhooks($widget->id);
            foreach ($webhooks as $webhook) {
                $webhookConfig = $webhook->toArray();
                // Remove sensitive data for export
                unset($webhookConfig['secret_key']);
                unset($webhookConfig['last_error']);
                $config['webhooks'][] = $webhookConfig;
            }

            return response()->json([
                'message' => 'Widget configuration exported successfully',
                'config' => $config,
                'filename' => 'widget-' . $widget->widget_id . '-config.json',
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to export widget config: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to export widget configuration'], 500);
        }
    }

    /**
     * Get version history for a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVersionHistory(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $versions = $this->widgetService->getVersionHistory($widget->id);

            return response()->json($versions);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to get version history: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get version history'], 500);
        }
    }

    /**
     * Create a new version of a widget.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function createVersion(Request $request, $id)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $validator = Validator::make($request->all(), [
                'version_description' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            $version = $this->widgetService->createVersion(
                $widget->id,
                $request->input('version_description', 'Version created on ' . now()->format('Y-m-d H:i:s'))
            );

            return response()->json([
                'message' => 'Version created successfully',
                'version' => $version,
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to create version: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create version'], 500);
        }
    }

    /**
     * Restore a specific version of a widget.
     *
     * @param Request $request
     * @param int $id
     * @param string $version
     * @return \Illuminate\Http\JsonResponse
     */
    public function restoreVersion(Request $request, $id, $version)
    {
        try {
            // Verify widget ownership
            $widget = $this->widgetService->getUserWidget($request->user(), $id);

            $restoredWidget = $this->widgetService->restoreVersion($widget->id, $version);

            return response()->json([
                'message' => 'Version restored successfully',
                'widget' => $this->widgetService->transformForApi($restoredWidget),
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget or version not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Failed to restore version: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to restore version'], 500);
        }
    }

    /**
     * Bulk update widgets.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkUpdate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'widget_ids' => 'required|array|min:1',
                'widget_ids.*' => 'integer|exists:widgets,id',
                'data' => 'required|array',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            $widgetIds = $request->input('widget_ids');
            $updateData = $request->input('data');

            $result = $this->widgetService->bulkUpdate($request->user(), $widgetIds, $updateData);

            return response()->json([
                'message' => 'Widgets updated successfully',
                'updated_count' => $result['updated_count'],
                'failed_count' => $result['failed_count'],
                'errors' => $result['errors'],
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to bulk update widgets: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to bulk update widgets'], 500);
        }
    }

    /**
     * Bulk delete widgets.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkDelete(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'widget_ids' => 'required|array|min:1',
                'widget_ids.*' => 'integer|exists:widgets,id',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            $widgetIds = $request->input('widget_ids');

            $result = $this->widgetService->bulkDelete($request->user(), $widgetIds);

            return response()->json([
                'message' => 'Widgets deleted successfully',
                'deleted_count' => $result['deleted_count'],
                'failed_count' => $result['failed_count'],
                'errors' => $result['errors'],
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to bulk delete widgets: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to bulk delete widgets'], 500);
        }
    }

    /**
     * Bulk activate/deactivate widgets.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkActivate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'widget_ids' => 'required|array|min:1',
                'widget_ids.*' => 'integer|exists:widgets,id',
                'is_active' => 'required|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            $widgetIds = $request->input('widget_ids');
            $isActive = $request->input('is_active');

            $result = $this->widgetService->bulkActivate($request->user(), $widgetIds, $isActive);

            return response()->json([
                'message' => 'Widgets ' . ($isActive ? 'activated' : 'deactivated') . ' successfully',
                'updated_count' => $result['updated_count'],
                'failed_count' => $result['failed_count'],
                'errors' => $result['errors'],
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to bulk activate widgets: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to bulk activate widgets'], 500);
        }
    }
}
