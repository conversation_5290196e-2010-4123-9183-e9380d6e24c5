import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useWidgetSettings } from "@/hooks/use-widget-settings";
import { useEmbedCode } from "@/hooks/use-embed-code";
import { AlertCircle, Copy, Check, HelpCircle, Code, ExternalLink } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { WidgetSelector } from "@/components/embed-code/widget-selector";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export function EmbedCodeModule() {
  // Widget list comes from useWidgetSettings, which fetches from the backend via widgetService.getAllWidgets()
  const [activeTab, setActiveTab] = useState("standard");
  const {
    widgets,
    selectedWidget,
    setSelectedWidget,
    getWidgetConfig
  } = useWidgetSettings();
  const { copied, setCopied, generateEmbedCode, getEmbedDescription } = useEmbedCode();
  const [embedCode, setEmbedCode] = useState("");

  const handleGenerateCode = async (type: string) => {
    const code = await generateEmbedCode(type, getWidgetConfig());
    setEmbedCode(code);
  };

  const handleWidgetChange = (widgetId: string) => {
    setSelectedWidget(widgetId);
    handleGenerateCode(activeTab);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(embedCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-6">
      <WidgetSelector
        widgets={widgets}
        selectedWidget={selectedWidget}
        onWidgetChange={handleWidgetChange}
      />
      <Card>
        <CardHeader>
          <CardTitle>Embed Code Generator</CardTitle>
          <CardDescription>
            Generate code to add the chat widget to your website. No coding knowledge required!
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <HelpCircle className="h-4 w-4" />
            <AlertTitle>Easy Integration</AlertTitle>
            <AlertDescription>
              Simply copy the generated code and paste it into your website's HTML. The widget will appear automatically.
            </AlertDescription>
          </Alert>

          <Tabs value={activeTab} onValueChange={(value) => {
            setActiveTab(value);
            handleGenerateCode(value);
          }}>
            <TabsList className="mb-4">
              <TabsTrigger value="standard">Standard Script</TabsTrigger>
              <TabsTrigger value="iframe">iFrame (Recommended)</TabsTrigger>
              <TabsTrigger value="webcomponent">Web Component</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-4">
              <p className="text-muted-foreground">
                {getEmbedDescription(activeTab)}
              </p>

              <div className="relative">
                <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                  <code className="text-sm">{embedCode || "Click 'Generate Code' to create embed code"}</code>
                </pre>

                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={copyToClipboard}
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>

              <Button onClick={() => handleGenerateCode(activeTab)} className="mb-4">
                <Code className="h-4 w-4 mr-2" />
                Generate Code
              </Button>

              <Accordion type="single" collapsible className="bg-muted/50 p-4 rounded-md">
                <AccordionItem value="installation-guide">
                  <AccordionTrigger>Installation Guide</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Step 1: Copy the code</h4>
                        <p className="text-sm text-muted-foreground">
                          Click the "Generate Code" button above, then click the copy icon in the top-right corner of the code box.
                        </p>
                      </div>

                      <div>
                        <h4 className="font-medium mb-2">Step 2: Paste into your website</h4>
                        <p className="text-sm text-muted-foreground">
                          For most website platforms, paste the code just before the closing <code>&lt;/body&gt;</code> tag in your HTML.
                        </p>
                      </div>

                      <div>
                        <h4 className="font-medium mb-2">Common Platforms</h4>
                        <ul className="text-sm text-muted-foreground space-y-2">
                          <li><strong>WordPress:</strong> Use a "Custom HTML" block or add to your theme's footer.php file</li>
                          <li><strong>Shopify:</strong> Go to Online Store → Themes → Edit code → theme.liquid (at the bottom)</li>
                          <li><strong>Wix:</strong> Add a "Custom Code" element to your page</li>
                          <li><strong>Squarespace:</strong> Go to Settings → Advanced → Code Injection → Footer</li>
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium mb-2">Testing</h4>
                        <p className="text-sm text-muted-foreground">
                          After adding the code, refresh your website and you should see the chat widget appear. Click it to test functionality.
                        </p>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
