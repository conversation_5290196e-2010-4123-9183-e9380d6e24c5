import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import {
    Upload, FileText, Download, Trash2, Eye, Search,
    RefreshCw, Grid, List, SlidersHorizontal, Database
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Progress } from '@/components/ui/progress'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { EmbeddingStatus } from '@/components/knowledge-base/embedding-status'
import { BatchEmbeddingButton } from '@/components/knowledge-base/batch-embedding-button'
import NoDocumentsPlaceholder from '@/components/knowledge-base/no-documents-placeholder'
import { AIModelSelector } from '@/components/ai-models/ai-model-selector'

export default function DocumentsTab({
    projects,
    selectedProjectId,
    setSelectedProjectId,
    refreshTrigger,
    onRefresh
}) {
    // State
    const [documents, setDocuments] = useState([])
    const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
    const [files, setFiles] = useState([])
    const [fileFilter, setFileFilter] = useState('')
    const [sortOrder, setSortOrder] = useState('desc')
    const [selectedModel, setSelectedModel] = useState(null)
    const [isActiveSource, setIsActiveSource] = useState(true)
    const [category, setCategory] = useState('general')
    const [uploadProgress, setUploadProgress] = useState({})
    const [selectedFiles, setSelectedFiles] = useState([])
    const [selectedDocument, setSelectedDocument] = useState(null)
    const [previewOpen, setPreviewOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [pagination, setPagination] = useState({
        total: 0,
        per_page: 20,
        current_page: 1,
        last_page: 1,
        from: null,
        to: null
    })
    const [metadata, setMetadata] = useState({
        active_sources_count: 0,
        documents_with_embeddings_count: 0,
        total_document_count: 0,
        categories: []
    })
    const [currentPage, setCurrentPage] = useState(1)
    const [selectedSourceId, setSelectedSourceId] = useState(null)
    const [selectedCategory, setSelectedCategory] = useState('general')
    const [selectedModelId, setSelectedModelId] = useState(null)
    const [isUploading, setIsUploading] = useState(false)

    // Fetch documents for selected project
    useEffect(() => {
        const fetchDocuments = async () => {
            if (!selectedProjectId) return

            setIsLoading(true)
            try {
                // Build query parameters
                const params = new URLSearchParams();
                params.append('project_id', selectedProjectId.toString());
                params.append('sort_by', sortOrder === 'asc' ? 'created_at' : 'created_at');
                params.append('sort_dir', sortOrder);

                if (fileFilter) {
                    params.append('search', fileFilter);
                }

                // Get page size based on view mode (more for list, fewer for grid)
                const perPage = viewMode === 'list' ? 50 : 20;
                params.append('per_page', perPage.toString());

                const response = await knowledgeBaseService.getDocuments(
                    selectedProjectId,
                    params.toString()
                );

                if (response?.data?.success) {
                    setDocuments(response.data.data || []);

                    // Store pagination info if available
                    if (response.data.pagination) {
                        setPagination(response.data.pagination);
                    }

                    // Store metadata if available
                    if (response.data.meta) {
                        setMetadata(response.data.meta);
                    }
                } else {
                    setDocuments([]);
                    toast.error('Failed to load documents: ' + (response?.data?.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Failed to load documents:', error);
                setDocuments([]);
                toast.error('Failed to load documents. Please try again.');
            } finally {
                setIsLoading(false);
            }
        };

        fetchDocuments();
    }, [selectedProjectId, refreshTrigger, fileFilter, sortOrder, viewMode]);

    // File upload handlers
    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files?.length || !selectedProjectId) return;

        const file = e.target.files[0];

        // Check file size (max 100MB)
        const maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
            toast.error(`File is too large. Maximum size is 100MB`);
            return;
        }

        // Check file type
        const supportedFormats = [
            'pdf', 'docx', 'doc', 'xlsx', 'xls', 'csv', 'txt', 'json',
            'html', 'htm', 'md', 'markdown'
        ];

        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        if (!fileExtension || !supportedFormats.includes(fileExtension)) {
            toast.error(`Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`);
            return;
        }

        setIsUploading(true);

        try {
            // Show loading toast
            toast.loading(`Uploading ${file.name}...`);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('project_id', selectedProjectId.toString());

            if (selectedSourceId) {
                formData.append('source_id', selectedSourceId.toString());
            }

            if (selectedCategory) {
                formData.append('category', selectedCategory);
            }

            // Optional: automatically generate embeddings if a model is selected
            if (selectedModelId) {
                formData.append('model_id', selectedModelId.toString());
            }

            const response = await knowledgeBaseService.uploadDocument(formData);

            // Dismiss loading toast
            toast.dismiss();

            if (response?.data?.success) {
                toast.success(`Successfully uploaded ${file.name}`);

                // If embeddings were requested, show additional notification
                if (selectedModelId) {
                    toast.info(`Generating embeddings for ${file.name}. This may take a few minutes.`);
                }

                // Refresh document list
                onRefresh();
            } else {
                toast.error(`Failed to upload: ${response?.data?.message || 'Unknown error'}`);
            }
        } catch (error) {
            toast.dismiss();
            console.error('Failed to upload document:', error);
            toast.error(`Failed to upload ${file.name}: ${error.message || 'Unknown error'}`);
        } finally {
            setIsUploading(false);

            // Reset the file input
            if (e.target) {
                e.target.value = '';
            }
        }
    };

    // Toggle AI source
    const handleToggleActive = useCallback((id, active) => {
        knowledgeBaseService.toggleDocumentAsAISource(id, active)
            .then(() => {
                setDocuments(documents.map(doc =>
                    doc.id === id ? { ...doc, is_active_source: active } : doc
                ))
                setSelectedFiles(prev =>
                    prev.map(f => f.id === id ? { ...f, is_active_source: active } : f)
                )
                toast.success('AI source updated')
            })
            .catch((error) => {
                console.error('Error toggling AI source:', error)
                toast.error('Update failed')
            })
    }, [documents])

    // Delete document
    const handleDeleteDocument = useCallback((id) => {
        if (confirm('Are you sure you want to delete this document?')) {
            knowledgeBaseService.deleteDocument(id)
                .then(response => {
                    if (response.data?.success) {
                        setSelectedFiles(prev => prev.filter(f => f.id !== id))
                        onRefresh()
                        toast.success('Document deleted')
                    } else {
                        toast.error(response.data?.message || 'Delete failed')
                    }
                })
                .catch((error) => {
                    console.error('Error deleting document:', error)
                    toast.error('Delete failed')
                })
        }
    }, [onRefresh])

    // Filtered and sorted documents
    const filteredDocuments = useCallback(() => {
        return documents
            .filter(doc =>
                !fileFilter ? true : (
                    doc.file_name?.toLowerCase().includes(fileFilter.toLowerCase()) ||
                    (doc.category || '').toLowerCase().includes(fileFilter.toLowerCase()) ||
                    (doc.file_type || '').toLowerCase().includes(fileFilter.toLowerCase())
                )
            )
            .sort((a, b) => {
                const dateA = new Date(a.created_at || 0).getTime()
                const dateB = new Date(b.created_at || 0).getTime()
                return sortOrder === 'asc' ? dateA - dateB : dateB - dateA
            })
    }, [documents, fileFilter, sortOrder])

    // Function to handle page change
    const handlePageChange = (page) => {
        setCurrentPage(page);

        // Trigger a refetch with the new page
        const fetchPage = async () => {
            if (!selectedProjectId) return;

            setIsLoading(true);
            try {
                // Build query parameters
                const params = new URLSearchParams();
                params.append('project_id', selectedProjectId.toString());
                params.append('sort_by', sortOrder === 'asc' ? 'created_at' : 'created_at');
                params.append('sort_dir', sortOrder);
                params.append('page', page.toString());

                if (fileFilter) {
                    params.append('search', fileFilter);
                }

                // Get page size based on view mode
                const perPage = viewMode === 'list' ? 50 : 20;
                params.append('per_page', perPage.toString());

                const response = await knowledgeBaseService.getDocuments(
                    selectedProjectId,
                    params.toString()
                );

                if (response?.data?.success) {
                    setDocuments(response.data.data || []);

                    // Store pagination info if available
                    if (response.data.pagination) {
                        setPagination(response.data.pagination);
                    }
                } else {
                    toast.error('Failed to load page');
                }
            } catch (error) {
                console.error('Failed to load page:', error);
                toast.error('Failed to load page');
            } finally {
                setIsLoading(false);
            }
        };

        fetchPage();
    };

    return (
        <div className="space-y-6">
            {/* Project selector and controls */}
            <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                    <ProjectSelector
                        projects={projects}
                        selectedProjectId={selectedProjectId}
                        setSelectedProjectId={setSelectedProjectId}
                    />

                    <Button
                        variant="outline"
                        size="icon"
                        onClick={onRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                </div>

                <div className="flex items-center gap-3">
                    <div className="relative">
                        <Input
                            placeholder="Filter documents..."
                            value={fileFilter}
                            onChange={e => setFileFilter(e.target.value)}
                            className="pl-8 w-[200px]"
                        />
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    </div>

                    <Select
                        value={sortOrder}
                        onValueChange={setSortOrder}
                    >
                        <SelectTrigger className="w-[130px]">
                            <SelectValue placeholder="Sort order" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="desc">Newest first</SelectItem>
                            <SelectItem value="asc">Oldest first</SelectItem>
                        </SelectContent>
                    </Select>

                    <div className="flex items-center gap-2 border rounded-md p-1">
                        <Button
                            variant={viewMode === 'grid' ? 'default' : 'ghost'}
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => setViewMode('grid')}
                        >
                            <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'list' ? 'default' : 'ghost'}
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => setViewMode('list')}
                        >
                            <List className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>

            {/* Main content area */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Upload panel */}
                <Card className="md:col-span-1">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-lg">
                            <Upload className="h-5 w-5" />
                            Upload Documents
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div
                            className="border-2 border-dashed rounded-md p-6 mb-4 text-center hover:bg-muted/50 transition cursor-pointer"
                            onClick={() => document.getElementById('file-input')?.click()}
                        >
                            <input
                                type="file"
                                id="file-input"
                                className="hidden"
                                onChange={handleFileChange}
                            />
                            <Upload className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                            <p className="text-sm font-medium">Drag and drop files here or click to browse</p>
                            <p className="text-xs text-muted-foreground mt-1">
                                Supported formats: PDF, DOCX, XLSX, TXT, CSV, JSON
                            </p>
                        </div>

                        <div className="space-y-4 mt-4">
                            <div className="space-y-2">
                                <Label htmlFor="category-input">Category</Label>
                                <Input
                                    id="category-input"
                                    value={selectedCategory}
                                    onChange={(e) => setSelectedCategory(e.target.value)}
                                    placeholder="General"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="ai-model-selector">Processing Model (Optional)</Label>
                                <AIModelSelector
                                    id="ai-model-selector"
                                    selectedModelId={selectedModelId}
                                    onModelChange={(value) => {
                                        console.log("Model changed:", value);
                                        setSelectedModelId(value);
                                    }}
                                    placeholder="Select AI model"
                                    isOptional={true}
                                    className="w-full"
                                />
                                <p className="text-xs text-muted-foreground">
                                    AI model will be used for processing and embedding
                                </p>
                            </div>

                            <div className="flex items-center gap-2">
                                <Checkbox
                                    id="ai-source-checkbox"
                                    checked={isActiveSource}
                                    onCheckedChange={v => setIsActiveSource(!!v)}
                                />
                                <Label htmlFor="ai-source-checkbox" className="cursor-pointer">
                                    Set as AI Source
                                </Label>
                            </div>

                            <Button
                                onClick={() => { }}
                                className="w-full"
                                disabled={isUploading}
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                Upload
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Document gallery/list */}
                <Card className="md:col-span-2">
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-lg">
                            Document Library
                        </CardTitle>

                        {selectedFiles.length > 0 && (
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-muted-foreground">
                                    {selectedFiles.length} selected
                                </span>
                                <BatchEmbeddingButton
                                    selectedFiles={selectedFiles}
                                    onComplete={() => {
                                        onRefresh()
                                        setSelectedFiles([])
                                    }}
                                />
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setSelectedFiles([])}
                                >
                                    Clear
                                </Button>
                            </div>
                        )}
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="h-[400px] flex items-center justify-center">
                                <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                            </div>
                        ) : documents.length === 0 ? (
                            <NoDocumentsPlaceholder />
                        ) : filteredDocuments().length === 0 ? (
                            <div className="h-[400px] flex items-center justify-center flex-col">
                                <Search className="h-12 w-12 text-muted-foreground mb-4" />
                                <p className="text-muted-foreground">No documents match your filter</p>
                            </div>
                        ) : viewMode === 'grid' ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {filteredDocuments().map(doc => (
                                    <div
                                        key={doc.id}
                                        className={`
                      group relative border rounded-lg overflow-hidden hover:shadow-md transition-all
                      ${selectedFiles.some(f => f.id === doc.id) ? 'ring-2 ring-primary' : ''}
                    `}
                                        onClick={() => {
                                            if (selectedFiles.some(f => f.id === doc.id)) {
                                                setSelectedFiles(selectedFiles.filter(f => f.id !== doc.id))
                                            } else {
                                                setSelectedFiles([...selectedFiles, doc])
                                            }
                                        }}
                                    >
                                        <div className="aspect-square bg-muted/30 flex items-center justify-center">
                                            <FileText className="h-16 w-16 text-muted-foreground" />
                                        </div>
                                        <div className="p-3">
                                            <div className="flex items-center justify-between mb-1">
                                                <h3 className="font-medium text-sm truncate">{doc.file_name}</h3>
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-2">
                                                {new Date(doc.created_at).toLocaleDateString()}
                                            </div>
                                            <div className="flex flex-wrap gap-2">
                                                <Badge variant="outline" className="text-xs">
                                                    {doc.file_type}
                                                </Badge>
                                                {doc.category && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        {doc.category}
                                                    </Badge>
                                                )}
                                            </div>
                                            <div className="mt-2">
                                                <EmbeddingStatus
                                                    hasEmbeddings={!!doc.has_embeddings}
                                                    embeddingsCount={doc.embeddings_count}
                                                    embeddingsProvider={doc.embeddings_provider}
                                                    embeddingsModel={doc.embeddings_model}
                                                    size="sm"
                                                />
                                            </div>
                                        </div>
                                        <div className="absolute top-2 right-2 flex gap-1">
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            size="icon"
                                                            variant="ghost"
                                                            className="h-7 w-7 opacity-0 group-hover:opacity-100 bg-background/80"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleToggleActive(doc.id, !doc.is_active_source);
                                                            }}
                                                        >
                                                            <Database className={`h-4 w-4 ${doc.is_active_source ? 'text-primary' : 'text-muted-foreground'}`} />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        {doc.is_active_source ? 'Active AI Source' : 'Set as AI Source'}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7 opacity-0 group-hover:opacity-100 bg-background/80"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setSelectedDocument(doc);
                                                    setPreviewOpen(true);
                                                }}
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7 opacity-0 group-hover:opacity-100 bg-background/80"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteDocument(doc.id);
                                                }}
                                            >
                                                <Trash2 className="h-4 w-4 text-destructive" />
                                            </Button>
                                        </div>

                                        <div className="absolute top-3 left-3">
                                            <Checkbox
                                                checked={selectedFiles.some(f => f.id === doc.id)}
                                                className="h-5 w-5 bg-background/80"
                                                onClick={(e) => e.stopPropagation()}
                                                onCheckedChange={(checked) => {
                                                    if (checked) {
                                                        setSelectedFiles([...selectedFiles, doc])
                                                    } else {
                                                        setSelectedFiles(selectedFiles.filter(f => f.id !== doc.id))
                                                    }
                                                }}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="divide-y border rounded-md">
                                {filteredDocuments().map(doc => (
                                    <div
                                        key={doc.id}
                                        className={`
                      flex items-center justify-between p-3 hover:bg-muted/50 transition cursor-pointer
                      ${selectedFiles.some(f => f.id === doc.id) ? 'bg-primary/10 border-l-4 border-primary' : ''}
                    `}
                                    >
                                        <div className="flex items-center gap-3">
                                            <Checkbox
                                                checked={selectedFiles.some(f => f.id === doc.id)}
                                                className="mr-2"
                                                onCheckedChange={(checked) => {
                                                    if (checked) {
                                                        setSelectedFiles([...selectedFiles, doc])
                                                    } else {
                                                        setSelectedFiles(selectedFiles.filter(f => f.id !== doc.id))
                                                    }
                                                }}
                                            />
                                            <FileText className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium text-sm truncate max-w-[200px]">{doc.file_name}</span>
                                            <Badge variant="outline" className="text-xs">{doc.file_type}</Badge>
                                            {doc.category && (
                                                <Badge variant="secondary" className="text-xs">{doc.category}</Badge>
                                            )}
                                        </div>

                                        <div className="flex items-center gap-3">
                                            <span className="text-xs text-muted-foreground">
                                                {new Date(doc.created_at).toLocaleDateString()}
                                            </span>

                                            <EmbeddingStatus
                                                hasEmbeddings={!!doc.has_embeddings}
                                                embeddingsCount={doc.embeddings_count}
                                                embeddingsProvider={doc.embeddings_provider}
                                                embeddingsModel={doc.embeddings_model}
                                                size="sm"
                                            />

                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            size="icon"
                                                            variant="ghost"
                                                            className="h-7 w-7"
                                                            onClick={() => handleToggleActive(doc.id, !doc.is_active_source)}
                                                        >
                                                            <Database className={`h-4 w-4 ${doc.is_active_source ? 'text-primary' : 'text-muted-foreground'}`} />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        {doc.is_active_source ? 'Active AI Source' : 'Set as AI Source'}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7"
                                                onClick={() => {
                                                    setSelectedDocument(doc)
                                                    setPreviewOpen(true)
                                                }}
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7"
                                                onClick={() => handleDeleteDocument(doc.id)}
                                            >
                                                <Trash2 className="h-4 w-4 text-destructive" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Document preview would go here - could be implemented as a modal */}
            {selectedDocument && previewOpen && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    {/* Preview modal content */}
                </div>
            )}

            {/* Pagination controls */}
            {pagination && pagination.last_page > 1 && (
                <div className="flex justify-between items-center mt-6 px-2">
                    <div className="text-sm text-muted-foreground">
                        Showing {pagination.from || 0} to {pagination.to || 0} of {pagination.total} documents
                    </div>
                    <div className="flex space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.current_page - 1)}
                            disabled={pagination.current_page <= 1}
                        >
                            Previous
                        </Button>
                        <div className="flex items-center space-x-1">
                            {Array.from({ length: Math.min(5, pagination.last_page) }, (_, i) => {
                                // Show current page and neighbors
                                let pageNum;
                                if (pagination.last_page <= 5) {
                                    // Show all pages if 5 or fewer
                                    pageNum = i + 1;
                                } else if (pagination.current_page <= 3) {
                                    // Near the start
                                    pageNum = i + 1;
                                } else if (pagination.current_page >= pagination.last_page - 2) {
                                    // Near the end
                                    pageNum = pagination.last_page - 4 + i;
                                } else {
                                    // In the middle
                                    pageNum = pagination.current_page - 2 + i;
                                }

                                return (
                                    <Button
                                        key={pageNum}
                                        variant={pagination.current_page === pageNum ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => handlePageChange(pageNum)}
                                        className="w-9"
                                    >
                                        {pageNum}
                                    </Button>
                                );
                            })}
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.current_page + 1)}
                            disabled={pagination.current_page >= pagination.last_page}
                        >
                            Next
                        </Button>
                    </div>
                </div>
            )}
        </div>
    )
}