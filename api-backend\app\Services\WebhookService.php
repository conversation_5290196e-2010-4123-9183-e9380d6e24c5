<?php

namespace App\Services;

use App\Models\Widget;
use App\Models\WidgetWebhook;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class WebhookService
{
    /**
     * The supported integration types
     */
    protected const INTEGRATION_TYPES = [
        'slack',
        'ms-teams',
        'discord',
        'zapier',
        'generic',
    ];

    /**
     * Cache time for webhook validation (in seconds)
     */
    protected const WEBHOOK_VALIDATION_CACHE_TTL = 3600; // 1 hour

    /**
     * Send a notification to all configured integrations for a widget
     *
     * @param Widget $widget The widget that triggered the event
     * @param string $eventType Type of event (message.new, rating.submit, etc.)
     * @param array $payload Event data
     * @return array Results of all webhook attempts
     */
    public function notifyAllIntegrations(Widget $widget, string $eventType, array $payload): array
    {
        $results = [];
        $integrations = $this->getWidgetIntegrations($widget);

        foreach ($integrations as $integration) {
            try {
                $result = $this->sendNotification($integration, $eventType, $payload);
                $results[$integration['id']] = $result;
            } catch (\Exception $e) {
                Log::error("Failed to send integration notification: " . $e->getMessage(), [
                    'integration' => $integration['type'],
                    'event' => $eventType,
                    'widget_id' => $widget->id,
                    'error' => $e->getMessage()
                ]);

                $results[$integration['id']] = [
                    'success' => false,
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Parse and get all integrations configured for a widget
     *
     * @param Widget $widget The widget
     * @return array Array of parsed integration configurations
     */
    public function getWidgetIntegrations(Widget $widget): array
    {
        // If we have a legacy webhookUrl, convert it to the new format
        if (isset($widget->settings['advanced']['webhookUrl']) &&
            !empty($widget->settings['advanced']['webhookUrl']) &&
            !isset($widget->settings['integrations'])) {

            // Convert legacy webhook URL to generic integration
            return [
                [
                    'id' => 'legacy',
                    'type' => 'generic',
                    'url' => $widget->settings['advanced']['webhookUrl'],
                    'active' => true,
                    'events' => ['message.new', 'rating.submit', 'session.start', 'session.end'],
                    'created_at' => now()->toIso8601String()
                ]
            ];
        }

        // Return properly configured integrations array
        return $widget->settings['integrations'] ?? [];
    }

    /**
     * Send a notification to a specific integration
     *
     * @param array $integration Integration configuration
     * @param string $eventType Type of event
     * @param array $payload Event data
     * @return array Result of the webhook attempt
     */
    public function sendNotification(array $integration, string $eventType, array $payload): array
    {
        if (!$integration['active']) {
            return [
                'success' => false,
                'message' => 'Integration is not active'
            ];
        }

        if (!in_array($eventType, $integration['events'])) {
            return [
                'success' => false,
                'message' => 'Event type not configured for this integration'
            ];
        }

        try {
            $response = $this->sendWebhook($integration, array_merge($payload, [
                'event_type' => $eventType,
                'timestamp' => now()->toIso8601String()
            ]));

            return [
                'success' => $response->successful(),
                'message' => $response->successful()
                    ? 'Notification sent successfully'
                    : 'Failed to send notification: ' . $response->body(),
                'status_code' => $response->status()
            ];
        } catch (\Exception $e) {
            Log::error('Webhook notification failed', [
                'integration' => $integration,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send a webhook request
     *
     * @param array $integration
     * @param array $payload
     * @return \Illuminate\Http\Client\Response
     */
    protected function sendWebhook(array $integration, array $payload): \Illuminate\Http\Client\Response
    {
        $headers = [
            'Content-Type' => 'application/json',
            'User-Agent' => 'ChatWidget/1.0',
            'X-Integration-Type' => $integration['type'],
            'X-Integration-ID' => $integration['id']
        ];

        // Add signature if secret is provided
        if (!empty($integration['secret'])) {
            $signature = hash_hmac('sha256', json_encode($payload), $integration['secret']);
            $headers['X-Webhook-Signature'] = $signature;
        }

        return Http::withHeaders($headers)
            ->timeout(10)
            ->post($integration['url'], $payload);
    }

    /**
     * Test an integration configuration
     *
     * @param array $integration
     * @param array $payload
     * @return array
     */
    public function testIntegration(array $integration, array $payload): array
    {
        try {
            $response = $this->sendWebhook($integration, $payload);

            return [
                'success' => $response->successful(),
                'message' => $response->successful()
                    ? 'Integration test successful'
                    : 'Integration test failed: ' . $response->body(),
                'status_code' => $response->status(),
                'integration_type' => $integration['type'],
                'test_payload' => $payload
            ];
        } catch (\Exception $e) {
            Log::error('Webhook test failed', [
                'integration' => $integration,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to test integration: ' . $e->getMessage(),
                'integration_type' => $integration['type']
            ];
        }
    }

    /**
     * Get the content type header used for an integration type
     *
     * @param string $integrationType
     * @return string
     */
    protected function getContentTypeForIntegrationType(string $integrationType): string
    {
        switch ($integrationType) {
            case 'slack':
            case 'ms-teams':
            case 'discord':
            case 'zapier':
            case 'generic':
            default:
                return 'application/json';
        }
    }

    /**
     * Generate a secure signature for webhook payload
     */
    protected function generateWebhookSignature(array $payload, string $secret): string
    {
        $payloadJson = json_encode($payload);
        return hash_hmac('sha256', $payloadJson, $secret);
    }

    /**
     * Format a payload for Slack
     */
    protected function formatSlackPayload(string $eventType, array $payload): array
    {
        $blocks = [];
        $text = '';

        switch ($eventType) {
            case 'message.new':
                $text = 'New message received in chat widget';
                $blocks = [
                    [
                        'type' => 'header',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'New Chat Message'
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => '*From:*\n' . ($payload['user_name'] ?? 'Guest User')
                            ],
                            [
                                'type' => 'mrkdwn',
                                'text' => '*Time:*\n' . now()->format('Y-m-d H:i:s')
                            ]
                        ]
                    ],
                    [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => '*Message:*\n' . ($payload['message'] ?? 'No message content')
                        ]
                    ]
                ];
                break;

            case 'rating.submit':
                $rating = $payload['rating'] ?? 'unknown';
                $ratingText = $rating === 'thumbsUp' ? '👍 Positive' : '👎 Negative';

                $text = 'New message rating received';
                $blocks = [
                    [
                        'type' => 'header',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'New Message Rating'
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => '*Rating:*\n' . $ratingText
                            ],
                            [
                                'type' => 'mrkdwn',
                                'text' => '*Time:*\n' . now()->format('Y-m-d H:i:s')
                            ]
                        ]
                    ]
                ];

                // Add feedback if available
                if (isset($payload['feedback']) && !empty($payload['feedback'])) {
                    $blocks[] = [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => '*Feedback:*\n' . $payload['feedback']
                        ]
                    ];
                }
                break;

            default:
                $text = 'Notification from chat widget: ' . $eventType;
                $blocks = [
                    [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => 'Event: ' . $eventType
                        ]
                    ],
                    [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => '```' . json_encode($payload, JSON_PRETTY_PRINT) . '```'
                        ]
                    ]
                ];
        }

        return [
            'text' => $text, // Fallback text
            'blocks' => $blocks
        ];
    }

    /**
     * Format a payload for Microsoft Teams
     */
    protected function formatTeamsPayload(string $eventType, array $payload): array
    {
        $title = '';
        $facts = [];
        $text = '';

        switch ($eventType) {
            case 'message.new':
                $title = 'New Chat Message';
                $facts = [
                    ['name' => 'From', 'value' => $payload['user_name'] ?? 'Guest User'],
                    ['name' => 'Time', 'value' => now()->format('Y-m-d H:i:s')]
                ];
                $text = $payload['message'] ?? 'No message content';
                break;

            case 'rating.submit':
                $rating = $payload['rating'] ?? 'unknown';
                $ratingText = $rating === 'thumbsUp' ? '👍 Positive' : '👎 Negative';

                $title = 'New Message Rating';
                $facts = [
                    ['name' => 'Rating', 'value' => $ratingText],
                    ['name' => 'Time', 'value' => now()->format('Y-m-d H:i:s')]
                ];

                if (isset($payload['feedback']) && !empty($payload['feedback'])) {
                    $text = "Feedback: " . $payload['feedback'];
                }
                break;

            default:
                $title = 'Chat Widget Notification: ' . $eventType;
                $text = '```' . json_encode($payload, JSON_PRETTY_PRINT) . '```';
        }

        return [
            '@type' => 'MessageCard',
            '@context' => 'http://schema.org/extensions',
            'themeColor' => '0076D7',
            'summary' => $title,
            'sections' => [
                [
                    'activityTitle' => $title,
                    'facts' => $facts,
                    'text' => $text
                ]
            ]
        ];
    }

    /**
     * Format a payload for Discord
     */
    protected function formatDiscordPayload(string $eventType, array $payload): array
    {
        $title = '';
        $description = '';
        $fields = [];
        $color = 3447003; // Blue

        switch ($eventType) {
            case 'message.new':
                $title = 'New Chat Message';
                $description = $payload['message'] ?? 'No message content';
                $fields = [
                    ['name' => 'From', 'value' => $payload['user_name'] ?? 'Guest User', 'inline' => true],
                    ['name' => 'Time', 'value' => now()->format('Y-m-d H:i:s'), 'inline' => true]
                ];
                break;

            case 'rating.submit':
                $rating = $payload['rating'] ?? 'unknown';
                $ratingText = $rating === 'thumbsUp' ? '👍 Positive' : '👎 Negative';
                $color = $rating === 'thumbsUp' ? 5763719 : 15548997; // Green or Red

                $title = 'New Message Rating';
                $fields = [
                    ['name' => 'Rating', 'value' => $ratingText, 'inline' => true],
                    ['name' => 'Time', 'value' => now()->format('Y-m-d H:i:s'), 'inline' => true]
                ];

                if (isset($payload['feedback']) && !empty($payload['feedback'])) {
                    $description = "Feedback: " . $payload['feedback'];
                }
                break;

            default:
                $title = 'Chat Widget Notification: ' . $eventType;
                $description = '```' . json_encode($payload, JSON_PRETTY_PRINT) . '```';
        }

        return [
            'embeds' => [
                [
                    'title' => $title,
                    'description' => $description,
                    'color' => $color,
                    'fields' => $fields,
                    'timestamp' => now()->toIso8601String()
                ]
            ]
        ];
    }

    // ========================================
    // NEW NORMALIZED WEBHOOK METHODS
    // ========================================

    /**
     * Get all webhooks for a widget.
     */
    public function getWebhooks(int $widgetId): \Illuminate\Database\Eloquent\Collection
    {
        return WidgetWebhook::where('widget_id', $widgetId)->get();
    }

    /**
     * Get active webhooks for a widget.
     */
    public function getActiveWebhooks(int $widgetId): \Illuminate\Database\Eloquent\Collection
    {
        return WidgetWebhook::where('widget_id', $widgetId)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Create or update a webhook.
     */
    public function createOrUpdateWebhook(int $widgetId, array $webhookData): WidgetWebhook
    {
        // If updating existing webhook by URL
        $webhook = WidgetWebhook::where('widget_id', $widgetId)
            ->where('url', $webhookData['url'])
            ->first();

        if ($webhook) {
            $webhook->update($webhookData);
            return $webhook;
        }

        // Create new webhook
        $webhookData['widget_id'] = $widgetId;
        return WidgetWebhook::create($webhookData);
    }

    /**
     * Delete a webhook.
     */
    public function deleteWebhook(int $webhookId): bool
    {
        return WidgetWebhook::destroy($webhookId) > 0;
    }

    /**
     * Trigger webhooks for a specific event using new normalized structure.
     */
    public function triggerNormalizedWebhooks(int $widgetId, string $event, array $payload): array
    {
        $webhooks = $this->getActiveWebhooks($widgetId);
        $results = [];

        foreach ($webhooks as $webhook) {
            if ($webhook->shouldTriggerFor($event)) {
                $result = $this->triggerNormalizedWebhook($webhook, $event, $payload);
                $results[] = [
                    'webhook_id' => $webhook->id,
                    'success' => $result['success'],
                    'response_code' => $result['response_code'] ?? null,
                    'error' => $result['error'] ?? null,
                ];
            }
        }

        return $results;
    }

    /**
     * Trigger a single webhook using normalized structure.
     */
    public function triggerNormalizedWebhook(WidgetWebhook $webhook, string $event, array $payload): array
    {
        try {
            // Prepare payload
            $webhookPayload = [
                'event' => $event,
                'widget_id' => $webhook->widget_id,
                'timestamp' => now()->toISOString(),
                'data' => $payload,
            ];

            // Add signature for security
            if ($webhook->secret_key) {
                $webhookPayload['signature'] = $this->generateNormalizedSignature($webhookPayload, $webhook->secret_key);
            }

            // Prepare headers
            $headers = array_merge([
                'Content-Type' => 'application/json',
                'User-Agent' => 'Widget-Webhook/1.0',
            ], $webhook->headers ?? []);

            // Make HTTP request with retry logic
            $response = $this->makeNormalizedHttpRequest($webhook, $webhookPayload, $headers);

            if ($response->successful()) {
                $webhook->recordSuccess();
                return [
                    'success' => true,
                    'response_code' => $response->status(),
                    'response_body' => $response->body(),
                ];
            } else {
                $error = "HTTP {$response->status()}: {$response->body()}";
                $webhook->recordFailure($error);
                return [
                    'success' => false,
                    'response_code' => $response->status(),
                    'error' => $error,
                ];
            }
        } catch (\Exception $e) {
            $error = "Exception: {$e->getMessage()}";
            $webhook->recordFailure($error);
            Log::error("Webhook trigger failed", [
                'webhook_id' => $webhook->id,
                'event' => $event,
                'error' => $error,
            ]);

            return [
                'success' => false,
                'error' => $error,
            ];
        }
    }

    /**
     * Make HTTP request with retry logic for normalized webhooks.
     */
    protected function makeNormalizedHttpRequest(WidgetWebhook $webhook, array $payload, array $headers): Response
    {
        $attempts = 0;
        $maxAttempts = $webhook->retry_attempts;

        while ($attempts < $maxAttempts) {
            try {
                $http = Http::timeout($webhook->timeout_seconds)
                    ->withHeaders($headers);

                if (!$webhook->verify_ssl) {
                    $http = $http->withoutVerifying();
                }

                $response = $http->{strtolower($webhook->method)}($webhook->url, $payload);

                // If successful or client error (4xx), don't retry
                if ($response->successful() || $response->clientError()) {
                    return $response;
                }

                // Server error (5xx), retry
                $attempts++;
                if ($attempts < $maxAttempts) {
                    sleep(pow(2, $attempts)); // Exponential backoff
                }
            } catch (\Exception $e) {
                $attempts++;
                if ($attempts >= $maxAttempts) {
                    throw $e;
                }
                sleep(pow(2, $attempts)); // Exponential backoff
            }
        }

        // This should not be reached, but just in case
        throw new \Exception("Max retry attempts reached");
    }

    /**
     * Generate webhook signature for security (normalized version).
     */
    protected function generateNormalizedSignature(array $payload, string $secret): string
    {
        $jsonPayload = json_encode($payload, JSON_UNESCAPED_SLASHES);
        return hash_hmac('sha256', $jsonPayload, $secret);
    }

    /**
     * Test a webhook configuration (normalized version).
     */
    public function testNormalizedWebhook(WidgetWebhook $webhook): array
    {
        $testPayload = [
            'test' => true,
            'message' => 'This is a test webhook from your widget configuration.',
        ];

        return $this->triggerNormalizedWebhook($webhook, 'test', $testPayload);
    }

    /**
     * Get webhook statistics (normalized version).
     */
    public function getNormalizedWebhookStats(int $widgetId): array
    {
        $webhooks = $this->getWebhooks($widgetId);

        $stats = [
            'total_webhooks' => $webhooks->count(),
            'active_webhooks' => $webhooks->where('is_active', true)->count(),
            'total_triggers' => 0,
            'successful_triggers' => 0,
            'failed_triggers' => 0,
            'last_triggered' => null,
        ];

        foreach ($webhooks as $webhook) {
            $stats['total_triggers'] += $webhook->success_count + $webhook->failure_count;
            $stats['successful_triggers'] += $webhook->success_count;
            $stats['failed_triggers'] += $webhook->failure_count;

            if ($webhook->last_triggered_at &&
                (!$stats['last_triggered'] || $webhook->last_triggered_at > $stats['last_triggered'])) {
                $stats['last_triggered'] = $webhook->last_triggered_at;
            }
        }

        return $stats;
    }
}
