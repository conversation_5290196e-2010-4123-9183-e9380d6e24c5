<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class FixCorruptedApiKeys extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the ai_models table exists
        if (Schema::hasTable('ai_models')) {
            // Get all models with potentially corrupted API keys
            $models = DB::table('ai_models')->get();
            
            foreach ($models as $model) {
                try {
                    // Try to decrypt the API key
                    if (!empty($model->api_key)) {
                        \Illuminate\Support\Facades\Crypt::decryptString($model->api_key);
                    }
                } catch (\Exception $e) {
                    // If decryption fails, set the API key to null
                    DB::table('ai_models')
                        ->where('id', $model->id)
                        ->update(['api_key' => null]);
                    
                    \Illuminate\Support\Facades\Log::info("Fixed corrupted API key for model ID: {$model->id}");
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration cannot be reversed
    }
}
