import React, { useState, useEffect, ReactNode } from 'react';
import { AlertTriangle, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ApiErrorBus, ApiError } from '@/utils/api-error-handler';
import { Button } from '@/components/ui/button';

interface PermissionErrorBoundaryProps {
    children: ReactNode;
    feature?: string;
    paths?: string[];
    // Component to show when error occurs, defaults to Alert
    fallback?: React.ComponentType<{ error: ApiError | null, onDismiss: () => void }>;
    // Whether to automatically dismiss the error after a timeout
    autoDismiss?: boolean;
    // Timeout in ms before auto-dismissing
    dismissTimeout?: number;
}

/**
 * A component that catches and displays permission errors for specific API paths
 */
const PermissionErrorBoundary: React.FC<PermissionErrorBoundaryProps> = ({
    children,
    feature = 'feature',
    paths = ['*'],
    fallback: ErrorFallback,
    autoDismiss = false,
    dismissTimeout = 5000,
}) => {
    const [error, setError] = useState<ApiError | null>(null);
    const [dismissed, setDismissed] = useState<boolean>(false);

    useEffect(() => {
        const unsubscribers = paths.map(path =>
            ApiErrorBus.subscribe(path, (apiError: ApiError) => {
                if (apiError.code === 403) {
                    setError(apiError);
                    setDismissed(false);

                    // Auto-dismiss if enabled
                    if (autoDismiss) {
                        setTimeout(() => {
                            setDismissed(true);
                        }, dismissTimeout);
                    }
                }
            })
        );

        // Cleanup all subscriptions
        return () => {
            unsubscribers.forEach(unsubscribe => unsubscribe());
        };
    }, [paths, autoDismiss, dismissTimeout]);

    // Handle error dismissal
    const handleDismiss = () => {
        setDismissed(true);
    };

    // Return children when no error or error is dismissed
    if (!error || dismissed) {
        return <>{children}</>;
    }

    // Show custom fallback if provided
    if (ErrorFallback) {
        return <ErrorFallback error={error} onDismiss={handleDismiss} />;
    }

    // Default error UI
    return (
        <div className="space-y-4">
            <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4 mr-2" />
                <AlertTitle>Permission Error</AlertTitle>
                <AlertDescription className="flex flex-col gap-1">
                    <p>You don't have permission to access this {feature}.</p>
                    <p className="text-sm mt-1">
                        The system returned: {error.message}
                    </p>
                    <div className="flex justify-end mt-2">
                        <Button variant="outline" size="sm" onClick={handleDismiss}>
                            Dismiss
                        </Button>
                    </div>
                </AlertDescription>
            </Alert>

            {/* Still render children but they may not function properly */}
            {children}
        </div>
    );
};

export default PermissionErrorBoundary; 