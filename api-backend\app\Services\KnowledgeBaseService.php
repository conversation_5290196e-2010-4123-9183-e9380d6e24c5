<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\KnowledgeSource;
use App\Models\KnowledgeDocument;
use App\Models\KnowledgeScrapedUrl;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use DOMDocument;
use Exception;

class KnowledgeBaseService
{
    /**
     * Get all knowledge sources
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllSources()
    {
        return KnowledgeSource::withCount('documents')->orderBy('priority')->get();
    }

    /**
     * Create a new knowledge source
     *
     * @param array $data
     * @param int|null $userId
     * @return KnowledgeSource
     */
    public function createSource(array $data, ?int $userId = null): KnowledgeSource
    {
        $data['created_by'] = $userId;
        return KnowledgeSource::create($data);
    }

    /**
     * Get a single knowledge source with its documents
     *
     * @param int $id
     * @return KnowledgeSource
     */
    public function getSource(int $id): KnowledgeSource
    {
        return KnowledgeSource::with('documents')->findOrFail($id);
    }

    /**
     * Update a knowledge source
     *
     * @param int $id
     * @param array $data
     * @return KnowledgeSource
     */
    public function updateSource(int $id, array $data): KnowledgeSource
    {
        $source = KnowledgeSource::findOrFail($id);
        $source->update($data);
        return $source;
    }

    /**
     * Delete a knowledge source
     *
     * @param int $id
     * @return bool
     */
    public function deleteSource(int $id): bool
    {
        $source = KnowledgeSource::findOrFail($id);
        return $source->delete();
    }

    /**
     * Upload a document to a source
     *
     * @param int $sourceId
     * @param UploadedFile $file
     * @param array $data
     * @param int|null $userId
     * @return KnowledgeDocument
     */
    public function uploadDocument(int $sourceId, UploadedFile $file, array $data, ?int $userId = null): KnowledgeDocument
    {
        $source = KnowledgeSource::findOrFail($sourceId);
        $path = $file->store('knowledge_documents');

        // Extract text content based on file type
        $extractedText = $this->extractTextFromFile($file);

        return KnowledgeDocument::create([
            'source_id' => $source->id,
            'file_path' => $path,
            'file_name' => $file->getClientOriginalName(),
            'file_type' => $file->getClientMimeType(),
            'size' => $file->getSize(),
            'status' => 'active',
            'is_active_source' => $data['is_active_source'] ?? false,
            'category' => $data['category'] ?? null,
            'project_id' => $data['project_id'] ?? null,
            'extracted_text' => $extractedText,
            'created_by' => $userId,
        ]);
    }

    /**
     * List documents for a source
     *
     * @param int $sourceId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDocumentsBySource(int $sourceId)
    {
        $source = KnowledgeSource::findOrFail($sourceId);
        return $source->documents()->get();
    }

    /**
     * Get all documents, optionally filtered by project
     *
     * @param int|null $projectId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllDocuments(?int $projectId = null)
    {
        $query = KnowledgeDocument::query();

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Delete a document
     *
     * @param int $docId
     * @return bool
     */
    public function deleteDocument(int $docId): bool
    {
        $doc = KnowledgeDocument::findOrFail($docId);
        Storage::delete($doc->file_path);
        return $doc->delete();
    }

    /**
     * Toggle a document as an AI source
     *
     * @param int $docId
     * @param bool $isActiveSource
     * @return KnowledgeDocument
     */
    public function toggleDocumentAsAISource(int $docId, bool $isActiveSource): KnowledgeDocument
    {
        $doc = KnowledgeDocument::findOrFail($docId);
        $doc->update(['is_active_source' => $isActiveSource]);
        return $doc;
    }

    /**
     * Scrape content from a URL
     *
     * @param string $url
     * @param array $data
     * @param int|null $userId
     * @return array
     */
    public function scrapeUrl(string $url, array $data, ?int $userId = null): array
    {
        // Fetch the URL content
        $response = \Illuminate\Support\Facades\Http::timeout(30)->get($url);

        if (!$response->successful()) {
            throw new Exception('Failed to fetch URL: ' . $response->status());
        }

        $html = $response->body();

        // Parse the HTML content
        $dom = new DOMDocument();
        @$dom->loadHTML($html);

        // Extract title
        $title = '';
        $titleTags = $dom->getElementsByTagName('title');
        if ($titleTags->length > 0) {
            $title = $titleTags->item(0)->textContent;
        }

        // Extract text content
        $textContent = $this->extractTextFromHtml($html);

        // Extract tables
        $tableContent = $this->extractTablesFromHtml($html);

        // Create JSON representation
        $jsonContent = [
            'title' => $title,
            'url' => $url,
            'content' => Str::limit($textContent, 1000),
            'tables' => count($tableContent) > 0,
            'scraped_at' => now()->toIso8601String(),
        ];

        // Store in database if source_id is provided
        $scrapedUrl = null;
        if (isset($data['source_id'])) {
            $source = KnowledgeSource::findOrFail($data['source_id']);

            $scrapedUrl = KnowledgeScrapedUrl::create([
                'source_id' => $source->id,
                'url' => $url,
                'title' => $title,
                'raw_content' => $html,
                'text_content' => $textContent,
                'table_content' => json_encode($tableContent),
                'json_content' => $jsonContent,
                'status' => 'active',
                'project_id' => $data['project_id'] ?? null,
                'table_name' => $data['table_name'] ?? null,
                'created_by' => $userId,
            ]);
        }

        return [
            'url' => $url,
            'title' => $title,
            'raw' => Str::limit($html, 10000),
            'text' => $textContent,
            'table' => $tableContent,
            'json' => $jsonContent,
            'stored' => $scrapedUrl ? true : false,
            'scraped_url_id' => $scrapedUrl ? $scrapedUrl->id : null,
        ];
    }

    /**
     * Save scraped content to database
     *
     * @param array $data
     * @param int|null $userId
     * @return KnowledgeScrapedUrl
     */
    public function saveScrape(array $data, ?int $userId = null): KnowledgeScrapedUrl
    {
        // Create a knowledge source if not provided
        $sourceId = $data['source_id'] ?? null;
        if (!$sourceId) {
            $source = KnowledgeSource::create([
                'type' => 'website',
                'name' => $data['title'] ?? ('Scraped URL: ' . $data['url']),
                'description' => 'Automatically created from URL scrape',
                'status' => 'active',
                'created_by' => $userId,
            ]);
            $sourceId = $source->id;
        }

        // Store the scraped content
        return KnowledgeScrapedUrl::create([
            'source_id' => $sourceId,
            'url' => $data['url'],
            'title' => $data['title'] ?? null,
            'text_content' => $data['format'] === 'text' ? $data['content'] : null,
            'raw_content' => $data['format'] === 'raw' ? $data['content'] : null,
            'table_content' => $data['format'] === 'table' ? $data['content'] : null,
            'json_content' => $data['format'] === 'json' ? json_decode($data['content'], true) : null,
            'status' => 'active',
            'project_id' => $data['project_id'],
            'table_name' => $data['table_name'],
            'created_by' => $userId,
        ]);
    }

    /**
     * List all scraped URLs
     *
     * @param array $filters
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function listScrapedUrls(array $filters = [])
    {
        $query = KnowledgeScrapedUrl::query();

        if (isset($filters['source_id'])) {
            $query->where('source_id', $filters['source_id']);
        }

        if (isset($filters['project_id'])) {
            $query->where('project_id', $filters['project_id']);
        }

        return $query->orderBy('created_at', 'desc')->paginate(20);
    }

    /**
     * Delete a scraped URL
     *
     * @param int $id
     * @return bool
     */
    public function deleteScrapedUrl(int $id): bool
    {
        $scrapedUrl = KnowledgeScrapedUrl::findOrFail($id);
        return $scrapedUrl->delete();
    }

    /**
     * Extract text from a file based on its type
     *
     * @param UploadedFile $file
     * @return string|null
     */
    private function extractTextFromFile(UploadedFile $file): ?string
    {
        try {
            $mimeType = $file->getMimeType();
            $filePath = $file->getRealPath();
            $extension = strtolower($file->getClientOriginalExtension());

            // Plain text files
            if (in_array($mimeType, ['text/plain', 'text/csv', 'application/json'])) {
                return file_get_contents($filePath);
            }

            // PDF files
            if ($mimeType === 'application/pdf' || $extension === 'pdf') {
                // Using Spatie/PdfToText
                if (class_exists('\Spatie\PdfToText\Pdf')) {
                    return \Spatie\PdfToText\Pdf::getText($filePath);
                }

                // Fallback to pdftotext command if available
                if (exec('which pdftotext')) {
                    $tempOutput = tempnam(sys_get_temp_dir(), 'pdf_');
                    exec("pdftotext -layout '{$filePath}' '{$tempOutput}'");
                    $text = file_get_contents($tempOutput);
                    @unlink($tempOutput);
                    return $text;
                }
            }

            // Word documents
            if (in_array($mimeType, [
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/msword'
            ]) || in_array($extension, ['docx', 'doc'])) {
                // Using PhpOffice/PhpWord
                if (class_exists('\PhpOffice\PhpWord\IOFactory')) {
                    $phpWord = \PhpOffice\PhpWord\IOFactory::load($filePath);
                    $text = '';

                    foreach ($phpWord->getSections() as $section) {
                        foreach ($section->getElements() as $element) {
                            if (method_exists($element, 'getText')) {
                                $text .= $element->getText() . "\n";
                            } elseif (method_exists($element, 'getElements')) {
                                foreach ($element->getElements() as $childElement) {
                                    if (method_exists($childElement, 'getText')) {
                                        $text .= $childElement->getText() . "\n";
                                    }
                                }
                            }
                        }
                    }

                    return $text;
                }
            }

            // Excel files
            if (in_array($mimeType, [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ]) || in_array($extension, ['xlsx', 'xls'])) {
                // Using PhpOffice/PhpSpreadsheet
                if (class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
                    $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($filePath);
                    $text = '';

                    foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                        $text .= 'Sheet: ' . $worksheet->getTitle() . "\n\n";

                        foreach ($worksheet->getRowIterator() as $row) {
                            $rowText = '';
                            $cellIterator = $row->getCellIterator();
                            $cellIterator->setIterateOnlyExistingCells(false);

                            foreach ($cellIterator as $cell) {
                                $rowText .= $cell->getValue() . "\t";
                            }

                            $text .= trim($rowText) . "\n";
                        }

                        $text .= "\n\n";
                    }

                    return $text;
                }
            }

            // Extract metadata
            $metadata = $this->extractFileMetadata($file);

            // Log unsupported file type
            \Log::info('Unsupported file type for text extraction', [
                'mime_type' => $mimeType,
                'extension' => $extension,
                'file_name' => $file->getClientOriginalName(),
                'metadata' => $metadata,
            ]);

            return null;
        } catch (Exception $e) {
            \Log::error('Failed to extract text from file: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract text content from HTML
     *
     * @param string $html
     * @return string
     */
    private function extractTextFromHtml(string $html): string
    {
        // Remove script and style elements
        $html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $html);
        $html = preg_replace('/<style\b[^>]*>(.*?)<\/style>/is', '', $html);

        // Convert HTML to plain text
        $text = strip_tags($html);

        // Normalize whitespace
        $text = preg_replace('/\s+/', ' ', $text);

        // Trim and clean up
        $text = trim($text);

        return $text;
    }

    /**
     * Extract tables from HTML
     *
     * @param string $html
     * @return array
     */
    private function extractTablesFromHtml(string $html): array
    {
        $tables = [];
        $dom = new DOMDocument();
        @$dom->loadHTML($html);

        $tableTags = $dom->getElementsByTagName('table');

        foreach ($tableTags as $index => $tableTag) {
            $table = [];
            $rows = $tableTag->getElementsByTagName('tr');

            foreach ($rows as $rowIndex => $row) {
                $rowData = [];
                $cells = $row->getElementsByTagName('td');

                if ($cells->length === 0) {
                    $cells = $row->getElementsByTagName('th');
                }

                foreach ($cells as $cell) {
                    $rowData[] = trim($cell->textContent);
                }

                if (!empty($rowData)) {
                    $table[] = $rowData;
                }
            }

            if (!empty($table)) {
                $tables[] = $table;
            }

            // Limit to first 5 tables for performance
            if (count($tables) >= 5) {
                break;
            }
        }

        return $tables;
    }

    /**
     * Extract metadata from a file
     *
     * @param UploadedFile $file
     * @return array
     */
    private function extractFileMetadata(UploadedFile $file): array
    {
        $metadata = [
            'filename' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientOriginalExtension(),
        ];

        // Try to extract more metadata based on file type
        try {
            $mimeType = $file->getMimeType();
            $filePath = $file->getRealPath();

            // PDF metadata
            if ($mimeType === 'application/pdf' && class_exists('\Smalot\PdfParser\Parser')) {
                $parser = new \Smalot\PdfParser\Parser();
                $pdf = $parser->parseFile($filePath);
                $details = $pdf->getDetails();

                if (is_array($details)) {
                    $metadata['author'] = $details['Author'] ?? null;
                    $metadata['creator'] = $details['Creator'] ?? null;
                    $metadata['producer'] = $details['Producer'] ?? null;
                    $metadata['creation_date'] = $details['CreationDate'] ?? null;
                    $metadata['modification_date'] = $details['ModDate'] ?? null;
                    $metadata['pages'] = count($pdf->getPages());
                }
            }

            // Office document metadata
            if (in_array($mimeType, [
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ])) {
                $zip = new \ZipArchive();

                if ($zip->open($filePath) === true) {
                    if (($xml = $zip->getFromName('docProps/core.xml')) !== false) {
                        $dom = new \DOMDocument();
                        $dom->loadXML($xml);
                        $xpath = new \DOMXPath($dom);
                        $xpath->registerNamespace('dc', 'http://purl.org/dc/elements/1.1/');
                        $xpath->registerNamespace('cp', 'http://schemas.openxmlformats.org/package/2006/metadata/core-properties');
                        $xpath->registerNamespace('dcterms', 'http://purl.org/dc/terms/');

                        $metadata['title'] = $this->getXPathValue($xpath, '//dc:title');
                        $metadata['subject'] = $this->getXPathValue($xpath, '//dc:subject');
                        $metadata['creator'] = $this->getXPathValue($xpath, '//dc:creator');
                        $metadata['keywords'] = $this->getXPathValue($xpath, '//cp:keywords');
                        $metadata['description'] = $this->getXPathValue($xpath, '//dc:description');
                        $metadata['created'] = $this->getXPathValue($xpath, '//dcterms:created');
                        $metadata['modified'] = $this->getXPathValue($xpath, '//dcterms:modified');
                    }

                    $zip->close();
                }
            }
        } catch (Exception $e) {
            \Log::warning('Error extracting file metadata: ' . $e->getMessage());
        }

        return $metadata;
    }

    /**
     * Helper method to get XPath values
     *
     * @param \DOMXPath $xpath
     * @param string $query
     * @return string|null
     */
    private function getXPathValue(\DOMXPath $xpath, string $query): ?string
    {
        $nodes = $xpath->query($query);
        if ($nodes->length > 0) {
            return $nodes->item(0)->nodeValue;
        }
        return null;
    }
}
