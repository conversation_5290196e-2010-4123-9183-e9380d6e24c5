/**
 * Slider Field Component
 * 
 * A form field for range input using a slider.
 */

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Slider } from '@/components/ui/slider';

interface SliderFieldProps {
  label: string;
  fieldName: string;
  control: Control<any>;
  min?: number;
  max?: number;
  step?: number;
  description?: string;
  disabled?: boolean;
  showValue?: boolean;
  unit?: string;
}

/**
 * Slider Field Component
 * A form field for range input using a slider
 */
export function SliderField({
  label,
  fieldName,
  control,
  min = 0,
  max = 100,
  step = 1,
  description,
  disabled = false,
  showValue = true,
  unit = ''
}: SliderFieldProps) {
  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <div className="flex justify-between items-center">
            <FormLabel>{label}</FormLabel>
            {showValue && (
              <span className="text-sm text-muted-foreground">
                {field.value}{unit}
              </span>
            )}
          </div>
          <FormControl>
            <Slider
              min={min}
              max={max}
              step={step}
              value={[field.value]}
              onValueChange={(values) => field.onChange(values[0])}
              disabled={disabled}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
