import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Smartphone,
  Tablet,
  Monitor,
  Eye,
  Settings,
  Zap,
  CheckCircle2
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Import existing component for preview
import { DevicePreview, WidgetPreview } from '@/components/widget-builder';

interface MobileModalProps {
  form: UseFormReturn<any>;
  onClose: () => void;
  widgetId?: string;
}

const mobilePresets = [
  {
    id: 'auto',
    name: 'Auto-Optimize',
    description: 'Automatically adjust for all screen sizes',
    icon: Zap,
    settings: {
      responsiveSize: true,
      mobilePosition: 'bottom-right',
      tabletPosition: 'bottom-right',
      mobileScale: 0.9,
      hideOnSmallScreens: false,
    }
  },
  {
    id: 'mobile-first',
    name: 'Mobile-First',
    description: 'Optimized primarily for mobile devices',
    icon: Smartphone,
    settings: {
      responsiveSize: true,
      mobilePosition: 'bottom-center',
      tabletPosition: 'bottom-right',
      mobileScale: 1.0,
      hideOnSmallScreens: false,
    }
  },
  {
    id: 'desktop-focus',
    name: 'Desktop Focus',
    description: 'Optimized for desktop with mobile fallback',
    icon: Monitor,
    settings: {
      responsiveSize: false,
      mobilePosition: 'bottom-right',
      tabletPosition: 'bottom-right',
      mobileScale: 0.8,
      hideOnSmallScreens: true,
    }
  },
];

/**
 * Mobile Modal Component
 *
 * Provides a user-friendly interface for configuring mobile optimization
 * with live preview and preset configurations.
 */
const MobileModal = ({ form, onClose, widgetId }: MobileModalProps) => {
  const [selectedPreset, setSelectedPreset] = useState('auto');
  const [previewDevice, setPreviewDevice] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [mobileSettings, setMobileSettings] = useState({
    enabled: true,
    responsiveSize: true,
    mobilePosition: 'bottom-right',
    tabletPosition: 'bottom-right',
    mobileScale: [90],
    hideOnSmallScreens: false,
    touchOptimized: true,
    swipeGestures: true,
  });

  const handlePresetSelect = (presetId: string) => {
    setSelectedPreset(presetId);
    const preset = mobilePresets.find(p => p.id === presetId);
    if (preset) {
      setMobileSettings({
        ...mobileSettings,
        ...preset.settings,
        mobileScale: [preset.settings.mobileScale * 100],
      });
    }
    form.setValue('features.mobileOptimization', true);
  };

  const handleSaveAndClose = () => {
    form.setValue('features.mobileOptimization', mobileSettings.enabled);
    onClose();
  };

  const positionOptions = [
    { value: 'bottom-right', label: 'Bottom Right' },
    { value: 'bottom-left', label: 'Bottom Left' },
    { value: 'bottom-center', label: 'Bottom Center' },
    { value: 'top-right', label: 'Top Right' },
    { value: 'top-left', label: 'Top Left' },
  ];

  return (
    <div className="space-y-6">
      {/* Mobile Optimization Toggle */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Smartphone className="w-5 h-5" />
                <span>Mobile Optimization</span>
              </CardTitle>
              <CardDescription>
                Optimize your widget for mobile and tablet devices
              </CardDescription>
            </div>
            <Switch
              checked={mobileSettings.enabled}
              onCheckedChange={(checked) => {
                setMobileSettings({ ...mobileSettings, enabled: checked });
                form.setValue('features.mobileOptimization', checked);
              }}
            />
          </div>
        </CardHeader>

        {mobileSettings.enabled && (
          <CardContent>
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription className="text-sm">
                <strong>Why mobile optimization matters:</strong><br />
                • 60%+ of web traffic comes from mobile devices<br />
                • Mobile users expect fast, touch-friendly interfaces<br />
                • Proper mobile UX increases engagement by 40%
              </AlertDescription>
            </Alert>
          </CardContent>
        )}
      </Card>

      {mobileSettings.enabled && (
        <>
          {/* Quick Presets */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Presets</CardTitle>
              <CardDescription>
                Choose a preset that matches your audience and use case
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {mobilePresets.map((preset) => {
                  const Icon = preset.icon;
                  const isSelected = selectedPreset === preset.id;

                  return (
                    <Card
                      key={preset.id}
                      className={`
                        cursor-pointer transition-all hover:shadow-md
                        ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/30' : ''}
                      `}
                      onClick={() => handlePresetSelect(preset.id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`
                            p-2 rounded-lg
                            ${isSelected ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' : 'bg-muted text-muted-foreground'}
                          `}>
                            <Icon className="w-5 h-5" />
                          </div>
                          <div>
                            <CardTitle className="text-base flex items-center space-x-2">
                              <span>{preset.name}</span>
                              {isSelected && (
                                <CheckCircle2 className="w-4 h-4 text-green-600" />
                              )}
                            </CardTitle>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <CardDescription className="text-sm">
                          {preset.description}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Live Preview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Eye className="w-5 h-5" />
                    <span>Live Preview</span>
                  </CardTitle>
                  <CardDescription>
                    See how your widget looks on different devices
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
                  <Button
                    variant={previewDevice === "desktop" ? "secondary" : "ghost"}
                    size="sm"
                    className="h-8 px-2.5"
                    onClick={() => setPreviewDevice("desktop")}
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === "tablet" ? "secondary" : "ghost"}
                    size="sm"
                    className="h-8 px-2.5"
                    onClick={() => setPreviewDevice("tablet")}
                  >
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === "mobile" ? "secondary" : "ghost"}
                    size="sm"
                    className="h-8 px-2.5"
                    onClick={() => setPreviewDevice("mobile")}
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-muted rounded-lg p-4 min-h-[400px] flex items-center justify-center">
                <DevicePreview device={previewDevice}>
                  <WidgetPreview config={form.getValues()} />
                </DevicePreview>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Advanced Settings</span>
              </CardTitle>
              <CardDescription>
                Fine-tune mobile behavior and positioning
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Position Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Mobile Position</Label>
                  <Select
                    value={mobileSettings.mobilePosition}
                    onValueChange={(value) =>
                      setMobileSettings({ ...mobileSettings, mobilePosition: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {positionOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Tablet Position</Label>
                  <Select
                    value={mobileSettings.tabletPosition}
                    onValueChange={(value) =>
                      setMobileSettings({ ...mobileSettings, tabletPosition: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {positionOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Scale Setting */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">
                  Mobile Scale: {mobileSettings.mobileScale[0]}%
                </Label>
                <Slider
                  value={mobileSettings.mobileScale}
                  onValueChange={(value) =>
                    setMobileSettings({ ...mobileSettings, mobileScale: value })
                  }
                  max={120}
                  min={60}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>60% (Smaller)</span>
                  <span>100% (Normal)</span>
                  <span>120% (Larger)</span>
                </div>
              </div>

              {/* Behavior Options */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">Mobile Behavior</Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="responsive" className="text-sm">
                        Responsive sizing
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Automatically adjust size based on screen
                      </p>
                    </div>
                    <Switch
                      id="responsive"
                      checked={mobileSettings.responsiveSize}
                      onCheckedChange={(checked) =>
                        setMobileSettings({ ...mobileSettings, responsiveSize: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="touch" className="text-sm">
                        Touch-optimized controls
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Larger buttons and touch-friendly interface
                      </p>
                    </div>
                    <Switch
                      id="touch"
                      checked={mobileSettings.touchOptimized}
                      onCheckedChange={(checked) =>
                        setMobileSettings({ ...mobileSettings, touchOptimized: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="swipe" className="text-sm">
                        Swipe gestures
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Allow swipe to minimize/expand widget
                      </p>
                    </div>
                    <Switch
                      id="swipe"
                      checked={mobileSettings.swipeGestures}
                      onCheckedChange={(checked) =>
                        setMobileSettings({ ...mobileSettings, swipeGestures: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="hide-small" className="text-sm">
                        Hide on very small screens
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Hide widget on screens smaller than 320px
                      </p>
                    </div>
                    <Switch
                      id="hide-small"
                      checked={mobileSettings.hideOnSmallScreens}
                      onCheckedChange={(checked) =>
                        setMobileSettings({ ...mobileSettings, hideOnSmallScreens: checked })
                      }
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSaveAndClose}>
          Save & Continue
        </Button>
      </div>
    </div>
  );
};

export default MobileModal;
