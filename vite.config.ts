import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { tempo } from "tempo-devtools/dist/vite";
import type { Connect, ViteDevServer } from "vite";
import type { IncomingMessage, ServerResponse } from "http";
import type { Plugin } from "vite";

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 9090,
    // @ts-ignore
    allowedHosts: process.env.TEMPO === "true" ? true : undefined,
    proxy: {
      "/api": {
        target: "http://localhost:9000",
        changeOrigin: true,
        secure: false,
      },
      "/sanctum": {
        target: "http://localhost:9000",
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [
    react(),
    mode === "development" && componentTagger(),
    secureWidgetCorsPlugin(),
    tempo(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));

// --- Secure CORS plugin for widget assets ---
function secureWidgetCorsPlugin(): Plugin {
  return {
    name: "secure-widget-cors",
    configureServer(server) {
      server.middlewares.use(
        (req: IncomingMessage, res: ServerResponse, next) => {
          // Only apply to widget assets
          if (req.url && req.url.startsWith("/widget/v1/")) {
            const origin = req.headers.origin || "";

            // Check if origin is in allowed list
            if (isAllowedOrigin(origin)) {
              res.setHeader("Access-Control-Allow-Origin", origin);
              res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
              res.setHeader("Access-Control-Allow-Headers", "Content-Type");
              res.setHeader("Access-Control-Max-Age", "86400"); // 24 hours
            }
          }
          next();
        },
      );
    },
    configurePreviewServer(server) {
      server.middlewares.use(
        (req: IncomingMessage, res: ServerResponse, next) => {
          // Only apply to widget assets
          if (req.url && req.url.startsWith("/widget/v1/")) {
            const origin = req.headers.origin || "";

            // Check if origin is in allowed list
            if (isAllowedOrigin(origin)) {
              res.setHeader("Access-Control-Allow-Origin", origin);
              res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
              res.setHeader("Access-Control-Allow-Headers", "Content-Type");
              res.setHeader("Access-Control-Max-Age", "86400"); // 24 hours
            }
          }
          next();
        },
      );
    },
  };
}

/**
 * Check if the origin is allowed to access widget resources
 * In production, this should validate against a database of registered domains
 */
function isAllowedOrigin(origin: string): boolean {
  // For development, allow local development servers
  if (process.env.NODE_ENV !== "production") {
    const allowedDevOrigins = [
      "http://localhost:3000",
      "http://localhost:8000",
      "http://localhost:9090",
      "http://127.0.0.1:3000",
      "http://127.0.0.1:8000",
      "http://127.0.0.1:9090",
    ];

    if (allowedDevOrigins.includes(origin) || origin === "") {
      return true;
    }
  }

  // In production, we would check against a database of registered domains
  // This is a placeholder for that logic
  const frontendUrl = process.env.FRONTEND_URL || "http://localhost:9090";

  // Allow your own frontend domain and null (for file:// protocol)
  return origin === frontendUrl || origin === "null";
}
