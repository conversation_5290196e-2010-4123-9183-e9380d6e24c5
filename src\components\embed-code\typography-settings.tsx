import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, X, Type } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface TypographyOptions {
    titleFont?: string;
    bodyFont?: string;
    fontSize?: number;
    customFonts?: string[];
}

interface TypographySettingsProps {
    options: TypographyOptions;
    onChange: (options: TypographyOptions) => void;
}

// List of common web-safe fonts
const COMMON_FONTS = [
    "Inter",
    "Arial",
    "Helvetica",
    "Verdana",
    "Georgia",
    "Times New Roman",
    "Courier New",
    "Tahoma",
    "Trebuchet MS",
    "Segoe UI",
    "Roboto",
    "Open Sans",
    "Lato",
];

export function TypographySettings({ options, onChange }: TypographySettingsProps) {
    const [localOptions, setLocalOptions] = useState<TypographyOptions>({
        titleFont: options.titleFont || "Inter",
        bodyFont: options.bodyFont || "Inter",
        fontSize: options.fontSize || 16,
        customFonts: options.customFonts || [],
    });
    const [newCustomFont, setNewCustomFont] = useState<string>("");
    const { toast } = useToast();

    // Handle changes to typography options
    const handleChange = (key: keyof TypographyOptions, value: any) => {
        const updated = { ...localOptions, [key]: value };
        setLocalOptions(updated);
        onChange(updated);
    };

    // Add a custom font
    const addCustomFont = () => {
        if (!newCustomFont.trim()) return;

        // Check for duplicates
        if (localOptions.customFonts?.includes(newCustomFont)) {
            toast({
                title: "Font already added",
                description: "This font is already in your custom fonts list",
                variant: "destructive",
            });
            return;
        }

        const updatedFonts = [...(localOptions.customFonts || []), newCustomFont];
        handleChange('customFonts', updatedFonts);
        setNewCustomFont("");

        toast({
            title: "Custom font added",
            description: `${newCustomFont} has been added to your custom fonts`,
        });
    };

    // Remove a custom font
    const removeCustomFont = (font: string) => {
        const updatedFonts = localOptions.customFonts?.filter(f => f !== font) || [];
        handleChange('customFonts', updatedFonts);

        toast({
            title: "Font removed",
            description: `${font} has been removed from your custom fonts`,
        });
    };

    // Get all available fonts (common + custom)
    const getAllFonts = () => {
        return [
            ...COMMON_FONTS,
            ...(localOptions.customFonts || [])
        ];
    };

    return (
        <div className="space-y-6">
            <div>
                <Label className="text-base">Typography Settings</Label>
                <p className="text-sm text-muted-foreground mb-4">
                    Customize fonts and text styles for your widget
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="title-font">Header Font</Label>
                    <Select
                        value={localOptions.titleFont}
                        onValueChange={(value) => handleChange('titleFont', value)}
                    >
                        <SelectTrigger id="title-font">
                            <SelectValue placeholder="Select font" />
                        </SelectTrigger>
                        <SelectContent>
                            {getAllFonts().map((font) => (
                                <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                                    {font}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="body-font">Body Font</Label>
                    <Select
                        value={localOptions.bodyFont}
                        onValueChange={(value) => handleChange('bodyFont', value)}
                    >
                        <SelectTrigger id="body-font">
                            <SelectValue placeholder="Select font" />
                        </SelectTrigger>
                        <SelectContent>
                            {getAllFonts().map((font) => (
                                <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                                    {font}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <div className="space-y-2">
                <div className="flex justify-between">
                    <Label htmlFor="font-size">
                        Base Font Size: {localOptions.fontSize}px
                    </Label>
                </div>
                <Slider
                    id="font-size"
                    min={12}
                    max={20}
                    step={1}
                    value={[localOptions.fontSize || 16]}
                    onValueChange={(value) => handleChange('fontSize', value[0])}
                />
                <p className="text-xs text-muted-foreground">
                    Adjust the base font size for the widget text
                </p>
            </div>

            <Card>
                <CardContent className="pt-6">
                    <Label className="mb-2 block">Add Custom Fonts</Label>
                    <p className="text-sm text-muted-foreground mb-4">
                        Add custom web fonts to use in your widget
                    </p>

                    <div className="flex gap-2 mb-4">
                        <Input
                            placeholder="e.g., Montserrat, sans-serif"
                            value={newCustomFont}
                            onChange={(e) => setNewCustomFont(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                    addCustomFont();
                                }
                            }}
                        />
                        <Button
                            onClick={addCustomFont}
                            disabled={!newCustomFont.trim()}
                        >
                            <PlusCircle className="h-4 w-4 mr-1" />
                            Add
                        </Button>
                    </div>

                    {localOptions.customFonts && localOptions.customFonts.length > 0 ? (
                        <div className="space-y-2">
                            {localOptions.customFonts.map((font) => (
                                <div
                                    key={font}
                                    className="flex items-center justify-between p-2 bg-muted rounded-md"
                                >
                                    <div className="flex items-center">
                                        <Type className="h-4 w-4 mr-2 text-muted-foreground" />
                                        <span style={{ fontFamily: font }} className="text-sm font-medium">
                                            {font}
                                        </span>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeCustomFont(font)}
                                        className="h-8 w-8 p-0"
                                    >
                                        <X className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                                    </Button>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="p-4 border rounded-md text-center text-sm text-muted-foreground">
                            No custom fonts added
                        </div>
                    )}
                </CardContent>
            </Card>

            <div className="p-4 border rounded-md bg-muted">
                <p className="text-sm">
                    <strong>Note:</strong> When using custom fonts, make sure they are accessible on your website.
                    You may need to include additional CSS or web font imports in your site's header.
                </p>
            </div>
        </div>
    );
} 