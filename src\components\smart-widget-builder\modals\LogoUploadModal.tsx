import React, { useState, useRef, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Image, Upload, CheckCircle, X, Eye, AlertCircle, Link, Trash2 } from 'lucide-react';
import { FeatureModalProps } from '../index';
import { toast } from 'sonner';
import { widgetService } from '@/utils/widgetService';

const LogoUploadModal = ({ form, onClose, widgetId }: FeatureModalProps) => {
  const [logoUrl, setLogoUrl] = useState('');
  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');
  const [previewUrl, setPreviewUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const [currentLogo, setCurrentLogo] = useState<any>(null);
  const [hasExistingLogo, setHasExistingLogo] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load existing logo on mount
  useEffect(() => {
    const loadExistingLogo = async () => {
      if (!widgetId) return;

      setIsLoading(true);
      try {
        const response = await widgetService.getLogo(Number(widgetId));
        if (response.data.logo) {
          setCurrentLogo(response.data.logo);
          setPreviewUrl(response.data.logoUrl);
          setHasExistingLogo(true);
        }
      } catch (error: any) {
        // 404 is expected if no logo exists
        if (error.response?.status !== 404) {
          console.error('Failed to load existing logo:', error);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadExistingLogo();
  }, [widgetId]);

  const validateFile = (file: File): string | null => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      return 'Please select an image file';
    }

    // Check supported formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      return 'Supported formats: JPEG, PNG, GIF, SVG, WebP';
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return 'File size must be less than 5MB';
    }

    return null;
  };

  const processFile = async (file: File) => {
    setIsUploading(true);
    setError('');

    try {
      if (widgetId) {
        // Widget exists - upload directly to server
        const metadata = {
          position: 'header' as const,
          altText: `${file.name} logo`,
          quality: 92,
        };

        const response = await widgetService.uploadLogo(Number(widgetId), file, metadata);

        if (response.data.logoUrl) {
          setPreviewUrl(response.data.logoUrl);
          setCurrentLogo(response.data.logo);
          setHasExistingLogo(true);

          // Update form values
          form.setValue('features.logoUpload', true);
          form.setValue('advanced.logoUrl', response.data.logoUrl);
          form.setValue('appearance.showLogo', true);

          toast.success('Logo uploaded successfully');
        }
      } else {
        // Widget doesn't exist yet - store file temporarily for upload after widget creation
        const fileUrl = URL.createObjectURL(file);
        setPreviewUrl(fileUrl);

        // Store file data in form for later upload
        form.setValue('features.logoUpload', true);
        form.setValue('advanced.logoFile', file);
        form.setValue('advanced.logoUrl', fileUrl);
        form.setValue('appearance.showLogo', true);

        toast.success('Logo selected - will be uploaded when widget is saved');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to process logo';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      toast.error(validationError);
      return;
    }

    processFile(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      const file = files[0];
      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        toast.error(validationError);
        return;
      }
      processFile(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const validateUrl = (url: string): string | null => {
    if (!url.trim()) return null;

    try {
      new URL(url);
    } catch {
      return 'Invalid URL format';
    }

    // Check if URL points to an image
    const imageExtensions = /\.(jpg|jpeg|png|gif|svg|webp)(\?.*)?$/i;
    if (!imageExtensions.test(url)) {
      return 'URL must point to an image file (jpg, png, gif, svg, webp)';
    }

    return null;
  };

  const handleUrlChange = (url: string) => {
    setError('');
    setLogoUrl(url);

    if (url.trim()) {
      const validationError = validateUrl(url);
      if (validationError) {
        setError(validationError);
        return;
      }
      setPreviewUrl(url);
    } else {
      setPreviewUrl('');
    }
  };

  const handleUrlSave = async () => {
    if (!logoUrl.trim()) {
      setError('URL is required');
      toast.error('URL is required');
      return;
    }

    const validationError = validateUrl(logoUrl);
    if (validationError) {
      setError(validationError);
      toast.error(validationError);
      return;
    }

    setIsUploading(true);
    setError('');

    try {
      if (widgetId) {
        // Widget exists - update logo URL on server
        const metadata = {
          position: 'header' as const,
          altText: 'Widget logo',
        };

        const response = await widgetService.updateLogoUrl(Number(widgetId), logoUrl, metadata);

        if (response.data.logoUrl) {
          setPreviewUrl(response.data.logoUrl);
          setCurrentLogo(response.data.logo);
          setHasExistingLogo(true);

          // Update form values
          form.setValue('features.logoUpload', true);
          form.setValue('advanced.logoUrl', response.data.logoUrl);
          form.setValue('appearance.showLogo', true);

          toast.success('Logo URL updated successfully');
        }
      } else {
        // Widget doesn't exist yet - store URL for later use
        setPreviewUrl(logoUrl);

        // Update form values
        form.setValue('features.logoUpload', true);
        form.setValue('advanced.logoUrl', logoUrl);
        form.setValue('appearance.showLogo', true);

        toast.success('Logo URL saved - will be applied when widget is saved');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to process logo URL';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSave = () => {
    if (uploadMethod === 'url' && logoUrl.trim()) {
      handleUrlSave();
    } else {
      onClose();
    }
  };

  const clearLogo = async () => {
    if (!widgetId || !hasExistingLogo) {
      // Just clear local state if no widget ID or no existing logo
      setLogoUrl('');
      setPreviewUrl('');
      setError('');
      setHasExistingLogo(false);
      setCurrentLogo(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Update form values
      form.setValue('features.logoUpload', false);
      form.setValue('advanced.logoUrl', '');
      form.setValue('appearance.showLogo', false);

      toast.success('Logo removed');
      return;
    }

    setIsUploading(true);
    setError('');

    try {
      await widgetService.deleteLogo(Number(widgetId));

      // Clear all state
      setLogoUrl('');
      setPreviewUrl('');
      setHasExistingLogo(false);
      setCurrentLogo(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Update form values
      form.setValue('features.logoUpload', false);
      form.setValue('advanced.logoUrl', '');
      form.setValue('appearance.showLogo', false);

      toast.success('Logo removed successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to remove logo';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Image className="w-5 h-5 mr-2 text-blue-600" />
            Widget Logo & Avatar
          </DialogTitle>
          <DialogDescription>
            Upload a custom logo or avatar to strengthen your brand identity
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-500 mt-2">Loading existing logo...</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upload Panel */}
          <div className="space-y-6">
            {/* Current Logo Status */}
            {hasExistingLogo && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
                    Current Logo
                  </CardTitle>
                  <CardDescription>Your widget currently has a logo</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {previewUrl && (
                        <img
                          src={previewUrl}
                          alt="Current logo"
                          className="w-10 h-10 rounded object-cover"
                        />
                      )}
                      <div>
                        <p className="text-sm font-medium text-green-800">Logo active</p>
                        <p className="text-xs text-green-600">
                          {currentLogo?.logoType === 'base64' ? 'Uploaded file' : 'External URL'}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearLogo}
                      disabled={isUploading}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Remove
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {hasExistingLogo ? 'Replace Logo' : 'Upload Method'}
                </CardTitle>
                <CardDescription>
                  {hasExistingLogo ? 'Choose a new logo to replace the current one' : 'Choose how to add your logo'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={uploadMethod === 'upload' ? 'default' : 'outline'}
                    onClick={() => {
                      setUploadMethod('upload');
                      setError('');
                    }}
                    className="justify-start"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload File
                  </Button>
                  <Button
                    variant={uploadMethod === 'url' ? 'default' : 'outline'}
                    onClick={() => {
                      setUploadMethod('url');
                      setError('');
                    }}
                    className="justify-start"
                  >
                    <Link className="w-4 h-4 mr-2" />
                    Image URL
                  </Button>
                </div>

                {uploadMethod === 'upload' && (
                  <div className="space-y-4">
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragActive
                        ? 'border-blue-400 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                        }`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={handleDrag}
                      onDrop={handleDrop}
                    >
                      <Upload className={`w-8 h-8 mx-auto mb-2 ${dragActive ? 'text-blue-500' : 'text-gray-400'}`} />
                      <p className="text-sm text-gray-600 mb-2">
                        {dragActive ? 'Drop your image here' : 'Click to upload or drag and drop'}
                      </p>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF, SVG, WebP up to 5MB
                      </p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/jpeg,image/png,image/gif,image/svg+xml,image/webp"
                        onChange={handleFileUpload}
                        className="hidden"
                        disabled={isUploading}
                      />
                      <Button
                        variant="outline"
                        className="mt-2"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        {isUploading ? 'Processing...' : 'Choose File'}
                      </Button>
                    </div>
                  </div>
                )}

                {uploadMethod === 'url' && (
                  <div className="space-y-3">
                    <Label htmlFor="logo-url">Image URL</Label>
                    <Input
                      id="logo-url"
                      value={logoUrl}
                      onChange={(e) => handleUrlChange(e.target.value)}
                      placeholder="https://example.com/logo.png"
                      className={error ? 'border-red-500' : ''}
                    />
                    <p className="text-xs text-gray-500">
                      Enter a direct link to your logo image (JPG, PNG, GIF, SVG, WebP)
                    </p>
                    {logoUrl && !error && (
                      <div className="text-xs text-green-600 flex items-center">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        URL looks valid
                      </div>
                    )}
                  </div>
                )}

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {logoUrl && !error && (
                  <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-800">Logo ready</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearLogo}
                      disabled={isUploading}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Logo Guidelines</CardTitle>
                <CardDescription>Best practices for optimal display</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Square aspect ratio (1:1) recommended</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Minimum size: 64x64 pixels</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Maximum file size: 5MB</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Formats: PNG, JPG, GIF, SVG, WebP</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Transparent background (PNG) works best</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Eye className="w-5 h-5 mr-2" />
                  Logo Preview
                </CardTitle>
                <CardDescription>How your logo will appear in the widget</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-4">
                  {/* Widget Header Preview */}
                  <div className="bg-blue-600 rounded-t-lg p-4 text-white">
                    <div className="flex items-center space-x-3">
                      {previewUrl ? (
                        <img
                          src={previewUrl}
                          alt="Logo preview"
                          className="w-8 h-8 rounded-full object-cover bg-white"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                          <span className="text-xs">🤖</span>
                        </div>
                      )}
                      <div>
                        <h3 className="font-medium text-sm">AI Assistant</h3>
                        <p className="text-xs opacity-90">Online</p>
                      </div>
                    </div>
                  </div>

                  {/* Message with Avatar */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 space-y-3">
                    <div className="flex items-start space-x-2">
                      {previewUrl ? (
                        <img
                          src={previewUrl}
                          alt="Avatar preview"
                          className="w-6 h-6 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                          <span className="text-xs">🤖</span>
                        </div>
                      )}
                      <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-2 flex-1">
                        <p className="text-sm">Hello! How can I help you today?</p>
                      </div>
                    </div>
                  </div>

                  {!previewUrl && (
                    <div className="text-center text-gray-500 py-4">
                      <Image className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Upload a logo to see preview</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">Benefits</h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-200 mt-1 space-y-1">
                    <li>• Strengthens brand recognition</li>
                    <li>• Builds user trust and credibility</li>
                    <li>• Professional appearance</li>
                    <li>• Consistent brand experience</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button variant="outline" onClick={onClose} disabled={isUploading}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700"
            disabled={isUploading || !!error}
          >
            {isUploading ? 'Processing...' : 'Save Logo Settings'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LogoUploadModal;
