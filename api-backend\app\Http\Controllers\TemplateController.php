<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\AIModel;
use App\Services\TemplateProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;

class TemplateController extends Controller
{
    protected $templateProcessingService;

    public function __construct(TemplateProcessingService $templateProcessingService)
    {
        $this->templateProcessingService = $templateProcessingService;
    }

    /**
     * Display a listing of the templates.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Template::query();

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        if ($request->has('is_default') && $request->is_default) {
            $query->where('is_default', true);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        $templates = $query->orderBy('updated_at', 'desc')->get();

        return response()->json($templates);
    }

    /**
     * Store a newly created template in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'category' => 'required|string|max:100',
                'content' => 'required|string',
                'version' => 'nullable|numeric',
                'is_default' => 'nullable|boolean',
                'variables' => 'nullable|array',
                'status' => 'nullable|string|in:active,inactive,draft',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                    'success' => false
                ], 422);
            }

            // If this is set as default, unset any existing default
            if ($request->input('is_default', false)) {
                Template::where('is_default', true)->update(['is_default' => false]);
            }

            // Add user ID to created_by and updated_by
            $data = $request->all();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            $template = Template::create($data);
            return response()->json([
                'data' => $template,
                'message' => 'Template created successfully',
                'success' => true
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create template: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to create template',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Display the specified template.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $template = Template::findOrFail($id);
            return response()->json(['data' => $template, 'success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch template: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to fetch template',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Update the specified template in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $template = Template::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'category' => 'sometimes|required|string|max:100',
                'content' => 'sometimes|required|string',
                'version' => 'nullable|numeric',
                'is_default' => 'nullable|boolean',
                'variables' => 'nullable|array',
                'status' => 'nullable|string|in:active,inactive,draft',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                    'success' => false
                ], 422);
            }

            // If this is set as default, unset any existing default
            if ($request->input('is_default', false) && !$template->is_default) {
                Template::where('is_default', true)->update(['is_default' => false]);
            }

            // Add updated_by field
            $data = $request->all();
            $data['updated_by'] = Auth::id();

            $template->update($data);
            return response()->json([
                'data' => $template,
                'message' => 'Template updated successfully',
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update template: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to update template',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Remove the specified template from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $template = Template::findOrFail($id);

            // Check if any models are using this template
            $modelsUsingTemplate = AIModel::where('template_id', $id)->count();
            if ($modelsUsingTemplate > 0) {
                return response()->json([
                    'message' => 'Cannot delete template that is in use by AI models',
                    'success' => false
                ], 422);
            }

            $template->delete();
            return response()->json([
                'message' => 'Template deleted successfully',
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete template: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to delete template',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get templates for a specific model.
     *
     * @param  int  $modelId
     * @return \Illuminate\Http\Response
     */
    public function getModelTemplates($modelId)
    {
        try {
            // Verify the model exists
            $model = AIModel::findOrFail($modelId);

            // Get all templates
            $templates = Template::orderBy('name')->get();

            return response()->json(['data' => $templates, 'success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch model templates: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to fetch model templates',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Assign a template to a model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $modelId
     * @return \Illuminate\Http\Response
     */
    public function assignTemplateToModel(Request $request, $modelId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'template_id' => 'nullable|exists:templates,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                    'success' => false
                ], 422);
            }

            $model = AIModel::findOrFail($modelId);
            $model->template_id = $request->input('template_id');
            $model->save();

            return response()->json([
                'data' => $model,
                'message' => 'Template assigned successfully',
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to assign template to model: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to assign template to model',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Set a template as the default.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function setDefault(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Unset any existing defaults
            Template::where('is_default', true)->update(['is_default' => false]);

            // Set the specified template as default
            $template = Template::findOrFail($id);
            $template->is_default = true;
            $template->save();

            DB::commit();

            return response()->json($template);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error setting default template: ' . $e->getMessage(), [
                'exception' => $e,
                'template_id' => $id
            ]);

            if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
                return response()->json(['error' => 'Template not found'], 404);
            }

            return response()->json(['error' => 'Failed to set default template: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get templates for a specific AI model.
     *
     * @param int $modelId
     * @return JsonResponse
     */
    public function getTemplatesForModel(int $modelId): JsonResponse
    {
        try {
            // Check if model exists
            $model = AIModel::findOrFail($modelId);

            // Get all active templates
            $templates = Template::where('status', 'active')->get();

            return response()->json($templates);
        } catch (\Exception $e) {
            Log::error('Error getting templates for model: ' . $e->getMessage(), [
                'exception' => $e,
                'model_id' => $modelId
            ]);

            if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
                return response()->json(['error' => 'AI Model not found'], 404);
            }

            return response()->json(['error' => 'Failed to get templates for model: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Test a template by processing its variables.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function testTemplate(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'variables' => 'required|array',
        ]);

        try {
            $template = Template::findOrFail($id);

            $processedContent = $this->templateProcessingService->processTemplate($template, $request->variables);

            return response()->json(['content' => $processedContent]);
        } catch (\Exception $e) {
            Log::error('Error testing template: ' . $e->getMessage(), [
                'exception' => $e,
                'template_id' => $id,
                'variables' => $request->variables
            ]);

            if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
                return response()->json(['error' => 'Template not found'], 404);
            }

            return response()->json(['error' => 'Failed to test template: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get all unique template categories.
     *
     * @return JsonResponse
     */
    public function getCategories(): JsonResponse
    {
        try {
            $categories = Template::distinct('category')->pluck('category')->toArray();
            return response()->json($categories);
        } catch (\Exception $e) {
            Log::error('Error getting template categories: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return response()->json(['error' => 'Failed to get template categories: ' . $e->getMessage()], 500);
        }
    }
}
