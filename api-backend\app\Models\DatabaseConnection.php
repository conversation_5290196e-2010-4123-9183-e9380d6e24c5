<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DatabaseConnection extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',
        'name',
        'description',
        'driver',
        'host',
        'port',
        'database',
        'username',
        'password',
        'status',
        'last_sync_at',
        'sync_frequency',
        'auto_sync',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'port' => 'integer',
        'auto_sync' => 'boolean',
        'last_sync_at' => 'datetime',
    ];

    /**
     * Get the project that owns the database connection.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}
