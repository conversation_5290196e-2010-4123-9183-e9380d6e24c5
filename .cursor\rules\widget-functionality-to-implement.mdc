---
description: 
globs: 
alwaysApply: true
---
Widget CRUD Functionality Checklist
Here's a comprehensive checklist of items needed to ensure the widget CRUD functionality is complete and production-ready:
Backend
Database & Models
[x] Basic widget table schema with core fields
[x] Extended widget table with advanced customization fields
[ ] Add missing fields for security settings (CSP, SRI flags, data sanitization)
[ ] Add missing field for webhook URL
[ ] Add missing fields for A/B testing configuration
[ ] Add audit trail/history table for widget changes
[ ] Add proper indexes for performance optimization
Controllers & Routes
[x] Basic CRUD routes for widgets
[x] Widget ownership validation
[ ] Complete input validation for all fields
[ ] Proper error handling with meaningful messages
[ ] Prevent partial updates from overwriting JSON fields
[ ] Implement version incrementation on updates
[ ] Add bulk operations (batch update/delete)
[ ] Add widget duplication/cloning functionality
[ ] Add sorting and filtering of widgets list
Security
[ ] Implement proper sanitization for custom CSS
[ ] Add CSRF protection for all form submissions
[ ] Add rate limiting for embed code generation
[ ] Implement proper domain validation during widget creation
[ ] Add proper validation for all JSON structured data
[ ] Implement permissions checking for all widget operations
Validation
[ ] Create comprehensive validation rules for all widget fields
[ ] Validate nested JSON structures consistently
[ ] Add validation for color formats
[ ] Add validation for dimension constraints
[ ] Implement domain format validation with proper error messages
Frontend
Form Components
[x] General settings form section
[x] Appearance settings form section
[x] Behavior settings form section
[x] Advanced settings form section
[x] Position controls
[x] Button customization
[x] Security settings
[x] Domain restrictions
[ ] Form error state handling for all fields
[ ] Loading states for API interactions
[ ] Unsaved changes detection and warnings
CRUD Operations
[ ] Implement proper form submission with all fields
[ ] Fix nested data structure mapping to API endpoints
[ ] Add optimistic UI updates
[ ] Implement proper error handling with user feedback
[ ] Add confirmation dialogs for destructive actions
[ ] Fix partial update issues with JSON fields
[ ] Add widget duplication functionality
[ ] Implement proper widget listing with sorting/filtering
State Management
[ ] Implement proper state management for complex form
[ ] Add form validation before submission
[ ] Track dirty state for individual sections
[ ] Add validation for interdependent fields
[ ] Implement proper form reset functionality
[ ] Add autosave functionality
UI/UX Improvements
[ ] Add loading indicators for all API operations
[ ] Add success/error notifications for all operations
[ ] Implement proper form section navigation
[ ] Fix responsive design issues in form layout
[ ] Add keyboard navigation support
[ ] Implement accessible form controls
[ ] Add progress indicator for multi-step form completion
Integration Tests
Backend Tests
[ ] Create tests for widget creation with all fields
[ ] Add tests for widget updates with partial data
[ ] Add tests for security validations
[ ] Test domain restriction functionality
[ ] Test JSON field integrity during updates
[ ] Test version incrementation
[ ] Add authorization tests for widget ownership
Frontend Tests
[ ] Test form validation for all fields
[ ] Test widget creation workflow
[ ] Test widget update with all field types
[ ] Test error state handling
[ ] Test form reset functionality
[ ] Test navigation between form sections
[ ] Test responsive behavior
Documentation
[ ] Document all widget configuration options
[ ] Create API documentation for widget endpoints
[ ] Document JSON structure for settings fields
[ ] Add clear error documentation for validation failures
[ ] Document widget versioning system
[ ] Create implementation guide for developers
Critical Implementation Tasks
Fix JSON Field Update Issue
Implement proper merging of existing JSON data with partial updates
Prevent accidental data loss when updating specific sections
Complete Security Settings Storage
Add dedicated database fields for security settings
Update controllers to handle security setting persistence
Implement Validation System
Create comprehensive validation rules for all widget fields
Ensure consistent validation between frontend and backend
Complete Frontend-Backend Data Mapping
Ensure consistent field naming between frontend and API
Fix any data transformation issues between UI and database
Add Version Management
Implement proper version incrementation on widget updates
Add migration path for widgets when structure changes
Complete Custom CSS Handling
Add proper sanitization for custom CSS
Implement validation for CSS syntax
Fix Domain Restriction Implementation
Complete domain validation during widget creation/update
Implement proper error handling for invalid domains
Implement Form Section State Management
Add proper state tracking for multi-section form
Implement validation for interdependent fields
Complete Error Handling
Add comprehensive error messages for all validation failures
Implement proper UI feedback for API errors
Add Audit Trail
Implement history tracking for widget changes

Add user activity logging for security purposes