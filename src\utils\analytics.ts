/**
 * Analytics Utilities
 * 
 * This file contains utilities for tracking widget analytics.
 */

import { generateVisitorId } from './widget-utils';

/**
 * Track a widget view event
 * @param widgetId Widget ID
 * @param baseUrl API base URL
 * @returns Promise that resolves when tracking is complete
 */
export async function trackWidgetView(widgetId: string, baseUrl: string): Promise<void> {
  if (!widgetId) {
    console.warn('Widget ID is required for tracking');
    return;
  }

  try {
    const visitorId = generateVisitorId();
    
    const data = {
      widget_id: widgetId,
      event_type: 'view',
      visitor_id: visitorId,
      url: window.location.href
    };

    // Use fetch with keepalive to ensure the request completes even if the page is unloaded
    const response = await fetch(`${baseUrl}/api/widget/analytics/view`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(data),
      keepalive: true,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`Analytics tracking failed: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    // Log error but don't break the widget
    console.warn('Failed to track widget view:', error);
    
    // Fallback to image beacon as last resort
    try {
      const visitorId = generateVisitorId();
      const img = new Image();
      const params = new URLSearchParams({
        widget_id: widgetId,
        event_type: 'view',
        visitor_id: visitorId,
        url: window.location.href
      }).toString();
      
      img.src = `${baseUrl}/api/widget/analytics/view?${params}`;
      img.style.display = 'none';
      document.body.appendChild(img);
    } catch (beaconError) {
      console.warn('Failed to send beacon for widget view:', beaconError);
    }
  }
}

/**
 * Track a widget interaction event
 * @param widgetId Widget ID
 * @param eventType Event type (e.g., 'click', 'message', 'rating')
 * @param metadata Additional metadata for the event
 * @param baseUrl API base URL
 * @returns Promise that resolves when tracking is complete
 */
export async function trackWidgetInteraction(
  widgetId: string,
  eventType: string,
  metadata: Record<string, any> = {},
  baseUrl: string
): Promise<void> {
  if (!widgetId || !eventType) {
    console.warn('Widget ID and event type are required for tracking');
    return;
  }

  try {
    const visitorId = generateVisitorId();
    
    const data = {
      widget_id: widgetId,
      event_type: eventType,
      visitor_id: visitorId,
      url: window.location.href,
      metadata
    };

    // Use fetch with keepalive to ensure the request completes even if the page is unloaded
    await fetch(`${baseUrl}/api/widget/analytics/interaction`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(data),
      keepalive: true,
      credentials: 'include'
    });
  } catch (error) {
    // Log error but don't break the widget
    console.warn(`Failed to track widget interaction (${eventType}):`, error);
  }
}
