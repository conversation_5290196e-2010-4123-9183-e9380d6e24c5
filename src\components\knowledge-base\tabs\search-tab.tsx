import React, { useState, useEffect } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AIModelSelector } from '@/components/ai-models/ai-model-selector'
import { FileText, Search, ArrowUp, ArrowDown, Filter, RefreshCcw, X } from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { Skeleton } from '@/components/ui/skeleton'
import { toast } from 'sonner'

interface SearchTabProps {
    projects: any[];
    selectedProjectId: number | null;
    setSelectedProjectId: (id: number) => void;
    refreshTrigger: any;
    onRefresh: () => void;
}

export default function SearchTab({ projects, selectedProjectId, setSelectedProjectId, refreshTrigger, onRefresh }: SearchTabProps) {
    const { toast } = useToast()

    // Search state
    const [searchQuery, setSearchQuery] = useState('')
    const [isSearching, setIsSearching] = useState(false)
    const [searchResults, setSearchResults] = useState([])
    const [hasSearched, setHasSearched] = useState(false)

    // Filter state
    const [threshold, setThreshold] = useState(0.7)
    const [limit, setLimit] = useState(10)
    const [category, setCategory] = useState('')
    const [fileType, setFileType] = useState('')
    const [selectedModelId, setSelectedModelId] = useState(null)
    const [categories, setCategories] = useState([])
    const [fileTypes, setFileTypes] = useState([])

    // UI state
    const [showFilters, setShowFilters] = useState(false)

    // Load available categories and file types
    useEffect(() => {
        if (!selectedProjectId) return

        const fetchMetadata = async () => {
            try {
                // Get categories
                const response = await knowledgeBaseService.getDocuments(
                    selectedProjectId,
                    'per_page=1'
                )

                if (response?.data?.meta) {
                    if (response.data.meta.categories) {
                        setCategories(response.data.meta.categories)
                    }

                    // Extract file types from all documents
                    const docsResponse = await knowledgeBaseService.getDocuments(
                        selectedProjectId,
                        'per_page=100'
                    )

                    if (docsResponse?.data?.data) {
                        const types = [...new Set(docsResponse.data.data.map(doc => doc.file_type))]
                        setFileTypes(types)
                    }
                }
            } catch (error) {
                console.error('Failed to load metadata:', error)
            }
        }

        fetchMetadata()
    }, [selectedProjectId, refreshTrigger])

    // Handle search
    const handleSearch = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!searchQuery.trim() || !selectedProjectId) {
            toast({
                variant: "destructive",
                title: "Please enter a search query"
            })
            return
        }

        if (searchQuery.trim().length < 3) {
            toast({
                variant: "destructive",
                title: "Search query must be at least 3 characters long"
            })
            return
        }

        setIsSearching(true)
        setHasSearched(true)

        try {
            // Define filter object with proper interface
            const filter: {
                category?: string;
                file_type?: string;
                [key: string]: any;
            } = {};

            if (category) {
                filter.category = category
            }

            if (fileType) {
                filter.file_type = fileType
            }

            const response = await knowledgeBaseService.searchDocuments({
                query: searchQuery,
                project_id: selectedProjectId,
                limit,
                threshold,
                filter,
                model_id: selectedModelId
            })

            if (response?.data?.success) {
                setSearchResults(response.data.data || [])

                if (response.data.data.length === 0) {
                    toast({
                        variant: "default",
                        title: "No results found for your query"
                    })
                }
            } else {
                toast({
                    variant: "destructive",
                    title: `Search failed: ${response?.data?.message || 'Unknown error'}`
                })
                setSearchResults([])
            }
        } catch (error) {
            console.error('Search failed:', error)
            toast({
                variant: "destructive",
                title: "Search failed. Please try again."
            })
            setSearchResults([])
        } finally {
            setIsSearching(false)
        }
    }

    // Clear search
    const clearSearch = () => {
        setSearchQuery('')
        setSearchResults([])
        setHasSearched(false)
    }

    // Clear filters
    const clearFilters = () => {
        setThreshold(0.7)
        setLimit(10)
        setCategory('')
        setFileType('')
        setSelectedModelId(null)
    }

    // Calculate score color based on similarity
    const getScoreColor = (score) => {
        if (score >= 0.9) return 'text-green-500'
        if (score >= 0.8) return 'text-emerald-500'
        if (score >= 0.7) return 'text-blue-500'
        if (score >= 0.6) return 'text-amber-500'
        return 'text-red-500'
    }

    // Format score for display
    const formatScore = (score) => {
        return (score * 100).toFixed(1) + '%'
    }

    // Get file icon based on type
    const getFileIcon = (fileType) => {
        return <FileText className="h-5 w-5 mr-2" />
    }

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader>
                    <CardTitle className="text-xl">Semantic Search</CardTitle>
                    <CardDescription>
                        Search through your documents using natural language and AI-powered semantic understanding
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSearch} className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <div className="relative w-full">
                                <Input
                                    type="text"
                                    placeholder="Search your documents..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pr-10"
                                    disabled={isSearching || !selectedProjectId}
                                />
                                {searchQuery && (
                                    <button
                                        type="button"
                                        onClick={clearSearch}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                    >
                                        <X className="h-4 w-4" />
                                    </button>
                                )}
                            </div>
                            <Button
                                type="submit"
                                disabled={isSearching || !selectedProjectId || !searchQuery.trim()}
                            >
                                {isSearching ? (
                                    <div className="flex items-center">
                                        <Skeleton className="h-4 w-4 animate-pulse rounded-full mr-2" />
                                        Searching...
                                    </div>
                                ) : (
                                    <>
                                        <Search className="h-4 w-4 mr-2" />
                                        Search
                                    </>
                                )}
                            </Button>
                        </div>

                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => setShowFilters(!showFilters)}
                            >
                                <Filter className="h-4 w-4 mr-2" />
                                {showFilters ? 'Hide Filters' : 'Show Filters'}
                            </Button>

                            {showFilters && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={clearFilters}
                                >
                                    <RefreshCcw className="h-3 w-3 mr-2" />
                                    Reset
                                </Button>
                            )}
                        </div>

                        {showFilters && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-md">
                                <div className="space-y-2">
                                    <Label htmlFor="threshold">Similarity Threshold: {(threshold * 100).toFixed(0)}%</Label>
                                    <Slider
                                        id="threshold"
                                        min={0.5}
                                        max={0.95}
                                        step={0.05}
                                        value={[threshold]}
                                        onValueChange={(value) => setThreshold(value[0])}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="limit">Results Limit: {limit}</Label>
                                    <Slider
                                        id="limit"
                                        min={5}
                                        max={50}
                                        step={5}
                                        value={[limit]}
                                        onValueChange={(value) => setLimit(value[0])}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="category">Category</Label>
                                    <Select value={category} onValueChange={setCategory}>
                                        <SelectTrigger id="category">
                                            <SelectValue placeholder="All categories" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All categories</SelectItem>
                                            {categories.map((cat) => (
                                                <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="fileType">File Type</Label>
                                    <Select value={fileType} onValueChange={setFileType}>
                                        <SelectTrigger id="fileType">
                                            <SelectValue placeholder="All file types" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All file types</SelectItem>
                                            {fileTypes.map((type) => (
                                                <SelectItem key={type} value={type}>{type.toUpperCase()}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="embeddingModel">Embedding Model</Label>
                                    <AIModelSelector
                                        id="embeddingModel"
                                        selectedModelId={selectedModelId}
                                        onModelChange={setSelectedModelId}
                                        placeholder="Default embedding model"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Select a specific embedding model or leave blank to use the system default
                                    </p>
                                </div>
                            </div>
                        )}
                    </form>
                </CardContent>
            </Card>

            {/* Search Results */}
            {hasSearched && (
                <Card>
                    <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex justify-between items-center">
                            <span>Search Results</span>
                            <Badge variant="outline">{searchResults.length} results</Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {isSearching ? (
                            <div className="space-y-4">
                                {[1, 2, 3].map((i) => (
                                    <div key={i} className="border rounded-md p-4 space-y-2">
                                        <div className="flex items-center">
                                            <Skeleton className="h-5 w-5 mr-2" />
                                            <Skeleton className="h-4 w-48" />
                                        </div>
                                        <Skeleton className="h-20 w-full" />
                                        <div className="flex justify-between">
                                            <Skeleton className="h-4 w-24" />
                                            <Skeleton className="h-4 w-16" />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : searchResults.length > 0 ? (
                            <div className="space-y-4">
                                {searchResults.map((result, index) => (
                                    <div key={index} className="border rounded-md p-4 hover:bg-muted/50 transition-colors">
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center">
                                                {getFileIcon(result.file_type)}
                                                <h4 className="font-medium">{result.file_name}</h4>
                                            </div>
                                            <div className={`font-medium ${getScoreColor(result.similarity_score)}`}>
                                                {formatScore(result.similarity_score)}
                                            </div>
                                        </div>

                                        {result.chunk_text && (
                                            <div className="bg-muted/50 p-3 rounded-md my-2 text-sm whitespace-pre-wrap">
                                                {result.chunk_text.length > 300
                                                    ? `${result.chunk_text.substring(0, 300)}...`
                                                    : result.chunk_text
                                                }
                                            </div>
                                        )}

                                        <div className="flex justify-between text-xs text-muted-foreground mt-2">
                                            <div>
                                                {result.category && (
                                                    <Badge variant="outline" className="mr-2">
                                                        {result.category}
                                                    </Badge>
                                                )}
                                                <Badge variant="outline">
                                                    {result.file_type.toUpperCase()}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-10">
                                <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-muted mb-4">
                                    <Search className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <h3 className="font-medium">No results found</h3>
                                <p className="text-muted-foreground mt-2">
                                    Try adjusting your search query or filters
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}
        </div>
    )
} 