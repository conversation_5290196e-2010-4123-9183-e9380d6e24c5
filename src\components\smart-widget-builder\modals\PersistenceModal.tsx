import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import {
  Database,
  Clock,
  Users,
  Shield,
  CheckCircle2,
  Info,
  Trash2
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface PersistenceModalProps {
  form: UseFormReturn<any>;
  onClose: () => void;
  widgetId?: string;
}

const persistenceOptions = [
  {
    id: 'none',
    name: 'No Memory',
    description: 'Each conversation starts fresh - most private option',
    icon: Shield,
    privacy: 'high',
    benefit: 'Maximum privacy - no data stored',
    color: 'bg-green-100 text-green-600',
  },
  {
    id: 'session',
    name: 'Session Only',
    description: 'Remember conversation during current browser session',
    icon: Clock,
    privacy: 'medium',
    benefit: 'Good balance of convenience and privacy',
    color: 'bg-blue-100 text-blue-600',
  },
  {
    id: 'persistent',
    name: 'Remember Always',
    description: 'Keep conversation history across visits (30 days)',
    icon: Database,
    privacy: 'low',
    benefit: 'Best user experience - seamless conversations',
    color: 'bg-purple-100 text-purple-600',
  },
];

/**
 * Persistence Modal Component
 *
 * Provides a user-friendly interface for configuring conversation persistence
 * with clear privacy implications and user experience benefits.
 */
const PersistenceModal = ({ form, onClose, widgetId }: PersistenceModalProps) => {
  const [selectedOption, setSelectedOption] = useState('session');
  const [retentionDays, setRetentionDays] = useState([30]);
  const [advancedSettings, setAdvancedSettings] = useState({
    clearOnNewSession: false,
    encryptStorage: true,
    allowUserClear: true,
  });

  const handleOptionSelect = (optionId: string) => {
    setSelectedOption(optionId);
    form.setValue('features.conversationPersistence', optionId !== 'none');
  };

  const handleSaveAndClose = () => {
    form.setValue('features.conversationPersistence', selectedOption !== 'none');
    onClose();
  };

  const getPrivacyColor = (privacy: string) => {
    switch (privacy) {
      case 'high': return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950/50 dark:border-green-800';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-950/50 dark:border-yellow-800';
      case 'low': return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-950/50 dark:border-red-800';
      default: return 'text-muted-foreground bg-muted border-border';
    }
  };

  return (
    <div className="space-y-6">
      {/* Persistence Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center space-x-2">
            <Database className="w-5 h-5" />
            <span>Conversation Memory</span>
          </CardTitle>
          <CardDescription>
            Choose how long to remember conversations with your visitors
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {persistenceOptions.map((option) => {
              const Icon = option.icon;
              const isSelected = selectedOption === option.id;

              return (
                <Card
                  key={option.id}
                  className={`
                    cursor-pointer transition-all hover:shadow-md
                    ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/30' : ''}
                  `}
                  onClick={() => handleOptionSelect(option.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${option.color}`}>
                          <Icon className="w-5 h-5" />
                        </div>
                        <div>
                          <CardTitle className="text-base flex items-center space-x-2">
                            <span>{option.name}</span>
                            {isSelected && (
                              <CheckCircle2 className="w-4 h-4 text-green-600" />
                            )}
                          </CardTitle>
                          <CardDescription className="text-sm">
                            {option.description}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge
                        variant="outline"
                        className={`text-xs ${getPrivacyColor(option.privacy)}`}
                      >
                        {option.privacy} privacy
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="bg-green-50 border border-green-200 rounded-md p-2 dark:bg-green-950/50 dark:border-green-800">
                      <p className="text-sm text-green-700 dark:text-green-300">
                        💡 {option.benefit}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings for Persistent Storage */}
      {selectedOption === 'persistent' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Advanced Settings</CardTitle>
            <CardDescription>
              Fine-tune how conversation data is stored and managed
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Retention Period */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                Data Retention Period: {retentionDays[0]} days
              </Label>
              <Slider
                value={retentionDays}
                onValueChange={setRetentionDays}
                max={90}
                min={7}
                step={7}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>7 days</span>
                <span>30 days</span>
                <span>90 days</span>
              </div>
              <p className="text-xs text-muted-foreground">
                Conversations will be automatically deleted after this period
              </p>
            </div>

            {/* Privacy Options */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">Privacy & Security</Label>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="encrypt" className="text-sm">
                      Encrypt stored conversations
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Adds extra security layer to stored data
                    </p>
                  </div>
                  <Switch
                    id="encrypt"
                    checked={advancedSettings.encryptStorage}
                    onCheckedChange={(checked) =>
                      setAdvancedSettings({ ...advancedSettings, encryptStorage: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="user-clear" className="text-sm">
                      Allow users to clear their history
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Adds "Clear History" button to chat widget
                    </p>
                  </div>
                  <Switch
                    id="user-clear"
                    checked={advancedSettings.allowUserClear}
                    onCheckedChange={(checked) =>
                      setAdvancedSettings({ ...advancedSettings, allowUserClear: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="new-session" className="text-sm">
                      Clear history on new browser session
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Balances persistence with privacy
                    </p>
                  </div>
                  <Switch
                    id="new-session"
                    checked={advancedSettings.clearOnNewSession}
                    onCheckedChange={(checked) =>
                      setAdvancedSettings({ ...advancedSettings, clearOnNewSession: checked })
                    }
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Privacy Notice */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription className="text-sm">
          <strong>Privacy Compliance:</strong><br />
          • All stored data follows GDPR and privacy regulations<br />
          • Users can request data deletion at any time<br />
          • No personal data is stored without explicit consent<br />
          • Data is encrypted in transit and at rest
        </AlertDescription>
      </Alert>

      {/* User Experience Impact */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>User Experience Impact</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {selectedOption === 'none' ? '0%' : selectedOption === 'session' ? '25%' : '45%'}
              </div>
              <p className="text-xs text-muted-foreground">Faster conversations</p>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {selectedOption === 'none' ? '100%' : selectedOption === 'session' ? '85%' : '70%'}
              </div>
              <p className="text-xs text-muted-foreground">Privacy protection</p>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {selectedOption === 'none' ? '60%' : selectedOption === 'session' ? '80%' : '95%'}
              </div>
              <p className="text-xs text-muted-foreground">User satisfaction</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSaveAndClose}>
          Save & Continue
        </Button>
      </div>
    </div>
  );
};

export default PersistenceModal;
