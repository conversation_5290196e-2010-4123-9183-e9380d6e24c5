<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
        }
        #test-area {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ccc;
            min-height: 300px;
        }
    </style>
</head>
<body>
    <h1>Widget Tester</h1>
    <p>Paste your widget code below and click "Test Widget" to see it in action.</p>
    
    <textarea id="widget-code">
<script>
  window.CHAT_WIDGET_BASE_URL = (() => {
    const scriptTags = document.querySelectorAll('script');
    const currentScript = Array.from(scriptTags).find(script => 
      script.src && script.src.includes('/widget/script.js')
    );
    if (currentScript) {
      return currentScript.src.split('/widget/script.js')[0];
    }
    return 'http://localhost:9090'; // Fallback
  })();
</script>
<script type="module" src="http://localhost:9090/widget/v1.0/web-component.js"></script>
<ai-chat-widget
  widget-id="Ycahb9rBhSXr"
  primary-color="#4f46e5"
  border-radius="8"
  allowed-domains="*"
  guest-flow="auto"
  analytics="true">
</ai-chat-widget>
    </textarea>
    
    <button id="test-button">Test Widget</button>
    
    <div id="test-area">
        <p>Widget will appear here after clicking "Test Widget"</p>
    </div>
    
    <script>
        document.getElementById('test-button').addEventListener('click', function() {
            const code = document.getElementById('widget-code').value;
            document.getElementById('test-area').innerHTML = code;
        });
    </script>
</body>
</html>
