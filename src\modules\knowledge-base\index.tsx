import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import DocumentsTab from '@/components/knowledge-base/tabs/documents-tab'
import WebScrapingTab from '@/components/knowledge-base/tabs/web-scraping-tab'
import DatabaseTab from '@/components/knowledge-base/tabs/database-tab'
import ContextTab from '@/components/knowledge-base/tabs/context-tab'
import SearchTab from '@/components/knowledge-base/tabs/search-tab'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'

export default function KnowledgeBaseModule() {
    const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null)
    const [selectedTab, setSelectedTab] = useState('documents')
    const [isProjectSelected, setIsProjectSelected] = useState(false)
    const [projects, setProjects] = useState<any[]>([])
    const [refreshTrigger, setRefreshTrigger] = useState(0)
    const [isLoading, setIsLoading] = useState(false)

    // Fetch projects when component mounts or refreshTrigger changes
    useEffect(() => {
        const fetchProjects = async () => {
            setIsLoading(true)
            try {
                const response = await knowledgeBaseService.getProjects()
                if (response?.data?.success && response.data?.data) {
                    setProjects(response.data.data)
                }
            } catch (error) {
                console.error('Failed to fetch projects:', error)
                toast.error('Failed to load knowledge bases')
            } finally {
                setIsLoading(false)
            }
        }

        fetchProjects()
    }, [refreshTrigger])

    useEffect(() => {
        setIsProjectSelected(selectedProjectId !== null)
    }, [selectedProjectId])

    const handleRefresh = () => {
        setRefreshTrigger(prev => prev + 1)
    }

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Knowledge Base</CardTitle>
                    <CardDescription>
                        Manage document sources, web scraping, database connections, and context rules
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="mb-6">
                        <label className="block text-sm font-medium mb-2">
                            Select Project
                        </label>
                        <ProjectSelector
                            projects={projects}
                            selectedProjectId={selectedProjectId}
                            setSelectedProjectId={setSelectedProjectId}
                            onRefreshProjects={handleRefresh}
                        />
                    </div>

                    {!isProjectSelected && (
                        <Alert variant="default" className="mb-4">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Project Required</AlertTitle>
                            <AlertDescription>
                                Please select a project to manage its knowledge base.
                            </AlertDescription>
                        </Alert>
                    )}

                    <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
                        <TabsList className="w-full">
                            <TabsTrigger value="documents" className="flex-1">Documents</TabsTrigger>
                            <TabsTrigger value="search" className="flex-1">Search</TabsTrigger>
                            <TabsTrigger value="web-scraping" className="flex-1">Web Scraping</TabsTrigger>
                            <TabsTrigger value="database" className="flex-1">Database</TabsTrigger>
                            <TabsTrigger value="context" className="flex-1">Context</TabsTrigger>
                        </TabsList>
                        <div className="mt-6">
                            <TabsContent value="documents">
                                <DocumentsTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="search">
                                <SearchTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="web-scraping">
                                <WebScrapingTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="database">
                                <DatabaseTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="context">
                                <ContextTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                        </div>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    )
}
