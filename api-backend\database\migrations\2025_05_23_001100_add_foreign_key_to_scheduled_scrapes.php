<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scheduled_scrapes', function (Blueprint $table) {
            // First check if the foreign key doesn't already exist
            if (!Schema::hasColumn('scheduled_scrapes', 'source_id')) {
                $table->unsignedBigInteger('source_id')->nullable();
            }

            // Add foreign key constraint
            $table->foreign('source_id')
                ->references('id')
                ->on('knowledge_sources')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scheduled_scrapes', function (Blueprint $table) {
            $table->dropForeign(['source_id']);
        });
    }
};
