import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Brain, CheckCircle } from 'lucide-react';
import { FeatureModalProps } from '../index';

const AIModelModal = ({ form, onClose }: FeatureModalProps) => {
  const [selectedModel, setSelectedModel] = useState(form.getValues().advanced?.modelSelection || 'auto');

  // Simple, static model list - easy to manage
  const models = [
    { id: 'auto', name: 'Auto Selection', provider: 'System', tier: 'free', recommended: true },
    { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', provider: 'OpenAI', tier: 'medium', new: true },
    { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', tier: 'high' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', tier: 'low' },
    { id: 'claude-3-opus', name: 'Claude 3 Opus', provider: 'Anthropic', tier: 'high' },
    { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'Anthropic', tier: 'medium' },
    { id: 'gemini-pro', name: 'Gemini Pro', provider: 'Google', tier: 'medium' },
    { id: 'llama-2-70b', name: 'Llama 2 70B', provider: 'Meta', tier: 'low' },
    { id: 'mistral-large', name: 'Mistral Large', provider: 'Mistral AI', tier: 'medium', new: true },
  ];

  const handleSave = () => {
    form.setValue('features.aiModelSelection', true);
    form.setValue('advanced.modelSelection', selectedModel);
    onClose();
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'free': return 'bg-green-100 text-green-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            AI Model Selection
          </DialogTitle>
          <DialogDescription>
            Choose the AI model for your widget responses
          </DialogDescription>
        </DialogHeader>

        {/* COMPACT GRID - Fits any number of models */}
        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 max-h-[400px] overflow-y-auto p-2">
          {models.map((model) => (
            <Card
              key={model.id}
              className={`cursor-pointer transition-all p-3 text-center ${selectedModel === model.id
                ? 'ring-2 ring-purple-500 bg-purple-50'
                : 'hover:border-purple-300 hover:shadow-md'
                }`}
              onClick={() => setSelectedModel(model.id)}
            >
              <div className="space-y-2">
                <div className={`w-8 h-8 mx-auto rounded-lg flex items-center justify-center ${selectedModel === model.id ? 'bg-purple-100 text-purple-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                  <Brain className="w-4 h-4" />
                </div>

                <div>
                  <h4 className="font-medium text-xs">{model.name}</h4>
                  <p className="text-xs text-gray-500">{model.provider}</p>
                </div>

                <div className="space-y-1">
                  <Badge variant="outline" className={`text-xs ${getTierColor(model.tier)}`}>
                    {model.tier}
                  </Badge>
                  {model.recommended && (
                    <Badge className="bg-green-100 text-green-800 text-xs">Recommended</Badge>
                  )}
                  {model.new && (
                    <Badge className="bg-blue-100 text-blue-800 text-xs">New</Badge>
                  )}
                </div>

                {selectedModel === model.id && (
                  <CheckCircle className="w-4 h-4 text-green-600 mx-auto" />
                )}
              </div>
            </Card>
          ))}
        </div>

        {/* Selected Model Info */}
        {selectedModel && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-blue-600" />
              <span className="font-medium text-sm">
                Selected: {models.find(m => m.id === selectedModel)?.name}
              </span>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-purple-600 hover:bg-purple-700">
            Save Selection
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AIModelModal;
