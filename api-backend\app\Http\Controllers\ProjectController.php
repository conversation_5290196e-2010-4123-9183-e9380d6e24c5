<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;

class ProjectController extends Controller
{
    /**
     * Get all projects
     */
    public function index(): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => Project::all()
            ]);
        } catch (Exception $e) {
            \Log::error('Failed to get projects: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get projects: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new project
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
            ]);

            $project = Project::create([
                'name' => $request->name,
                'description' => $request->description,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Project created successfully',
                'data' => $project
            ], 201);
        } catch (Exception $e) {
            \Log::error('Failed to create project: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to create project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tables for a specific project
     */
    public function getTables(Request $request, $projectId): JsonResponse
    {
        try {
            // Try to find the project
            $project = Project::find($projectId);

            // If project doesn't exist, create a default one with ID 1
            if (!$project && $projectId == 1) {
                $project = Project::create([
                    'id' => 1,
                    'name' => 'Default Project',
                    'description' => 'Default project created automatically'
                ]);

                \Log::info('Created default project with ID 1');
            } elseif (!$project) {
                // If it's not ID 1, return an error with a helpful message
                return response()->json([
                    'success' => false,
                    'message' => "Project with ID {$projectId} not found. Try using ID 1 for the default project.",
                    'create_default' => true
                ], 404);
            }

            // Query the database schema to get actual tables
            // This uses the information_schema to get all tables in the current database
            $tables = DB::select("
                SELECT table_name as name
                FROM information_schema.tables
                WHERE table_schema = ?
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            ", [env('DB_DATABASE')]);

            // Filter out Laravel system tables if needed
            $excludedTables = ['migrations', 'password_reset_tokens', 'personal_access_tokens', 'failed_jobs'];
            $filteredTables = array_filter($tables, function($table) use ($excludedTables) {
                return !in_array($table->name, $excludedTables);
            });

            // If no tables are found after filtering, add some default tables
            if (empty($filteredTables)) {
                $defaultTables = [
                    (object)['name' => 'knowledge_data'],
                    (object)['name' => 'project_documents'],
                    (object)['name' => 'scraped_content']
                ];

                return response()->json([
                    'success' => true,
                    'project_id' => $projectId,
                    'tables' => $defaultTables,
                    'default_tables' => true
                ]);
            }

            return response()->json([
                'success' => true,
                'project_id' => $projectId,
                'tables' => array_values($filteredTables)
            ]);

        } catch (Exception $e) {
            \Log::error('Error in getTables: ' . $e->getMessage());

            // Return a more helpful error message with default tables
            $defaultTables = [
                (object)['name' => 'knowledge_data'],
                (object)['name' => 'project_documents'],
                (object)['name' => 'scraped_content']
            ];

            return response()->json([
                'success' => true,
                'project_id' => $projectId,
                'tables' => $defaultTables,
                'error_message' => $e->getMessage(),
                'default_tables' => true
            ]);
        }
    }
}
