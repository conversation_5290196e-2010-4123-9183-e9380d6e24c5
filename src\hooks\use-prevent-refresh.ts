import { useCallback } from 'react';

/**
 * Hook to prevent full page refreshes and ensure proper SPA behavior
 * 
 * This hook provides utilities to prevent common causes of page refreshes:
 * - Form submissions without preventDefault
 * - <PERSON><PERSON> clicks that trigger form submissions
 * - Link clicks that bypass React Router
 */
export const usePreventRefresh = () => {
  /**
   * Prevents default form submission behavior
   * Use this in form onSubmit handlers
   */
  const preventFormSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  /**
   * Prevents default button behavior that might trigger form submission
   * Use this for buttons inside forms that shouldn't submit
   */
  const preventButtonSubmit = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  /**
   * Prevents default link behavior
   * Use this for links that should be handled by React Router
   */
  const preventLinkDefault = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
  }, []);

  /**
   * Generic event handler that prevents default and stops propagation
   * Use this for any event that might cause unwanted page behavior
   */
  const preventDefault = useCallback((e: React.SyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  /**
   * Wrapper for async functions that ensures proper error handling
   * and prevents page refreshes on errors
   */
  const safeAsyncHandler = useCallback(
    <T extends any[]>(
      handler: (...args: T) => Promise<void>,
      onError?: (error: Error) => void
    ) => {
      return async (...args: T) => {
        try {
          await handler(...args);
        } catch (error) {
          console.error('Async handler error:', error);
          if (onError) {
            onError(error as Error);
          }
          // Don't rethrow to prevent unhandled promise rejections
          // that might cause page refreshes
        }
      };
    },
    []
  );

  return {
    preventFormSubmit,
    preventButtonSubmit,
    preventLinkDefault,
    preventDefault,
    safeAsyncHandler,
  };
};

/**
 * Higher-order component to wrap forms and prevent page refreshes
 */
export const withPreventRefresh = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => {
    const { preventFormSubmit } = usePreventRefresh();
    
    return (
      <form onSubmit={preventFormSubmit}>
        <Component {...props} />
      </form>
    );
  };
};

/**
 * Utility to ensure navigation uses React Router instead of page refresh
 */
export const createSafeNavigationHandler = (
  navigate: (to: string, options?: { replace?: boolean }) => void,
  to: string,
  options?: { replace?: boolean }
) => {
  return (e?: React.MouseEvent | React.FormEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    navigate(to, options);
  };
};
