import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Sparkles, 
  Palette, 
  ArrowRight, 
  Zap, 
  Clock, 
  Users,
  CheckCircle2,
  Plus
} from 'lucide-react';
import { Widget } from '@/utils/widgetService';

interface SmartBuilderActionsProps {
  widget?: Widget;
  showCreateButton?: boolean;
}

/**
 * Smart Builder Actions Component
 * 
 * Provides navigation options for the Smart Widget Builder with
 * clear benefits and comparison to the classic builder.
 */
const SmartBuilderActions = ({ widget, showCreateButton = true }: SmartBuilderActionsProps) => {
  const navigate = useNavigate();
  const [showComparison, setShowComparison] = useState(false);

  const handleSmartBuilderCreate = () => {
    navigate('/dashboard/widget-builder/smart');
  };

  const handleSmartBuilderEdit = () => {
    if (widget?.id) {
      navigate(`/dashboard/widget-builder/smart/${widget.id}`);
    }
  };

  const handleClassicBuilderCreate = () => {
    navigate('/dashboard/widget-builder');
  };

  const handleClassicBuilderEdit = () => {
    if (widget?.id) {
      navigate('/dashboard/widget-builder'); // Add widget ID handling in classic builder
    }
  };

  const builderComparison = [
    {
      feature: 'Setup Time',
      smart: '3-5 minutes',
      classic: '15-30 minutes',
      smartBetter: true,
    },
    {
      feature: 'User Experience',
      smart: 'Visual & intuitive',
      classic: 'Form-based',
      smartBetter: true,
    },
    {
      feature: 'Feature Discovery',
      smart: 'Guided with benefits',
      classic: 'Hidden in tabs',
      smartBetter: true,
    },
    {
      feature: 'Mobile Support',
      smart: 'Fully optimized',
      classic: 'Limited',
      smartBetter: true,
    },
    {
      feature: 'Advanced Features',
      smart: 'All features available',
      classic: 'All features available',
      smartBetter: false,
    },
  ];

  if (showCreateButton) {
    return (
      <div className="space-y-4">
        {/* Primary Action - Smart Builder */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Sparkles className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg text-blue-900">
                    Smart Widget Builder
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Recommended for all users
                  </CardDescription>
                </div>
              </div>
              <Badge className="bg-blue-600 text-white">
                New
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <Clock className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">3-5 min</span>
                </div>
                <p className="text-xs text-blue-700">Setup time</p>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <Zap className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Visual</span>
                </div>
                <p className="text-xs text-blue-700">Interface</p>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <Users className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">85%</span>
                </div>
                <p className="text-xs text-blue-700">Success rate</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-blue-800">
                ✨ Template-first approach with visual customization
              </p>
              <p className="text-sm text-blue-800">
                🎯 Smart feature discovery with clear benefits
              </p>
              <p className="text-sm text-blue-800">
                📱 Mobile-optimized interface and preview
              </p>
            </div>

            <Button 
              onClick={handleSmartBuilderCreate}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Create with Smart Builder
            </Button>
          </CardContent>
        </Card>

        {/* Secondary Option - Classic Builder */}
        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <Palette className="w-5 h-5 text-gray-600" />
                </div>
                <div>
                  <CardTitle className="text-lg text-gray-900">
                    Classic Widget Builder
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    Full control for advanced users
                  </CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-700">
              Traditional form-based interface with all advanced features accessible upfront.
              Best for users who prefer detailed control over every setting.
            </p>

            <div className="flex space-x-2">
              <Button 
                variant="outline"
                onClick={handleClassicBuilderCreate}
                className="flex-1"
              >
                <Palette className="w-4 h-4 mr-2" />
                Use Classic Builder
              </Button>
              
              <Dialog open={showComparison} onOpenChange={setShowComparison}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm">
                    Compare
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Builder Comparison</DialogTitle>
                    <DialogDescription>
                      Choose the builder that best fits your needs and experience level
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4 text-sm font-medium border-b pb-2">
                      <div>Feature</div>
                      <div className="text-center text-blue-600">Smart Builder</div>
                      <div className="text-center text-gray-600">Classic Builder</div>
                    </div>
                    
                    {builderComparison.map((item, index) => (
                      <div key={index} className="grid grid-cols-3 gap-4 text-sm">
                        <div className="font-medium">{item.feature}</div>
                        <div className={`text-center ${item.smartBetter ? 'text-green-600 font-medium' : ''}`}>
                          {item.smartBetter && <CheckCircle2 className="w-4 h-4 inline mr-1" />}
                          {item.smart}
                        </div>
                        <div className="text-center text-gray-600">{item.classic}</div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex space-x-2 pt-4">
                    <Button 
                      onClick={() => {
                        setShowComparison(false);
                        handleSmartBuilderCreate();
                      }}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      <Sparkles className="w-4 h-4 mr-2" />
                      Choose Smart Builder
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => {
                        setShowComparison(false);
                        handleClassicBuilderCreate();
                      }}
                      className="flex-1"
                    >
                      <Palette className="w-4 h-4 mr-2" />
                      Choose Classic Builder
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Edit actions for existing widget
  return (
    <div className="flex space-x-2">
      <Button
        onClick={handleSmartBuilderEdit}
        size="sm"
        className="bg-blue-600 hover:bg-blue-700"
      >
        <Sparkles className="w-4 h-4 mr-1" />
        Smart Edit
      </Button>
      <Button
        variant="outline"
        onClick={handleClassicBuilderEdit}
        size="sm"
      >
        <Palette className="w-4 h-4 mr-1" />
        Classic Edit
      </Button>
    </div>
  );
};

export default SmartBuilderActions;
