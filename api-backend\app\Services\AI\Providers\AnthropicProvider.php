<?php

namespace App\Services\AI\Providers;

use App\Models\AIModel;
use App\Services\AI\AbstractProvider;
use Illuminate\Support\Facades\Log;

class AnthropicProvider extends AbstractProvider
{
    /**
     * Get provider name
     * 
     * @return string
     */
    protected function getProviderName(): string
    {
        return 'anthropic';
    }
    
    /**
     * Process a message using Anthropic
     * 
     * @param AIModel $model
     * @param array $messages
     * @param array $options
     * @return array
     */
    public function processMessage(AIModel $model, array $messages, array $options = [])
    {
        try {
            $modelName = $this->getValidModelName($model);
            $temperature = $options['temperature'] ?? $model->settings['temperature'] ?? 0.7;
            $maxTokens = $options['max_tokens'] ?? $model->settings['max_tokens'] ?? 2048;
            
            // Format messages for Anthropic (Claude API format)
            $formattedMessages = [];
            foreach ($messages as $message) {
                $formattedMessages[] = [
                    'role' => $message['role'] === 'assistant' ? 'assistant' : 'user',
                    'content' => $message['content'],
                ];
            }
            
            $payload = [
                'model' => $modelName,
                'messages' => $formattedMessages,
                'temperature' => $temperature,
                'max_tokens' => $maxTokens,
            ];
            
            $response = $this->makeRequest('post', '/messages', [
                'headers' => [
                    'x-api-key' => $model->api_key,
                    'anthropic-version' => '2023-06-01',
                    'Content-Type' => 'application/json',
                ],
                'data' => $payload,
            ]);
            
            return [
                'content' => $response['content'][0]['text'] ?? 'No response content',
                'metadata' => [
                    'model' => $response['model'] ?? $modelName,
                    'provider' => $this->getProviderName(),
                    'stop_reason' => $response['stop_reason'] ?? null,
                ],
            ];
        } catch (\Exception $e) {
            Log::error("Anthropic API error: " . $e->getMessage(), [
                'model_id' => $model->id,
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Test connection to Anthropic
     * 
     * @param AIModel $model
     * @return array
     */
    public function testConnection(AIModel $model): array
    {
        try {
            $response = $this->makeRequest('get', '/models', [
                'headers' => [
                    'x-api-key' => $model->api_key,
                    'anthropic-version' => '2023-06-01',
                    'Content-Type' => 'application/json',
                ],
            ]);
            
            // Discover and update available models
            $discoveredModels = $this->parseModelsFromResponse($response);
            $this->updateModelSettings($model, $discoveredModels);
            
            return [
                'success' => true,
                'message' => 'Successfully connected to Anthropic API',
                'data' => [
                    'available_models' => array_keys($discoveredModels),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Anthropic connection test failed: ' . $e->getMessage(),
            ];
        }
    }
    
    /**
     * Discover available models
     * 
     * @param AIModel $model
     * @return array
     */
    public function discoverModels(AIModel $model): array
    {
        try {
            $response = $this->makeRequest('get', '/models', [
                'headers' => [
                    'x-api-key' => $model->api_key,
                    'anthropic-version' => '2023-06-01',
                    'Content-Type' => 'application/json',
                ],
            ]);
            
            $discoveredModels = $this->parseModelsFromResponse($response);
            
            // Update model settings
            $this->updateModelSettings($model, $discoveredModels);
            
            return [
                'success' => true,
                'models' => $discoveredModels,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to discover Anthropic models: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
    
    /**
     * Parse models from API response
     * 
     * @param array $response
     * @return array
     */
    protected function parseModelsFromResponse(array $response): array
    {
        $models = [];
        
        foreach ($response['models'] ?? [] as $model) {
            $modelId = $model['id'] ?? '';
            
            if (empty($modelId)) {
                continue;
            }
            
            $models[$modelId] = [
                'name' => $modelId,
                'display_name' => $this->formatModelName($modelId),
                'description' => $model['description'] ?? '',
                'input_token_limit' => $model['context_window'] ?? 200000,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => str_contains($modelId, 'claude-3'),
                ],
            ];
        }
        
        // If no models were found, return default models
        if (empty($models)) {
            return $this->getDefaultModels();
        }
        
        return $models;
    }
    
    /**
     * Format model name for display
     * 
     * @param string $modelId
     * @return string
     */
    protected function formatModelName(string $modelId): string
    {
        // Convert model IDs like "claude-3-opus-20240229" to "Claude 3 Opus"
        $parts = explode('-', $modelId);
        $formattedParts = [];
        
        foreach ($parts as $part) {
            if (is_numeric($part)) {
                continue; // Skip date parts
            } elseif ($part === 'claude') {
                $formattedParts[] = 'Claude';
            } else {
                $formattedParts[] = ucfirst($part);
            }
        }
        
        return implode(' ', $formattedParts);
    }
    
    /**
     * Get default models when API doesn't return any
     * 
     * @return array
     */
    protected function getDefaultModels(): array
    {
        return [
            'claude-3-opus-20240229' => [
                'name' => 'claude-3-opus-20240229',
                'display_name' => 'Claude 3 Opus',
                'description' => 'Anthropic\'s most powerful model',
                'input_token_limit' => 200000,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => true,
                ],
            ],
            'claude-3-sonnet-20240229' => [
                'name' => 'claude-3-sonnet-20240229',
                'display_name' => 'Claude 3 Sonnet',
                'description' => 'Anthropic\'s balanced model for performance and efficiency',
                'input_token_limit' => 200000,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => true,
                ],
            ],
            'claude-3-haiku-20240307' => [
                'name' => 'claude-3-haiku-20240307',
                'display_name' => 'Claude 3 Haiku',
                'description' => 'Anthropic\'s fastest and most compact model',
                'input_token_limit' => 200000,
                'output_token_limit' => 4096,
                'supported_features' => [
                    'streaming' => true,
                    'vision' => true,
                ],
            ],
        ];
    }
    
    /**
     * Get provider capabilities
     * 
     * @return array
     */
    public function getCapabilities(): array
    {
        return [
            'streaming' => true,
            'function_calling' => true,
            'vision' => true,
            'embeddings' => false,
            'max_context_length' => 200000,
        ];
    }
}
