/**
 * Textarea Field Component
 * 
 * A form field for multiline text input.
 */

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';

interface TextareaFieldProps {
  label: string;
  fieldName: string;
  control: Control<any>;
  placeholder?: string;
  description?: string;
  rows?: number;
  disabled?: boolean;
}

/**
 * Textarea Field Component
 * A form field for multiline text input
 */
export function TextareaField({
  label,
  fieldName,
  control,
  placeholder,
  description,
  rows = 3,
  disabled = false
}: TextareaFieldProps) {
  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Textarea
              placeholder={placeholder}
              rows={rows}
              {...field}
              disabled={disabled}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
