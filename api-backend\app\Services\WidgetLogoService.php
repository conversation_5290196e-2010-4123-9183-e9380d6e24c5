<?php

namespace App\Services;

use App\Models\WidgetLogo;
use Illuminate\Support\Facades\Log;

class WidgetLogoService
{
    /**
     * Get logo for a widget.
     */
    public function getLogo(int $widgetId): ?WidgetLogo
    {
        return WidgetLogo::where('widget_id', $widgetId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Create or update logo for a widget.
     */
    public function updateLogo(int $widgetId, array $logoData): WidgetLogo
    {
        return WidgetLogo::createFromData($widgetId, $logoData);
    }

    /**
     * Delete logo for a widget.
     */
    public function deleteLogo(int $widgetId): bool
    {
        return WidgetLogo::where('widget_id', $widgetId)->delete() > 0;
    }

    /**
     * Get logo URL for display (backward compatibility).
     */
    public function getLogoUrl(int $widgetId): ?string
    {
        $logo = $this->getLogo($widgetId);
        return $logo ? $logo->getDisplayData() : null;
    }

    /**
     * Process and optimize uploaded image data.
     */
    public function processImageUpload(int $widgetId, string $base64Data, array $metadata = []): WidgetLogo
    {
        // Extract image information from base64 data
        $imageInfo = $this->extractImageInfo($base64Data);
        
        // Merge with provided metadata
        $logoData = array_merge([
            'logoData' => $base64Data,
            'mimeType' => $imageInfo['mime_type'],
            'width' => $imageInfo['width'],
            'height' => $imageInfo['height'],
            'fileSize' => $imageInfo['file_size'],
        ], $metadata);

        return $this->updateLogo($widgetId, $logoData);
    }

    /**
     * Process URL-based logo.
     */
    public function processLogoUrl(int $widgetId, string $url, array $metadata = []): WidgetLogo
    {
        $logoData = array_merge([
            'logoUrl' => $url,
            'logoData' => $url,
        ], $metadata);

        return $this->updateLogo($widgetId, $logoData);
    }

    /**
     * Extract image information from base64 data.
     */
    protected function extractImageInfo(string $base64Data): array
    {
        $info = [
            'mime_type' => null,
            'width' => null,
            'height' => null,
            'file_size' => null,
        ];

        try {
            // Extract MIME type from data URL
            if (preg_match('/^data:image\/([a-zA-Z+]+);base64,/', $base64Data, $matches)) {
                $info['mime_type'] = 'image/' . $matches[1];
                
                // Remove data URL prefix to get pure base64
                $base64Pure = preg_replace('/^data:image\/[a-zA-Z+]+;base64,/', '', $base64Data);
                
                // Calculate file size
                $info['file_size'] = strlen(base64_decode($base64Pure));
                
                // Try to get image dimensions
                $imageData = base64_decode($base64Pure);
                $image = imagecreatefromstring($imageData);
                
                if ($image !== false) {
                    $info['width'] = imagesx($image);
                    $info['height'] = imagesy($image);
                    imagedestroy($image);
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to extract image info from base64 data', [
                'error' => $e->getMessage(),
            ]);
        }

        return $info;
    }

    /**
     * Validate logo data.
     */
    public function validateLogoData(array $logoData): array
    {
        $errors = [];

        // Check if logo data is provided
        if (empty($logoData['logoData']) && empty($logoData['logoUrl'])) {
            $errors['logo'] = 'Logo data or URL is required.';
            return $errors;
        }

        $logoValue = $logoData['logoData'] ?? $logoData['logoUrl'];

        // Validate URL format
        if (filter_var($logoValue, FILTER_VALIDATE_URL)) {
            // It's a URL, validate URL-specific rules
            if (!preg_match('/\.(jpg|jpeg|png|gif|svg|webp)$/i', $logoValue)) {
                $errors['logo'] = 'Logo URL must point to a valid image file (jpg, jpeg, png, gif, svg, webp).';
            }
        } elseif (str_starts_with($logoValue, 'data:image/')) {
            // It's a base64 data URL, validate base64-specific rules
            if (!preg_match('/^data:image\/(jpeg|jpg|png|gif|svg\+xml|webp);base64,/', $logoValue)) {
                $errors['logo'] = 'Invalid base64 image format. Supported formats: jpeg, jpg, png, gif, svg, webp.';
            } else {
                // Check file size (approximate)
                $base64Pure = preg_replace('/^data:image\/[a-zA-Z+]+;base64,/', '', $logoValue);
                $fileSize = strlen(base64_decode($base64Pure));
                
                // Limit to 5MB
                if ($fileSize > 5 * 1024 * 1024) {
                    $errors['logo'] = 'Logo file size cannot exceed 5MB.';
                }
            }
        } else {
            $errors['logo'] = 'Logo must be a valid URL or base64 encoded image.';
        }

        // Validate display dimensions if provided
        if (isset($logoData['displayWidth']) && ($logoData['displayWidth'] < 10 || $logoData['displayWidth'] > 500)) {
            $errors['displayWidth'] = 'Display width must be between 10 and 500 pixels.';
        }

        if (isset($logoData['displayHeight']) && ($logoData['displayHeight'] < 10 || $logoData['displayHeight'] > 500)) {
            $errors['displayHeight'] = 'Display height must be between 10 and 500 pixels.';
        }

        // Validate quality if provided
        if (isset($logoData['quality']) && ($logoData['quality'] < 1 || $logoData['quality'] > 100)) {
            $errors['quality'] = 'Image quality must be between 1 and 100.';
        }

        return $errors;
    }

    /**
     * Get optimized logo for public display.
     */
    public function getOptimizedLogo(int $widgetId): ?array
    {
        $logo = $this->getLogo($widgetId);
        
        if (!$logo) {
            return null;
        }

        return [
            'url' => $logo->getOptimizedData(),
            'alt' => $logo->alt_text,
            'width' => $logo->display_width,
            'height' => $logo->display_height,
            'position' => $logo->position,
        ];
    }

    /**
     * Check if widget has a logo.
     */
    public function hasLogo(int $widgetId): bool
    {
        return $this->getLogo($widgetId) !== null;
    }

    /**
     * Get logo metadata.
     */
    public function getLogoMetadata(int $widgetId): ?array
    {
        $logo = $this->getLogo($widgetId);
        return $logo ? $logo->getMetadata() : null;
    }

    /**
     * Update logo display settings.
     */
    public function updateDisplaySettings(int $widgetId, array $settings): bool
    {
        $logo = $this->getLogo($widgetId);
        
        if (!$logo) {
            return false;
        }

        $updateData = [];
        
        if (isset($settings['displayWidth'])) {
            $updateData['display_width'] = $settings['displayWidth'];
        }
        
        if (isset($settings['displayHeight'])) {
            $updateData['display_height'] = $settings['displayHeight'];
        }
        
        if (isset($settings['position'])) {
            $updateData['position'] = $settings['position'];
        }
        
        if (isset($settings['altText'])) {
            $updateData['alt_text'] = $settings['altText'];
        }

        if (empty($updateData)) {
            return false;
        }

        return $logo->update($updateData);
    }

    /**
     * Get default logo settings.
     */
    public function getDefaultSettings(): array
    {
        return [
            'position' => 'header',
            'displayWidth' => null,
            'displayHeight' => null,
            'quality' => 92,
            'autoOptimize' => true,
            'formatPreference' => 'auto',
        ];
    }
}
