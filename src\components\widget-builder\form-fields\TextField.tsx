/**
 * Text Field Component
 * 
 * A form field for text input.
 */

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

interface TextFieldProps {
  label: string;
  fieldName: string;
  control: Control<any>;
  placeholder?: string;
  description?: string;
  disabled?: boolean;
}

/**
 * Text Field Component
 * A form field for text input
 */
export function TextField({
  label,
  fieldName,
  control,
  placeholder,
  description,
  disabled = false
}: TextFieldProps) {
  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              placeholder={placeholder}
              {...field}
              disabled={disabled}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
