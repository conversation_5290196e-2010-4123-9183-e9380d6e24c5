// This is an updated comment to force the file to be recompiled.
// Widget Preview Component - Displays a live preview of the widget with current settings
// All variable names have been standardized to remove the "final" prefix.

import { MessageSquare, MessageCircle, BotMessageSquare, X, Maximize, Minimize, HelpCircle, Mail, Send, ThumbsUp, ThumbsDown, Move } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Pre-Chat Form and Post-Chat Survey components
import { EnhancedPreChatForm } from '@/components/pre-chat-form/enhanced-pre-chat-form';
import { PostChatSurvey } from '@/components/post-chat-survey/post-chat-survey';
import { usePreChatForm } from '@/hooks/use-pre-chat-form';

import { messageRatingService } from '@/utils/api-service';

interface WidgetPreviewProps {
  config: any;
  previewConfig?: any; // Real-time preview configuration
  aiModel?: {
    id: string;
    name: string;
  } | null;
  promptTemplate?: {
    id: string;
    name: string;
  } | null;
}

// Map of icon names to icon components
const ICONS: Record<string, React.ComponentType<any>> = {
  'message-square': MessageSquare,
  'message-circle': MessageCircle,
  'help-circle': HelpCircle,
  'mail': Mail,
  'bot': BotMessageSquare,
};

const DEFAULT_AVATAR_URL = '/assets/default-avatar.svg'; // Default avatar URL

const WidgetPreview = ({ config = {}, previewConfig = null, aiModel = null, promptTemplate = null }: WidgetPreviewProps) => {
  // Use previewConfig for real-time updates, fallback to config
  const activeConfig = previewConfig || config;
  // Add custom scrollbar styling
  useEffect(() => {
    // Create a style element
    const styleEl = document.createElement('style');
    styleEl.setAttribute('id', 'widget-preview-scrollbar-styles');

    // Extract the primary color for scrollbar styling
    const general = activeConfig?.general || {};
    const appearance = activeConfig?.appearance || {};
    const primaryColor = activeConfig?.primaryColor || appearance?.primaryColor || '#4f46e5';
    const primaryColorRGB = hexToRgb(primaryColor);

    // Define scrollbar styles
    styleEl.textContent = `
      .widget-custom-scrollbar::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      .widget-custom-scrollbar::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }

      .widget-custom-scrollbar::-webkit-scrollbar-thumb {
        background: rgba(${primaryColorRGB?.r || 79}, ${primaryColorRGB?.g || 70}, ${primaryColorRGB?.b || 229}, 0.6);
        border-radius: 4px;
        transition: background 0.2s ease;
      }

      .widget-custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: rgba(${primaryColorRGB?.r || 79}, ${primaryColorRGB?.g || 70}, ${primaryColorRGB?.b || 229}, 0.8);
      }

      .dark .widget-custom-scrollbar::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
      }
    `;

    // Append style to head if it doesn't exist
    if (!document.getElementById('widget-preview-scrollbar-styles')) {
      document.head.appendChild(styleEl);
    } else {
      // Update existing style
      const existingStyle = document.getElementById('widget-preview-scrollbar-styles');
      if (existingStyle) {
        existingStyle.textContent = styleEl.textContent;
      }
    }

    // Cleanup function
    return () => {
      const styleElement = document.getElementById('widget-preview-scrollbar-styles');
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, [activeConfig]);

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    // Remove # if present
    hex = hex.replace(/^#/, '');

    // Parse hex values
    let bigint = parseInt(hex, 16);
    let r = (bigint >> 16) & 255;
    let g = (bigint >> 8) & 255;
    let b = bigint & 255;

    return { r, g, b };
  };

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isWidgetOpen, setIsWidgetOpen] = useState(!(activeConfig?.startMinimized || false));
  const [showPreChat, setShowPreChat] = useState(activeConfig?.preChat || false);
  const [showPostChat, setShowPostChat] = useState(false);
  const [chatStarted, setChatStarted] = useState(false);
  const [chatEnded, setChatEnded] = useState(false);
  const [userMessage, setUserMessage] = useState("");
  const [messages, setMessages] = useState<any[]>([]);
  const [formData, setFormData] = useState<Record<string, any>>({});

  // Add resize state for custom dimensions
  const [customDimensions, setCustomDimensions] = useState({
    width: 0, // Will be initialized from config
    height: 0, // Will be initialized from config
    isResizing: false,
    resizeType: '' as 'width' | 'height' | 'both' | ''
  });

  // Refs for resize tracking
  const resizeStartPos = useRef({ x: 0, y: 0 });
  const resizeStartDim = useRef({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Get active pre-chat template if available
  const { activeTemplate } = usePreChatForm({
    widgetId: activeConfig?.id ? Number(activeConfig.id) : 0
  });

  // Add this to the component's state variables section
  const [sessionId, setSessionId] = useState<string>(`session-${Date.now()}`);

  // Sync state with config changes
  useEffect(() => {
    // Reset chat state when relevant settings change
    setIsWidgetOpen(!(activeConfig?.behavior?.startMinimized || activeConfig?.startMinimized || false));
    setShowPreChat(activeConfig?.behavior?.preChat || activeConfig?.preChat || false);
    setShowPostChat(false); // Reset post-chat when config changes
    setChatStarted(false); // Reset chat state

    // Initialize messages with welcome message if not already present
    const welcomeMsg = activeConfig?.general?.welcomeMessage || activeConfig?.welcomeMessage || 'Hello! How can I help you today?';
    setMessages([{
      type: 'bot',
      content: welcomeMsg,
      timestamp: new Date().toISOString()
    }]);

    // Initialize custom dimensions from config with a wider default
    const width = activeConfig?.width || activeConfig?.appearance?.widgetWidth || 460; // Increased from 350 to 420
    const height = activeConfig?.height || activeConfig?.appearance?.widgetHeight || 600;
    setCustomDimensions(prev => ({
      ...prev,
      width,
      height,
    }));

    console.log('Widget config updated:', activeConfig); // Debug log
  }, [activeConfig]);

  // Extract settings from both flat and nested config structure with proper fallbacks
  // Support both flat and nested configurations for maximum compatibility
  const general = activeConfig?.general || {};
  const appearance = activeConfig?.appearance || {};
  const behavior = activeConfig?.behavior || {};
  const advanced = activeConfig?.advanced || {};

  // Get theme and apply theme-specific settings
  const theme = activeConfig?.theme || appearance?.theme || 'modern';

  // Apply theme-specific defaults
  const getThemeDefaults = (themeName: string) => {
    switch (themeName) {
      case 'modern':
        return {
          primaryColor: '#7E69AB',
          secondaryColor: '#ffffff',
          borderRadius: 12,
          glassMorphism: false,
          shadow: 'md'
        };
      case 'glass':
        return {
          primaryColor: '#6366f1',
          secondaryColor: '#f8fafc',
          borderRadius: 16,
          glassMorphism: true,
          shadow: 'lg'
        };
      case 'dark':
        return {
          primaryColor: '#10b981',
          secondaryColor: '#1f2937',
          borderRadius: 8,
          glassMorphism: false,
          shadow: 'xl'
        };
      case 'rounded':
        return {
          primaryColor: '#f59e0b',
          secondaryColor: '#fef3c7',
          borderRadius: 20,
          glassMorphism: false,
          shadow: 'md'
        };
      case 'minimal':
        return {
          primaryColor: '#6b7280',
          secondaryColor: '#f9fafb',
          borderRadius: 4,
          glassMorphism: false,
          shadow: 'sm'
        };
      default:
        return {
          primaryColor: '#7E69AB',
          secondaryColor: '#ffffff',
          borderRadius: 12,
          glassMorphism: false,
          shadow: 'md'
        };
    }
  };

  const themeDefaults = getThemeDefaults(theme);

  // Appearance settings with proper fallbacks and theme defaults
  const primaryColor = activeConfig?.primaryColor || appearance?.primaryColor || themeDefaults.primaryColor;
  const secondaryColor = activeConfig?.secondaryColor || appearance?.secondaryColor || themeDefaults.secondaryColor;
  const headerBgColor = activeConfig?.headerBgColor || appearance?.headerBgColor || '#1f2937';
  const textColor = activeConfig?.textColor || appearance?.textColor || '#111827';
  const borderRadius = activeConfig?.borderRadius || appearance?.borderRadius || themeDefaults.borderRadius;
  const widgetWidth = activeConfig?.width || appearance?.widgetWidth || 350;
  const widgetHeight = activeConfig?.height || appearance?.widgetHeight || 600;
  const darkMode = activeConfig?.darkMode || appearance?.darkMode || false;
  const glassMorphism = activeConfig?.glassMorphism || appearance?.glassMorphism || themeDefaults.glassMorphism;
  const shadow = activeConfig?.shadow || appearance?.shadow || themeDefaults.shadow;

  // Logo settings with proper fallbacks
  const showLogo = activeConfig?.showLogo ?? appearance?.showLogo ?? true;
  const logoUrl = activeConfig?.logo_url || advanced?.logoUrl || DEFAULT_AVATAR_URL;

  // Content settings with proper fallbacks
  const botName = activeConfig?.botName || general?.botName || 'AI Assistant';
  const welcomeMessage = activeConfig?.welcomeMessage || general?.welcomeMessage || 'Hello! How can I help you today?';
  const placeholderText = activeConfig?.placeholderText || general?.placeholderText || 'Type your message...';
  const widgetPosition = activeConfig?.position || general?.widgetPosition || 'bottom-right';

  // Behavior settings with proper fallbacks
  const startMinimized = activeConfig?.startMinimized || behavior?.startMinimized || false;
  const autoOpen = activeConfig?.autoOpen || behavior?.autoOpen || false;
  const autoOpenDelay = activeConfig?.autoOpenDelay || behavior?.autoOpenDelay || 5;
  const showTypingIndicator = activeConfig?.showTypingIndicator ?? behavior?.showTypingIndicator ?? true;
  const enableUserRatings = activeConfig?.enableUserRatings ?? behavior?.enableUserRatings ?? false;
  const requireGuestInfo = activeConfig?.requireGuestInfo ?? behavior?.requireGuestInfo ?? false;
  const persistConversation = activeConfig?.persistConversation ?? behavior?.persistConversation ?? false;
  const preChat = activeConfig?.preChat ?? behavior?.preChat ?? false;
  const postChat = activeConfig?.postChat ?? behavior?.postChat ?? false;
  const closeAfterInactivity = activeConfig?.closeAfterInactivity ?? behavior?.closeAfterInactivity ?? false;
  const inactivityTimeout = activeConfig?.inactivityTimeout ?? behavior?.inactivityTimeout ?? 5;

  // Advanced settings with proper fallbacks
  const customCSS = activeConfig?.customCSS || advanced?.customCSS || '';
  const aiModelId = activeConfig?.ai_model_id || advanced?.modelSelection || null;
  const contextRetention = activeConfig?.contextRetention || advanced?.contextRetention || 'session';
  const maxMessagesStored = activeConfig?.maxMessagesStored || advanced?.maxMessagesStored || 100;

  // Button customization
  const buttonCustomization = activeConfig?.buttonCustomization || {
    iconName: activeConfig?.buttonIconName || 'message-circle',
    animation: activeConfig?.buttonAnimation || 'none',
    shape: activeConfig?.buttonShape || 'circle'
  };

  // Typography and styling
  const fontFamily = activeConfig?.fontFamily || 'Inter, sans-serif';

  // Handle pre-chat form submission
  const handlePreChatSubmit = (data: Record<string, string>) => {
    console.log('Pre-chat form submitted:', data); // Debug log

    // Store the form data
    setFormData(data);
    // Hide the pre-chat form
    setShowPreChat(false);
    // Indicate chat has started
    setChatStarted(true);

    // Add a system message showing the user's info
    const userInfo = Object.entries(data)
      .filter(([key, value]) => value && value.trim() !== '')
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');

    setMessages([
      {
        type: 'bot',
        content: welcomeMessage,
        timestamp: new Date().toISOString()
      },
      {
        type: 'system',
        content: `User information collected: ${userInfo || 'No information provided'}`,
        timestamp: new Date().toISOString()
      }
    ]);
  };

  // Handle pre-chat form cancellation (only if not required)
  const handlePreChatCancel = () => {
    console.log('Pre-chat form cancel attempted, requireGuestInfo:', requireGuestInfo); // Debug log

    // If guest info is required, don't allow cancellation
    if (requireGuestInfo) {
      console.log('Pre-chat form is required, cancellation blocked'); // Debug log
      return;
    }

    // Allow cancellation only if not required
    setShowPreChat(false);
    setChatStarted(true);
    setMessages([{
      type: 'bot',
      content: welcomeMessage,
      timestamp: new Date().toISOString()
    }]);
  };

  // Handle post-chat survey submission
  const handlePostChatSubmit = (data: Record<string, any>) => {
    // Store the survey responses
    console.log('Post-chat survey data:', data);
    // Hide the post-chat survey
    setShowPostChat(false);
    // Reset chat state
    setChatEnded(false);
  };

  // Get the icon component to use
  const getIconComponent = () => {
    const iconName = buttonCustomization?.iconName || 'message-circle';
    return ICONS[iconName] || MessageCircle;
  };

  const IconComponent = getIconComponent();

  const getShadowClass = () => {
    switch (shadow) {
      case 'sm': return 'shadow-sm';
      case 'md': return 'shadow-md';
      case 'lg': return 'shadow-lg';
      case 'xl': return 'shadow-xl';
      default: return '';
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const containerStyles = isFullscreen ? {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100vw',
    height: '100vh',
    maxWidth: '100vw',
    maxHeight: '100vh',
    zIndex: 50,
    margin: 0,
    borderRadius: 0,
  } : {
    width: `${customDimensions.width}px`,
    height: `${customDimensions.height}px`,
    borderRadius: `${borderRadius}px`,
    maxWidth: '100%',
    maxHeight: '800px'
  };

  const toggleWidget = () => {
    setIsWidgetOpen(!isWidgetOpen);
  };

  // Handle sending a message
  const handleSendMessage = () => {
    if (!userMessage.trim()) return;

    const timestamp = new Date().toISOString();

    // Add user message to chat
    const newMessages = [...messages, {
      type: 'user',
      content: userMessage,
      timestamp
    }];
    setMessages(newMessages);

    // Clear input
    setUserMessage("");

    // Show typing indicator
    if (showTypingIndicator) {
      setMessages([
        ...newMessages,
        { type: 'typing', timestamp: new Date().toISOString() }
      ]);
    }

    // Simulate bot response after a short delay
    setTimeout(() => {
      // Remove typing indicator if exists
      const messagesWithoutTyping = newMessages.filter(m => m.type !== 'typing');

      const aiModelName = aiModel?.name || 'AI Assistant';
      let responseContent = `Thank you for your message! I'm ${botName}, your AI assistant.`;

      // Add context about the AI model and template being used
      if (aiModel) {
        responseContent += ` I'm powered by ${aiModelName}`;
        if (promptTemplate) {
          responseContent += ` and configured with the "${promptTemplate.name}" template to provide you with the best assistance possible.`;
        } else {
          responseContent += ` and ready to help you with your questions.`;
        }
      } else {
        responseContent += ` This is a preview of how I'll respond to your messages once you've configured an AI model.`;
      }

      setMessages([
        ...messagesWithoutTyping,
        {
          type: 'bot',
          content: responseContent,
          timestamp: new Date().toISOString()
        }
      ]);

      // If user ratings are enabled, show rating options after bot message
      if (enableUserRatings) {
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            {
              type: 'rating-request',
              messageId: `msg-${Date.now()}`,
              timestamp: new Date().toISOString()
            }
          ]);
        }, 500);
      }
    }, showTypingIndicator ? 1500 : 800);
  };

  // Update the handleRateMessage function to actually call the API
  const handleRateMessage = (messageId: string, rating: 'thumbsUp' | 'thumbsDown') => {
    // In a real implementation, this would send the rating to the server
    const messageParts = messageId.split('-');
    const actualMessageId = messageParts.length > 1 ? parseInt(messageParts[1], 10) : 0;

    if (actualMessageId && sessionId) {
      try {
        messageRatingService.rateMessage({
          message_id: actualMessageId,
          rating: rating,
          session_id: sessionId
        });

        // Update the UI regardless of API success to maintain good UX
        setMessages(prev =>
          prev.map(msg =>
            msg.type === 'rating-request' && msg.messageId === messageId
              ? { ...msg, type: 'rating-given', rating }
              : msg
          )
        );
      } catch (error) {
        console.error('Failed to send message rating:', error);
        // Still update UI even on error
        setMessages(prev =>
          prev.map(msg =>
            msg.type === 'rating-request' && msg.messageId === messageId
              ? { ...msg, type: 'rating-given', rating }
              : msg
          )
        );
      }
    } else {
      // For demo purposes without actual message IDs
      setMessages(prev =>
        prev.map(msg =>
          msg.type === 'rating-request' && msg.messageId === messageId
            ? { ...msg, type: 'rating-given', rating }
            : msg
        )
      );
    }
  };

  // Handle ending the chat to show post-chat survey
  const handleEndChat = () => {
    if (postChat) {
      setChatEnded(true);
      setShowPostChat(true);
    }
  };

  // Determine bubble position based on widget position setting and open state
  const getBubblePosition = () => {
    // Position the bubble differently when widget is open
    if (isWidgetOpen) {
      // When widget is open, position the bubble in the middle of the right side of the widget
      switch (widgetPosition || 'bottom-right') {
        case 'top-left':
        case 'bottom-left':
          return { left: 'calc(100% + 10px)', top: '50%', transform: 'translateY(-50%)' };
        case 'top-right':
        case 'bottom-right':
        default:
          return { right: 'calc(100% + 10px)', top: '50%', transform: 'translateY(-50%)' };
      }
    } else {
      // When widget is closed, position the bubble in the corner
      switch (widgetPosition || 'bottom-right') {
        case 'top-left':
          return { left: '20px', top: '20px' };
        case 'top-right':
          return { right: '20px', top: '20px' };
        case 'bottom-left':
          return { left: '20px', bottom: '20px' };
        case 'bottom-right':
        default:
          return { right: '20px', bottom: '20px' };
      }
    }
  };

  // Chat toggle button is always shown when not in fullscreen mode
  const showToggleButton = !isFullscreen;

  // Render chat message based on type
  const renderMessage = (message: any, index: number) => {
    switch (message.type) {
      case 'bot':
        return (
          <div key={index} className="flex items-start mb-4">
            <Avatar className="mr-2 flex-shrink-0 bg-gray-100 dark:bg-gray-700">
              {showLogo ? (
                <>
                  <AvatarImage
                    src={logoUrl}
                    alt={botName}
                    className="object-contain"
                    style={{ padding: '2px' }} // Add padding to ensure logo doesn't touch edges
                    onError={() => {
                      console.log('Avatar logo failed to load in message:', logoUrl);
                      // Error handling is managed by AvatarFallback
                    }}
                  />
                  <AvatarFallback
                    style={{ backgroundColor: primaryColor, color: 'white' }}
                  >
                    {botName.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </>
              ) : (
                <AvatarFallback
                  style={{ backgroundColor: primaryColor, color: 'white' }}
                >
                  {botName.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-2 max-w-[80%]">
              <div className="font-medium text-sm">{botName}</div>
              <div className="text-sm mt-1">{message.content}</div>
              <div className="text-xs text-gray-500 mt-1">
                {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </div>
        );
      case 'user':
        return (
          <div key={index} className="flex items-start justify-end mb-4">
            <div
              className="rounded-lg px-4 py-2 max-w-[80%]"
              style={{ backgroundColor: primaryColor, color: 'white' }}
            >
              <div className="text-sm">{message.content}</div>
              <div className="text-xs opacity-70 mt-1 text-right">
                {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </div>
        );
      case 'system':
        return (
          <div key={index} className="flex justify-center mb-4">
            <div className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg px-4 py-2 max-w-[90%] text-xs">
              {message.content}
            </div>
          </div>
        );
      case 'typing':
        return (
          <div key={index} className="flex items-start mb-4">
            <Avatar className="mr-2 flex-shrink-0 bg-gray-100 dark:bg-gray-700">
              {showLogo ? (
                <>
                  <AvatarImage
                    src={logoUrl}
                    alt={botName}
                    className="object-contain"
                    style={{ padding: '2px' }} // Add padding to ensure logo doesn't touch edges
                    onError={() => {
                      console.log('Avatar logo failed to load in typing indicator:', logoUrl);
                      // Error handling is managed by AvatarFallback
                    }}
                  />
                  <AvatarFallback
                    style={{ backgroundColor: primaryColor, color: 'white' }}
                  >
                    {botName.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </>
              ) : (
                <AvatarFallback
                  style={{ backgroundColor: primaryColor, color: 'white' }}
                >
                  {botName.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-2 max-w-[80%]">
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        );
      case 'rating-request':
        return (
          <div key={index} className="flex justify-center mb-4">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-2 text-center">
              <div className="text-xs text-gray-600 dark:text-gray-300 mb-2">Was this response helpful?</div>
              <div className="flex space-x-2 justify-center">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleRateMessage(message.messageId, 'thumbsUp')}
                  className="h-8 px-2"
                >
                  👍
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleRateMessage(message.messageId, 'thumbsDown')}
                  className="h-8 px-2"
                >
                  👎
                </Button>
              </div>
            </div>
          </div>
        );
      case 'rating-given':
        return (
          <div key={index} className="flex justify-center mb-4">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-2 text-center">
              <div className="text-xs text-gray-600 dark:text-gray-300">
                Thanks for your feedback {message.rating === 'thumbsUp' ? '👍' : '👎'}
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Add resize event handlers
  const startResize = (e: React.MouseEvent, type: 'width' | 'height' | 'both') => {
    e.preventDefault();
    e.stopPropagation();

    // Initialize resize state
    resizeStartPos.current = { x: e.clientX, y: e.clientY };
    resizeStartDim.current = {
      width: customDimensions.width,
      height: customDimensions.height
    };

    setCustomDimensions(prev => ({
      ...prev,
      isResizing: true,
      resizeType: type
    }));

    // Add window event listeners
    window.addEventListener('mousemove', handleResize);
    window.addEventListener('mouseup', stopResize);
  };

  const handleResize = (e: MouseEvent) => {
    if (!customDimensions.isResizing) return;

    const diffX = e.clientX - resizeStartPos.current.x;
    const diffY = e.clientY - resizeStartPos.current.y;

    let newWidth = resizeStartDim.current.width;
    let newHeight = resizeStartDim.current.height;

    if (customDimensions.resizeType === 'width' || customDimensions.resizeType === 'both') {
      newWidth = Math.max(320, resizeStartDim.current.width + diffX); // Increased from 280 to 320
    }

    if (customDimensions.resizeType === 'height' || customDimensions.resizeType === 'both') {
      newHeight = Math.max(400, resizeStartDim.current.height + diffY);
    }

    setCustomDimensions(prev => ({
      ...prev,
      width: newWidth,
      height: newHeight
    }));
  };

  const stopResize = () => {
    setCustomDimensions(prev => ({
      ...prev,
      isResizing: false,
      resizeType: ''
    }));

    window.removeEventListener('mousemove', handleResize);
    window.removeEventListener('mouseup', stopResize);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      window.removeEventListener('mousemove', handleResize);
      window.removeEventListener('mouseup', stopResize);
    };
  }, []);

  // Update initChatSession function to track the session ID if it doesn't already do so
  const initChatSession = () => {
    // Generate a unique session ID if none exists
    if (!sessionId) {
      setSessionId(`session-${Date.now()}`);
    }

    // Add the welcome message
    setMessages([
      {
        type: 'bot',
        content: welcomeMessage,
        timestamp: new Date().toISOString()
      }
    ]);

    setChatStarted(true);
  };

  return (
    <div className="relative" style={{ minHeight: '500px' }} ref={containerRef}>
      {/* Main Widget */}
      <div
        className={`flex flex-col overflow-hidden ${getShadowClass()} transition-all duration-300 ${darkMode ? 'bg-gray-800' : 'bg-white'
          } ${glassMorphism ? 'backdrop-blur-md bg-opacity-80' : ''} relative ${isWidgetOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'}`}
        style={containerStyles}
      >
        {/* AI Model Badge (if applicable) */}
        {aiModel && (
          <div
            className="absolute top-2 left-2 z-20 px-2 py-1 text-xs rounded-full"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              color: 'white'
            }}
          >
            AI: {aiModel.name}
          </div>
        )}

        {/* Widget Header */}
        <div
          className="p-4 flex items-center justify-between"
          style={{
            backgroundColor: headerBgColor,
            borderTopLeftRadius: isFullscreen ? 0 : `${borderRadius}px`,
            borderTopRightRadius: isFullscreen ? 0 : `${borderRadius}px`,
            ...(glassMorphism && { backdropFilter: 'blur(10px)', backgroundColor: `${headerBgColor}cc` })
          }}
        >
          <div className="flex items-center">
            {showLogo ? (
              <div className="w-8 h-8 flex-shrink-0 mr-3 rounded-md overflow-hidden flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                <img
                  src={logoUrl}
                  alt="Widget Logo"
                  className="w-full h-full object-contain"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                  onError={(e) => {
                    // Fallback to initial on error
                    console.log('Logo image failed to load:', logoUrl);
                    e.currentTarget.style.display = 'none';

                    // Create and insert fallback content - a div with the botName initials
                    const container = e.currentTarget.parentElement;
                    if (container) {
                      // Add fallback class for styling
                      container.classList.add('logo-error');

                      // Create a rounded fallback with initials
                      const fallback = document.createElement('div');
                      fallback.className = 'w-full h-full rounded-full flex items-center justify-center';
                      fallback.style.backgroundColor = primaryColor;

                      const initials = document.createElement('span');
                      initials.className = 'text-white text-xs font-bold';
                      initials.textContent = botName.substring(0, 2).toUpperCase();

                      fallback.appendChild(initials);
                      container.appendChild(fallback);
                    }
                  }}
                />
              </div>
            ) : (
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
                style={{ backgroundColor: primaryColor }}
              >
                <IconComponent className="w-5 h-5 text-white" />
              </div>
            )}
            <h3 className="font-semibold text-white">{botName}</h3>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="text-white hover:bg-white/10 rounded-full p-1"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? (
                <Minimize className="w-5 h-5" />
              ) : (
                <Maximize className="w-5 h-5" />
              )}
            </button>
            <button
              className="text-white hover:bg-white/10 rounded-full p-1"
              title="Close"
              onClick={toggleWidget}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Pre-Chat Form - updated with custom scrollbar */}
        {isWidgetOpen && showPreChat && !chatStarted && (
          <div className="flex-1 overflow-y-auto widget-custom-scrollbar">
            <EnhancedPreChatForm
              onSubmit={handlePreChatSubmit}
              onCancel={handlePreChatCancel}
              primaryColor={primaryColor}
              title={activeTemplate?.title || "Welcome! Let's get to know you"}
              description={activeTemplate?.description || "Please share a few details so we can provide you with better assistance."}
              fields={activeTemplate?.fields || []}
              showCancelButton={!requireGuestInfo}
              isRequired={requireGuestInfo}
            />
          </div>
        )}

        {/* Post-Chat Survey - updated with custom scrollbar */}
        {isWidgetOpen && showPostChat && chatEnded && (
          <div className="flex-1 overflow-y-auto widget-custom-scrollbar">
            <PostChatSurvey
              onSubmit={handlePostChatSubmit}
              onCancel={() => {
                setShowPostChat(false);
                setChatEnded(false);
              }}
            />
          </div>
        )}

        {/* Chat Area - Only shown when not showing forms - updated with custom scrollbar */}
        {isWidgetOpen && (!showPreChat || chatStarted) && !showPostChat && (
          <>
            <div
              className={`flex-1 p-4 overflow-y-auto widget-custom-scrollbar ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}
              style={{ color: textColor }}
            >
              {/* Display messages */}
              {messages.length === 0 ? (
                <div className="mb-4 flex">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
                    style={{ backgroundColor: primaryColor }}
                  >
                    <IconComponent className="w-4 h-4 text-white" />
                  </div>
                  <div
                    className={`py-3 px-4 rounded-lg max-w-[80%] ${darkMode ? 'bg-gray-800' : 'bg-white'}`}
                    style={{ borderRadius: `${Math.max(8, borderRadius - 2)}px` }}
                  >
                    <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                      {welcomeMessage}
                      {promptTemplate && (
                        <span className="block text-xs mt-1 opacity-75">
                          Using template: {promptTemplate.name}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              ) : (
                messages.map((message, index) => renderMessage(message, index))
              )}
            </div>

            {/* Input Area */}
            <div
              className={`p-3 border-t ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
              style={{
                borderBottomLeftRadius: isFullscreen ? 0 : `${borderRadius}px`,
                borderBottomRightRadius: isFullscreen ? 0 : `${borderRadius}px`
              }}
            >
              <div className="flex items-center relative gap-2 pr-2">
                <input
                  type="text"
                  placeholder={placeholderText}
                  className={`flex-1 py-2 px-3 text-sm rounded-md border outline-none ${darkMode
                    ? 'bg-gray-700 text-white border-gray-600 focus:border-blue-400'
                    : 'bg-gray-50 text-gray-800 border-gray-300 focus:border-blue-300'
                    }`}
                  value={userMessage}
                  onChange={(e) => setUserMessage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSendMessage();
                    }
                  }}
                />
                <button
                  title="Send"
                  className="flex-shrink-0 p-0 rounded-md flex items-center justify-center transition-colors hover:opacity-90 active:opacity-75"
                  style={{
                    backgroundColor: primaryColor,
                    borderRadius: `${Math.max(6, borderRadius - 4)}px`,
                    minWidth: "36px",
                    height: "36px",
                    width: "36px"
                  }}
                  onClick={handleSendMessage}
                >
                  <Send size={18} className="text-white" />
                </button>
              </div>

              {/* End Chat Button for Post-Chat Survey */}
              {postChat && messages.length > 0 && (
                <div className="flex justify-center mt-2">
                  <button
                    className="text-xs px-3 py-1 rounded-md text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    onClick={handleEndChat}
                  >
                    End Chat & Provide Feedback
                  </button>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Resize Handles (only shown when widget is open and not in fullscreen) */}
      {isWidgetOpen && !isFullscreen && (
        <>
          {/* Right resize handle */}
          <div
            className="absolute cursor-ew-resize w-4 top-0 bottom-0 hover:bg-blue-200/30 active:bg-blue-300/40 transition-colors"
            style={{
              right: -4,
              height: `${customDimensions.height}px`,
              touchAction: 'none'
            }}
            onMouseDown={(e) => startResize(e, 'width')}
          />

          {/* Bottom resize handle */}
          <div
            className="absolute cursor-ns-resize h-4 left-0 right-0 hover:bg-blue-200/30 active:bg-blue-300/40 transition-colors"
            style={{
              bottom: -4,
              width: `${customDimensions.width}px`,
              touchAction: 'none'
            }}
            onMouseDown={(e) => startResize(e, 'height')}
          />

          {/* Corner resize handle */}
          <div
            className="absolute cursor-nwse-resize w-6 h-6 flex items-center justify-center hover:bg-blue-200/30 active:bg-blue-300/40 rounded-br transition-colors"
            style={{
              right: -4,
              bottom: -4,
              touchAction: 'none'
            }}
            onMouseDown={(e) => startResize(e, 'both')}
          >
            <Move className="w-3 h-3 text-gray-500/50" />
          </div>

          {/* Dimension tooltip */}
          <div
            className="absolute px-2 py-1 text-xs bg-gray-800 text-white rounded-md opacity-70 pointer-events-none"
            style={{
              right: 8,
              bottom: -25,
              display: customDimensions.isResizing ? 'block' : 'none'
            }}
          >
            {Math.round(customDimensions.width)} × {Math.round(customDimensions.height)}
          </div>
        </>
      )}

      {/* Chat Toggle Button (Minimized Bubble) */}
      {showToggleButton && (
        <div
          className="absolute cursor-pointer transition-all duration-300 transform hover:scale-110 active:scale-95"
          style={{
            width: isWidgetOpen ? '42px' : '55px',
            height: isWidgetOpen ? '42px' : '55px',
            borderRadius: '50%',
            backgroundColor: primaryColor,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.25)',
            border: isWidgetOpen ? '2px solid white' : 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 100,
            ...getBubblePosition()
          }}
          onClick={toggleWidget}
          role="button"
          aria-label="Toggle chat widget"
          title={isWidgetOpen ? "Close chat" : "Open chat"}
        >
          {isWidgetOpen ? <X className="w-4 h-4 text-white" /> : <IconComponent className="w-6 h-6 text-white" />}
        </div>
      )}
    </div>
  );
};

export default WidgetPreview;
