import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { FileUp, Globe, Database, Wand, Sparkles } from "lucide-react";

interface QuickActionProps {
  onAction: (action: string) => void;
  activeTab: string;
  selectedProjectId: number | null;
}

export function QuickActionPanel({
  onAction,
  activeTab,
  selectedProjectId,
}: QuickActionProps) {
  // Don't show if no project is selected
  if (!selectedProjectId) return null;

  const actions = {
    documents: [
      {
        id: "upload",
        label: "Upload Documents",
        icon: <FileUp className="h-4 w-4" />,
        primary: true,
      },
      {
        id: "generate-embeddings",
        label: "Generate Embeddings",
        icon: <Sparkles className="h-4 w-4" />,
      },
      {
        id: "activate-all",
        label: "Activate All Documents",
        icon: <Wand className="h-4 w-4" />,
      },
    ],
    "web-scraping": [
      {
        id: "new-scrape",
        label: "New Website Scrape",
        icon: <Globe className="h-4 w-4" />,
        primary: true,
      },
      {
        id: "view-history",
        label: "View Scraping History",
        icon: <Database className="h-4 w-4" />,
      },
    ],
    "scheduled-scraping": [
      {
        id: "new-schedule",
        label: "Create Schedule",
        icon: <Wand className="h-4 w-4" />,
        primary: true,
      },
      {
        id: "run-now",
        label: "Run Existing Schedule Now",
        icon: <Sparkles className="h-4 w-4" />,
      },
    ],
    database: [
      {
        id: "connect-db",
        label: "Connect Database",
        icon: <Database className="h-4 w-4" />,
        primary: true,
      },
      {
        id: "sync-tables",
        label: "Sync Selected Tables",
        icon: <Sparkles className="h-4 w-4" />,
      },
    ],
    context: [
      {
        id: "new-rule",
        label: "Create New Rule",
        icon: <Wand className="h-4 w-4" />,
        primary: true,
      },
      {
        id: "test-rules",
        label: "Test Rules with Sample Query",
        icon: <Sparkles className="h-4 w-4" />,
      },
    ],
  };

  const currentActions = actions[activeTab as keyof typeof actions] || [];

  return (
    <Card className="p-4 mb-6 bg-muted/20 border-dashed">
      <div className="flex flex-col sm:flex-row items-center gap-3 justify-center">
        <span className="text-sm font-medium mr-2">Quick Actions:</span>
        {currentActions.map((action) => (
          <Button
            key={action.id}
            onClick={() => onAction(action.id)}
            variant={action.primary ? "default" : "outline"}
            size="sm"
            className="flex items-center gap-2 min-w-[180px] justify-center"
          >
            {action.icon}
            {action.label}
          </Button>
        ))}
      </div>
    </Card>
  );
}
