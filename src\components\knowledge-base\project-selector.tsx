import { useState, useEffect } from 'react'
import { Plus, FolderPlus } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ProjectCreateDialog } from './project-create-dialog'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'

interface ProjectSelectorProps {
  projects: any[]
  selectedProjectId: number | null
  setSelectedProjectId: (id: number) => void
  className?: string
  onRefreshProjects?: () => void
}

export function ProjectSelector({
  projects,
  selectedProjectId,
  setSelectedProjectId,
  className = '',
  onRefreshProjects
}: ProjectSelectorProps) {
  const [showProjectDialog, setShowProjectDialog] = useState(false)
  const [isCreatingDefault, setIsCreatingDefault] = useState(false)

  const handleProjectCreated = (project: any) => {
    if (project && project.id) {
      setSelectedProjectId(project.id)
      toast.success(`Project "${project.name}" created successfully`)
      if (onRefreshProjects) onRefreshProjects()
    }
  }

  const createDefaultProject = async () => {
    try {
      setIsCreatingDefault(true)
      const response = await knowledgeBaseService.createProject({
        name: "My Knowledge Base",
        description: "Default knowledge base for AI assistant"
      })

      if (response?.data?.success) {
        handleProjectCreated(response.data.data)
      } else {
        toast.error("Couldn't create default project. Please try manually.")
      }
    } catch (error) {
      console.error("Error creating default project:", error)
      toast.error("Couldn't create default project. Please try manually.")
    } finally {
      setIsCreatingDefault(false)
    }
  }

  // Show empty state if no projects
  if (projects.length === 0) {
    return (
      <div className={`${className} flex flex-col gap-3 items-start`}>
        <div className="text-sm text-muted-foreground">
          You don't have any knowledge bases yet. Create one to get started.
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowProjectDialog(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" /> Create Custom Knowledge Base
          </Button>
          <Button
            onClick={createDefaultProject}
            disabled={isCreatingDefault}
            className="flex items-center gap-2"
          >
            {isCreatingDefault ? (
              <>Creating...</>
            ) : (
              <>
                <FolderPlus className="h-4 w-4" /> Quick Start with Default
              </>
            )}
          </Button>
        </div>

        <ProjectCreateDialog
          open={showProjectDialog}
          onOpenChange={setShowProjectDialog}
          onProjectCreated={handleProjectCreated}
        />
      </div>
    )
  }

  // Normal selector if we have projects
  return (
    <div className={className}>
      <Select
        value={selectedProjectId ? String(selectedProjectId) : 'no-project'}
        onValueChange={value => {
          if (value === 'create-project') {
            setShowProjectDialog(true)
          } else if (value && value !== 'no-project') {
            setSelectedProjectId(Number(value))
          }
        }}
      >
        <SelectTrigger className="w-full md:w-[250px]">
          <SelectValue placeholder="Select knowledge base" />
        </SelectTrigger>
        <SelectContent>
          {projects.map(project => (
            <SelectItem key={project.id} value={String(project.id)}>
              {project.name}
            </SelectItem>
          ))}

          <Separator className="my-2" />

          <SelectItem value="create-project" className="text-primary">
            <div className="flex items-center">
              <Plus className="h-4 w-4 mr-2" />
              Create New Knowledge Base
            </div>
          </SelectItem>
        </SelectContent>
      </Select>

      <ProjectCreateDialog
        open={showProjectDialog}
        onOpenChange={setShowProjectDialog}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  )
}
