import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Sparkles } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { AdminLayout } from "@/components/admin-layout";

// Import the new Smart Widget Builder
import { SmartWidgetBuilder } from '@/components/smart-widget-builder';
import { Widget } from '@/utils/widgetService';

/**
 * Smart Widget Builder Page
 *
 * This page demonstrates how to use the new SmartWidgetBuilder component
 * alongside the existing WidgetBuilder without conflicts.
 */
const SmartWidgetBuilderPage = () => {
  const navigate = useNavigate();
  const { widgetId } = useParams();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleWidgetSave = async (widget: Widget) => {
    setIsLoading(true);
    try {
      // Widget is already saved by the SmartWidgetBuilder component
      toast({
        title: "✅ Widget Saved Successfully!",
        description: `Your widget "${widget.name}" has been saved and is ready to deploy. Redirecting to widgets list...`,
        className: "bg-green-50 border-green-200 text-green-800",
        duration: 3000,
      });

      // Navigate back to widgets list with a shorter delay for better UX
      setTimeout(() => {
        navigate('/dashboard/widgets');
      }, 1500);
    } catch (error) {
      console.error('Error handling widget save:', error);
      toast({
        title: "❌ Error",
        description: "There was a problem processing your widget. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/widgets');
  };

  const handleBackToOldBuilder = () => {
    // Navigate to the old widget builder if user prefers it
    navigate('/dashboard/widget-builder');
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard/widgets')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Widgets</span>
            </Button>

            <div className="h-6 w-px bg-gray-300" />

            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-bold tracking-tight">
                {widgetId ? 'Edit Widget' : 'Create New Widget'}
              </h1>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <Sparkles className="w-3 h-3 mr-1" />
                Smart Builder
              </Badge>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToOldBuilder}
              className="text-gray-600"
            >
              Use Classic Builder
            </Button>
          </div>
        </div>

        {/* Smart Widget Builder Component */}
        <div className="bg-white rounded-lg border">
          <SmartWidgetBuilder
            widgetId={widgetId}
            onSave={handleWidgetSave}
            onCancel={handleCancel}
          />
        </div>

        {/* Loading overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-900">Processing your widget...</span>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default SmartWidgetBuilderPage;
