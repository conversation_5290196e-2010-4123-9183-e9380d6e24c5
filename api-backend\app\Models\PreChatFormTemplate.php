<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PreChatFormTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'widget_id',
        'title',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the widget that owns the template.
     */
    public function widget()
    {
        return $this->belongsTo(Widget::class);
    }

    /**
     * Get the fields for this template.
     */
    public function fields()
    {
        return $this->hasMany(PreChatFormField::class, 'template_id');
    }

    /**
     * Get the submissions for this template.
     */
    public function submissions()
    {
        return $this->hasMany(PreChatFormSubmission::class, 'template_id');
    }
}
