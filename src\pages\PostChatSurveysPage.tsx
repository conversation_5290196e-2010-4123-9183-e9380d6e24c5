import React from 'react';
import { AdminLayout } from "@/components/admin-layout";
import PostChatSurveys from '@/components/post-chat-surveys/PostChatSurveys';
import PermissionErrorBoundary from '@/components/error-handling/PermissionErrorBoundary';

const PostChatSurveysPage = () => {
  return (
    <AdminLayout>
      <PermissionErrorBoundary 
        feature="widget management"
        paths={['/widgets']}
      >
        <PostChatSurveys />
      </PermissionErrorBoundary>
    </AdminLayout>
  );
};

export default PostChatSurveysPage;
