"use client"

import React, { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { PageHeader } from "@/components/page-header"
import { AdminLayout } from "@/components/admin-layout"
import { Button } from "@/components/ui/button"
import { Loader2, ArrowLeft } from "lucide-react"
import { TemplateForm } from "@/components/templates/template-form"
import { templateService } from "@/utils/template-service"
import { useToast } from "@/components/ui/use-toast"

export default function EditTemplatePage() {
    const navigate = useNavigate()
    const params = useParams()
    const [id, setId] = useState<number | null>(null)
    const { toast } = useToast()
    const [loading, setLoading] = useState<boolean>(true)
    const [template, setTemplate] = useState<any>(null)

    useEffect(() => {
        // Get id from URL params
        const pathId = params.id

        if (pathId && !isNaN(Number(pathId))) {
            setId(Number(pathId))
            loadTemplate(Number(pathId))
        }
    }, [params])

    const loadTemplate = async (templateId: number) => {
        setLoading(true)
        try {
            const response = await templateService.getTemplate(templateId)
            setTemplate(response)
        } catch (error) {
            console.error("Error loading template:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to load template. Please try again."
            })
        } finally {
            setLoading(false)
        }
    }

    const handleSuccess = () => {
        toast({
            title: "Success",
            description: "Template updated successfully."
        })
        navigate("/templates")
    }

    const handleCancel = () => {
        navigate("/templates")
    }

    return (
        <AdminLayout>
            <div className="w-full">
                <PageHeader
                    title="Edit Template"
                    description="Update your prompt template settings"
                    actions={
                        <Button
                            variant="outline"
                            className="flex items-center gap-1"
                            onClick={handleCancel}
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Templates
                        </Button>
                    }
                />

                <div className="mt-6">
                    {loading ? (
                        <div className="flex justify-center py-12">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                    ) : (
                        <TemplateForm
                            templateId={id || undefined}
                            onSuccess={handleSuccess}
                            onCancel={handleCancel}
                        />
                    )}
                </div>
            </div>
        </AdminLayout>
    )
} 