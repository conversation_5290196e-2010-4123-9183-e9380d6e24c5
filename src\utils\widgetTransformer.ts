/**
 * Widget Transformer Utility
 * 
 * This utility provides functions to transform data between the SmartWidgetBuilder form schema
 * and the API schema required by the backend. It handles mapping between different property
 * naming conventions and structures.
 */

import { z } from 'zod';
import { Widget, WidgetSettings } from './widgetService';

// Import the schema from SmartWidgetBuilder to ensure type safety
// This is the same schema used in the SmartWidgetBuilder component
const smartWidgetSchema = z.object({
    // General Settings
    name: z.string().min(1, "Widget name is required"),
    welcomeMessage: z.string().min(1, "Welcome message is required"),
    botName: z.string().default("AI Assistant"),
    placeholderText: z.string().default("Type your message..."),
    presetTheme: z.string().default("modern"),

    // Appearance Settings
    primaryColor: z.string().default("#7E69AB"),
    secondaryColor: z.string().default("#ffffff"),
    headerBgColor: z.string().default("#1f2937"),
    textColor: z.string().default("#111827"),
    theme: z.enum(["modern", "glass", "dark", "rounded", "minimal"]).default("modern"),
    position: z.enum(["bottom-right", "bottom-left", "top-right", "top-left"]).default("bottom-right"),
    borderRadius: z.number().min(0).max(30).default(12),
    width: z.number().min(280).max(500).default(350),
    height: z.number().min(400).max(800).default(600),
    fontSize: z.number().min(12).max(20).default(14),
    animation: z.enum(["none", "fade", "slide", "bounce"]).default("fade"),
    shadow: z.enum(["none", "sm", "md", "lg", "xl"]).default("md"),

    // Features
    features: z.object({
        preChat: z.boolean().default(false),
        postChat: z.boolean().default(false),
        webhooks: z.boolean().default(false),
        domainRestriction: z.boolean().default(false),
        conversationPersistence: z.boolean().default(false),
        mobileOptimization: z.boolean().default(true),
        customCSS: z.boolean().default(false),
        aiModelSelection: z.boolean().default(false),
        logoUpload: z.boolean().default(false),
    }),

    // Behavior Settings
    behavior: z.object({
        autoOpen: z.boolean().default(false),
        autoOpenDelay: z.number().min(0).max(60).default(5),
        showLogo: z.boolean().default(true),
        showCloseButton: z.boolean().default(true),
        startMinimized: z.boolean().default(false),
        closeAfterInactivity: z.boolean().default(false),
        inactivityTimeout: z.number().min(1).max(60).default(10),
        darkMode: z.boolean().default(false),
        glassMorphism: z.boolean().default(false),
        showTypingIndicator: z.boolean().default(true),
        enableUserRatings: z.boolean().default(true),
        collectUserData: z.boolean().default(true),
        persistConversation: z.boolean().default(false),
    }),

    // Advanced Settings
    advanced: z.object({
        modelSelection: z.enum(["gemini", "gpt-4", "huggingface", "auto"]).default("auto"),
        contextRetention: z.enum(["session", "persistent", "none"]).default("session"),
        maxMessagesStored: z.number().min(10).max(1000).default(100),
        enableAnalytics: z.boolean().default(true),
        debugMode: z.boolean().default(false),
        loadTimeoutMs: z.number().min(1000).max(30000).default(5000),
        webhookUrl: z.string().optional(),
        allowedDomains: z.array(z.string()).default([]),
        customCSS: z.string().default(""),
        logoUrl: z.string().default(""),
        customParameters: z.record(z.any()).default({}),
        integrations: z.array(z.object({
            id: z.string(),
            type: z.string(),
            name: z.string(),
            url: z.string(),
            active: z.boolean(),
            events: z.array(z.string()),
            secret: z.string().optional(),
            created_at: z.string()
        })).default([]),
    }),
});

// Type definition for the form values
export type SmartWidgetFormValues = z.infer<typeof smartWidgetSchema>;

/**
 * Transforms SmartWidgetBuilder form values to API format
 * 
 * @param formData Form values from SmartWidgetBuilder
 * @returns Widget data in API format
 */
export function transformFormToApi(formData: SmartWidgetFormValues): Widget {
    // Create settings object with properly nested structure
    const settings: WidgetSettings = {
        // General settings
        welcomeMessage: formData.welcomeMessage,
        headerTitle: formData.botName,
        inputPlaceholder: formData.placeholderText,

        // Appearance settings
        primaryColor: formData.primaryColor,
        secondaryColor: formData.secondaryColor,
        headerBgColor: formData.headerBgColor,
        textColor: formData.textColor,
        theme: formData.theme,
        position: formData.position,
        borderRadius: formData.borderRadius,
        width: formData.width,
        height: formData.height,
        fontSize: formData.fontSize,
        animation: formData.animation,
        shadow: formData.shadow,

        // Behavior properties
        showLogo: formData.behavior.showLogo,
        showCloseButton: formData.behavior.showCloseButton,
        darkMode: formData.behavior.darkMode,
        glassMorphism: formData.behavior.glassMorphism,
        startMinimized: formData.behavior.startMinimized,
        showTypingIndicator: formData.behavior.showTypingIndicator,
        enableUserRatings: formData.behavior.enableUserRatings,
        preChat: formData.features.preChat,
        postChat: formData.features.postChat,
        closeAfterInactivity: formData.behavior.closeAfterInactivity,
        inactivityTimeout: formData.behavior.inactivityTimeout,
        persistConversation: formData.behavior.persistConversation,

        // Advanced settings
        modelSelection: formData.advanced.modelSelection,
        contextRetention: formData.advanced.contextRetention,
        maxMessagesStored: formData.advanced.maxMessagesStored,
        enableAnalytics: formData.advanced.enableAnalytics,
        debugMode: formData.advanced.debugMode,
        loadTimeoutMs: formData.advanced.loadTimeoutMs,
        webhookUrl: formData.advanced.webhookUrl,
        customCSS: formData.advanced.customCSS,
        logoUrl: formData.advanced.logoUrl,
        customParameters: formData.advanced.customParameters,

        // Feature-dependent settings
        integrations: formData.features.webhooks ? formData.advanced.integrations : [],
    };

    // Create the complete widget object
    return {
        name: formData.name,
        settings,
        is_active: true, // Default to active

        // Add domains only if the feature is enabled
        allowed_domains: formData.features.domainRestriction ? formData.advanced.allowedDomains : [],

        // Add behavior settings
        behavior: {
            auto_open: formData.behavior.autoOpen,
            auto_open_delay: formData.behavior.autoOpenDelay,
            start_minimized: formData.behavior.startMinimized,
            show_typing_indicator: formData.behavior.showTypingIndicator,
            enable_user_ratings: formData.behavior.enableUserRatings,
            collect_user_data: formData.behavior.collectUserData,
            persist_conversation: formData.behavior.persistConversation,
            pre_chat_enabled: formData.features.preChat,
            post_chat_enabled: formData.features.postChat,
            close_after_inactivity: formData.behavior.closeAfterInactivity,
            inactivity_timeout: formData.behavior.inactivityTimeout,
            show_logo: formData.behavior.showLogo,
            show_close_button: formData.behavior.showCloseButton,
        },

        // Add custom CSS if enabled
        custom_css: formData.features.customCSS ? formData.advanced.customCSS : '',

        // Add position settings
        position_type: 'fixed', // Default to fixed positioning
        position_settings: {
            position: formData.position,
            offset_x: 20,
            offset_y: 20,
        },

        // Typography settings
        typography: {
            fontSize: formData.fontSize,
        },

        // Button customization
        button_customization: {
            animation: formData.animation,
        },

        // Add mobile settings if optimization is enabled
        mobile_settings: formData.features.mobileOptimization ? {
            responsive: true,
            fullscreen_on_mobile: true,
            hide_on_small_screens: false,
        } : undefined,
    } as Widget;
}

/**
 * Transforms API widget data to SmartWidgetBuilder form values
 * 
 * @param apiData Widget data from API
 * @returns Form values for SmartWidgetBuilder
 */
export function transformApiToForm(apiData: Widget): SmartWidgetFormValues {
    const settings = apiData.settings || {};
    const behavior = apiData.behavior || {};

    // Extract features from various sources
    const hasWebhooks = !!(settings.integrations?.length || settings.webhookUrl);
    const hasDomainRestriction = !!(apiData.allowed_domains && apiData.allowed_domains.length > 0);
    const hasCustomCSS = !!(apiData.custom_css && apiData.custom_css.length > 0);
    const hasLogoUrl = !!(settings.logoUrl || (apiData.logo && apiData.logo.logo_url));
    const hasMobileOptimization = !!(apiData.mobile_settings);

    // Create the form values
    return {
        // General Settings
        name: apiData.name || 'My Chat Widget',
        welcomeMessage: settings.welcomeMessage || 'Hello! How can I help you today?',
        botName: settings.headerTitle || 'AI Assistant',
        placeholderText: settings.inputPlaceholder || 'Type your message...',
        presetTheme: settings.theme || 'modern',

        // Appearance Settings
        primaryColor: settings.primaryColor || '#7E69AB',
        secondaryColor: settings.secondaryColor || '#ffffff',
        headerBgColor: settings.headerBgColor || '#1f2937',
        textColor: settings.textColor || '#111827',
        theme: (settings.theme as any) || 'modern',
        position: (settings.position as any) || 'bottom-right',
        borderRadius: settings.borderRadius || 12,
        width: settings.width || 350,
        height: settings.height || 600,
        fontSize: settings.fontSize || 14,
        animation: (settings.animation as any) || 'fade',
        shadow: (settings.shadow as any) || 'md',

        // Features
        features: {
            preChat: !!settings.preChat || !!behavior.pre_chat_enabled || false,
            postChat: !!settings.postChat || !!behavior.post_chat_enabled || false,
            webhooks: hasWebhooks,
            domainRestriction: hasDomainRestriction,
            conversationPersistence: !!settings.persistConversation || !!behavior.persist_conversation || false,
            mobileOptimization: hasMobileOptimization,
            customCSS: hasCustomCSS,
            aiModelSelection: !!apiData.ai_model_id,
            logoUpload: hasLogoUrl,
        },

        // Behavior Settings
        behavior: {
            autoOpen: !!settings.autoOpen || !!behavior.auto_open || false,
            autoOpenDelay: settings.autoOpenDelay || behavior.auto_open_delay || 5,
            showLogo: settings.showLogo !== false && behavior.show_logo !== false,
            showCloseButton: settings.showCloseButton !== false && behavior.show_close_button !== false,
            startMinimized: !!settings.startMinimized || !!behavior.start_minimized || false,
            closeAfterInactivity: !!settings.closeAfterInactivity || !!behavior.close_after_inactivity || false,
            inactivityTimeout: settings.inactivityTimeout || behavior.inactivity_timeout || 10,
            darkMode: !!settings.darkMode || false,
            glassMorphism: !!settings.glassMorphism || false,
            showTypingIndicator: settings.showTypingIndicator !== false && behavior.show_typing_indicator !== false,
            enableUserRatings: !!settings.enableUserRatings || !!behavior.enable_user_ratings || false,
            collectUserData: !!settings.collectUserData || !!behavior.collect_user_data || false,
            persistConversation: !!settings.persistConversation || !!behavior.persist_conversation || false,
        },

        // Advanced Settings
        advanced: {
            modelSelection: (settings.modelSelection as any) || 'auto',
            contextRetention: (settings.contextRetention as any) || 'session',
            maxMessagesStored: settings.maxMessagesStored || 100,
            enableAnalytics: settings.enableAnalytics !== false,
            debugMode: !!settings.debugMode || false,
            loadTimeoutMs: settings.loadTimeoutMs || 5000,
            webhookUrl: settings.webhookUrl || '',
            allowedDomains: apiData.allowed_domains || [],
            customCSS: apiData.custom_css || settings.customCSS || '',
            logoUrl: settings.logoUrl || (apiData.logo ? apiData.logo.logo_url : ''),
            customParameters: settings.customParameters || {},
            integrations: settings.integrations || [],
        },
    };
} 