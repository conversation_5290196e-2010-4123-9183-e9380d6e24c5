import { z } from 'zod';

// Widget configuration schema
export const widgetConfigSchema = z.object({
    general: z.object({
        name: z.string().min(2, { message: "Widget name must be at least 2 characters." }),
        welcomeMessage: z.string().min(5, { message: "Welcome message must be at least 5 characters." }),
        botName: z.string().min(2, { message: "Bot name must be at least 2 characters." }),
        placeholderText: z.string().optional(),
        widgetPosition: z.enum(["bottom-right", "bottom-left", "top-right", "top-left"]),
        presetTheme: z.string().optional(),
    }),
    appearance: z.object({
        primaryColor: z.string(),
        secondaryColor: z.string(),
        headerBgColor: z.string(),
        textColor: z.string(),
        fontSize: z.number().min(12).max(20),
        borderRadius: z.number().min(0).max(30),
        widgetWidth: z.number().min(280).max(500),
        widgetHeight: z.number().min(400).max(800),
        showLogo: z.boolean(),
        showCloseButton: z.boolean(),
        darkMode: z.boolean(),
        customCSS: z.string().optional(),
        animation: z.enum(["none", "fade", "slide", "bounce"]).default("fade"),
        shadow: z.enum(["none", "sm", "md", "lg", "xl"]).default("md"),
        glassMorphism: z.boolean().default(false),
    }),
    behavior: z.object({
        startMinimized: z.boolean(),
        autoOpen: z.boolean(),
        autoOpenDelay: z.number().min(0).max(60),
        showTypingIndicator: z.boolean(),
        enableUserRatings: z.boolean(),
        preChat: z.boolean().default(false),
        postChat: z.boolean().default(false),
        closeAfterInactivity: z.boolean().default(false),
        inactivityTimeout: z.number().min(1).max(60).default(5),
    }),
    advanced: z.object({
        modelSelection: z.enum(["gemini", "gpt-4", "huggingface", "auto"]),
        contextRetention: z.enum(["session", "persistent", "none"]),
        maxMessagesStored: z.number().min(10).max(1000),
        enableAnalytics: z.boolean(),
        debugMode: z.boolean(),
        loadTimeoutMs: z.number().min(1000).max(30000),
        webhookUrl: z.string().url().optional().or(z.literal('')),
        customParameters: z.record(z.string()).default({}),
    }),
    preChatForm: z.object({
        title: z.string().default("Welcome to Chat"),
        description: z.string().default("Please provide your information to get started"),
        buttonText: z.string().default("Start Chat"),
        required: z.boolean().default(true),
        fields: z.array(z.object({
            id: z.union([z.number(), z.string()]),
            name: z.string(),
            label: z.string(),
            type: z.enum(["text", "email", "phone", "select", "checkbox"]),
            placeholder: z.string().optional(),
            options: z.array(z.string()).optional(),
            isRequired: z.boolean().default(false),
            validationPattern: z.string().optional(),
            errorMessage: z.string().optional(),
            order: z.number().default(0),
        })).default([]),
    }).optional().default({}),
    postChatSurvey: z.object({
        title: z.string().default("How was your experience?"),
        description: z.string().default("Please take a moment to provide feedback"),
        thankYouMessage: z.string().default("Thank you for your feedback!"),
        buttonText: z.string().default("Submit Feedback"),
        showAlways: z.boolean().default(true),
        questions: z.array(z.object({
            id: z.union([z.number(), z.string()]),
            text: z.string(),
            type: z.enum(["rating", "text", "select", "boolean", "checkbox", "multiselect"]),
            options: z.array(z.string()).optional(),
            isRequired: z.boolean().default(false),
            order: z.number().default(0),
        })).default([]),
    }).optional().default({}),
    content: z.object({
        preChatForm: z.any().optional(),
        postChatSurvey: z.any().optional(),
    }).optional().default({}),
});

// Create a type from the schema
export type WidgetConfigFormValues = z.infer<typeof widgetConfigSchema>;

// Widget API types
export interface Widget {
    id: number;
    name: string;
    settings: WidgetSettings;
    is_active: boolean;
    created_at?: string;
    updated_at?: string;
}

export interface WidgetSettings {
    primaryColor: string;
    secondaryColor: string;
    borderRadius: number;
    position: string;
    welcomeMessage: string;
    headerTitle: string;
    inputPlaceholder: string;
    width: number;
    height: number;
    autoOpenDelay: number;
    inactivityTimeout: number;
    maxMessagesStored: number;
    loadTimeoutMs: number;
    customCSS: string;
    webhookUrl: string;
    modelSelection: string;
    contextRetention: string;
    requireGuestInfo: boolean;
    showLogo: boolean;
    showCloseButton: boolean;
    darkMode: boolean;
    glassMorphism: boolean;
    startMinimized: boolean;
    showTypingIndicator: boolean;
    enableUserRatings: boolean;
    preChat: boolean;
    postChat: boolean;
    closeAfterInactivity: boolean;
    enableAnalytics: boolean;
    debugMode: boolean;
    typography?: {
        fontSize: number;
    };
    autoPopSettings?: {
        enabled: boolean;
        timing: number;
    };
    buttonCustomization?: {
        animation: string;
    };
    preChatFormSettings?: {
        enabled: boolean;
        title?: string;
        description?: string;
        buttonText?: string;
        required?: boolean;
        fields?: any[];
    };
    postChatSurveySettings?: {
        enabled: boolean;
        title?: string;
        description?: string;
        thankYouMessage?: string;
        buttonText?: string;
        showAlways?: boolean;
        questions?: any[];
    };
    customParameters?: Record<string, string>;
}

export interface CreateWidgetRequest {
    name: string;
    settings: WidgetSettings;
    is_active: boolean;
}

export interface UpdateWidgetRequest {
    name: string;
    settings: WidgetSettings;
    is_active: boolean;
} 