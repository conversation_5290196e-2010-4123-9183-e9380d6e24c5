import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle, Check, Globe, Plus, Trash, Info } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface DomainRestrictionsProps {
    allowedDomains: string[];
    onChange: (domains: string[]) => void;
}

export function DomainRestrictions({
    allowedDomains = [],
    onChange,
}: DomainRestrictionsProps) {
    const [domains, setDomains] = useState<string[]>(allowedDomains);
    const [newDomain, setNewDomain] = useState("");
    const [error, setError] = useState<string | null>(null);
    const [restrictDomains, setRestrictDomains] = useState(allowedDomains.length > 0);
    const { toast } = useToast();

    // Update parent component when domains change
    useEffect(() => {
        onChange(domains);
    }, [domains, onChange]);

    // Validate domain format
    const isValidDomain = (domain: string): boolean => {
        // Allow wildcards like *.example.com
        if (domain.startsWith('*.')) {
            domain = domain.substring(2);
        }

        // Regular domain validation regex
        const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;
        return domainRegex.test(domain);
    };

    const handleAddDomain = () => {
        if (!newDomain.trim()) {
            setError("Domain cannot be empty");
            return;
        }

        // Check for duplicates
        if (domains.includes(newDomain.trim())) {
            setError("This domain is already in the list");
            return;
        }

        // Validate domain format
        if (!isValidDomain(newDomain.trim()) && newDomain.trim() !== '*') {
            setError("Invalid domain format. Use example.com or *.example.com");
            return;
        }

        // Add the new domain
        const updatedDomains = [...domains, newDomain.trim()];
        setDomains(updatedDomains);
        setNewDomain("");
        setError(null);

        toast({
            title: "Domain added",
            description: `${newDomain.trim()} has been added to allowed domains`,
        });
    };

    const handleRemoveDomain = (domain: string) => {
        const updatedDomains = domains.filter(d => d !== domain);
        setDomains(updatedDomains);

        toast({
            title: "Domain removed",
            description: `${domain} has been removed from allowed domains`,
        });
    };

    const handleToggleRestrictions = (enabled: boolean) => {
        setRestrictDomains(enabled);
        if (!enabled) {
            setDomains([]);
        } else if (domains.length === 0) {
            // Add current domain as default if turning on restrictions
            setDomains(['*']);
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div>
                    <Label htmlFor="restrict-domains" className="text-base">Domain Restrictions</Label>
                    <p className="text-sm text-muted-foreground">
                        Control which websites can embed your widget
                    </p>
                </div>
                <Switch
                    id="restrict-domains"
                    checked={restrictDomains}
                    onCheckedChange={handleToggleRestrictions}
                />
            </div>

            {restrictDomains && (
                <>
                    <Alert className="bg-muted/50">
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                            If no domains are specified, your widget can be embedded anywhere.
                            Add <Badge variant="outline">*</Badge> to allow all domains.
                        </AlertDescription>
                    </Alert>

                    <div className="flex gap-2">
                        <div className="relative flex-1">
                            <Globe className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="example.com or *.example.com"
                                value={newDomain}
                                onChange={(e) => setNewDomain(e.target.value)}
                                className="pl-9"
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                        handleAddDomain();
                                    }
                                }}
                            />
                        </div>
                        <Button
                            onClick={handleAddDomain}
                            disabled={!newDomain.trim()}
                        >
                            <Plus className="h-4 w-4 mr-1" />
                            Add
                        </Button>
                    </div>

                    {error && (
                        <div className="flex items-center text-destructive text-sm">
                            <AlertCircle className="h-4 w-4 mr-1" />
                            {error}
                        </div>
                    )}

                    {domains.length > 0 ? (
                        <div className="border rounded-md">
                            <ScrollArea className="h-40 w-full">
                                <div className="p-4 space-y-2">
                                    {domains.map(domain => (
                                        <div
                                            key={domain}
                                            className="flex items-center justify-between p-2 rounded-md bg-muted/50"
                                        >
                                            <div className="flex items-center">
                                                <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                                                <span className="font-mono text-sm">
                                                    {domain === '*' ? (
                                                        <Badge variant="outline">All Domains</Badge>
                                                    ) : domain.startsWith('*.') ? (
                                                        <>
                                                            <span className="text-primary">*.</span>
                                                            {domain.substring(2)}
                                                        </>
                                                    ) : domain}
                                                </span>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleRemoveDomain(domain)}
                                                className="h-8 w-8 p-0"
                                            >
                                                <Trash className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center border rounded-md p-6 bg-muted/30">
                            <Globe className="h-10 w-10 text-muted-foreground mb-2" />
                            <p className="text-sm text-center text-muted-foreground">
                                No domain restrictions added. Widget can be embedded on any site.
                            </p>
                        </div>
                    )}
                </>
            )}
        </div>
    );
} 