<?php

namespace App\Http\Controllers;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\Widget;
use App\Models\GuestUser;
use App\Services\AIService;
use App\Services\TemplateProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\MessageRating;
use App\Services\WebhookService;
use App\Models\AIModel;

class ChatController extends Controller
{
    protected $aiService;
    protected $templateProcessingService;

    public function __construct(
        AIService $aiService,
        TemplateProcessingService $templateProcessingService
    ) {
        $this->aiService = $aiService;
        $this->templateProcessingService = $templateProcessingService;
    }

    /**
     * Initialize a new chat session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function initSession(Request $request)
    {
        $request->validate([
            'widget_id' => 'required|string|exists:widgets,widget_id',
            'visitor_id' => 'nullable|string',
            'metadata' => 'nullable|array',
            'guest_session_id' => 'nullable|string|exists:guest_users,session_id',
        ]);

        // Find the widget
        $widget = Widget::where('widget_id', $request->widget_id)
                       ->where('is_active', true)
                       ->firstOrFail();

        // Generate session ID and visitor ID if not provided
        $sessionId = Str::uuid()->toString();
        $visitorId = $request->visitor_id ?? ('visitor_' . Str::random(12));

        // Create a new session
        $session = new ChatSession();
        $session->widget_id = $widget->id;
        $session->session_id = $sessionId;
        $session->visitor_id = $visitorId;
        $session->metadata = $request->metadata;
        $session->last_activity_at = now();

        // If a guest session ID is provided, link it to this chat session
        if ($request->has('guest_session_id')) {
            $guestUser = GuestUser::where('session_id', $request->guest_session_id)->first();
            if ($guestUser) {
                $session->visitor_id = $guestUser->session_id;
                $session->metadata = array_merge($session->metadata ?? [], [
                    'guest_user_id' => $guestUser->id,
                    'guest_name' => $guestUser->fullname
                ]);
            }
        }

        $session->save();

        return response()->json([
            'session_id' => $sessionId,
            'visitor_id' => $session->visitor_id,
        ]);
    }

    /**
     * Send a chat message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'message' => 'required|string',
            'metadata' => 'nullable|array',
        ]);

        // Find the session
        $session = ChatSession::where('session_id', $request->session_id)->firstOrFail();

        // Determine the role based on the session (assuming user is always 'user')
        $role = 'user';

        // Save the message
        $chatMessage = new ChatMessage();
        $chatMessage->chat_session_id = $session->id;
        $chatMessage->role = $role;
        $chatMessage->content = $request->message;
        $chatMessage->metadata = $request->metadata;
        $chatMessage->save();

        // Update the session's last activity timestamp
        $session->last_activity_at = now();
        $session->save();

        // Get AI response
        $response = $this->aiService->getAIResponse($session->widget_id, $request->message, $session->session_id);

        // Save the AI response
        $aiChatMessage = new ChatMessage();
        $aiChatMessage->chat_session_id = $session->id;
        $aiChatMessage->role = 'assistant';
        $aiChatMessage->content = $response;
        $aiChatMessage->save();

        return response()->json([
            'message' => $response,
        ]);
    }

    /**
     * Get chat history for a session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistory(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|exists:chat_sessions,session_id',
        ]);

        // Find the session
        $session = ChatSession::where('session_id', $request->session_id)->firstOrFail();

        // Get chat messages
        $chatMessages = ChatMessage::where('chat_session_id', $session->id)
                                    ->orderBy('created_at')
                                    ->get();

        return response()->json($chatMessages);
    }

    /**
     * List all chat sessions (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listSessions(Request $request)
    {
        // Check if the user is an admin
        if (!$request->user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $sessions = ChatSession::with('widget')->get();

        return response()->json($sessions);
    }

    /**
     * Process a chat message
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function processMessage(Request $request): JsonResponse
    {
        $request->validate([
            'messages' => 'required|array',
            'messages.*.role' => 'required|string|in:user,assistant,system,function',
            'messages.*.content' => 'required|string',
            'model_id' => 'nullable|exists:ai_models,id',
            'project_id' => 'nullable|exists:projects,id',
            'use_knowledge_base' => 'nullable|boolean',
            'widget_id' => 'nullable|exists:widgets,id'
        ]);

        $messages = $request->input('messages');
        $modelId = $request->input('model_id');
        $projectId = $request->input('project_id');
        $useKnowledgeBase = $request->input('use_knowledge_base', true);
        $widgetId = $request->input('widget_id');

        try {
            $model = $modelId
                ? AIModel::findOrFail($modelId)
                : AIModel::where('is_default', true)->first();

            if (!$model) {
                return response()->json([
                    'error' => 'No AI model available'
                ], 400);
            }

            // Check if any custom template processing is needed
            $templateId = $request->input('template_id') ?? $model->template_id;

            if ($templateId) {
                try {
                    $processedTemplate = $this->templateProcessingService->processTemplate($templateId, $messages);

                    // If we got a processed template, add it as a system message at the beginning
                    if ($processedTemplate) {
                        array_unshift($messages, [
                            'role' => 'system',
                            'content' => $processedTemplate
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Template processing failed: ' . $e->getMessage(), [
                        'template_id' => $templateId,
                        'error' => $e->getMessage()
                    ]);
                    // Continue without template if processing fails
                }
            }

            // Prepare context for AI processing
            $context = [
                'project_id' => $projectId,
                'use_knowledge_base' => $useKnowledgeBase,
                'widget_id' => $widgetId,
                'user_id' => $request->user()->id ?? null,
                'query_type' => 'chat',
                'tenant_id' => $request->user()->tenant_id ?? null
            ];

            // Process the message with the AI service
            $widgetSettings = null;
            if ($widgetId) {
                $widget = Widget::find($widgetId);
                if ($widget) {
                    $widgetSettings = $widget->settings;

                    // If widget has a project ID and none was specified, use it
                    if (!$projectId && isset($widget->project_id)) {
                        $context['project_id'] = $widget->project_id;
                    }
                }
            }

            $response = $this->aiService->processMessage($messages, $model, $widgetSettings, $context);

            // Log the interaction
            if ($request->user()) {
                // Log authenticated user interaction here if needed
            }

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('Chat processing error: ' . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to process message',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rate a message
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function rateMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message_id' => 'required|integer|exists:chat_messages,id',
            'rating' => 'required|string|in:thumbsUp,thumbsDown',
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'feedback' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Get the message
            $message = ChatMessage::findOrFail($request->message_id);
            $session = ChatSession::where('session_id', $request->session_id)->firstOrFail();
            $widget = Widget::findOrFail($session->widget_id);

            // Save the rating
            $rating = new MessageRating();
            $rating->message_id = $message->id;
            $rating->chat_session_id = $session->id;
            $rating->widget_id = $session->widget_id;
            $rating->rating = $request->rating;
            $rating->feedback = $request->feedback;
            $rating->metadata = [
                'timestamp' => now()->toIso8601String(),
                'user_agent' => $request->header('User-Agent'),
                'ip' => $request->ip()
            ];
            $rating->save();

            // Send webhook notification for rating
            try {
                $webhookService = app(WebhookService::class);
                $webhookService->notifyAllIntegrations($widget, 'rating.submit', [
                    'rating' => $request->rating,
                    'feedback' => $request->feedback,
                    'session_id' => $request->session_id,
                    'message_id' => $request->message_id,
                    'timestamp' => now()->toIso8601String()
                ]);
            } catch (\Exception $e) {
                // Log webhook error but don't fail the request
                Log::error('Webhook notification failed: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Rating submitted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to rate message: ' . $e->getMessage());

            return response()->json([
                'error' => 'Failed to rate message',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred while rating the message.'
            ], 500);
        }
    }

    /**
     * Handle chat messages in the widget
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function message(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'message' => 'required|string|max:4000',
            'metadata' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Find the session
            $session = ChatSession::where('session_id', $request->session_id)->firstOrFail();
            $widget = Widget::find($session->widget_id);

            if (!$widget) {
                return response()->json(['error' => 'Widget not found'], 404);
            }

            // Save the user message
            $userMessage = new ChatMessage();
            $userMessage->chat_session_id = $session->id;
            $userMessage->role = 'user';
            $userMessage->content = $request->message;
            $userMessage->metadata = $request->metadata;
            $userMessage->save();

            // Update the session's last activity timestamp
            $session->last_activity_at = now();
            $session->save();

            // Get conversation history for context
            $history = ChatMessage::where('chat_session_id', $session->id)
                                  ->orderBy('created_at')
                                  ->get();

            // Format messages for the AI service
            $messages = [];
            foreach ($history as $msg) {
                $messages[] = [
                    'role' => $msg->role,
                    'content' => $msg->content
                ];
            }

            // Prepare context for AI processing
            $context = [
                'widget_id' => $widget->id,
                'session_id' => $session->id,
                'project_id' => $widget->project_id ?? null,
                'use_knowledge_base' => $widget->settings['use_knowledge_base'] ?? true,
                'visitor_id' => $session->visitor_id,
                'query_type' => 'widget_chat'
            ];

            // Process with AI service
            $aiModel = null;
            if (!empty($widget->ai_model_id)) {
                $aiModel = AIModel::find($widget->ai_model_id);
            }

            $response = $this->aiService->processMessage($messages, $aiModel, $widget->settings, $context);

            // Save the AI response
            $aiMessage = new ChatMessage();
            $aiMessage->chat_session_id = $session->id;
            $aiMessage->role = 'assistant';
            $aiMessage->content = $response['content'];
            $aiMessage->metadata = $response['metadata'] ?? null;
            $aiMessage->save();

            // Trigger any configured webhooks
            $this->triggerWebhooks($widget, 'message.new', [
                'session_id' => $session->session_id,
                'visitor_id' => $session->visitor_id,
                'message' => $request->message,
                'response' => $response['content'],
                'timestamp' => now()->toIso8601String(),
                'metadata' => $request->metadata
            ]);

            return response()->json([
                'response' => $response['content'],
                'metadata' => $response['metadata'] ?? null,
                'id' => $aiMessage->id
            ]);
        } catch (\Exception $e) {
            Log::error('Error in chat message processing: ' . $e->getMessage(), [
                'session_id' => $request->session_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to process message',
                'message' => 'An error occurred while processing your message.'
            ], 500);
        }
    }

    /**
     * Trigger webhooks for the widget event
     *
     * @param Widget $widget
     * @param string $event
     * @param array $data
     * @return void
     */
    protected function triggerWebhooks(Widget $widget, string $event, array $data): void
    {
        try {
            $webhookService = app(WebhookService::class);
            $webhookService->triggerWidgetWebhooks($widget->id, $event, $data);
        } catch (\Exception $e) {
            Log::error('Webhook trigger error: ' . $e->getMessage(), [
                'widget_id' => $widget->id,
                'event' => $event,
                'error' => $e->getMessage()
            ]);
            // Continue execution even if webhooks fail
        }
    }
}
