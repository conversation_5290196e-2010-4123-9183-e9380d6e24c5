import * as React from "react";

type LocalTheme = "dark" | "light" | "system";

type LocalThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: LocalTheme;
  storageKey?: string;
};

type LocalThemeProviderState = {
  theme: LocalTheme;
  setTheme: (theme: LocalTheme) => void;
};

const initialState: LocalThemeProviderState = {
  theme: "system",
  setTheme: () => null,
};

const LocalThemeProviderContext = React.createContext<LocalThemeProviderState>(initialState);

export function LocalThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "smart-widget-builder-theme",
  ...props
}: LocalThemeProviderProps) {
  const [theme, setTheme] = React.useState<LocalTheme>(
    () => (localStorage.getItem(storageKey) as LocalTheme) || defaultTheme
  );

  const [containerRef, setContainerRef] = React.useState<HTMLDivElement | null>(null);

  React.useEffect(() => {
    if (!containerRef) return;
    
    // Remove existing theme classes from the container
    containerRef.classList.remove("light", "dark");

    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";

      containerRef.classList.add(systemTheme);
      return;
    }

    containerRef.classList.add(theme);
  }, [theme, containerRef]);

  const value = {
    theme,
    setTheme: (theme: LocalTheme) => {
      localStorage.setItem(storageKey, theme);
      setTheme(theme);
    },
  };

  return (
    <LocalThemeProviderContext.Provider {...props} value={value}>
      <div 
        ref={setContainerRef}
        className="smart-widget-builder-theme-container"
        style={{ isolation: 'isolate' }}
      >
        {children}
      </div>
    </LocalThemeProviderContext.Provider>
  );
}

export const useLocalTheme = () => {
  const context = React.useContext(LocalThemeProviderContext);

  if (context === undefined)
    throw new Error("useLocalTheme must be used within a LocalThemeProvider");

  return context;
};
