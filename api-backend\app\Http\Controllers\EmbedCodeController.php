<?php

namespace App\Http\Controllers;

use App\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;

class EmbedCodeController extends Controller
{
    /**
     * Generate embed code for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function generate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'widget_id' => 'required|integer|exists:widgets,id',
            'type' => 'required|string|in:standard,iframe,web-component',
            'allowed_domains' => 'nullable|array',
            'allowed_domains.*' => 'string|max:255|regex:/^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$|^\*\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$|^\*$/',
            'position_type' => 'nullable|string|in:fixed,relative,inline',
            'enable_sri' => 'nullable|boolean',
            'csp_enabled' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Ensure widget belongs to authenticated user
            $widget = Widget::where('id', $request->widget_id)
                        ->where('user_id', $request->user()->id)
                        ->firstOrFail();

            // Update allowed domains if provided
            if ($request->has('allowed_domains')) {
                // Sanitize domain list
                $sanitizedDomains = array_map(function($domain) {
                    return trim($domain);
                }, $request->allowed_domains);

                // Filter out empty domains
                $sanitizedDomains = array_filter($sanitizedDomains);

                // If no valid domains, default to wildcard
                if (empty($sanitizedDomains)) {
                    $sanitizedDomains = ['*'];
                }

                $widget->allowed_domains = $sanitizedDomains;
                $widget->save();
            }

            // Update position type if provided
            if ($request->has('position_type')) {
                $widget->position_type = $request->position_type;
                $widget->save();
            }

            $embedCode = $this->generateEmbedCode(
                $widget,
                $request->type,
                $request->enable_sri ?? false,
                $request->csp_enabled ?? false
            );

            return response()->json([
                'embed_code' => $embedCode,
                'widget' => [
                    'id' => $widget->id,
                    'widget_id' => $widget->widget_id,
                    'version' => $widget->version,
                    'allowed_domains' => $widget->allowed_domains,
                    'position_type' => $widget->position_type,
                ]
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Widget not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Error generating embed code: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate embed code'], 500);
        }
    }

    /**
     * Generate SRI hash for a script URL.
     *
     * @param string $url
     * @return string|null
     */
    private function generateSriHash($url)
    {
        try {
            $response = Http::get($url);
            if ($response->successful()) {
                $content = $response->body();
                $hash = hash('sha384', $content, true);
                return 'sha384-' . base64_encode($hash);
            }
        } catch (\Exception $e) {
            // Log error but continue without SRI if it fails
            \Log::error('Failed to generate SRI hash: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Generate different types of embed codes.
     *
     * @param  \App\Models\Widget  $widget
     * @param  string  $type
     * @param  bool  $enableSri
     * @param  bool  $cspEnabled
     * @return string
     */
    private function generateEmbedCode(Widget $widget, string $type, bool $enableSri = false, bool $cspEnabled = false)
    {
        // Best practice: Always use a dedicated asset domain or the frontend server for widget assets
        $baseUrl = env('WIDGET_ASSET_URL');
        if (!$baseUrl) {
            // Fail fast if not configured
            throw new \RuntimeException('WIDGET_ASSET_URL environment variable must be set to the public asset server (e.g., http://localhost:9090 or your CDN domain)');
        }

        $widgetId = $widget->widget_id;
        $version = $widget->version;

        // Domain restrictions
        $allowedDomains = $widget->allowed_domains ? implode(',', $widget->allowed_domains) : '*';

        // Extract relevant settings for the embed code
        $settings = $widget->settings;
        $primaryColor = $settings['primaryColor'] ?? '#4f46e5';
        $borderRadius = $settings['borderRadius'] ?? '8';

        // SRI hash (for standard and web-component types)
        $sriHash = null;
        if ($enableSri) {
            if ($type === 'standard') {
                $sriHash = $this->generateSriHash($baseUrl . '/widget/v' . $version . '/script.js');
            } elseif ($type === 'web-component') {
                $sriHash = $this->generateSriHash($baseUrl . '/widget/v' . $version . '/web-component.js');
            }
        }

        // Generate appropriate code based on type
        switch ($type) {
            case 'standard':
                $code = '<script src="' . $baseUrl . '/widget/v' . $version . '/script.js"
  data-widget-id="' . $widgetId . '"
  data-primary-color="' . $primaryColor . '"
  data-border-radius="' . $borderRadius . '"
  data-allowed-domains="' . $allowedDomains . '"
  data-persist-conversation="' . ($settings['persistConversation'] ?? 'false') . '"';

                // Add SRI if available
                if ($sriHash) {
                    $code .= "\n  integrity=\"" . $sriHash . "\"
  crossorigin=\"anonymous\"";
                }

                $code .= '
  async>
</script>';

                // Add CSP declaration if enabled
                if ($cspEnabled) {
                    $code = "<!-- Content Security Policy -->
<meta http-equiv=\"Content-Security-Policy\" content=\"script-src 'self' " . $baseUrl . " 'unsafe-inline';\">

" . $code;
                }

                return $code;

            case 'iframe':
                $positionStyles = 'position: fixed; bottom: 20px; right: 20px;';
                if ($widget->position_type === 'relative') {
                    $positionStyles = 'position: relative;';
                } elseif ($widget->position_type === 'inline') {
                    $positionStyles = '';
                }

                return '<iframe
  src="' . $baseUrl . '/widget/v' . $version . '/iframe/' . $widgetId . '"
  id="ai-chat-iframe-' . $widgetId . '"
  style="' . $positionStyles . ' width: 50px; height: 50px; border: none; z-index: 9999;"
  allow="microphone; camera"
  loading="lazy"
  data-allowed-domains="' . $allowedDomains . '"
  title="AI Chat Widget">
</iframe>';

            case 'web-component':
                $code = '<script type="module" src="' . $baseUrl . '/widget/v' . $version . '/web-component.js"';

                // Add SRI if available
                if ($sriHash) {
                    $code .= "\n  integrity=\"" . $sriHash . "\"
  crossorigin=\"anonymous\"";
                }

                $code .= '></script>
<ai-chat-widget
  widget-id="' . $widgetId . '"
  primary-color="' . $primaryColor . '"
  border-radius="' . $borderRadius . '"
  allowed-domains="' . $allowedDomains . '"
  persist-conversation="' . ($settings['persistConversation'] ?? 'false') . '">
</ai-chat-widget>';

                // Add CSP declaration if enabled
                if ($cspEnabled) {
                    $code = "<!-- Content Security Policy -->
<meta http-equiv=\"Content-Security-Policy\" content=\"script-src 'self' " . $baseUrl . " 'unsafe-inline';\">

" . $code;
                }

                return $code;

            default:
                return '';
        }
    }

    /**
     * Validate if a domain is allowed to use the widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function validateDomain(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'widget_id' => 'required|string|exists:widgets,widget_id',
            'domain' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $widget = Widget::where('widget_id', $request->widget_id)->first();

        if (!$widget) {
            return response()->json(['allowed' => false, 'message' => 'Widget not found'], 404);
        }

        $isAllowed = $widget->isDomainAllowed($request->domain);

        return response()->json([
            'allowed' => $isAllowed,
            'message' => $isAllowed ? 'Domain is allowed' : 'Domain is not allowed'
        ]);
    }
}
