<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Test Routes
|--------------------------------------------------------------------------
|
| These routes are used for testing the permission middleware.
| They should not be accessible in production.
|
*/

// Test routes for permission middleware
Route::middleware(['auth:sanctum', 'permission:user.view'])->get('/test-permission-middleware', function () {
    return response()->json(['message' => 'You have access to this route.'], 200);
});

Route::middleware(['auth:sanctum', 'permission:user.edit'])->get('/test-permission-middleware-denied', function () {
    return response()->json(['message' => 'You should not see this message.'], 200);
});