APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=api_backend
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
SAME_SITE_COOKIES=none

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# AI Provider Configuration
# OpenAI
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=your-openai-api-key
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo
OPENAI_TIMEOUT=30
OPENAI_RETRY_ATTEMPTS=3

# Anthropic
ANTHROPIC_API_URL=https://api.anthropic.com/v1
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_DEFAULT_MODEL=claude-3-sonnet-20240229
ANTHROPIC_TIMEOUT=30
ANTHROPIC_RETRY_ATTEMPTS=3

# Google Gemini
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1
GEMINI_API_KEY=your-gemini-api-key
GEMINI_DEFAULT_MODEL=gemini-1.5-pro
GEMINI_TIMEOUT=30
GEMINI_RETRY_ATTEMPTS=3

# Grok
GROK_API_URL=https://api.grok.x.com/v1
GROK_API_KEY=your-grok-api-key
GROK_DEFAULT_MODEL=grok-1
GROK_TIMEOUT=30
GROK_RETRY_ATTEMPTS=3

# Hugging Face
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models
HUGGINGFACE_API_KEY=your-huggingface-api-key
HUGGINGFACE_DEFAULT_MODEL=meta-llama/Llama-2-70b-chat-hf
HUGGINGFACE_TIMEOUT=60
HUGGINGFACE_RETRY_ATTEMPTS=3

# OpenRouter
OPENROUTER_API_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo
OPENROUTER_TIMEOUT=30
OPENROUTER_RETRY_ATTEMPTS=3

# Mistral
MISTRAL_API_URL=https://api.mistral.ai/v1
MISTRAL_API_KEY=your-mistral-api-key
MISTRAL_DEFAULT_MODEL=mistral-large-latest
MISTRAL_TIMEOUT=30
MISTRAL_RETRY_ATTEMPTS=3

# DeepSeek
DEEPSEEK_API_URL=https://api.deepseek.com/v1
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_DEFAULT_MODEL=deepseek-chat
DEEPSEEK_TIMEOUT=30
DEEPSEEK_RETRY_ATTEMPTS=3

# Cohere
COHERE_API_URL=https://api.cohere.ai/v1
COHERE_API_KEY=your-cohere-api-key
COHERE_DEFAULT_MODEL=command
COHERE_TIMEOUT=30
COHERE_RETRY_ATTEMPTS=3
