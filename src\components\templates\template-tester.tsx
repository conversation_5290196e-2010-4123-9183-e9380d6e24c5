"use client"

import React, { useState, useEffect } from "react"
import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON><PERSON>oot<PERSON>,
    CardHeader,
    CardTitle,
    CardDescription
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Zap, RefreshCw } from "lucide-react"
import { templateService } from "@/utils/template-service"
import templateProcessingService, {
    extractTemplateVariables,
    processTemplateContent,
    initializeVariableValues,
    isNotFunction
} from "@/services/template-processing-service"

interface TemplateTestProps {
    templateId: number
    templateContent?: string
    variables?: string[]
    predefinedValues?: Record<string, unknown>
}

export function TemplateTester({
    templateId,
    templateContent,
    variables = [],
    predefinedValues = {}
}: TemplateTestProps) {
    const { toast } = useToast()
    const [loading, setLoading] = useState<boolean>(false)
    const [processedTemplate, setProcessedTemplate] = useState<string>("")
    const [variableValues, setVariableValues] = useState<Record<string, string>>({})
    const [content, setContent] = useState<string>(templateContent || "")
    const [templateVars, setTemplateVars] = useState<string[]>(variables || [])

    // Initialize with predefined values if provided
    useEffect(() => {
        if (Object.keys(predefinedValues).length > 0) {
            const processedValues = templateProcessingService.processVariableValues(predefinedValues);
            setVariableValues(prev => ({
                ...prev,
                ...processedValues
            }));
        }
    }, [predefinedValues]);

    useEffect(() => {
        if (templateId && !templateContent) {
            loadTemplate(templateId)
        } else if (templateContent) {
            setContent(templateContent)
            extractVariables(templateContent)
        }
    }, [templateId, templateContent])

    useEffect(() => {
        if (variables && variables.length > 0) {
            setTemplateVars(variables)
            initVariableValues(variables)
        }
    }, [variables])

    const loadTemplate = async (id: number) => {
        setLoading(true)
        try {
            const response = await templateService.getTemplate(id)
            if (response) {
                setContent(response.content || "")

                if (response.variables && response.variables.length > 0) {
                    setTemplateVars(response.variables)
                    initVariableValues(response.variables)
                } else {
                    extractVariables(response.content)
                }
            }
        } catch (error) {
            console.error("Error loading template:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to load template. Please try again."
            })
        } finally {
            setLoading(false)
        }
    }

    const extractVariables = (templateText: string) => {
        const extractedVars = extractTemplateVariables(templateText);
        setTemplateVars(extractedVars);
        initVariableValues(extractedVars);
    }

    const initVariableValues = (vars: string[]) => {
        const initialValues = initializeVariableValues(vars, variableValues, predefinedValues);
        setVariableValues(initialValues);
    }

    const handleVariableChange = (variable: string, value: string) => {
        setVariableValues(prev => ({
            ...prev,
            [variable]: value
        }))
    }

    const processTemplate = () => {
        setLoading(true)

        try {
            const result = processTemplateContent(content, variableValues);
            setProcessedTemplate(result);
        } catch (error) {
            console.error("Error processing template:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to process template. Please check your syntax."
            })
        } finally {
            setLoading(false)
        }
    }

    const resetValues = () => {
        initVariableValues(templateVars)
        toast({
            title: "Reset",
            description: "Variable values have been reset to sample values."
        })
    }

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Template Tester
                </CardTitle>
                <CardDescription>
                    Test your template by providing values for variables.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <Tabs defaultValue="variables">
                    <TabsList className="mb-4">
                        <TabsTrigger value="variables">Variables</TabsTrigger>
                        <TabsTrigger value="preview">Preview</TabsTrigger>
                    </TabsList>

                    <TabsContent value="variables" className="space-y-4">
                        {loading ? (
                            <div className="flex justify-center py-8">
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            </div>
                        ) : (
                            <>
                                <div className="flex justify-end">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={resetValues}
                                        className="flex items-center gap-1"
                                    >
                                        <RefreshCw className="h-4 w-4" />
                                        Reset Values
                                    </Button>
                                </div>

                                {templateVars.length > 0 ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {templateVars.map(variable => (
                                            <div key={variable} className="space-y-2">
                                                <Label htmlFor={`var-${variable}`}>{variable}</Label>
                                                <Input
                                                    id={`var-${variable}`}
                                                    value={variableValues[variable] || ""}
                                                    onChange={(e) => handleVariableChange(variable, e.target.value)}
                                                    placeholder={`Value for ${variable}`}
                                                />
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <p className="text-gray-500">
                                            No variables found in this template.
                                        </p>
                                    </div>
                                )}
                            </>
                        )}
                    </TabsContent>

                    <TabsContent value="preview" className="space-y-4">
                        {processedTemplate ? (
                            <>
                                <div className="mb-4">
                                    <Label>Processed Template</Label>
                                    <div className="mt-2 p-4 border rounded-md bg-gray-50 whitespace-pre-wrap">
                                        {processedTemplate}
                                    </div>
                                </div>

                                <div>
                                    <Label>Original Template</Label>
                                    <div className="mt-2 p-4 border rounded-md bg-gray-50 whitespace-pre-wrap text-gray-500">
                                        {content}
                                    </div>
                                </div>
                            </>
                        ) : (
                            <div className="py-8 text-center">
                                <p className="text-gray-500">
                                    Click "Process Template" to see the result.
                                </p>
                            </div>
                        )}
                    </TabsContent>
                </Tabs>
            </CardContent>
            <CardFooter className="flex justify-end">
                <Button
                    onClick={processTemplate}
                    disabled={loading}
                >
                    {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Process Template
                </Button>
            </CardFooter>
        </Card>
    )
} 