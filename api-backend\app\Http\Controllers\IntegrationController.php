<?php

namespace App\Http\Controllers;

use App\Models\Widget;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class IntegrationController extends Controller
{
    protected $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Test an integration configuration
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testIntegration(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:slack,ms-teams,discord,zapier,generic',
            'url' => 'required|url',
            'name' => 'required|string|max:255',
            'events' => 'required|array',
            'events.*' => 'string|in:message.new,rating.submit,session.start,session.end',
            'secret' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create test integration config
            $integration = [
                'id' => 'test-' . time(),
                'type' => $request->type,
                'name' => $request->name,
                'url' => $request->url,
                'active' => true,
                'events' => $request->events,
                'secret' => $request->secret,
                'created_at' => now()->toIso8601String()
            ];

            // Generate test payload based on integration type
            $testPayload = $this->generateTestPayload($request->type);

            // Test the integration
            $result = $this->webhookService->testIntegration($integration, $testPayload);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Integration test failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to test integration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a test payload based on integration type
     */
    private function generateTestPayload(string $integrationType): array
    {
        $timestamp = now()->toIso8601String();
        $sessionId = 'test-session-' . time();

        switch ($integrationType) {
            case 'slack':
                // Slack requires a text field or properly formatted blocks
                return [
                    'text' => 'This is a test message from your chat widget',
                    'username' => 'Test User',
                    'icon_emoji' => ':bell:',
                    'blocks' => [
                        [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => '*Test Message*\nThis is a test message from your chat widget.'
                            ]
                        ],
                        [
                            'type' => 'context',
                            'elements' => [
                                [
                                    'type' => 'mrkdwn',
                                    'text' => "Sent by: Test User • $timestamp"
                                ]
                            ]
                        ]
                    ]
                ];

            case 'ms-teams':
                // Teams uses MessageCard format
                return [
                    '@type' => 'MessageCard',
                    '@context' => 'http://schema.org/extensions',
                    'themeColor' => '0076D7',
                    'summary' => 'Test Message',
                    'sections' => [
                        [
                            'activityTitle' => 'Test Notification',
                            'activitySubtitle' => 'From Chat Widget',
                            'text' => 'This is a test message from your chat widget',
                            'facts' => [
                                ['name' => 'User', 'value' => 'Test User'],
                                ['name' => 'Time', 'value' => $timestamp]
                            ]
                        ]
                    ]
                ];

            case 'discord':
                // Discord uses embeds for rich messages
                return [
                    'content' => 'This is a test message from your chat widget',
                    'username' => 'Chat Widget',
                    'embeds' => [
                        [
                            'title' => 'Test Notification',
                            'description' => 'This is a test message from your chat widget',
                            'color' => 3447003,
                            'fields' => [
                                ['name' => 'User', 'value' => 'Test User', 'inline' => true],
                                ['name' => 'Session', 'value' => $sessionId, 'inline' => true]
                            ],
                            'timestamp' => $timestamp
                        ]
                    ]
                ];

            case 'zapier':
                // Zapier prefers flat data structure
                return [
                    'event_type' => 'test',
                    'timestamp' => $timestamp,
                    'data' => [
                        'message' => 'This is a test message from your chat widget',
                        'user_name' => 'Test User',
                        'session_id' => $sessionId
                    ]
                ];

            default:
                // Generic webhook format
                return [
                    'event_type' => 'test',
                    'timestamp' => $timestamp,
                    'integration_type' => $integrationType,
                    'message' => 'This is a test message from your chat widget',
                    'user_name' => 'Test User',
                    'session_id' => $sessionId
                ];
        }
    }

    /**
     * Send a notification to an integration
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendNotification(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'integration' => 'required|array',
            'integration.id' => 'required|string',
            'integration.type' => 'required|string|in:slack,ms-teams,discord,zapier,generic',
            'integration.url' => 'required|url',
            'integration.active' => 'required|boolean',
            'integration.events' => 'required|array',
            'integration.events.*' => 'string',
            'event_type' => 'required|string',
            'payload' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->webhookService->sendNotification(
                $request->integration,
                $request->event_type,
                $request->payload
            );

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Failed to send notification: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available integration types
     *
     * @return JsonResponse
     */
    public function getIntegrationTypes(): JsonResponse
    {
        return response()->json([
            'types' => [
                [
                    'id' => 'slack',
                    'name' => 'Slack',
                    'description' => 'Send notifications to your Slack channel',
                    'icon' => '/assets/integrations/slack-icon.svg',
                    'help_url' => 'https://api.slack.com/messaging/webhooks'
                ],
                [
                    'id' => 'ms-teams',
                    'name' => 'Microsoft Teams',
                    'description' => 'Send notifications to your Microsoft Teams channel',
                    'icon' => '/assets/integrations/teams-icon.svg',
                    'help_url' => 'https://learn.microsoft.com/en-us/microsoftteams/platform/webhooks-and-connectors/how-to/add-incoming-webhook'
                ],
                [
                    'id' => 'discord',
                    'name' => 'Discord',
                    'description' => 'Send notifications to your Discord server',
                    'icon' => '/assets/integrations/discord-icon.svg',
                    'help_url' => 'https://support.discord.com/hc/en-us/articles/228383668-Intro-to-Webhooks'
                ],
                [
                    'id' => 'zapier',
                    'name' => 'Zapier',
                    'description' => 'Connect your chat widget to thousands of apps with Zapier',
                    'icon' => '/assets/integrations/zapier-icon.svg',
                    'help_url' => 'https://zapier.com/apps/webhook/help'
                ],
                [
                    'id' => 'generic',
                    'name' => 'Custom Webhook',
                    'description' => 'Connect to any custom endpoint that accepts webhook data',
                    'icon' => '/assets/integrations/webhook-icon.svg',
                    'help_url' => 'https://en.wikipedia.org/wiki/Webhook'
                ]
            ]
        ]);
    }
}
