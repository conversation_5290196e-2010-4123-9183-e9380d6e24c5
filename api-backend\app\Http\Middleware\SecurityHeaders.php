<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Set basic security headers for all environments
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Apply Content-Security-Policy based on environment
        // Development uses a more permissive policy, production is stricter
        $cspValue = app()->environment('production')
            ? $this->getProductionCspValue()
            : $this->getDevelopmentCspValue();

        $response->headers->set('Content-Security-Policy', $cspValue);

        return $response;
    }

    /**
     * Get CSP value for production environment.
     *
     * @return string
     */
    private function getProductionCspValue(): string
    {
        return "default-src 'self'; " .
               "script-src 'self'; " .
               "style-src 'self'; " .
               "img-src 'self' data:; " .
               "font-src 'self' data:; " .
               "connect-src 'self' " . env('API_URL', '') . "; " .
               "frame-ancestors 'self'; " .
               "form-action 'self';";
    }

    /**
     * Get CSP value for development environment.
     *
     * @return string
     */
    private function getDevelopmentCspValue(): string
    {
        return "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " .
               "style-src 'self' 'unsafe-inline'; " .
               "img-src 'self' data:; " .
               "font-src 'self' data:; " .
               "connect-src 'self' *; " .
               "frame-ancestors 'self'; " .
               "form-action 'self';";
    }
}
