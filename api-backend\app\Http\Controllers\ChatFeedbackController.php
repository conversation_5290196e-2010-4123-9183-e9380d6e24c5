<?php

namespace App\Http\Controllers;

use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\MessageRating;
use App\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ChatFeedbackController extends Controller
{
    /**
     * Store a new message rating.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storeRating(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string',
            'widget_id' => 'required|string|exists:widgets,widget_id',
            'message_id' => 'required|string',
            'rating' => 'required|string|in:thumbsUp,thumbsDown',
            'feedback' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Find the chat session
            $session = ChatSession::where('session_id', $request->session_id)->first();
            
            if (!$session) {
                return response()->json(['error' => 'Chat session not found'], 404);
            }

            // Find the message
            $message = ChatMessage::where('message_id', $request->message_id)
                ->where('session_id', $session->id)
                ->first();
            
            if (!$message) {
                return response()->json(['error' => 'Message not found'], 404);
            }

            // Create or update the rating
            $rating = MessageRating::updateOrCreate(
                [
                    'message_id' => $message->id,
                ],
                [
                    'rating' => $request->rating,
                    'feedback' => $request->feedback,
                    'widget_id' => Widget::where('widget_id', $request->widget_id)->first()->id,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Rating saved successfully',
                'data' => $rating
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving message rating: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save rating'], 500);
        }
    }

    /**
     * Get ratings for a specific widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $widgetId
     * @return \Illuminate\Http\Response
     */
    public function getWidgetRatings(Request $request, $widgetId)
    {
        try {
            $widget = Widget::where('widget_id', $widgetId)->first();
            
            if (!$widget) {
                return response()->json(['error' => 'Widget not found'], 404);
            }

            $ratings = MessageRating::where('widget_id', $widget->id)
                ->with('message')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return response()->json([
                'success' => true,
                'data' => $ratings
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving widget ratings: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to retrieve ratings'], 500);
        }
    }

    /**
     * Get rating analytics for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $widgetId
     * @return \Illuminate\Http\Response
     */
    public function getRatingAnalytics(Request $request, $widgetId)
    {
        try {
            $widget = Widget::where('widget_id', $widgetId)->first();
            
            if (!$widget) {
                return response()->json(['error' => 'Widget not found'], 404);
            }

            $totalRatings = MessageRating::where('widget_id', $widget->id)->count();
            $positiveRatings = MessageRating::where('widget_id', $widget->id)
                ->where('rating', 'thumbsUp')
                ->count();
            $negativeRatings = MessageRating::where('widget_id', $widget->id)
                ->where('rating', 'thumbsDown')
                ->count();

            $positivePercentage = $totalRatings > 0 ? ($positiveRatings / $totalRatings) * 100 : 0;
            $negativePercentage = $totalRatings > 0 ? ($negativeRatings / $totalRatings) * 100 : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_ratings' => $totalRatings,
                    'positive_ratings' => $positiveRatings,
                    'negative_ratings' => $negativeRatings,
                    'positive_percentage' => round($positivePercentage, 2),
                    'negative_percentage' => round($negativePercentage, 2),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving rating analytics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to retrieve rating analytics'], 500);
        }
    }
}
