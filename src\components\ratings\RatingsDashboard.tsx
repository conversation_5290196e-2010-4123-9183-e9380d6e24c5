import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ThumbsUp, ThumbsDown, MessageSquare, BarChart3 } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import { Progress } from '@/components/ui/progress'
import { widgetService } from '@/services/widget-service'

interface Rating {
  id: number
  message_id: number
  rating: string
  feedback: string | null
  created_at: string
  message: {
    content: string
    role: string
  }
}

interface RatingAnalytics {
  total_ratings: number
  positive_ratings: number
  negative_ratings: number
  positive_percentage: number
  negative_percentage: number
}

export default function RatingsDashboard() {
  const { widgetId } = useParams<{ widgetId: string }>()
  const [ratings, setRatings] = useState<Rating[]>([])
  const [analytics, setAnalytics] = useState<RatingAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!widgetId) return

    const fetchRatings = async () => {
      try {
        setLoading(true)
        const response = await widgetService.getWidgetRatings(widgetId)
        setRatings(response.data)
        
        const analyticsResponse = await widgetService.getRatingAnalytics(widgetId)
        setAnalytics(analyticsResponse.data)
        
        setError(null)
      } catch (err) {
        console.error('Error fetching ratings:', err)
        setError('Failed to load ratings. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchRatings()
  }, [widgetId])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  const truncateText = (text: string, maxLength = 100) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Message Ratings</h1>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="ratings">
            <MessageSquare className="h-4 w-4 mr-2" />
            All Ratings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Total Ratings</CardTitle>
                <CardDescription>All-time ratings count</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <Skeleton className="h-12 w-24" />
                ) : (
                  <div className="text-3xl font-bold">{analytics?.total_ratings || 0}</div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <ThumbsUp className="h-4 w-4 mr-2 text-green-500" />
                  Positive Ratings
                </CardTitle>
                <CardDescription>Thumbs up count</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <Skeleton className="h-12 w-24" />
                ) : (
                  <div className="text-3xl font-bold text-green-500">
                    {analytics?.positive_ratings || 0}
                    <span className="text-sm text-muted-foreground ml-2">
                      ({analytics?.positive_percentage.toFixed(1) || 0}%)
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <ThumbsDown className="h-4 w-4 mr-2 text-red-500" />
                  Negative Ratings
                </CardTitle>
                <CardDescription>Thumbs down count</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <Skeleton className="h-12 w-24" />
                ) : (
                  <div className="text-3xl font-bold text-red-500">
                    {analytics?.negative_ratings || 0}
                    <span className="text-sm text-muted-foreground ml-2">
                      ({analytics?.negative_percentage.toFixed(1) || 0}%)
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Rating Distribution</CardTitle>
              <CardDescription>Positive vs. negative feedback</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-full" />
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="flex items-center">
                      <ThumbsUp className="h-4 w-4 mr-1 text-green-500" /> Positive
                    </span>
                    <span>{analytics?.positive_percentage.toFixed(1) || 0}%</span>
                  </div>
                  <Progress value={analytics?.positive_percentage || 0} className="h-2 bg-gray-200" />
                  
                  <div className="flex justify-between text-sm mt-4">
                    <span className="flex items-center">
                      <ThumbsDown className="h-4 w-4 mr-1 text-red-500" /> Negative
                    </span>
                    <span>{analytics?.negative_percentage.toFixed(1) || 0}%</span>
                  </div>
                  <Progress value={analytics?.negative_percentage || 0} className="h-2 bg-gray-200" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ratings" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>All Message Ratings</CardTitle>
              <CardDescription>
                User feedback on AI responses
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : error ? (
                <div className="text-center py-4 text-red-500">{error}</div>
              ) : ratings.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">No ratings found</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Rating</TableHead>
                      <TableHead>Feedback</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {ratings.map((rating) => (
                      <TableRow key={rating.id}>
                        <TableCell className="whitespace-nowrap">
                          {formatDate(rating.created_at)}
                        </TableCell>
                        <TableCell className="max-w-md">
                          {truncateText(rating.message?.content || 'N/A')}
                        </TableCell>
                        <TableCell>
                          {rating.rating === 'thumbsUp' ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              <ThumbsUp className="h-3 w-3 mr-1" /> Positive
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                              <ThumbsDown className="h-3 w-3 mr-1" /> Negative
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {rating.feedback || <span className="text-muted-foreground italic">No feedback</span>}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
