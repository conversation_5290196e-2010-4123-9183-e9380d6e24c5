import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import {
  setupGlobalErrorHandler,
  setupAxiosErrorInterceptor,
} from "@/utils/api-error-handler";
import api from "@/utils/api";
import { TempoDevtools } from "tempo-devtools";

// Set up global error handling
setupGlobalErrorHandler();

// Set up axios interceptors for API error handling
setupAxiosErrorInterceptor(api);

// Initialize Tempo Devtools
TempoDevtools.init();

createRoot(document.getElementById("root")!).render(<App />);
