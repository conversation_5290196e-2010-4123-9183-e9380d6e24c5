import { useState, useEffect } from "react"
import { use<PERSON>arams } from "react-router-dom"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger
} from "@/components/ui/tabs"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel
} from "@/components/ui/form"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  FileText,
  Settings,
  Eye,
  HelpCircle,
  Info,
  CheckCircle2
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { PreChatFormTemplates } from "@/components/pre-chat-form/PreChatFormTemplates"
import { EnhancedPreChatForm } from "@/components/pre-chat-form/enhanced-pre-chat-form"
import { usePreChatForm } from "@/hooks/use-pre-chat-form"
import { useWidgetSettings } from "@/hooks/use-widget-settings"

// Form schema for pre-chat settings
const settingsSchema = z.object({
  enabled: z.boolean().default(false),
  required: z.boolean().default(false),
  collectName: z.boolean().default(true),
  collectEmail: z.boolean().default(true),
  collectPhone: z.boolean().default(false),
  collectMessage: z.boolean().default(true),
})

type SettingsFormValues = z.infer<typeof settingsSchema>

/**
 * Pre-Chat Form Settings Page
 *
 * A user-friendly page for managing pre-chat form settings and templates.
 * Designed for non-technical users with intuitive controls and visual feedback.
 */
export default function PreChatSettings() {
  const params = useParams()
  const widgetId = Number(params.id)
  const [activeTab, setActiveTab] = useState("settings")

  const { toast } = useToast()
  const {
    widgetSettings,
    updateWidgetSettings,
    isLoading: isWidgetLoading
  } = useWidgetSettings()

  const {
    activeTemplate,
    isLoading: isTemplateLoading
  } = usePreChatForm({ widgetId })

  // Set up form with default values from widget settings
  const form = useForm<SettingsFormValues>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      enabled: widgetSettings?.preChat || false,
      required: widgetSettings?.requireGuestInfo || false,
      collectName: true,
      collectEmail: true,
      collectPhone: false,
      collectMessage: true,
    }
  })

  // Update form values when widget settings change
  useEffect(() => {
    if (widgetSettings) {
      form.reset({
        enabled: widgetSettings.preChat || false,
        required: widgetSettings.requireGuestInfo || false,
        collectName: widgetSettings.preChatSettings?.collectName !== false,
        collectEmail: widgetSettings.preChatSettings?.collectEmail !== false,
        collectPhone: widgetSettings.preChatSettings?.collectPhone || false,
        collectMessage: widgetSettings.preChatSettings?.collectMessage !== false,
      })
    }
  }, [widgetSettings, form])

  // Handle form submission
  const onSubmit = async (data: SettingsFormValues) => {
    try {
      await updateWidgetSettings({
        preChat: data.enabled,
        requireGuestInfo: data.required,
        preChatSettings: {
          collectName: data.collectName,
          collectEmail: data.collectEmail,
          collectPhone: data.collectPhone,
          collectMessage: data.collectMessage,
          templateId: activeTemplate?.id
        }
      })

      toast({
        title: "Settings saved",
        description: "Pre-chat form settings have been updated successfully.",
        variant: "default",
      })
    } catch (error) {
      console.error("Error saving settings:", error)
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle toggling the required field setting
  const handleRequiredToggle = (value: boolean) => {
    form.setValue("required", value)
  }

  // Loading state
  if (isWidgetLoading || isTemplateLoading) {
    return (
      <div className="container py-6">
        <div className="flex justify-center p-12">
          <div className="h-8 w-8 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span>Templates</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="settings" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Pre-Chat Form Settings</CardTitle>
                    <CardDescription>
                      Configure how the pre-chat form behaves
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                          control={form.control}
                          name="enabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Enable Pre-Chat Form</FormLabel>
                                <FormDescription>
                                  Show a form to collect visitor information before starting a chat
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        {form.watch("enabled") && (
                          <>
                            <Separator />

                            <FormField
                              control={form.control}
                              name="required"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">Required Information</FormLabel>
                                    <FormDescription>
                                      Visitors must complete the form before starting a chat
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={handleRequiredToggle}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <Alert variant="outline" className="bg-blue-50/50 border-blue-100">
                              <Info className="h-4 w-4 text-blue-500" />
                              <AlertDescription className="text-sm text-blue-700">
                                {form.watch("required")
                                  ? "Visitors must complete the form before they can start chatting."
                                  : "The form is optional, but visitors are encouraged to complete it for better assistance."}
                              </AlertDescription>
                            </Alert>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="collectName"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                    <FormLabel>Collect Name</FormLabel>
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="collectEmail"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                    <FormLabel>Collect Email</FormLabel>
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="collectPhone"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                    <FormLabel>Collect Phone</FormLabel>
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="collectMessage"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                    <FormLabel>Collect Initial Message</FormLabel>
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                            </div>

                            <div className="flex justify-end">
                              <Button type="submit">
                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                Save Settings
                              </Button>
                            </div>
                          </>
                        )}
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {form.watch("enabled") && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Form Preview</CardTitle>
                      <CardDescription>
                        Preview how your pre-chat form will appear to visitors
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {activeTemplate ? (
                        <div className="border rounded-lg p-4 bg-background">
                          <EnhancedPreChatForm
                            fields={activeTemplate.fields}
                            title={activeTemplate.title}
                            description={activeTemplate.description}
                            onSubmit={() => { }}
                            onCancel={() => { }}
                            primaryColor={widgetSettings?.primaryColor}
                          />
                        </div>
                      ) : (
                        <Alert variant="outline" className="bg-amber-50/50 border-amber-100">
                          <HelpCircle className="h-4 w-4 text-amber-500" />
                          <AlertDescription className="text-sm text-amber-700">
                            No active template found. Please create and activate a template in the Templates tab.
                          </AlertDescription>
                        </Alert>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Help & Tips</CardTitle>
                    <CardDescription>
                      Learn how to use pre-chat forms effectively
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="font-medium">What is a pre-chat form?</h3>
                      <p className="text-sm text-muted-foreground">
                        A pre-chat form collects visitor information before starting a chat session. This helps personalize the conversation and gather important context.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <h3 className="font-medium">Best practices</h3>
                      <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
                        <li>Keep forms short and simple</li>
                        <li>Only ask for essential information</li>
                        <li>Explain why you're collecting information</li>
                        <li>Make most fields optional unless absolutely necessary</li>
                        <li>Use clear labels and helpful placeholder text</li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <h3 className="font-medium">How to get started</h3>
                      <ol className="text-sm text-muted-foreground space-y-1 list-decimal pl-4">
                        <li>Enable the pre-chat form in Settings</li>
                        <li>Create a template in the Templates tab</li>
                        <li>Add the fields you need</li>
                        <li>Activate your template</li>
                        <li>Save your settings</li>
                      </ol>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="templates">
            <PreChatFormTemplates
              widgetId={widgetId}
              primaryColor={widgetSettings?.primaryColor}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
