/**
 * Widget Configuration
 * This file contains configuration settings for the widget.
 */

// API URL Configuration
const API_CONFIG = {
  // Default API URL (used when no explicit URL is provided)
  DEFAULT_API_URL: 'http://localhost:9000',

  // Frontend URL (used to detect when we need to switch to API URL)
  FRONTEND_URL: 'http://localhost:9090',

  // API endpoints
  ENDPOINTS: {
    ANALYTICS: '/api/widget/analytics/view',
    GUEST_REGISTER: '/api/guest/register',
    GUEST_VALIDATE: '/api/guest/validate',
    GUEST_DETAILS: '/api/guest/details',
    CHAT_SESSION_INIT: '/api/chat/session/init',
    CHAT_MESSAGE: '/api/chat/message',
    CHAT_HISTORY: '/api/chat/history',
    CHAT_FEEDBACK: '/api/chat/feedback',
    POST_CHAT_SURVEY: '/api/post-chat-survey',
    POST_CHAT_SURVEY_SUBMIT: '/api/post-chat-survey/submit'
  }
};

// Export configuration
export { API_CONFIG };
