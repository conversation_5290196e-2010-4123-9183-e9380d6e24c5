<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Project;
use App\Models\DatabaseConnection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Connection;
use Illuminate\Database\Query\Builder;
use Exception;
use PDO;

class DatabaseConnectionService
{
    /**
     * Get a database connection for a project
     *
     * @param int $projectId
     * @return Connection|null
     */
    public function getConnection(int $projectId): ?Connection
    {
        try {
            $connection = DatabaseConnection::where('project_id', $projectId)
                ->where('status', 'active')
                ->first();

            if (!$connection) {
                return null;
            }

            // Create a dynamic connection
            config([
                "database.connections.dynamic_{$projectId}" => [
                    'driver' => $connection->driver,
                    'host' => $connection->host,
                    'port' => $connection->port,
                    'database' => $connection->database,
                    'username' => $connection->username,
                    'password' => $connection->password,
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                    'strict' => true,
                    'options' => [
                        PDO::ATTR_EMULATE_PREPARES => true,
                        PDO::ATTR_TIMEOUT => 30,
                        PDO::ATTR_PERSISTENT => false,
                    ],
                ],
            ]);

            return DB::connection("dynamic_{$projectId}");
        } catch (Exception $e) {
            Log::error("Error getting database connection for project {$projectId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all tables for a project
     *
     * @param int $projectId
     * @return array
     */
    public function getTables(int $projectId): array
    {
        try {
            $connection = $this->getConnection($projectId);

            if (!$connection) {
                return [];
            }

            $tables = [];
            $schemaManager = $connection->getDoctrineSchemaManager();
            $tableNames = $schemaManager->listTableNames();

            foreach ($tableNames as $tableName) {
                $columns = $schemaManager->listTableColumns($tableName);
                $tables[] = [
                    'name' => $tableName,
                    'columns' => array_map(function ($column) {
                        return [
                            'name' => $column->getName(),
                            'type' => $column->getType()->getName(),
                            'nullable' => !$column->getNotnull(),
                        ];
                    }, $columns),
                    'row_count' => $this->getTableRowCount($connection, $tableName),
                ];
            }

            return $tables;
        } catch (Exception $e) {
            Log::error("Error getting tables for project {$projectId}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get table details
     *
     * @param int $projectId
     * @param string $tableName
     * @return array|null
     */
    public function getTableDetails(int $projectId, string $tableName): ?array
    {
        try {
            $connection = $this->getConnection($projectId);

            if (!$connection) {
                return null;
            }

            $schemaManager = $connection->getDoctrineSchemaManager();
            
            // Check if table exists
            if (!$schemaManager->tablesExist([$tableName])) {
                return null;
            }

            $columns = $schemaManager->listTableColumns($tableName);
            $primaryKey = $this->getPrimaryKey($connection, $tableName);
            $rowCount = $this->getTableRowCount($connection, $tableName);
            $sampleData = $this->getSampleData($connection, $tableName);

            return [
                'name' => $tableName,
                'columns' => array_map(function ($column) {
                    return [
                        'name' => $column->getName(),
                        'type' => $column->getType()->getName(),
                        'nullable' => !$column->getNotnull(),
                    ];
                }, $columns),
                'primary_key' => $primaryKey,
                'row_count' => $rowCount,
                'sample_data' => $sampleData,
            ];
        } catch (Exception $e) {
            Log::error("Error getting table details for {$tableName} in project {$projectId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Execute a read-only query
     *
     * @param int $projectId
     * @param string $query
     * @param int $limit
     * @return array
     */
    public function executeReadOnlyQuery(int $projectId, string $query, int $limit = 100): array
    {
        try {
            $connection = $this->getConnection($projectId);

            if (!$connection) {
                return [
                    'error' => 'Database connection not found',
                    'columns' => [],
                    'rows' => [],
                ];
            }

            // Validate query is read-only
            if (!$this->isReadOnlyQuery($query)) {
                return [
                    'error' => 'Only SELECT queries are allowed',
                    'columns' => [],
                    'rows' => [],
                ];
            }

            // Add LIMIT to query if not present
            if (!preg_match('/\bLIMIT\s+\d+\b/i', $query)) {
                $query .= " LIMIT {$limit}";
            }

            $results = $connection->select($query);
            
            if (empty($results)) {
                return [
                    'columns' => [],
                    'rows' => [],
                ];
            }

            return [
                'columns' => array_keys((array)$results[0]),
                'rows' => $results,
            ];
        } catch (Exception $e) {
            Log::error("Error executing query for project {$projectId}: " . $e->getMessage());
            return [
                'error' => $e->getMessage(),
                'columns' => [],
                'rows' => [],
            ];
        }
    }

    /**
     * Check if a query is read-only
     *
     * @param string $query
     * @return bool
     */
    protected function isReadOnlyQuery(string $query): bool
    {
        // Remove comments and normalize whitespace
        $query = preg_replace('/--.*$/m', '', $query);
        $query = preg_replace('/\/\*.*?\*\//s', '', $query);
        $query = preg_replace('/\s+/', ' ', trim($query));

        // Check if query starts with SELECT
        if (!preg_match('/^SELECT\b/i', $query)) {
            return false;
        }

        // Check for disallowed statements
        $disallowedPatterns = [
            '/\bINSERT\b/i',
            '/\bUPDATE\b/i',
            '/\bDELETE\b/i',
            '/\bDROP\b/i',
            '/\bALTER\b/i',
            '/\bCREATE\b/i',
            '/\bTRUNCATE\b/i',
            '/\bREPLACE\b/i',
            '/\bGRANT\b/i',
            '/\bREVOKE\b/i',
            '/\bCALL\b/i',
            '/\bEXEC\b/i',
            '/\bSET\b/i',
        ];

        foreach ($disallowedPatterns as $pattern) {
            if (preg_match($pattern, $query)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the primary key for a table
     *
     * @param Connection $connection
     * @param string $tableName
     * @return string|null
     */
    protected function getPrimaryKey(Connection $connection, string $tableName): ?string
    {
        try {
            $schemaManager = $connection->getDoctrineSchemaManager();
            $indexes = $schemaManager->listTableIndexes($tableName);

            foreach ($indexes as $index) {
                if ($index->isPrimary()) {
                    $columns = $index->getColumns();
                    return count($columns) === 1 ? $columns[0] : null;
                }
            }

            return null;
        } catch (Exception $e) {
            Log::error("Error getting primary key for table {$tableName}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the row count for a table
     *
     * @param Connection $connection
     * @param string $tableName
     * @return int
     */
    protected function getTableRowCount(Connection $connection, string $tableName): int
    {
        try {
            return $connection->table($tableName)->count();
        } catch (Exception $e) {
            Log::error("Error getting row count for table {$tableName}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get sample data from a table
     *
     * @param Connection $connection
     * @param string $tableName
     * @param int $limit
     * @return array
     */
    protected function getSampleData(Connection $connection, string $tableName, int $limit = 5): array
    {
        try {
            return $connection->table($tableName)->limit($limit)->get()->toArray();
        } catch (Exception $e) {
            Log::error("Error getting sample data for table {$tableName}: " . $e->getMessage());
            return [];
        }
    }
}
