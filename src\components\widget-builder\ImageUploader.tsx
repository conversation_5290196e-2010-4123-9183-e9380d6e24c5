'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { UploadCloud, ImageIcon, X, RefreshCw, AlertCircle, Crop, Check } from 'lucide-react';
import { FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ImageUploaderProps {
    defaultImageUrl?: string;
    onImageChange: (imageUrl: string) => void;
    control: any;
    name: string;
    label?: string;
    description?: string;
    showRemoveButton?: boolean;
    maxSize?: number; // in bytes, default to 2MB
    idealDimensions?: { width: number; height: number }; // ideal image dimensions
    defaultAvatarUrl?: string; // Default avatar URL to use if none is provided
}

export default function ImageUploader({
    defaultImageUrl = '',
    onImageChange,
    control,
    name,
    label = 'Upload Image',
    description = 'Upload an image or enter an image URL',
    showRemoveButton = true,
    maxSize = 2 * 1024 * 1024, // Default to 2MB
    idealDimensions = { width: 80, height: 80 },
    defaultAvatarUrl = '/assets/default-avatar.svg', // Using SVG for better scaling
}: ImageUploaderProps) {
    const [imageUrl, setImageUrl] = useState<string>(defaultImageUrl);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const [uploadError, setUploadError] = useState<string>('');
    const [uploadWarning, setUploadWarning] = useState<string>('');
    const [activeTab, setActiveTab] = useState<string>('upload');
    const [imageInfo, setImageInfo] = useState<{ width: number; height: number; size: number } | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    // Check existing image when component mounts
    useEffect(() => {
        if (defaultImageUrl) {
            validateImageUrl(defaultImageUrl);
        }
    }, [defaultImageUrl]);

    // Add useEffect to use default avatar if none is provided
    useEffect(() => {
        if (!defaultImageUrl && defaultAvatarUrl) {
            setImageUrl(defaultAvatarUrl);
            // Only set the form value if it's empty
            if (!control._formValues[name]) {
                onImageChange(defaultAvatarUrl);
                validateImageUrl(defaultAvatarUrl);
            }
        }
    }, [defaultAvatarUrl]);

    // Validate an image URL
    const validateImageUrl = (url: string) => {
        if (!url) return;

        setUploadError('');
        setUploadWarning('');
        setImageInfo(null);

        const img = new Image();
        img.onload = () => {
            const imageWidth = img.width;
            const imageHeight = img.height;

            // Store image dimensions
            setImageInfo({
                width: imageWidth,
                height: imageHeight,
                size: 0 // We can't determine file size from URL
            });

            // Check dimensions ratio
            const aspectRatio = imageWidth / imageHeight;
            const idealRatio = idealDimensions.width / idealDimensions.height;

            if (aspectRatio !== idealRatio) {
                setUploadWarning(`Image has ${aspectRatio.toFixed(2)} aspect ratio, ideal is ${idealRatio.toFixed(2)}. This may affect display.`);
            }

            // Check size (dimensions)
            if (imageWidth > 500 || imageHeight > 500) {
                setUploadWarning((prev) =>
                    prev ? `${prev} Large image (${imageWidth}x${imageHeight}px) may affect performance.`
                        : `Large image (${imageWidth}x${imageHeight}px) may affect performance.`
                );
            }
        };

        img.onerror = () => {
            setUploadError('Failed to load image. Please check the URL and ensure it\'s accessible.');
        };

        img.src = url;
    };

    // Process and resize image if needed
    const processImage = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (event) => {
                if (!event.target?.result) {
                    reject('Failed to read file');
                    return;
                }

                const img = new Image();
                img.onload = () => {
                    const canvas = canvasRef.current;
                    if (!canvas) {
                        reject('Canvas not available');
                        return;
                    }

                    // Store original dimensions
                    setImageInfo({
                        width: img.width,
                        height: img.height,
                        size: file.size
                    });

                    // Check if we need to resize
                    let finalWidth = img.width;
                    let finalHeight = img.height;

                    // Auto-resize large images to prevent performance issues
                    if (img.width > 500 || img.height > 500) {
                        const aspectRatio = img.width / img.height;

                        if (img.width > img.height) {
                            finalWidth = 500;
                            finalHeight = Math.round(finalWidth / aspectRatio);
                        } else {
                            finalHeight = 500;
                            finalWidth = Math.round(finalHeight * aspectRatio);
                        }

                        setUploadWarning(`Image resized from ${img.width}x${img.height} to ${finalWidth}x${finalHeight} for performance optimization.`);
                    }

                    // Set canvas size to match image dimensions or resized dimensions
                    canvas.width = finalWidth;
                    canvas.height = finalHeight;

                    // Draw image to canvas (this also handles the resize)
                    const ctx = canvas.getContext('2d');
                    if (!ctx) {
                        reject('Could not get canvas context');
                        return;
                    }

                    // Use high-quality image rendering
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // Draw image on canvas (resized if necessary)
                    ctx.drawImage(img, 0, 0, finalWidth, finalHeight);

                    // Convert canvas to data URL (using quality parameter for JPEGs)
                    let dataUrl = '';
                    if (file.type === 'image/jpeg' || file.type === 'image/jpg') {
                        dataUrl = canvas.toDataURL('image/jpeg', 0.92); // 92% quality
                    } else {
                        dataUrl = canvas.toDataURL(file.type);
                    }

                    resolve(dataUrl);
                };

                img.onerror = () => {
                    reject('Failed to load image for processing');
                };

                img.src = event.target.result as string;
            };

            reader.onerror = () => {
                reject('Failed to read file');
            };

            reader.readAsDataURL(file);
        });
    };

    // Handle file selection
    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Reset states
        setUploadError('');
        setUploadWarning('');

        // Validate file size
        if (file.size > maxSize) {
            setUploadError(`Image size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds the maximum allowed size of ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            setUploadError('File must be an image (JPG, PNG, GIF, SVG)');
            return;
        }

        setIsUploading(true);

        try {
            // Process and potentially resize the image
            const processedImageUrl = await processImage(file);

            // Update states
            setImageUrl(processedImageUrl);
            onImageChange(processedImageUrl);

            // Check aspect ratio
            if (imageInfo) {
                const aspectRatio = imageInfo.width / imageInfo.height;
                const idealRatio = idealDimensions.width / idealDimensions.height;

                if (Math.abs(aspectRatio - idealRatio) > 0.1) {
                    setUploadWarning((prev) =>
                        prev ? `${prev} Non-square aspect ratio may not display optimally in avatar containers.`
                            : `Non-square aspect ratio may not display optimally in avatar containers.`
                    );
                }
            }

        } catch (error) {
            console.error('Error processing image:', error);
            setUploadError('Failed to process image. Please try again with a different file.');
        } finally {
            setIsUploading(false);
        }
    };

    // Handle URL input
    const handleUrlChange = (url: string) => {
        setImageUrl(url);
        onImageChange(url);
        validateImageUrl(url);
    };

    // Handle removing the image
    const handleRemoveImage = () => {
        if (defaultAvatarUrl) {
            setImageUrl(defaultAvatarUrl);
            onImageChange(defaultAvatarUrl);
        } else {
            setImageUrl('');
            onImageChange('');
        }
        setUploadError('');
        setUploadWarning('');
        setImageInfo(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    // Trigger file input click
    const handleUploadClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem className="space-y-4">
                    <FormLabel>{label}</FormLabel>

                    <div className="flex items-center space-x-4">
                        <Avatar className="w-16 h-16 border-2 border-gray-200">
                            {imageUrl ? (
                                <AvatarImage
                                    src={imageUrl}
                                    alt="Widget logo"
                                    className="object-cover"
                                    onError={() => {
                                        setUploadError('Failed to load image. Please check the URL.');
                                        // If there's an error loading the image, try to use the default avatar
                                        if (defaultAvatarUrl && imageUrl !== defaultAvatarUrl) {
                                            setImageUrl(defaultAvatarUrl);
                                            onImageChange(defaultAvatarUrl);
                                        }
                                    }}
                                />
                            ) : (
                                <AvatarFallback className="bg-gray-200">
                                    {defaultAvatarUrl ? (
                                        <img
                                            src={defaultAvatarUrl}
                                            alt="Default logo"
                                            className="w-full h-full object-cover"
                                            onError={() => {
                                                // If default avatar fails to load, show icon
                                                console.log('Default avatar failed to load');
                                            }}
                                        />
                                    ) : (
                                        <ImageIcon className="w-6 h-6 text-gray-500" />
                                    )}
                                </AvatarFallback>
                            )}
                        </Avatar>

                        <div className="flex-1">
                            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                                <TabsList className="grid w-full grid-cols-2 mb-2">
                                    <TabsTrigger value="upload">Upload File</TabsTrigger>
                                    <TabsTrigger value="url">Image URL</TabsTrigger>
                                </TabsList>

                                <TabsContent value="upload" className="space-y-2">
                                    <input
                                        type="file"
                                        ref={fileInputRef}
                                        onChange={handleFileChange}
                                        accept="image/jpeg,image/png,image/gif,image/svg+xml"
                                        className="hidden"
                                        data-testid="file-input"
                                    />
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleUploadClick}
                                        className="w-full text-sm"
                                        disabled={isUploading}
                                    >
                                        {isUploading ? (
                                            <>
                                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                Processing...
                                            </>
                                        ) : (
                                            <>
                                                <UploadCloud className="w-4 h-4 mr-2" />
                                                Choose Image
                                            </>
                                        )}
                                    </Button>

                                    {/* Add information about recommended image specs */}
                                    <p className="text-xs text-gray-500">
                                        Recommended: {idealDimensions.width}x{idealDimensions.height}px, Square, PNG/JPG
                                    </p>
                                </TabsContent>

                                <TabsContent value="url" className="space-y-2">
                                    <FormControl>
                                        <Input
                                            placeholder="https://example.com/logo.png"
                                            value={field.value || ''}
                                            onChange={(e) => {
                                                field.onChange(e.target.value);
                                                handleUrlChange(e.target.value);
                                            }}
                                            className="text-sm"
                                        />
                                    </FormControl>
                                </TabsContent>
                            </Tabs>

                            {imageInfo && (
                                <div className="mt-1 text-xs text-gray-500">
                                    {imageInfo.width}x{imageInfo.height}px
                                    {imageInfo.size > 0 && `, ${(imageInfo.size / 1024).toFixed(1)}KB`}
                                </div>
                            )}

                            {uploadError && (
                                <Alert variant="destructive" className="mt-2 py-2 px-3">
                                    <AlertCircle className="h-4 w-4 mr-1" />
                                    <AlertDescription className="text-xs">{uploadError}</AlertDescription>
                                </Alert>
                            )}

                            {uploadWarning && !uploadError && (
                                <Alert variant="default" className="mt-2 py-2 px-3 bg-amber-50 text-amber-700 border-amber-200">
                                    <AlertCircle className="h-4 w-4 mr-1 text-amber-600" />
                                    <AlertDescription className="text-xs">{uploadWarning}</AlertDescription>
                                </Alert>
                            )}
                        </div>

                        {showRemoveButton && imageUrl && (
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={handleRemoveImage}
                                className="flex-shrink-0"
                            >
                                <X className="w-4 h-4" />
                            </Button>
                        )}
                    </div>

                    {/* Hidden canvas element for image processing */}
                    <canvas ref={canvasRef} style={{ display: 'none' }}></canvas>

                    <FormDescription className="text-xs">
                        {description}
                    </FormDescription>
                </FormItem>
            )}
        />
    );
} 