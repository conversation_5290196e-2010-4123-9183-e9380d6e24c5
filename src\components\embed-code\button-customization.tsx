import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MessageSquare, MessageCircle, HelpCircle, Mail, Phone, BotMessageSquare } from "lucide-react";
import { cn } from "@/lib/utils";

interface ButtonCustomizationOptions {
    text?: string;
    iconName?: string;
    shape?: 'circle' | 'square' | 'rounded';
    animation?: 'pulse' | 'bounce' | 'none';
}

interface ButtonCustomizationProps {
    options: ButtonCustomizationOptions;
    onChange: (options: ButtonCustomizationOptions) => void;
    primaryColor?: string;
}

// Available button icon options
const BUTTON_ICONS = [
    { name: 'message-square', component: MessageSquare, label: 'Message Square' },
    { name: 'message-circle', component: MessageCircle, label: 'Message Circle' },
    { name: 'help-circle', component: HelpCircle, label: 'Help Circle' },
    { name: 'mail', component: Mail, label: 'Mail' },
    { name: 'phone', component: Phone, label: 'Phone' },
    { name: 'bot', component: BotMessageSquare, label: 'Bot' },
];

export function ButtonCustomization({
    options,
    onChange,
    primaryColor = '#4f46e5'
}: ButtonCustomizationProps) {
    const [localOptions, setLocalOptions] = useState<ButtonCustomizationOptions>({
        text: options.text || "Chat with us",
        iconName: options.iconName || "message-square",
        shape: options.shape || "circle",
        animation: options.animation || "none",
    });

    // Handle changes to button options
    const handleChange = (key: keyof ButtonCustomizationOptions, value: any) => {
        const updated = { ...localOptions, [key]: value };
        setLocalOptions(updated);
        onChange(updated);
    };

    // Get the icon component based on the selected icon name
    const getIconComponent = () => {
        const icon = BUTTON_ICONS.find(icon => icon.name === localOptions.iconName);
        if (!icon) return MessageSquare;
        return icon.component;
    };

    // Generate button class based on shape
    const getButtonClass = () => {
        if (localOptions.shape === 'circle') return "rounded-full";
        if (localOptions.shape === 'square') return "rounded-md";
        return "rounded-xl";
    };

    // Generate animation class
    const getAnimationClass = () => {
        if (localOptions.animation === 'pulse') return "animate-pulse";
        if (localOptions.animation === 'bounce') return "animate-bounce";
        return "";
    };

    // Generate preview button styles
    const buttonStyle = {
        backgroundColor: primaryColor,
        color: 'white',
    };

    const IconComponent = getIconComponent();

    return (
        <div className="space-y-6">
            <div>
                <Label className="text-base">Chat Button Customization</Label>
                <p className="text-sm text-muted-foreground mb-4">
                    Customize the appearance of your widget's chat button
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="button-text">Button Text</Label>
                        <Input
                            id="button-text"
                            value={localOptions.text}
                            onChange={(e) => handleChange('text', e.target.value)}
                            placeholder="Chat with us"
                            maxLength={20}
                        />
                        <p className="text-xs text-muted-foreground">
                            Text shown when button expands on hover (max 20 chars)
                        </p>
                    </div>

                    <div className="space-y-2">
                        <Label>Button Icon</Label>
                        <div className="grid grid-cols-3 gap-2">
                            {BUTTON_ICONS.map(icon => (
                                <Button
                                    key={icon.name}
                                    type="button"
                                    variant={localOptions.iconName === icon.name ? 'default' : 'outline'}
                                    className={cn(
                                        "h-14 flex flex-col items-center justify-center gap-1 p-2",
                                        localOptions.iconName === icon.name && "border-primary"
                                    )}
                                    onClick={() => handleChange('iconName', icon.name)}
                                >
                                    <icon.component className="h-5 w-5" />
                                    <span className="text-xs whitespace-nowrap overflow-hidden text-ellipsis w-full text-center">
                                        {icon.label}
                                    </span>
                                </Button>
                            ))}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label>Button Shape</Label>
                        <RadioGroup
                            value={localOptions.shape}
                            onValueChange={(value: 'circle' | 'square' | 'rounded') => handleChange('shape', value)}
                            className="grid grid-cols-3 gap-2"
                        >
                            <div>
                                <RadioGroupItem
                                    value="circle"
                                    id="shape-circle"
                                    className="peer sr-only"
                                />
                                <Label
                                    htmlFor="shape-circle"
                                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                                >
                                    <div className="rounded-full bg-muted h-6 w-6"></div>
                                    <span className="mt-2 text-xs">Circle</span>
                                </Label>
                            </div>

                            <div>
                                <RadioGroupItem
                                    value="square"
                                    id="shape-square"
                                    className="peer sr-only"
                                />
                                <Label
                                    htmlFor="shape-square"
                                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                                >
                                    <div className="rounded-md bg-muted h-6 w-6"></div>
                                    <span className="mt-2 text-xs">Square</span>
                                </Label>
                            </div>

                            <div>
                                <RadioGroupItem
                                    value="rounded"
                                    id="shape-rounded"
                                    className="peer sr-only"
                                />
                                <Label
                                    htmlFor="shape-rounded"
                                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                                >
                                    <div className="rounded-xl bg-muted h-6 w-6"></div>
                                    <span className="mt-2 text-xs">Rounded</span>
                                </Label>
                            </div>
                        </RadioGroup>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="animation">Button Animation</Label>
                        <Select
                            value={localOptions.animation}
                            onValueChange={(value: 'pulse' | 'bounce' | 'none') => handleChange('animation', value)}
                        >
                            <SelectTrigger id="animation">
                                <SelectValue placeholder="Select animation" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                <SelectItem value="pulse">Pulse</SelectItem>
                                <SelectItem value="bounce">Bounce</SelectItem>
                            </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                            Add animation effects to attract attention to your chat button
                        </p>
                    </div>
                </div>

                <div className="flex flex-col items-center justify-center border rounded-md p-6 bg-muted/20">
                    <div className="mb-4 text-sm text-center text-muted-foreground">
                        Button Preview
                    </div>

                    <div
                        className={cn(
                            "flex items-center justify-center h-14 w-14 shadow-md transition-all",
                            getButtonClass(),
                            getAnimationClass()
                        )}
                        style={buttonStyle}
                    >
                        <IconComponent className="h-6 w-6" />
                    </div>

                    <div className="mt-8 w-full">
                        <div
                            className={cn(
                                "flex items-center justify-between py-2 px-4 mx-auto shadow-md max-w-[200px] transition-all",
                                localOptions.shape === 'square' ? "rounded-md" : "rounded-full"
                            )}
                            style={buttonStyle}
                        >
                            <span className="text-sm font-medium">{localOptions.text}</span>
                            <IconComponent className="h-5 w-5 ml-2" />
                        </div>
                        <p className="mt-2 text-xs text-center text-muted-foreground">
                            Expanded on hover
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
} 