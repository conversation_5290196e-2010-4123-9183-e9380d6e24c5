<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Widget;
use App\Models\WidgetAnalytics;
use Carbon\Carbon;

class WidgetAnalyticsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all widgets
        $widgets = Widget::all();

        if ($widgets->isEmpty()) {
            $this->command->info('No widgets found. Please run WidgetSeeder first.');
            return;
        }

        foreach ($widgets as $widget) {
            $this->createAnalyticsForWidget($widget);
        }

        $this->command->info('Widget analytics seeded successfully.');
    }

    private function createAnalyticsForWidget(Widget $widget): void
    {
        $eventTypes = ['view', 'conversation_start', 'conversation_end', 'message_sent'];
        $urls = [
            'https://example.com',
            'https://example.com/products',
            'https://example.com/support',
            'https://example.com/contact',
            'https://demo.example.com'
        ];

        // Generate analytics for the last 30 days
        for ($i = 30; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            
            // Generate random number of events for each day
            $dailyViews = rand(10, 50);
            $dailyConversations = rand(2, 15);
            $dailyMessages = rand(5, 40);

            // Create view events
            for ($j = 0; $j < $dailyViews; $j++) {
                WidgetAnalytics::create([
                    'widget_id' => $widget->id,
                    'event_type' => 'view',
                    'visitor_id' => 'visitor_' . uniqid(),
                    'url' => $urls[array_rand($urls)],
                    'metadata' => [
                        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'screen_resolution' => '1920x1080',
                        'referrer' => 'https://google.com'
                    ],
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->copy()->addMinutes(rand(0, 1439)),
                    'updated_at' => $date->copy()->addMinutes(rand(0, 1439)),
                ]);
            }

            // Create conversation start events
            for ($j = 0; $j < $dailyConversations; $j++) {
                WidgetAnalytics::create([
                    'widget_id' => $widget->id,
                    'event_type' => 'conversation_start',
                    'visitor_id' => 'visitor_' . uniqid(),
                    'url' => $urls[array_rand($urls)],
                    'metadata' => [
                        'session_id' => 'session_' . uniqid(),
                        'first_message' => 'Hello, I need help with...'
                    ],
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->copy()->addMinutes(rand(0, 1439)),
                    'updated_at' => $date->copy()->addMinutes(rand(0, 1439)),
                ]);
            }

            // Create message sent events
            for ($j = 0; $j < $dailyMessages; $j++) {
                WidgetAnalytics::create([
                    'widget_id' => $widget->id,
                    'event_type' => 'message_sent',
                    'visitor_id' => 'visitor_' . uniqid(),
                    'url' => $urls[array_rand($urls)],
                    'metadata' => [
                        'message_length' => rand(10, 200),
                        'response_time_ms' => rand(500, 3000)
                    ],
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->copy()->addMinutes(rand(0, 1439)),
                    'updated_at' => $date->copy()->addMinutes(rand(0, 1439)),
                ]);
            }

            // Create some conversation end events
            $conversationEnds = rand(1, $dailyConversations);
            for ($j = 0; $j < $conversationEnds; $j++) {
                WidgetAnalytics::create([
                    'widget_id' => $widget->id,
                    'event_type' => 'conversation_end',
                    'visitor_id' => 'visitor_' . uniqid(),
                    'url' => $urls[array_rand($urls)],
                    'metadata' => [
                        'session_duration_seconds' => rand(60, 1800),
                        'satisfaction_rating' => rand(1, 5)
                    ],
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->copy()->addMinutes(rand(0, 1439)),
                    'updated_at' => $date->copy()->addMinutes(rand(0, 1439)),
                ]);
            }
        }
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }
}
