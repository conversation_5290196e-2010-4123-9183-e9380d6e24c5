import React from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, MessageSquare, CheckCircle, Plus, X } from 'lucide-react';
import { FeatureModalProps } from '../index';

const PostChatModal = ({ form, onClose }: FeatureModalProps) => {
  const [questions, setQuestions] = React.useState([
    { id: 1, text: "How would you rate your experience?", type: "rating", required: true },
    { id: 2, text: "Any additional feedback?", type: "text", required: false }
  ]);

  const addQuestion = () => {
    const newQuestion = {
      id: Date.now(),
      text: "",
      type: "text",
      required: false
    };
    setQuestions([...questions, newQuestion]);
  };

  const removeQuestion = (id: number) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  const updateQuestion = (id: number, field: string, value: any) => {
    setQuestions(questions.map(q => 
      q.id === id ? { ...q, [field]: value } : q
    ));
  };

  const handleSave = () => {
    form.setValue('features.postChat', true);
    // Save questions configuration
    onClose();
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MessageSquare className="w-5 h-5 mr-2 text-blue-600" />
            Post-Chat Survey
          </DialogTitle>
          <DialogDescription>
            Collect valuable feedback from visitors after their chat session ends
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Survey Settings</CardTitle>
                <CardDescription>Configure when and how the survey appears</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="survey-enabled">Enable Post-Chat Survey</Label>
                  <Switch id="survey-enabled" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="survey-title">Survey Title</Label>
                  <Input 
                    id="survey-title" 
                    placeholder="How was your experience?"
                    defaultValue="How was your experience?"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="survey-description">Description</Label>
                  <Textarea 
                    id="survey-description" 
                    placeholder="Please take a moment to provide feedback"
                    defaultValue="Please take a moment to provide feedback"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="thank-you-message">Thank You Message</Label>
                  <Input 
                    id="thank-you-message" 
                    placeholder="Thank you for your feedback!"
                    defaultValue="Thank you for your feedback!"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center justify-between">
                  Survey Questions
                  <Button size="sm" onClick={addQuestion}>
                    <Plus className="w-4 h-4 mr-1" />
                    Add Question
                  </Button>
                </CardTitle>
                <CardDescription>Customize the questions you want to ask</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {questions.map((question, index) => (
                  <div key={question.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">Question {index + 1}</Badge>
                      {questions.length > 1 && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => removeQuestion(question.id)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Question Text</Label>
                      <Input 
                        value={question.text}
                        onChange={(e) => updateQuestion(question.id, 'text', e.target.value)}
                        placeholder="Enter your question"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Question Type</Label>
                        <select 
                          className="w-full p-2 border rounded-md"
                          value={question.type}
                          onChange={(e) => updateQuestion(question.id, 'type', e.target.value)}
                        >
                          <option value="rating">Star Rating</option>
                          <option value="text">Text Input</option>
                          <option value="boolean">Yes/No</option>
                          <option value="select">Multiple Choice</option>
                        </select>
                      </div>
                      
                      <div className="flex items-center space-x-2 pt-6">
                        <Switch 
                          checked={question.required}
                          onCheckedChange={(checked) => updateQuestion(question.id, 'required', checked)}
                        />
                        <Label>Required</Label>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Survey Preview</CardTitle>
                <CardDescription>How the survey will appear to visitors</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 space-y-4">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2">How was your experience?</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Please take a moment to provide feedback
                    </p>
                  </div>

                  {questions.map((question, index) => (
                    <div key={question.id} className="space-y-2">
                      <Label className="text-sm font-medium">
                        {question.text}
                        {question.required && <span className="text-red-500 ml-1">*</span>}
                      </Label>
                      
                      {question.type === 'rating' && (
                        <div className="flex space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star key={star} className="w-6 h-6 text-yellow-400 fill-current" />
                          ))}
                        </div>
                      )}
                      
                      {question.type === 'text' && (
                        <Textarea placeholder="Your feedback..." rows={3} />
                      )}
                      
                      {question.type === 'boolean' && (
                        <div className="flex space-x-4">
                          <Button variant="outline" size="sm">Yes</Button>
                          <Button variant="outline" size="sm">No</Button>
                        </div>
                      )}
                    </div>
                  ))}

                  <div className="flex justify-center pt-4">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      Submit Feedback
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">Benefits</h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-200 mt-1 space-y-1">
                    <li>• Improve customer satisfaction</li>
                    <li>• Identify areas for improvement</li>
                    <li>• Track service quality over time</li>
                    <li>• Build customer loyalty</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
            Enable Post-Chat Survey
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PostChatModal;
