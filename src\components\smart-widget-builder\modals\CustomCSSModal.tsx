import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Code, Eye, CheckCircle, AlertTriangle, Copy } from 'lucide-react';
import { FeatureModalProps } from '../index';

const CustomCSSModal = ({ form, onClose }: FeatureModalProps) => {
  const [cssCode, setCssCode] = useState(`/* Custom CSS for your widget */
.chat-widget {
  /* Widget container styles */
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.chat-header {
  /* Header customization */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chat-message {
  /* Message bubble styles */
  border-radius: 18px;
  margin-bottom: 8px;
}

.chat-input {
  /* Input field styles */
  border-radius: 20px;
  padding: 12px 16px;
}`);

  const [activeTab, setActiveTab] = useState('editor');

  const cssExamples = [
    {
      name: "Gradient Header",
      description: "Beautiful gradient background for the header",
      code: `.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}`
    },
    {
      name: "Rounded Messages",
      description: "More rounded message bubbles",
      code: `.chat-message {
  border-radius: 20px;
  padding: 12px 16px;
}`
    },
    {
      name: "Custom Shadows",
      description: "Enhanced shadow effects",
      code: `.chat-widget {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}`
    },
    {
      name: "Animation Effects",
      description: "Smooth animations for interactions",
      code: `.chat-widget {
  transition: all 0.3s ease;
}

.chat-widget:hover {
  transform: translateY(-2px);
}`
    }
  ];

  const handleSave = () => {
    form.setValue('features.customCSS', true);
    form.setValue('advanced.customCSS', cssCode);
    onClose();
  };

  const copyExample = (code: string) => {
    navigator.clipboard.writeText(code);
  };

  const insertExample = (code: string) => {
    setCssCode(cssCode + '\n\n' + code);
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Code className="w-5 h-5 mr-2 text-purple-600" />
            Custom CSS Styling
          </DialogTitle>
          <DialogDescription>
            Add custom CSS to completely customize your widget's appearance
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="editor">CSS Editor</TabsTrigger>
            <TabsTrigger value="examples">Examples</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="editor" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* CSS Editor */}
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">CSS Code Editor</CardTitle>
                    <CardDescription>Write your custom CSS styles</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <Label htmlFor="css-editor">Custom CSS</Label>
                      <Textarea
                        id="css-editor"
                        value={cssCode}
                        onChange={(e) => setCssCode(e.target.value)}
                        className="font-mono text-sm min-h-[400px]"
                        placeholder="Enter your custom CSS here..."
                      />
                      <div className="text-xs text-gray-500">
                        Use standard CSS syntax. Changes will be applied to your widget.
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* CSS Guidelines */}
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">CSS Guidelines</CardTitle>
                    <CardDescription>Available CSS classes and best practices</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Available Classes:</h4>
                      <div className="space-y-2 text-sm">
                        <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          .chat-widget
                        </div>
                        <div className="text-xs text-gray-600">Main widget container</div>
                        
                        <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          .chat-header
                        </div>
                        <div className="text-xs text-gray-600">Widget header section</div>
                        
                        <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          .chat-messages
                        </div>
                        <div className="text-xs text-gray-600">Messages container</div>
                        
                        <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          .chat-message
                        </div>
                        <div className="text-xs text-gray-600">Individual message bubbles</div>
                        
                        <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          .chat-input
                        </div>
                        <div className="text-xs text-gray-600">Input field</div>
                        
                        <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          .chat-button
                        </div>
                        <div className="text-xs text-gray-600">Action buttons</div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h4 className="font-medium mb-2">Best Practices:</h4>
                      <ul className="text-sm space-y-1 text-gray-600">
                        <li>• Use specific selectors to avoid conflicts</li>
                        <li>• Test on different screen sizes</li>
                        <li>• Consider dark mode compatibility</li>
                        <li>• Use relative units (rem, em, %)</li>
                        <li>• Avoid !important when possible</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-amber-900 dark:text-amber-100">Important</h4>
                      <p className="text-sm text-amber-700 dark:text-amber-200 mt-1">
                        Custom CSS will override default styles. Test thoroughly before deploying.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="examples" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {cssExamples.map((example, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center justify-between">
                      {example.name}
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => copyExample(example.code)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => insertExample(example.code)}
                        >
                          Insert
                        </Button>
                      </div>
                    </CardTitle>
                    <CardDescription>{example.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-x-auto">
                      <code>{example.code}</code>
                    </pre>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Eye className="w-5 h-5 mr-2" />
                  CSS Preview
                </CardTitle>
                <CardDescription>See how your custom CSS affects the widget</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                  <div className="text-center text-gray-500">
                    <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>CSS preview will be available in the main widget preview</p>
                    <p className="text-sm mt-2">Your custom styles will be applied to the live preview</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">Benefits</h4>
                  <ul className="text-sm text-green-700 dark:text-green-200 mt-1 space-y-1">
                    <li>• Complete visual customization</li>
                    <li>• Match your brand perfectly</li>
                    <li>• Advanced styling capabilities</li>
                    <li>• Professional appearance</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-purple-600 hover:bg-purple-700">
            Apply Custom CSS
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CustomCSSModal;
