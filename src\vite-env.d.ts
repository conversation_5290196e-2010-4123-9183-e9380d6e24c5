/// <reference types="vite/client" />

interface ImportMetaEnv {
    // API configuration
    readonly VITE_API_URL: string;
    readonly VITE_FRONTEND_URL: string;

    // Authentication
    readonly VITE_SANCTUM_STATEFUL_DOMAINS: string;
    readonly VITE_AUTH_SESSION_LIFETIME_MINUTES: string;

    // Widget configuration
    readonly VITE_WIDGET_VERSION: string;
    readonly VITE_CORS_ALLOWED_ORIGINS: string;

    // App settings
    readonly VITE_APP_NAME: string;
    readonly VITE_APP_ENV: 'production' | 'development' | 'testing';
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}
