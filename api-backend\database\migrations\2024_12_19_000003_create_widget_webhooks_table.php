<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('widget_webhooks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('widget_id')->constrained()->onDelete('cascade');
            
            // Webhook configuration
            $table->string('name')->nullable(); // Human-readable name
            $table->string('url'); // Webhook URL
            $table->enum('method', ['POST', 'PUT', 'PATCH'])->default('POST');
            $table->json('headers')->nullable(); // Custom headers
            
            // Event triggers
            $table->boolean('on_chat_start')->default(false);
            $table->boolean('on_chat_end')->default(false);
            $table->boolean('on_message_sent')->default(false);
            $table->boolean('on_message_received')->default(false);
            $table->boolean('on_user_rating')->default(false);
            $table->boolean('on_form_submission')->default(false);
            
            // Security and reliability
            $table->string('secret_key')->nullable(); // For webhook verification
            $table->integer('timeout_seconds')->default(30);
            $table->integer('retry_attempts')->default(3);
            $table->boolean('verify_ssl')->default(true);
            
            // Status and monitoring
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_triggered_at')->nullable();
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->text('last_error')->nullable();
            
            // Integration metadata
            $table->string('integration_type')->nullable(); // e.g., 'slack', 'discord', 'zapier'
            $table->json('integration_config')->nullable(); // Integration-specific settings
            
            $table->timestamps();
            
            // Indexes
            $table->index('widget_id');
            $table->index(['widget_id', 'is_active']);
            $table->index('integration_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('widget_webhooks');
    }
};
