<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create the table if it doesn't exist
        if (!Schema::hasTable('knowledge_embeddings')) {
            Schema::create('knowledge_embeddings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('document_id')->constrained('knowledge_documents')->onDelete('cascade');
                $table->integer('chunk_index');
                $table->text('chunk_text');
                $table->json('embedding');
                $table->string('provider');
                $table->string('model');
                $table->integer('dimensions');
                $table->json('metadata')->nullable();
                $table->timestamps();

                // Add indexes for faster retrieval
                $table->index('document_id');
                $table->index(['document_id', 'chunk_index']);
            });
        } else {
            // If the table exists but doesn't have the document_id index, add it
            if (!Schema::hasIndex('knowledge_embeddings', 'knowledge_embeddings_document_id_index')) {
                Schema::table('knowledge_embeddings', function (Blueprint $table) {
                    $table->index('document_id');
                });
            }
        }

        // Add embedding-related columns to knowledge_documents table if they don't exist
        if (!Schema::hasColumn('knowledge_documents', 'has_embeddings')) {
            Schema::table('knowledge_documents', function (Blueprint $table) {
                $table->boolean('has_embeddings')->default(false);
                $table->integer('embeddings_count')->nullable();
                $table->string('embeddings_provider')->nullable();
                $table->string('embeddings_model')->nullable();
                $table->timestamp('embeddings_generated_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('knowledge_embeddings');

        // Remove embedding-related columns from knowledge_documents table
        if (Schema::hasColumn('knowledge_documents', 'has_embeddings')) {
            Schema::table('knowledge_documents', function (Blueprint $table) {
                $table->dropColumn([
                    'has_embeddings',
                    'embeddings_count',
                    'embeddings_provider',
                    'embeddings_model',
                    'embeddings_generated_at'
                ]);
            });
        }
    }
};
