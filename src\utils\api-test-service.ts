import api from "./api";
import { toast } from "@/hooks/use-toast";

export interface ApiRoute {
  uri: string;
  methods: string[];
  name: string;
  controller: string;
  category: string;
}

export interface ApiTestRequest {
  method: string;
  url: string;
  data: any;
  headers?: Record<string, string>;
}

export interface ApiTestResponse {
  status: number;
  data: any;
  headers: any;
  duration: number;
}

export interface RequestHistoryItem extends ApiTestRequest {
  id: string;
  timestamp: Date;
  response?: ApiTestResponse;
}

export const apiTestService = {
  // Get all available API routes
  getApiRoutes: async (): Promise<ApiRoute[]> => {
    try {
      // In a real implementation, this would fetch from the backend
      // For now, we'll return mock data that resembles what might come from api.php

      // Uncomment this when the backend endpoint is ready
      // const response = await api.get('test/routes');
      // return response.data.data;

      // Mock data for demonstration
      const mockRoutes: ApiRoute[] = [
        // Widget endpoints
        {
          uri: "/api/widgets",
          methods: ["GET", "POST"],
          name: "ListCreateWidgets",
          controller: "WidgetController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}",
          methods: ["GET", "PUT", "DELETE"],
          name: "SingleWidget",
          controller: "WidgetController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/analytics",
          methods: ["GET"],
          name: "WidgetAnalytics",
          controller: "WidgetAnalyticsController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/behavior",
          methods: ["GET", "PUT"],
          name: "WidgetBehavior",
          controller: "WidgetController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/logo",
          methods: ["GET", "POST", "DELETE"],
          name: "WidgetLogo",
          controller: "WidgetController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/webhooks",
          methods: ["GET", "POST"],
          name: "WidgetWebhooks",
          controller: "WidgetController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/webhooks/{webhook_id}",
          methods: ["GET", "PUT", "DELETE"],
          name: "SingleWidgetWebhook",
          controller: "WidgetController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/embed-code",
          methods: ["GET"],
          name: "WidgetEmbedCode",
          controller: "EmbedCodeController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/statistics",
          methods: ["GET"],
          name: "WidgetStatistics",
          controller: "WidgetStatisticsController",
          category: "widgets",
        },
        {
          uri: "/api/widgets/{id}/domain-restrictions",
          methods: ["GET", "PUT"],
          name: "WidgetDomainRestrictions",
          controller: "WidgetController",
          category: "widgets",
        },

        // AI Model endpoints
        {
          uri: "/api/ai-models",
          methods: ["GET", "POST"],
          name: "ListCreateModels",
          controller: "AIModelController",
          category: "ai-models",
        },
        {
          uri: "/api/ai-models/{id}",
          methods: ["GET", "PUT", "DELETE"],
          name: "SingleModel",
          controller: "AIModelController",
          category: "ai-models",
        },
        {
          uri: "/api/ai-models/{id}/test",
          methods: ["POST"],
          name: "TestModel",
          controller: "AIModelController",
          category: "ai-models",
        },

        // Chat endpoints
        {
          uri: "/api/chat/session/init",
          methods: ["POST"],
          name: "InitChatSession",
          controller: "ChatController",
          category: "chat",
        },
        {
          uri: "/api/chat/message",
          methods: ["POST"],
          name: "SendChatMessage",
          controller: "ChatController",
          category: "chat",
        },
        {
          uri: "/api/chat/feedback",
          methods: ["POST"],
          name: "ChatFeedback",
          controller: "ChatFeedbackController",
          category: "chat",
        },

        // Knowledge Base endpoints
        {
          uri: "/api/knowledge-base/documents",
          methods: ["GET", "POST"],
          name: "ListCreateDocuments",
          controller: "KnowledgeBaseController",
          category: "knowledge-base",
        },
        {
          uri: "/api/knowledge-base/documents/{id}",
          methods: ["GET", "PUT", "DELETE"],
          name: "SingleDocument",
          controller: "KnowledgeBaseController",
          category: "knowledge-base",
        },
        {
          uri: "/api/knowledge-base/embeddings",
          methods: ["POST"],
          name: "GenerateEmbeddings",
          controller: "KnowledgeBaseController",
          category: "knowledge-base",
        },

        // User endpoints
        {
          uri: "/api/users",
          methods: ["GET", "POST"],
          name: "ListCreateUsers",
          controller: "UserController",
          category: "users",
        },
        {
          uri: "/api/users/{id}",
          methods: ["GET", "PUT", "DELETE"],
          name: "SingleUser",
          controller: "UserController",
          category: "users",
        },

        // Auth endpoints
        {
          uri: "/api/auth/login",
          methods: ["POST"],
          name: "Login",
          controller: "AuthController",
          category: "auth",
        },
        {
          uri: "/api/auth/register",
          methods: ["POST"],
          name: "Register",
          controller: "AuthController",
          category: "auth",
        },
        {
          uri: "/api/auth/logout",
          methods: ["POST"],
          name: "Logout",
          controller: "AuthController",
          category: "auth",
        },
        {
          uri: "/api/auth/refresh",
          methods: ["POST"],
          name: "RefreshToken",
          controller: "AuthController",
          category: "auth",
        },
      ];

      return mockRoutes;
    } catch (error) {
      console.error("Failed to fetch API routes:", error);
      toast({
        title: "Error",
        description: "Failed to fetch API routes",
        variant: "destructive",
      });
      return [];
    }
  },

  // Execute a test request to an API endpoint
  executeTest: async (request: ApiTestRequest): Promise<ApiTestResponse> => {
    const startTime = performance.now();

    try {
      let response;
      const url = request.url.startsWith("/")
        ? request.url.substring(1)
        : request.url;
      const headers = request.headers || {};

      switch (request.method.toLowerCase()) {
        case "get":
          response = await api.get(url, { headers });
          break;
        case "post":
          response = await api.post(url, request.data, { headers });
          break;
        case "put":
          response = await api.put(url, request.data, { headers });
          break;
        case "delete":
          response = await api.delete(url, { headers, data: request.data });
          break;
        default:
          throw new Error(`Unsupported method: ${request.method}`);
      }

      const duration = performance.now() - startTime;

      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
        duration,
      };
    } catch (error: any) {
      const duration = performance.now() - startTime;

      return {
        status: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        headers: error.response?.headers || {},
        duration,
      };
    }
  },

  // Generate example request data based on endpoint and method
  generateExampleData: (endpoint: string, method: string): any => {
    const timestamp = new Date().toISOString();
    const randomId = Math.floor(Math.random() * 1000);

    // Widget examples
    if (endpoint.includes("widgets")) {
      if (method.toLowerCase() === "post") {
        return {
          name: `Test Widget ${randomId}`,
          is_active: true,
          project_id: 1,
          settings: {
            theme: "light",
            primaryColor: "#3B82F6",
            secondaryColor: "#60A5FA",
            backgroundColor: "#FFFFFF",
            textColor: "#1F2937",
            fontFamily: "Inter, sans-serif",
            fontSize: "16px",
            borderRadius: "8px",
            chatHeight: "500px",
            chatWidth: "350px",
            position: "bottom-right",
            requireGuestInfo: true,
            welcomeMessage: "Hello! How can I help you today?",
            placeholderText: "Type your message here...",
            buttonText: "Chat with us",
            buttonIcon: "message-circle",
            allowAttachments: true,
            allowRatings: true,
            showAgentName: true,
            showAgentAvatar: true,
            showTimestamp: true,
            persistChat: true,
            autoOpen: false,
            soundEnabled: true,
            created_at: timestamp,
          },
          behavior: {
            initialMessage: "Welcome! How can I assist you today?",
            offlineMessage:
              "We're currently offline. Please leave a message and we'll get back to you.",
            awayMessage:
              "Our team is away right now. We'll respond as soon as possible.",
            responseDelay: 1000,
            typingIndicator: true,
            autoResponse: true,
            businessHours: {
              enabled: true,
              timezone: "America/New_York",
              monday: { start: "09:00", end: "17:00", closed: false },
              tuesday: { start: "09:00", end: "17:00", closed: false },
              wednesday: { start: "09:00", end: "17:00", closed: false },
              thursday: { start: "09:00", end: "17:00", closed: false },
              friday: { start: "09:00", end: "17:00", closed: false },
              saturday: { start: "10:00", end: "15:00", closed: true },
              sunday: { start: "10:00", end: "15:00", closed: true },
            },
            customCSS: "",
          },
          webhooks: [
            {
              event_type: "chat_started",
              url: "https://example.com/webhook/chat-started",
              headers: { "X-Custom-Header": "custom-value" },
              active: true,
            },
            {
              event_type: "message_received",
              url: "https://example.com/webhook/message-received",
              headers: {},
              active: true,
            },
          ],
          logo: {
            url: "https://example.com/logo.png",
            display: true,
            position: "top-center",
            size: "medium",
          },
          domain_restrictions: ["example.com", "test-site.org"],
          ai_model_id: 1,
        };
      }
      if (method.toLowerCase() === "put") {
        return {
          name: `Updated Widget ${randomId}`,
          is_active: true,
          project_id: 1,
          settings: {
            theme: "dark",
            primaryColor: "#6366F1",
            secondaryColor: "#818CF8",
            backgroundColor: "#1F2937",
            textColor: "#F9FAFB",
            fontFamily: "Roboto, sans-serif",
            fontSize: "14px",
            borderRadius: "12px",
            chatHeight: "600px",
            chatWidth: "400px",
            position: "bottom-left",
            requireGuestInfo: false,
            welcomeMessage: "Hi there! Need any help?",
            placeholderText: "Send a message...",
            buttonText: "Support",
            buttonIcon: "help-circle",
            allowAttachments: false,
            allowRatings: true,
            showAgentName: true,
            showAgentAvatar: true,
            showTimestamp: false,
            persistChat: true,
            autoOpen: true,
            soundEnabled: false,
            updated_at: timestamp,
          },
          behavior: {
            initialMessage:
              "Thanks for reaching out! How can I help you today?",
            offlineMessage:
              "Our team is currently offline. Please leave your message and email.",
            awayMessage: "We're away for a moment. We'll be back shortly.",
            responseDelay: 500,
            typingIndicator: true,
            autoResponse: false,
            businessHours: {
              enabled: false,
              timezone: "Europe/London",
              monday: { start: "08:00", end: "18:00", closed: false },
              tuesday: { start: "08:00", end: "18:00", closed: false },
              wednesday: { start: "08:00", end: "18:00", closed: false },
              thursday: { start: "08:00", end: "18:00", closed: false },
              friday: { start: "08:00", end: "16:00", closed: false },
              saturday: { start: "10:00", end: "14:00", closed: false },
              sunday: { start: "00:00", end: "00:00", closed: true },
            },
            customCSS:
              ".chat-widget { box-shadow: 0 8px 16px rgba(0,0,0,0.1); }",
          },
          webhooks: [
            {
              event_type: "chat_ended",
              url: "https://example.com/webhook/chat-ended",
              headers: { Authorization: "Bearer token123" },
              active: true,
            },
          ],
          logo: {
            url: "https://example.com/updated-logo.png",
            display: true,
            position: "top-left",
            size: "large",
          },
          domain_restrictions: [
            "updated-example.com",
            "new-site.org",
            "test-domain.com",
          ],
          ai_model_id: 2,
        };
      }
    }

    // AI Model examples
    if (endpoint.includes("ai-models")) {
      if (method.toLowerCase() === "post") {
        return {
          name: "Example AI Model",
          provider: "OpenAI",
          description: "GPT-4 model for chat",
          is_default: false,
          settings: {
            temperature: 0.7,
            max_tokens: 1000,
          },
        };
      }
      if (method.toLowerCase() === "put") {
        return {
          name: "Updated AI Model",
          provider: "OpenAI",
          description: "Updated description",
          is_default: true,
          settings: {
            temperature: 0.5,
            max_tokens: 2000,
          },
        };
      }
    }

    // Guest user examples
    if (endpoint.includes("guest")) {
      if (method.toLowerCase() === "post" && endpoint.includes("register")) {
        return {
          fullname: `Test User ${randomId}`,
          email: `test.user.${randomId}@example.com`,
          phone: `555${String(randomId).padStart(7, "0")}`,
          widget_id: `widget_${randomId}`,
          created_at: timestamp,
        };
      }
      if (endpoint.includes("validate") || endpoint.includes("details")) {
        return {
          session_id: `session_${randomId}_${Date.now()}`,
        };
      }
    }

    // Chat examples
    if (endpoint.includes("chat")) {
      if (endpoint.includes("session/init")) {
        return {
          widget_id: `widget_${randomId}`,
          visitor_id: `visitor_${randomId}_${Date.now()}`,
          timestamp: timestamp,
        };
      }
      if (endpoint.includes("message")) {
        return {
          session_id: `session_${randomId}_${Date.now()}`,
          message: `Test message from API tester at ${new Date().toLocaleTimeString()}`,
          metadata: {
            source: "api_tester",
            page: "test_page",
            timestamp: timestamp,
          },
        };
      }
    }

    // Auth examples
    if (endpoint.includes("auth")) {
      if (endpoint.includes("login")) {
        return {
          email: "<EMAIL>",
          password: "password123",
        };
      }
      if (endpoint.includes("register")) {
        return {
          name: `New User ${randomId}`,
          email: `new.user.${randomId}@example.com`,
          password: "password123",
          password_confirmation: "password123",
        };
      }
    }

    // Knowledge base examples
    if (endpoint.includes("knowledge-base")) {
      if (method.toLowerCase() === "post" && endpoint.includes("documents")) {
        return {
          title: `Test Document ${randomId}`,
          content: "This is a test document content.",
          source_type: "manual",
          metadata: {
            author: "API Tester",
            created_at: timestamp,
          },
        };
      }
    }

    // Default empty object for other endpoints
    return {};
  },

  // Save request to history
  saveToHistory: (request: ApiTestRequest, response: ApiTestResponse): void => {
    try {
      const historyItem: RequestHistoryItem = {
        id: Date.now().toString(),
        ...request,
        timestamp: new Date(),
        response,
      };

      // Get existing history from localStorage
      const existingHistory = localStorage.getItem("api_test_history");
      const history: RequestHistoryItem[] = existingHistory
        ? JSON.parse(existingHistory)
        : [];

      // Add new item to history and limit to 50 items
      history.unshift(historyItem);
      if (history.length > 50) history.pop();

      // Save back to localStorage
      localStorage.setItem("api_test_history", JSON.stringify(history));
    } catch (error) {
      console.error("Failed to save request to history:", error);
    }
  },

  // Get request history
  getHistory: (): RequestHistoryItem[] => {
    try {
      const history = localStorage.getItem("api_test_history");
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error("Failed to get request history:", error);
      return [];
    }
  },

  // Clear request history
  clearHistory: (): void => {
    try {
      localStorage.removeItem("api_test_history");
    } catch (error) {
      console.error("Failed to clear request history:", error);
    }
  },
};
