import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertCircle, Plus, Trash2, FormInput, Clipboard, MessageSquare, PencilLine } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { WidgetConfigFormValues } from "@/types/widget-types";

interface ContentTabProps {
    config: WidgetConfigFormValues;
    onChange: (config: WidgetConfigFormValues) => void;
}

/**
 * ContentTab component handles Pre-Chat Form and Post-Chat Survey configuration
 */
const ContentTab = ({ config, onChange }: ContentTabProps) => {
    const [activeTab, setActiveTab] = useState<string>("preChatForm");
    const [newFieldName, setNewFieldName] = useState<string>("");
    const [newQuestionText, setNewQuestionText] = useState<string>("");
    const darkMode = config?.appearance?.darkMode || false;

    // Get form fields with proper fallbacks
    const preChatFormFields = config?.preChatForm?.fields || [];
    const postChatSurveyQuestions = config?.postChatSurvey?.questions || [];

    /**
     * Toggle pre-chat form and update parent form state
     */
    const handlePreChatToggle = (enabled: boolean) => {
        // Create a deep copy of the config to avoid mutation issues
        const updatedConfig = structuredClone(config);

        // Update both the behavior flag and preChatForm structure
        updatedConfig.behavior.preChat = enabled;

        // Ensure preChatForm exists and has proper default values if being enabled
        if (enabled && (!updatedConfig.preChatForm || Object.keys(updatedConfig.preChatForm).length === 0)) {
            updatedConfig.preChatForm = {
                title: "Welcome to Chat",
                description: "Please provide your information to get started",
                buttonText: "Start Chat",
                required: true,
                fields: []
            };
        }

        onChange(updatedConfig);
    };

    /**
     * Toggle post-chat survey and update parent form state
     */
    const handlePostChatToggle = (enabled: boolean) => {
        // Create a deep copy of the config to avoid mutation issues
        const updatedConfig = structuredClone(config);

        // Update both the behavior flag and postChatSurvey structure
        updatedConfig.behavior.postChat = enabled;

        // Ensure postChatSurvey exists and has proper default values if being enabled
        if (enabled && (!updatedConfig.postChatSurvey || Object.keys(updatedConfig.postChatSurvey).length === 0)) {
            updatedConfig.postChatSurvey = {
                title: "How was your experience?",
                description: "Please take a moment to provide feedback",
                thankYouMessage: "Thank you for your feedback!",
                buttonText: "Submit Feedback",
                showAlways: true,
                questions: []
            };
        }

        onChange(updatedConfig);
    };

    /**
     * Add a new field to the pre-chat form
     */
    const handleAddField = () => {
        if (!newFieldName.trim()) return;

        const updatedConfig = structuredClone(config);

        // Ensure preChatForm exists
        if (!updatedConfig.preChatForm) {
            updatedConfig.preChatForm = {
                title: "Welcome to Chat",
                description: "Please provide your information to get started",
                buttonText: "Start Chat",
                required: true,
                fields: []
            };
        }

        // Create a new field with unique ID and add it to the fields array
        const newField = {
            id: Date.now(), // Use timestamp for unique ID
            name: newFieldName.toLowerCase().replace(/\s+/g, '_'),
            label: newFieldName,
            type: 'text' as "text" | "email" | "phone" | "select" | "checkbox",
            placeholder: `Enter your ${newFieldName.toLowerCase()}...`,
            isRequired: false,
            order: updatedConfig.preChatForm.fields?.length || 0
        };

        updatedConfig.preChatForm.fields = [...(updatedConfig.preChatForm.fields || []), newField];

        onChange(updatedConfig);
        setNewFieldName("");
    };

    /**
     * Remove a field from the pre-chat form
     */
    const handleRemoveField = (fieldId: string | number) => {
        if (!config?.preChatForm?.fields) return;

        const updatedConfig = structuredClone(config);
        updatedConfig.preChatForm.fields = updatedConfig.preChatForm.fields.filter(
            field => field.id !== fieldId
        );

        onChange(updatedConfig);
    };

    /**
     * Update a field's required status
     */
    const handleFieldRequiredChange = (fieldId: string | number, isRequired: boolean) => {
        if (!config?.preChatForm?.fields) return;

        const updatedConfig = structuredClone(config);
        updatedConfig.preChatForm.fields = updatedConfig.preChatForm.fields.map(field =>
            field.id === fieldId ? { ...field, isRequired } : field
        );

        onChange(updatedConfig);
    };

    /**
     * Update a field's type
     */
    const handleFieldTypeChange = (fieldId: string | number, type: "text" | "email" | "phone" | "select" | "checkbox") => {
        if (!config?.preChatForm?.fields) return;

        const updatedConfig = structuredClone(config);
        updatedConfig.preChatForm.fields = updatedConfig.preChatForm.fields.map(field =>
            field.id === fieldId ? { ...field, type } : field
        );

        onChange(updatedConfig);
    };

    /**
     * Add a new question to the post-chat survey
     */
    const handleAddQuestion = () => {
        if (!newQuestionText.trim()) return;

        const updatedConfig = structuredClone(config);

        // Ensure postChatSurvey exists
        if (!updatedConfig.postChatSurvey) {
            updatedConfig.postChatSurvey = {
                title: "How was your experience?",
                description: "Please take a moment to provide feedback",
                thankYouMessage: "Thank you for your feedback!",
                buttonText: "Submit Feedback",
                showAlways: true,
                questions: []
            };
        }

        // Create a new question with unique ID and add it to the questions array
        const newQuestion = {
            id: Date.now(), // Use timestamp for unique ID
            text: newQuestionText,
            type: 'rating' as "rating" | "text" | "select" | "boolean" | "checkbox" | "multiselect",
            isRequired: true,
            order: updatedConfig.postChatSurvey.questions?.length || 0
        };

        updatedConfig.postChatSurvey.questions = [...(updatedConfig.postChatSurvey.questions || []), newQuestion];

        onChange(updatedConfig);
        setNewQuestionText("");
    };

    /**
     * Remove a question from the post-chat survey
     */
    const handleRemoveQuestion = (questionId: string | number) => {
        if (!config?.postChatSurvey?.questions) return;

        const updatedConfig = structuredClone(config);
        updatedConfig.postChatSurvey.questions = updatedConfig.postChatSurvey.questions.filter(
            question => question.id !== questionId
        );

        onChange(updatedConfig);
    };

    /**
     * Update a question's type
     */
    const handleQuestionTypeChange = (questionId: string | number, type: "rating" | "text" | "select" | "boolean" | "checkbox" | "multiselect") => {
        if (!config?.postChatSurvey?.questions) return;

        const updatedConfig = structuredClone(config);
        updatedConfig.postChatSurvey.questions = updatedConfig.postChatSurvey.questions.map(question =>
            question.id === questionId ? { ...question, type } : question
        );

        onChange(updatedConfig);
    };

    /**
     * Update a question's required status
     */
    const handleQuestionRequiredChange = (questionId: string | number, isRequired: boolean) => {
        if (!config?.postChatSurvey?.questions) return;

        const updatedConfig = structuredClone(config);
        updatedConfig.postChatSurvey.questions = updatedConfig.postChatSurvey.questions.map(question =>
            question.id === questionId ? { ...question, isRequired } : question
        );

        onChange(updatedConfig);
    };

    // Update form settings (title, description, etc.)
    const handleFormSettingChange = (field: string, value: string) => {
        const updatedConfig = structuredClone(config);
        if (!updatedConfig.preChatForm) {
            updatedConfig.preChatForm = {
                title: "Welcome to Chat",
                description: "Please provide your information to get started",
                buttonText: "Start Chat",
                required: true,
                fields: []
            };
        }

        // @ts-ignore - type safety handled by only allowing valid fields
        updatedConfig.preChatForm[field] = value;
        onChange(updatedConfig);
    };

    // Update survey settings (title, description, etc.)
    const handleSurveySettingChange = (field: string, value: string | boolean) => {
        const updatedConfig = structuredClone(config);
        if (!updatedConfig.postChatSurvey) {
            updatedConfig.postChatSurvey = {
                title: "How was your experience?",
                description: "Please take a moment to provide feedback",
                thankYouMessage: "Thank you for your feedback!",
                buttonText: "Submit Feedback",
                showAlways: true,
                questions: []
            };
        }

        // @ts-ignore - type safety handled by only allowing valid fields
        updatedConfig.postChatSurvey[field] = value;
        onChange(updatedConfig);
    };

    return (
        <div className="space-y-6">
            <Alert className="bg-blue-50 border-blue-100 dark:bg-blue-900/20 dark:border-blue-800 mb-6">
                <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <AlertDescription className="text-blue-700 dark:text-blue-300">
                    The Pre-Chat Form and Post-Chat Survey features are now fully implemented. Enable them below to enhance your widget's user experience.
                </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 gap-8">
                {/* Pre-Chat Form Toggle */}
                <div className="flex items-start space-x-4">
                    <div>
                        <FormInput className="h-10 w-10 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="space-y-1 flex-1">
                        <div className="flex items-center justify-between">
                            <Label className="text-base font-medium">Pre-Chat Form</Label>
                            <Switch
                                checked={config?.behavior?.preChat || false}
                                onCheckedChange={handlePreChatToggle}
                            />
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            Show a form before starting the chat
                        </p>
                    </div>
                </div>

                {/* Post-Chat Survey Toggle */}
                <div className="flex items-start space-x-4">
                    <div>
                        <Clipboard className="h-10 w-10 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="space-y-1 flex-1">
                        <div className="flex items-center justify-between">
                            <Label className="text-base font-medium">Post-Chat Survey</Label>
                            <Switch
                                checked={config?.behavior?.postChat || false}
                                onCheckedChange={handlePostChatToggle}
                            />
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            Show a satisfaction survey after chat ends
                        </p>
                    </div>
                </div>
            </div>

            <Separator className="my-6" />

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="w-full justify-start bg-transparent border-b-2 border-gray-100 dark:border-gray-800 rounded-none p-0 h-auto">
                    <TabsTrigger
                        value="preChatForm"
                        className={`rounded-none pb-2 px-4 text-sm h-9 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:shadow-none data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 ${config?.behavior?.preChat ? '' : 'opacity-75'}`}
                    >
                        Pre-Chat Form
                        {!config?.behavior?.preChat && <Badge variant="outline" className="ml-2 text-xs py-0">Disabled</Badge>}
                    </TabsTrigger>
                    <TabsTrigger
                        value="postChatSurvey"
                        className={`rounded-none pb-2 px-4 text-sm h-9 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:shadow-none data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 ${config?.behavior?.postChat ? '' : 'opacity-75'}`}
                    >
                        Post-Chat Survey
                        {!config?.behavior?.postChat && <Badge variant="outline" className="ml-2 text-xs py-0">Disabled</Badge>}
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="preChatForm" className="pt-6">
                    <Card className={`${!config?.behavior?.preChat ? 'opacity-75 pointer-events-none' : ''}`}>
                        <CardHeader>
                            <CardTitle>Pre-Chat Form Configuration</CardTitle>
                            <CardDescription>
                                Customize the form that collects user information before starting the chat
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Form Text Configuration */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label>Form Title</Label>
                                    <Input
                                        value={config?.preChatForm?.title || "Welcome to Chat"}
                                        onChange={(e) => handleFormSettingChange("title", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Form Title"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label>Button Text</Label>
                                    <Input
                                        value={config?.preChatForm?.buttonText || "Start Chat"}
                                        onChange={(e) => handleFormSettingChange("buttonText", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Submit Button Text"
                                    />
                                </div>

                                <div className="space-y-2 col-span-2">
                                    <Label>Form Description</Label>
                                    <Input
                                        value={config?.preChatForm?.description || "Please provide your information to get started"}
                                        onChange={(e) => handleFormSettingChange("description", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Form Description"
                                    />
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium">Form Fields</h3>
                                </div>

                                {preChatFormFields.length > 0 ? (
                                    <div className="space-y-3">
                                        {preChatFormFields.map((field) => (
                                            <div key={field.id} className="flex items-center p-3 rounded-md border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                                <div className="flex-1 space-y-2">
                                                    <div className="flex items-center">
                                                        <p className="font-medium">{field.label}</p>
                                                        {field.isRequired && (
                                                            <Badge className="ml-2 text-xs py-0 bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800">
                                                                Required
                                                            </Badge>
                                                        )}
                                                    </div>

                                                    <div className="flex items-center gap-3">
                                                        <Select
                                                            value={field.type}
                                                            onValueChange={(value) => handleFieldTypeChange(field.id, value as "text" | "email" | "phone" | "select" | "checkbox")}
                                                        >
                                                            <SelectTrigger className={cn("w-32 h-8 text-xs", darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}>
                                                                <SelectValue />
                                                            </SelectTrigger>
                                                            <SelectContent className={cn(darkMode ? "bg-gray-800 border-gray-700 text-gray-100" : "")}>
                                                                <SelectItem value="text">Text</SelectItem>
                                                                <SelectItem value="email">Email</SelectItem>
                                                                <SelectItem value="phone">Phone</SelectItem>
                                                                <SelectItem value="select">Dropdown</SelectItem>
                                                                <SelectItem value="checkbox">Checkbox</SelectItem>
                                                            </SelectContent>
                                                        </Select>

                                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                                            Field ID: {field.name}
                                                        </p>
                                                    </div>
                                                </div>

                                                <div className="flex items-center gap-2">
                                                    <div className="flex items-center gap-2">
                                                        <Label htmlFor={`field-required-${field.id}`} className="text-xs">Required</Label>
                                                        <Switch
                                                            id={`field-required-${field.id}`}
                                                            checked={field.isRequired}
                                                            onCheckedChange={(checked) => handleFieldRequiredChange(field.id, checked)}
                                                        />
                                                    </div>

                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleRemoveField(field.id)}
                                                        className="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="p-8 text-center border rounded-md border-dashed border-gray-300 dark:border-gray-700">
                                        <MessageSquare className="mx-auto h-10 w-10 text-gray-400 dark:text-gray-600 mb-2" />
                                        <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">No form fields added</h3>
                                        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">Add fields to collect information from your users</p>
                                    </div>
                                )}

                                <div className="flex items-center gap-2 mt-4">
                                    <Input
                                        value={newFieldName}
                                        onChange={(e) => setNewFieldName(e.target.value)}
                                        placeholder="Enter field name..."
                                        className={cn("flex-1", darkMode ? "bg-gray-700 border-gray-600 text-gray-100 placeholder:text-gray-400" : "")}
                                    />
                                    <Button
                                        onClick={handleAddField}
                                        className="gap-1"
                                        disabled={!newFieldName.trim()}
                                    >
                                        <Plus className="h-4 w-4" /> Add Field
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="postChatSurvey" className="pt-6">
                    <Card className={`${!config?.behavior?.postChat ? 'opacity-75 pointer-events-none' : ''}`}>
                        <CardHeader>
                            <CardTitle>Post-Chat Survey Configuration</CardTitle>
                            <CardDescription>
                                Customize the survey that collects feedback after a chat session ends
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Survey Text Configuration */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label>Survey Title</Label>
                                    <Input
                                        value={config?.postChatSurvey?.title || "How was your experience?"}
                                        onChange={(e) => handleSurveySettingChange("title", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Survey Title"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label>Button Text</Label>
                                    <Input
                                        value={config?.postChatSurvey?.buttonText || "Submit Feedback"}
                                        onChange={(e) => handleSurveySettingChange("buttonText", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Submit Button Text"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label>Survey Description</Label>
                                    <Input
                                        value={config?.postChatSurvey?.description || "Please take a moment to provide feedback"}
                                        onChange={(e) => handleSurveySettingChange("description", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Survey Description"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label>Thank You Message</Label>
                                    <Input
                                        value={config?.postChatSurvey?.thankYouMessage || "Thank you for your feedback!"}
                                        onChange={(e) => handleSurveySettingChange("thankYouMessage", e.target.value)}
                                        className={cn(darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}
                                        placeholder="Thank You Message"
                                    />
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <Label htmlFor="survey-show-always">Always show survey</Label>
                                <Switch
                                    id="survey-show-always"
                                    checked={config?.postChatSurvey?.showAlways ?? true}
                                    onCheckedChange={(checked) => handleSurveySettingChange("showAlways", checked)}
                                />
                            </div>

                            <Separator />

                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium">Survey Questions</h3>
                                </div>

                                {postChatSurveyQuestions.length > 0 ? (
                                    <div className="space-y-3">
                                        {postChatSurveyQuestions.map((question) => (
                                            <div key={question.id} className="p-3 rounded-md border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                                <div className="flex justify-between">
                                                    <div className="flex-1">
                                                        <p className="font-medium">{question.text}</p>
                                                        <div className="flex items-center mt-2 gap-3">
                                                            <Select
                                                                value={question.type}
                                                                onValueChange={(value) => handleQuestionTypeChange(question.id, value as "rating" | "text" | "select" | "boolean" | "checkbox" | "multiselect")}
                                                            >
                                                                <SelectTrigger className={cn("w-32 h-8 text-xs", darkMode ? "bg-gray-700 border-gray-600 text-gray-100" : "")}>
                                                                    <SelectValue />
                                                                </SelectTrigger>
                                                                <SelectContent className={cn(darkMode ? "bg-gray-800 border-gray-700 text-gray-100" : "")}>
                                                                    <SelectItem value="rating">Star Rating</SelectItem>
                                                                    <SelectItem value="text">Text Input</SelectItem>
                                                                    <SelectItem value="select">Dropdown</SelectItem>
                                                                    <SelectItem value="boolean">Yes/No</SelectItem>
                                                                    <SelectItem value="checkbox">Checkbox</SelectItem>
                                                                    <SelectItem value="multiselect">Multi-Select</SelectItem>
                                                                </SelectContent>
                                                            </Select>

                                                            {question.isRequired && (
                                                                <Badge className="text-xs py-0 bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800">
                                                                    Required
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <div className="flex items-center gap-2">
                                                            <Label htmlFor={`question-required-${question.id}`} className="text-xs">Required</Label>
                                                            <Switch
                                                                id={`question-required-${question.id}`}
                                                                checked={question.isRequired}
                                                                onCheckedChange={(checked) => handleQuestionRequiredChange(question.id, checked)}
                                                            />
                                                        </div>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => handleRemoveQuestion(question.id)}
                                                            className="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="p-8 text-center border rounded-md border-dashed border-gray-300 dark:border-gray-700">
                                        <Clipboard className="mx-auto h-10 w-10 text-gray-400 dark:text-gray-600 mb-2" />
                                        <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">No survey questions added</h3>
                                        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">Add questions to gather feedback from your users</p>
                                    </div>
                                )}

                                <div className="flex items-center gap-2 mt-4">
                                    <Input
                                        value={newQuestionText}
                                        onChange={(e) => setNewQuestionText(e.target.value)}
                                        placeholder="Enter question text..."
                                        className={cn("flex-1", darkMode ? "bg-gray-700 border-gray-600 text-gray-100 placeholder:text-gray-400" : "")}
                                    />
                                    <Button
                                        onClick={handleAddQuestion}
                                        className="gap-1"
                                        disabled={!newQuestionText.trim()}
                                    >
                                        <Plus className="h-4 w-4" /> Add Question
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default ContentTab; 