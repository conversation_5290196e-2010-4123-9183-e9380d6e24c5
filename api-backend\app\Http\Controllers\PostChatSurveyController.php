<?php

namespace App\Http\Controllers;

use App\Models\Widget;
use App\Models\PostChatSurvey;
use App\Models\SurveyQuestion;
use App\Models\SurveyResponse;
use App\Models\ChatSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PostChatSurveyController extends Controller
{
    /**
     * Get post-chat survey for a widget.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getSurvey(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'widget_id' => 'required|string|exists:widgets,widget_id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::where('widget_id', $request->widget_id)
                          ->where('is_active', true)
                          ->firstOrFail();

            // Get the active post-chat survey with its questions
            $survey = PostChatSurvey::where('widget_id', $widget->id)
                                  ->where('is_active', true)
                                  ->with(['questions' => function($query) {
                                      $query->orderBy('order');
                                  }])
                                  ->first();

            if (!$survey) {
                return response()->json(['message' => 'No active post-chat survey found'], 404);
            }

            return response()->json($survey);
        } catch (\Exception $e) {
            Log::error('Failed to get post-chat survey: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get post-chat survey'], 500);
        }
    }

    /**
     * Submit post-chat survey.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitSurvey(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'survey_id' => 'required|integer|exists:post_chat_surveys,id',
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'answers' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Check if the survey is active
            $survey = PostChatSurvey::where('id', $request->survey_id)
                                  ->where('is_active', true)
                                  ->firstOrFail();

            // Get required questions
            $requiredQuestions = SurveyQuestion::where('survey_id', $survey->id)
                                            ->where('is_required', true)
                                            ->pluck('id')
                                            ->toArray();

            // Validate required questions
            foreach ($requiredQuestions as $questionId) {
                if (!isset($request->answers[$questionId]) ||
                    (is_string($request->answers[$questionId]) && empty(trim($request->answers[$questionId]))) ||
                    (is_array($request->answers[$questionId]) && empty($request->answers[$questionId]))) {
                    return response()->json([
                        'error' => 'Validation failed',
                        'message' => "Answer for question ID $questionId is required"
                    ], 422);
                }
            }

            // Create response
            $response = new SurveyResponse([
                'survey_id' => $request->survey_id,
                'session_id' => $request->session_id,
                'answers' => $request->answers,
            ]);

            $response->save();

            // Update chat session with survey data
            $session = ChatSession::where('session_id', $request->session_id)->first();
            if ($session) {
                $metadata = $session->metadata ?? [];
                $session->metadata = array_merge($metadata, [
                    'post_chat_survey_id' => $response->id,
                    'survey_completed' => true,
                ]);
                $session->save();
            }

            return response()->json([
                'message' => 'Survey submitted successfully',
                'response_id' => $response->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to submit post-chat survey: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to submit post-chat survey'], 500);
        }
    }

    /**
     * Get all post-chat surveys for a widget.
     */
    public function getSurveys(Request $request, $widgetId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);

            $surveys = PostChatSurvey::where('widget_id', $widget->id)
                ->with(['questions' => function($query) {
                    $query->orderBy('order');
                }])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json($surveys);
        } catch (\Exception $e) {
            Log::error('Failed to get post-chat surveys: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get post-chat surveys'], 500);
        }
    }

    /**
     * Create a new post-chat survey.
     */
    public function createSurvey(Request $request, $widgetId)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'questions' => 'required|array',
            'questions.*.question' => 'required|string',
            'questions.*.type' => 'required|string|in:rating,text,multiple_choice,yes_no',
            'questions.*.options' => 'nullable|array',
            'questions.*.is_required' => 'boolean',
            'questions.*.order' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::findOrFail($widgetId);

            $survey = new PostChatSurvey([
                'widget_id' => $widget->id,
                'title' => $request->title,
                'description' => $request->description,
                'is_active' => false,
            ]);
            $survey->save();

            // Create questions
            foreach ($request->questions as $index => $questionData) {
                $question = new SurveyQuestion([
                    'survey_id' => $survey->id,
                    'question' => $questionData['question'],
                    'type' => $questionData['type'],
                    'options' => $questionData['options'] ?? null,
                    'is_required' => $questionData['is_required'] ?? false,
                    'order' => $questionData['order'] ?? $index,
                ]);
                $question->save();
            }

            $survey->load('questions');
            return response()->json($survey, 201);
        } catch (\Exception $e) {
            Log::error('Failed to create post-chat survey: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create post-chat survey'], 500);
        }
    }

    /**
     * Update a post-chat survey.
     */
    public function updateSurvey(Request $request, $widgetId, $surveyId)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'questions' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $widget = Widget::findOrFail($widgetId);
            $survey = PostChatSurvey::where('widget_id', $widget->id)
                ->findOrFail($surveyId);

            $survey->update([
                'title' => $request->title,
                'description' => $request->description,
            ]);

            // Delete existing questions and recreate
            SurveyQuestion::where('survey_id', $survey->id)->delete();

            foreach ($request->questions as $index => $questionData) {
                $question = new SurveyQuestion([
                    'survey_id' => $survey->id,
                    'question' => $questionData['question'],
                    'type' => $questionData['type'],
                    'options' => $questionData['options'] ?? null,
                    'is_required' => $questionData['is_required'] ?? false,
                    'order' => $questionData['order'] ?? $index,
                ]);
                $question->save();
            }

            $survey->load('questions');
            return response()->json($survey);
        } catch (\Exception $e) {
            Log::error('Failed to update post-chat survey: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update post-chat survey'], 500);
        }
    }

    /**
     * Delete a post-chat survey.
     */
    public function deleteSurvey(Request $request, $widgetId, $surveyId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);
            $survey = PostChatSurvey::where('widget_id', $widget->id)
                ->findOrFail($surveyId);

            // Delete questions first
            SurveyQuestion::where('survey_id', $survey->id)->delete();

            // Delete survey
            $survey->delete();

            return response()->json(['message' => 'Survey deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete post-chat survey: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete post-chat survey'], 500);
        }
    }

    /**
     * Activate a post-chat survey.
     */
    public function activateSurvey(Request $request, $widgetId, $surveyId)
    {
        try {
            $widget = Widget::findOrFail($widgetId);

            // Deactivate all surveys for this widget
            PostChatSurvey::where('widget_id', $widget->id)
                ->update(['is_active' => false]);

            // Activate the selected survey
            $survey = PostChatSurvey::where('widget_id', $widget->id)
                ->findOrFail($surveyId);
            $survey->update(['is_active' => true]);

            return response()->json(['message' => 'Survey activated successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to activate post-chat survey: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to activate post-chat survey'], 500);
        }
    }
}
