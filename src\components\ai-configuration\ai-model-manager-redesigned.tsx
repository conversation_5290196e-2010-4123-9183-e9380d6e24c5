import { useState, useEffect } from "react";
import { useAIModelManagement } from "@/hooks/use-ai-model-management";
import { useModelActions } from "./model-management/model-actions";
import { AIModelData, aiModelService } from "@/utils/ai-model-service";
import { AIModelDialog } from "./model-management/ai-model-dialog";
import { ModelTestChatDialog } from "./model-management/model-test-chat-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Too<PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>rovider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  MessageSquare,
  Settings,
  History,
  BarChart,
  Plus,
  Pencil,
  ToggleLeft,
  ToggleRight,
  Star,
  Trash,
  MoreVertical,
  Check,
  AlertCircle,
  Key,
  RefreshCw,
  Info,
  Zap,
  Sparkles,
  Sliders,
  Search,
  Filter,
  ChevronRight,
  ArrowRight,
  Cpu,
  Database,
  Lock
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface AIModelManagerRedesignedProps {
  initialTab?: string;
  onTabChange?: (tab: string) => void;
}

export function AIModelManagerRedesigned({ initialTab = "basic", onTabChange }: AIModelManagerRedesignedProps = {}) {
  const {
    selectedModelId,
    setSelectedModelId,
    temperature,
    setTemperature,
    maxTokens,
    setMaxTokens,
    apiKey,
    setApiKey,
    isAPIKeyValid,
    setIsAPIKeyValid,
    isLoading,
    isSaving,
    setIsSaving,
    isTesting,
    setIsTesting,
    models,
    setModels,
    selectedModel,
    setSelectedModel,
    isDialogOpen,
    setIsDialogOpen,
    isTestChatOpen,
    setIsTestChatOpen,
    editingModel,
    setEditingModel,
    fetchModels
  } = useAIModelManagement();

  const { toast } = useToast();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [modelToDelete, setModelToDelete] = useState<AIModelData | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeSection, setActiveSection] = useState<string>("overview");
  const [availableModels, setAvailableModels] = useState<any[]>([]);
  const [isDiscoveringModels, setIsDiscoveringModels] = useState(false);
  const [testResult, setTestResult] = useState<any>({ status: "idle" });

  const {
    handleModelSelect,
    handleAPIKeySave,
    handleSaveConfiguration,
    handleTestConnection,
    handleModelDialogSubmit,
    handleOpenTestChat
  } = useModelActions();

  // Filter models based on search query
  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.provider.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Load available models when selected model changes
  useEffect(() => {
    if (selectedModel?.id) {
      loadAvailableModels(selectedModel.id);
    }
  }, [selectedModel?.id]);

  // Load available models from API
  const loadAvailableModels = async (modelId: number) => {
    try {
      const response = await aiModelService.getAvailableModels(modelId);
      if (response.success && response.data) {
        setAvailableModels(response.data);
      } else {
        setAvailableModels([]);
      }
    } catch (error) {
      console.error("Failed to load available models:", error);
      setAvailableModels([]);
    }
  };

  // Handle model selection
  const onModelSelect = (modelId: number) => {
    handleModelSelect(
      String(modelId),
      models,
      setSelectedModelId,
      setSelectedModel,
      setTemperature,
      setMaxTokens,
      setApiKey,
      setIsAPIKeyValid
    );
    setActiveSection("overview");
  };

  // Handle API key save
  const onApiKeySave = async () => {
    await handleAPIKeySave(
      selectedModel,
      selectedModelId,
      apiKey,
      setIsAPIKeyValid,
      setIsSaving
    );
  };

  // Handle configuration save
  const onSaveConfiguration = async () => {
    await handleSaveConfiguration(
      selectedModel,
      selectedModelId,
      temperature,
      maxTokens,
      setModels,
      models,
      setIsSaving
    );
  };

  // Handle model name change
  const onModelNameChange = (modelName: string) => {
    if (!selectedModel) return;
    const updatedSettings = {
      ...selectedModel.settings,
      model_name: modelName
    };
    setSelectedModel({
      ...selectedModel,
      settings: updatedSettings
    });
  };

  // Handle test connection
  const onTestConnection = async () => {
    setTestResult({ status: "pending", message: "Testing connection..." });
    try {
      const startTime = Date.now();
      await handleTestConnection(selectedModel, selectedModelId, setIsTesting);
      const latency = Date.now() - startTime;
      setTestResult({
        status: "success",
        message: "Connection successful! The API key is valid.",
        latency
      });
      return { data: { message: "Connection successful" } };
    } catch (error: any) {
      setTestResult({
        status: "error",
        message: error.message || "Connection failed. Please check your API key."
      });
      throw error;
    }
  };

  // Handle discover models
  const handleDiscoverModels = async () => {
    if (!selectedModel?.id) return;
    setIsDiscoveringModels(true);
    try {
      const response = await aiModelService.discoverModels(selectedModel.id);
      if (response.success) {
        toast({
          title: "Success",
          description: "Models discovered successfully.",
          variant: "success"
        });
        await loadAvailableModels(selectedModel.id);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to discover models.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Failed to discover models:", error);
      toast({
        title: "Error",
        description: "Failed to discover models. Please check your API key.",
        variant: "destructive"
      });
    } finally {
      setIsDiscoveringModels(false);
    }
  };

  // Handle adding new model
  const onAddNewModel = () => {
    window.location.href = "/dashboard/model-management/add";
  };

  // Handle editing model
  const onEditModel = (model: AIModelData) => {
    if (model.id) {
      window.location.href = `/dashboard/model-management/edit/${model.id}`;
    }
  };

  // Handle model dialog submit
  const onModelDialogSubmit = async (formData: AIModelData) => {
    await handleModelDialogSubmit(
      formData,
      editingModel,
      fetchModels,
      setIsDialogOpen,
      setIsSaving
    );
  };

  // Handle toggle model activation
  const onToggleActive = async (model: AIModelData, active: boolean) => {
    if (!model.id) return;
    setIsSaving(true);
    try {
      await aiModelService.toggleModelActivation(model.id, active);
      const updatedModels = models.map(m =>
        m.id === model.id ? { ...m, active } : m
      );
      setModels(updatedModels);
      if (selectedModelId === model.id) {
        setSelectedModel({ ...selectedModel!, active });
      }
      toast({
        title: active ? "Model Activated" : "Model Deactivated",
        description: `${model.name} has been ${active ? "activated" : "deactivated"}`,
        variant: "success"
      });
    } catch (error) {
      console.error("Failed to toggle model activation:", error);
      toast({
        title: "Operation Failed",
        description: `Could not ${active ? "activate" : "deactivate"} the model`,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle setting default model
  const onSetDefault = async (model: AIModelData) => {
    if (!model.id) return;
    setIsSaving(true);
    try {
      await aiModelService.setDefaultModel(model.id);
      const updatedModels = models.map(m => ({
        ...m,
        is_default: m.id === model.id
      }));
      setModels(updatedModels);
      if (selectedModelId === model.id) {
        setSelectedModel({ ...selectedModel!, is_default: true });
      }
      toast({
        title: "Default Model Updated",
        description: `${model.name} is now the default AI model`,
        variant: "success"
      });
    } catch (error) {
      console.error("Failed to set default model:", error);
      toast({
        title: "Operation Failed",
        description: "Could not set the default model",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle deleting model
  const onDeleteModel = (model: AIModelData) => {
    setModelToDelete(model);
    setIsDeleteDialogOpen(true);
  };

  // Confirm model deletion
  const confirmDeleteModel = async () => {
    if (!modelToDelete || !modelToDelete.id) return;
    setIsSaving(true);
    try {
      await aiModelService.deleteModel(modelToDelete.id);
      const updatedModels = models.filter(m => m.id !== modelToDelete.id);
      setModels(updatedModels);
      if (selectedModelId === modelToDelete.id) {
        setSelectedModelId(null);
        setSelectedModel(null);
      }
      toast({
        title: "Model Deleted",
        description: `${modelToDelete.name} has been deleted`,
        variant: "success"
      });
    } catch (error) {
      console.error("Failed to delete model:", error);
      toast({
        title: "Deletion Failed",
        description: "Could not delete the model",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
      setIsDeleteDialogOpen(false);
      setModelToDelete(null);
    }
  };

  // Open test chat dialog
  const onOpenTestChat = () => {
    handleOpenTestChat(
      selectedModel,
      selectedModelId,
      setIsTestChatOpen
    );
  };

  // Render the model card
  const renderModelCard = (model: AIModelData) => {
    const isSelected = selectedModelId === model.id;

    // Get provider logo based on provider name
    const getProviderLogo = (provider: string) => {
      switch (provider.toLowerCase()) {
        case 'openai':
          return '/images/providers/openai-logo.png';
        case 'anthropic':
          return '/images/providers/anthropic-logo.png';
        case 'deepseek':
          return '/images/providers/deepseek-logo.png';
        case 'google':
          return '/images/providers/google-logo.png';
        default:
          return null;
      }
    };

    const providerLogo = getProviderLogo(model.provider);

    return (
      <div
        key={model.id}
        className={cn(
          "group relative flex flex-col p-4 rounded-lg border transition-all duration-200 cursor-pointer hover:shadow-md",
          isSelected
            ? "border-primary bg-primary/5 shadow-sm ring-1 ring-primary/30"
            : "hover:border-primary/30"
        )}
        onClick={() => model.id && onModelSelect(model.id)}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              {providerLogo && (
                <div className="w-8 h-8 flex-shrink-0 rounded overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center">
                  <img
                    src={providerLogo}
                    alt={`${model.provider} logo`}
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              )}
              <div>
                <h3 className="font-medium truncate">{model.name}</h3>
                <div className="text-sm text-muted-foreground font-mono">
                  {model.provider}
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mb-2">
              <Button
                variant="outline"
                size="sm"
                className={`h-6 px-2 flex items-center text-xs ${model.active ? "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-300" : "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300"}`}
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleActive(model, !model.active);
                }}
              >
                {model.active ? (
                  <>
                    <Check className="h-3 w-3 mr-1" />
                    Active
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Inactive
                  </>
                )}
              </Button>

              {model.is_default && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="secondary" className="h-5 px-1 flex items-center">
                        <Star className="h-3 w-3 text-yellow-500 mr-1" />
                        Default
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>Default AI Model</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {model.is_free && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="h-5 px-1 flex items-center bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
                        <Zap className="h-3 w-3 mr-1" />
                        Free
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>Free AI Model</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            {model.settings?.model_name && (
              <div className="text-sm mb-2">
                <span className="text-xs font-medium">Model: </span>
                <span className="text-xs text-muted-foreground">{model.settings.model_name}</span>
              </div>
            )}

            {model.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">{model.description}</p>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onEditModel(model);
              }}>
                <Pencil className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>



              {onSetDefault && !model.is_default && (
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  onSetDefault(model);
                }}>
                  <Star className="mr-2 h-4 w-4" /> Set as Default
                </DropdownMenuItem>
              )}

              {onDeleteModel && (
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteModel(model);
                  }}
                >
                  <Trash className="mr-2 h-4 w-4" /> Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="mt-auto pt-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            {model.settings?.temperature !== undefined && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Sparkles className="h-3 w-3" />
                      <span>{model.settings.temperature.toFixed(1)}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>Temperature: {model.settings.temperature.toFixed(1)}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {model.settings?.max_tokens !== undefined && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Database className="h-3 w-3" />
                      <span>{model.settings.max_tokens}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>Max Tokens: {model.settings.max_tokens}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          {isSelected && (
            <div className="flex items-center">
              <span className="text-xs text-primary font-medium mr-1">Selected</span>
              <ArrowRight className="h-3 w-3 text-primary" />
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render the model list section
  const renderModelList = () => {
    // Sort models to show default ones first, then free ones, then active ones
    const sortedModels = [...filteredModels].sort((a, b) => {
      // Default models should appear first
      if (a.is_default && !b.is_default) return -1;
      if (!a.is_default && b.is_default) return 1;
      // Free models should appear next
      if (a.is_free && !b.is_free) return -1;
      if (!a.is_free && b.is_free) return 1;
      // Then sort by active status
      if (a.active && !b.active) return -1;
      if (!a.active && b.active) return 1;
      // Finally, sort alphabetically by name
      return a.name.localeCompare(b.name);
    });

    return (
      <div className="space-y-4">
        <div className="border rounded-lg p-4 bg-card">
          {isLoading ? (
            <div className="grid grid-cols-1 gap-4 mt-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2 w-full">
                      <div className="flex items-center gap-2">
                        <div className="w-1/3 h-5 bg-muted rounded animate-pulse"></div>
                        <div className="w-16 h-5 bg-muted rounded animate-pulse"></div>
                      </div>
                      <div className="w-1/4 h-4 bg-muted rounded animate-pulse"></div>
                      <div className="w-2/3 h-4 bg-muted rounded animate-pulse"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : sortedModels.length === 0 ? (
            <div className="border rounded-lg p-8 text-center mt-4 bg-card">
              <AlertCircle className="mx-auto h-10 w-10 text-muted-foreground mb-2" />
              <h3 className="font-medium text-lg">No models available</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? "No models match your search" : "Add your first AI model to get started"}
              </p>
              {!searchQuery && (
                <Button onClick={onAddNewModel}>
                  <Plus className="mr-1 h-4 w-4" /> Add Model
                </Button>
              )}
            </div>
          ) : (
            <ScrollArea className="h-[calc(100vh-300px)] pr-4 mt-4">
              <div className="grid grid-cols-1 gap-4">
                {sortedModels.map(renderModelCard)}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    );
  };

  // Render the model details section
  const renderModelDetails = () => {
    if (!selectedModel) {
      return (
        <div className="p-8 text-center">
          <Cpu className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
          <h3 className="font-medium text-lg">No model selected</h3>
          <p className="text-muted-foreground mb-4">
            Select a model from the list to view and edit its details
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-mono">
              {selectedModel.provider}
            </Badge>

            <div className="flex items-center gap-1">
              <span className="text-sm font-medium">Status:</span>
              <Badge variant={selectedModel.active ? "success" : "outline"}>
                {selectedModel.active ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onOpenTestChat}
              className="flex items-center gap-1"
            >
              <MessageSquare className="h-4 w-4" />
              Test Chat
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditModel(selectedModel)}
              className="flex items-center gap-1"
            >
              <Pencil className="h-4 w-4" />
              Edit
            </Button>
          </div>
        </div>

        <p className="text-muted-foreground">{selectedModel.description || "No description provided"}</p>

        <div className="flex items-center gap-4 border rounded-lg p-3 bg-muted/30">
          <div className="flex items-center gap-2">
            {!selectedModel.is_default && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => onSetDefault(selectedModel)}
              >
                <Star className="h-3 w-3 mr-1" /> Set as Default
              </Button>
            )}
          </div>
        </div>

        <div>
          {activeSection === "overview" && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Provider</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono">
                      {selectedModel.provider}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Model Name</h3>
                  <div className="text-sm">
                    {selectedModel.settings?.model_name || "Not specified"}
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Temperature</h3>
                  <div className="text-sm flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs font-mono">
                      {selectedModel.settings?.temperature !== undefined
                        ? selectedModel.settings.temperature.toFixed(1)
                        : "0.7"}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      ({selectedModel.settings?.temperature !== undefined && selectedModel.settings.temperature < 0.4
                        ? "Precise"
                        : selectedModel.settings?.temperature !== undefined && selectedModel.settings.temperature > 0.7
                          ? "Creative"
                          : "Balanced"})
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Max Tokens</h3>
                  <div className="text-sm flex items-center gap-2">
                    <Database className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs font-mono">
                      {selectedModel.settings?.max_tokens || "2048"}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Available Models</h3>
                {availableModels.length === 0 ? (
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">No models discovered yet</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDiscoverModels}
                      disabled={isDiscoveringModels}
                      className="h-8 bg-muted/30"
                    >
                      {isDiscoveringModels ? (
                        <Spinner size="sm" className="mr-2" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      Discover Models
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {availableModels.slice(0, 5).map((model) => (
                        <Badge key={model.name} variant="outline" className="py-1 px-2 bg-muted/30">
                          {model.display_name || model.name}
                        </Badge>
                      ))}
                      {availableModels.length > 5 && (
                        <Badge variant="outline" className="py-1 px-2 bg-muted/30">
                          +{availableModels.length - 5} more
                        </Badge>
                      )}
                    </div>
                    <div className="flex justify-between items-center">
                      <p className="text-xs text-muted-foreground">
                        {availableModels.length} models available
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleDiscoverModels}
                        disabled={isDiscoveringModels}
                        className="h-7 px-2 text-xs bg-muted/30"
                      >
                        {isDiscoveringModels ? (
                          <Spinner size="sm" className="mr-1" />
                        ) : (
                          <RefreshCw className="h-3 w-3 mr-1" />
                        )}
                        Refresh
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditModel(selectedModel)}
                  className="flex items-center gap-1"
                >
                  <Pencil className="h-4 w-4" />
                  Edit Details
                </Button>
              </div>
            </div>
          )}

          {activeSection === "configuration" && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="model-name">Model Name</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleDiscoverModels}
                      disabled={isDiscoveringModels}
                      className="h-7 px-2 text-xs"
                    >
                      {isDiscoveringModels ? (
                        <Spinner size="sm" className="mr-1" />
                      ) : (
                        <RefreshCw className="h-3 w-3 mr-1" />
                      )}
                      Discover Models
                    </Button>
                  </div>

                  <Select
                    value={selectedModel.settings?.model_name || ""}
                    onValueChange={onModelNameChange}
                    disabled={isDiscoveringModels || availableModels.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.map((model) => (
                        <SelectItem key={model.name} value={model.name}>
                          {model.display_name || model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {availableModels.length === 0 && (
                    <p className="text-sm text-muted-foreground">
                      No models available. Try discovering models first.
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="temperature">
                      Temperature: {temperature[0].toFixed(1)}
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-full">
                            <Info className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="w-80">
                          <p className="text-sm">
                            Controls randomness: lower values produce more predictable responses,
                            higher values more creative ones. Range from 0 to 1.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Slider
                    id="temperature"
                    min={0}
                    max={1}
                    step={0.1}
                    value={temperature}
                    onValueChange={setTemperature}
                    disabled={isSaving}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Precise</span>
                    <span>Balanced</span>
                    <span>Creative</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="max-tokens">
                      Max Output Tokens: {maxTokens[0]}
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-full">
                            <Info className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="w-80">
                          <p className="text-sm">
                            Limits the number of tokens in the model's response. One token is roughly 4 characters.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Slider
                    id="max-tokens"
                    min={100}
                    max={4096}
                    step={100}
                    value={maxTokens}
                    onValueChange={setMaxTokens}
                    disabled={isSaving}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Brief</span>
                    <span>Moderate</span>
                    <span>Detailed</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={onSaveConfiguration}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Settings className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {activeSection === "api" && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-key">API Key</Label>
                  <div className="mt-1 flex">
                    <div className="relative flex-1">
                      <Input
                        id="api-key"
                        type="password"
                        placeholder="Enter your API key"
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        className={isAPIKeyValid === false ? "border-red-500 pr-9" : "pr-9"}
                        disabled={isSaving}
                      />
                      <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    </div>
                    <Button
                      onClick={onApiKeySave}
                      className="ml-2"
                      disabled={isSaving}
                    >
                      {isSaving ? <Spinner size="sm" /> : "Save Key"}
                    </Button>
                  </div>
                  {isAPIKeyValid === false && (
                    <p className="text-sm text-red-500 mt-1 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" /> Invalid API key format
                    </p>
                  )}
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={onTestConnection}
                  disabled={isTesting || !isAPIKeyValid}
                >
                  {isTesting ? <Spinner size="sm" className="mr-2" /> : <RefreshCw className="mr-2 h-4 w-4" />}
                  Test Connection
                </Button>

                {testResult.status !== "idle" && (
                  <div className={cn(
                    "p-3 rounded-md text-sm flex items-start gap-2",
                    testResult.status === "success" ? "bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300" :
                      testResult.status === "error" ? "bg-red-50 text-red-700 dark:bg-red-950 dark:text-red-300" :
                        "bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-300"
                  )}>
                    {testResult.status === "success" ? (
                      <Check className="h-4 w-4 mt-0.5" />
                    ) : testResult.status === "error" ? (
                      <AlertCircle className="h-4 w-4 mt-0.5" />
                    ) : (
                      <Spinner size="sm" className="mt-0.5" />
                    )}
                    <div>
                      <p>{testResult.message}</p>
                      {testResult.status === "success" && testResult.latency && (
                        <p className="text-xs mt-1">Response time: {testResult.latency}ms</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeSection === "test" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Test Chat</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onOpenTestChat}
                  className="flex items-center gap-1"
                >
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Open Test Chat
                </Button>
              </div>

              <div className="border rounded-lg p-6 bg-muted/10 text-center">
                <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
                <h3 className="font-medium text-lg mb-2">Test Your AI Model</h3>
                <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                  Test how your AI model responds to different prompts and evaluate its performance with your current configuration.
                </p>
                <Button onClick={onOpenTestChat}>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Start Test Chat
                </Button>
              </div>
            </div>
          )}

          {activeSection === "analytics" && (
            <div className="text-center py-8">
              <BarChart className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
              <h3 className="font-medium text-lg">Analytics Coming Soon</h3>
              <p className="text-muted-foreground mb-4">
                Detailed analytics for model usage and performance will be available in a future update.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">AI Model Management</h2>
        <Button
          onClick={onAddNewModel}
          className="bg-primary text-white hover:bg-primary/90"
        >
          <Plus className="mr-1 h-4 w-4" /> Add Model
        </Button>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-4 p-4 bg-secondary/20 rounded-lg">
        <div className="w-full md:w-auto">
          <div className="relative w-full md:w-[300px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search models..."
              className="pl-8 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="w-full md:w-auto flex flex-wrap justify-center md:justify-end items-center">
          <div className="flex gap-1">
            <Button
              variant={activeSection === "overview" ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveSection("overview")}
              className="flex items-center gap-1"
            >
              <Settings className="h-4 w-4" />
              Overview
            </Button>

            <Button
              variant={activeSection === "configuration" ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveSection("configuration")}
              className="flex items-center gap-1"
            >
              <Sliders className="h-4 w-4" />
              Configuration
            </Button>

            <Button
              variant={activeSection === "api" ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveSection("api")}
              className="flex items-center gap-1"
            >
              <Key className="h-4 w-4" />
              API Key
            </Button>

            <Button
              variant={activeSection === "test" ? "default" : "outline"}
              size="sm"
              onClick={() => {
                if (selectedModel) {
                  setActiveSection("test");
                  onOpenTestChat();
                }
              }}
              disabled={!selectedModel}
              className="flex items-center gap-1"
            >
              <MessageSquare className="h-4 w-4" />
              Test Chat
            </Button>

            <Button
              variant={activeSection === "analytics" ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveSection("analytics")}
              className="flex items-center gap-1"
            >
              <BarChart className="h-4 w-4" />
              Analytics
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 items-start">
        <div className="lg:col-span-5">
          {renderModelList()}
        </div>

        <div className="lg:col-span-7">
          <div className="border rounded-lg p-5 bg-card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">
                {selectedModel ? `${selectedModel.provider}: ${selectedModel.settings?.model_name || selectedModel.name}` : 'Select a model'}
                {selectedModel?.is_default && (
                  <Badge variant="secondary" className="ml-2 flex-inline items-center">
                    <Star className="h-3 w-3 text-yellow-500 mr-1" />
                    Default
                  </Badge>
                )}
              </h2>
            </div>

            {renderModelDetails()}
          </div>
        </div>
      </div>

      {/* Add/Edit Model Dialog removed - now using full page editor */}

      {/* Test Chat Dialog */}
      {isTestChatOpen && selectedModel && (
        <ModelTestChatDialog
          model={selectedModel}
          open={isTestChatOpen}
          onOpenChange={(open) => setIsTestChatOpen(open)}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete AI Model</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {modelToDelete?.name}? This action cannot be undone.
              {modelToDelete?.is_default && (
                <span className="block mt-2 font-semibold text-destructive">
                  Warning: This is your default model. Deleting it will require setting a new default.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteModel}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
