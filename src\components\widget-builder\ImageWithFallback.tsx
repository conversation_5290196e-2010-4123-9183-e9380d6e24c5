import { useState } from 'react';

interface ImageWithFallbackProps extends React.ImgHTMLAttributes<HTMLImageElement> {
    fallbackSrc: string;
}

const ImageWithFallback = ({
    src,
    fallbackSrc,
    alt,
    ...props
}: ImageWithFallbackProps) => {
    const [imgSrc, setImgSrc] = useState<string>(src || '');

    return (
        <img
            {...props}
            src={imgSrc}
            alt={alt}
            onError={() => setImgSrc(fallbackSrc)}
        />
    );
};

export default ImageWithFallback; 