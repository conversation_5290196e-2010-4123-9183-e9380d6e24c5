# Chat Widget Documentation

## Overview

The Chat Widget is a fully-customizable, embeddable chat interface that can be integrated into any website. It provides real-time AI-powered chat functionality with extensive customization options, user data collection, and integration with various AI models.

## Features

### Core Features

- **Real-time Chat**: Seamless conversational interface with AI-powered responses
- **Customizable Appearance**: Fully configurable colors, sizes, animations, and positioning
- **Pre-Chat Forms**: Collect user information before starting conversations
- **Post-Chat Surveys**: Gather feedback after chat sessions
- **User Ratings**: Allow users to rate AI responses
- **Multiple Embedding Options**: Standard script, iframe, or web component
- **Domain Restrictions**: Control which domains can load your widget
- **AI Model Selection**: Use different AI models or smart routing
- **Mobile Responsive**: Optimized for all device sizes

### Security Features

- **Domain Validation**: Restrict widget usage to specific domains
- **Rate Limiting**: Prevent abuse with configurable rate limits
- **Data Sanitization**: All user inputs are properly sanitized
- **CSRF Protection**: Cross-site request forgery prevention
- **Secure Embedding**: Support for Subresource Integrity (SRI)
- **CSP Support**: Content Security Policy compatibility

## Widget Configuration

### General Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Widget Name | Internal name for the widget | "My Chat Widget" |
| Welcome Message | First message shown to users | "Hello! How can I help you today?" |
| Bot Name | Name displayed in the conversation | "AI Assistant" |
| Placeholder Text | Text shown in empty input field | "Type your message..." |
| Widget Position | Location on the page | bottom-right |

### Appearance Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Primary Color | Main accent color | #7E69AB |
| Secondary Color | Background color for messages | #f3f4f6 |
| Header Background Color | Background for the widget header | #1f2937 |
| Text Color | Color for message text | #111827 |
| Font Size | Size of text in the widget | 14px |
| Border Radius | Roundness of widget corners | 8px |
| Widget Width | Width of the expanded widget | 350px |
| Widget Height | Height of the expanded widget | 600px |
| Animation | Opening animation effect | fade |
| Shadow | Shadow effect intensity | medium |
| Glass Morphism | Modern glass-like effect | false |
| Dark Mode | Use dark color scheme | false |
| Custom CSS | Advanced styling options | - |

### Behavior Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Start Minimized | Widget starts collapsed | false |
| Auto Open | Automatically open after delay | false |
| Auto Open Delay | Seconds before auto-opening | 5 |
| Typing Indicator | Show animation when AI is "typing" | true |
| User Ratings | Allow users to rate responses | true |
| Collect User Data | Show pre-chat form | false |
| Pre-Chat Form | Show form before chat starts | false |
| Post-Chat Survey | Show survey after chat ends | false |
| Close After Inactivity | Auto-close when inactive | false |
| Inactivity Timeout | Minutes before closing | 5 |

### Advanced Settings

| Setting | Description | Default |
|---------|-------------|---------|
| AI Model Selection | Which AI model to use | auto |
| Context Retention | How long to keep conversation context | session |
| Max Messages Stored | Maximum message history | 100 |
| Enable Analytics | Collect usage statistics | true |
| Debug Mode | Show detailed logs | false |
| Load Timeout | Maximum widget load time (ms) | 5000 |
| Webhook URL | Send events to external URL | - |
| Custom Parameters | Additional parameters | - |

## Embedding the Widget

### Standard Script Tag

```html
<script src="https://your-domain.com/widget/v1.0/script.js"
  data-widget-id="your-widget-id"
  data-primary-color="#7E69AB"
  data-border-radius="8"
  data-allowed-domains="example.com,*.example.org"
  async>
</script>
```

### Iframe Embed

```html
<iframe
  src="https://your-domain.com/widget/v1.0/iframe/your-widget-id"
  id="ai-chat-iframe"
  style="position: fixed; bottom: 20px; right: 20px; width: 50px; height: 50px; border: none; z-index: 9999;"
  allow="microphone; camera"
  loading="lazy"
  title="AI Chat Widget">
</iframe>
```

### Web Component

```html
<script type="module" src="https://your-domain.com/widget/v1.0/web-component.js"></script>
<ai-chat-widget
  widget-id="your-widget-id"
  primary-color="#7E69AB"
  border-radius="8"
  allowed-domains="example.com,*.example.org">
</ai-chat-widget>
```

## Pre-Chat Forms

Pre-chat forms collect information from users before starting a conversation. You can configure custom fields including:

- Text inputs
- Email inputs
- Phone number inputs
- Select dropdowns
- Checkboxes

All fields can be set as required or optional, with custom validation rules.

## Post-Chat Surveys

Post-chat surveys gather feedback after a conversation ends. Survey options include:

- Rating questions (1-5 stars)
- Text feedback
- Multiple choice questions
- Yes/No questions
- Checkbox options

## Security Best Practices

1. **Always restrict domains**: Use the allowed_domains setting to limit where your widget can be used
2. **Use rate limiting**: The default rate limits prevent abuse, but adjust them based on your traffic needs
3. **Enable SRI**: For production, enable Subresource Integrity checks for the script embedding option
4. **Set up CSP**: Configure your site's Content Security Policy to work with the widget
5. **Sanitize data**: All user inputs should be sanitized (already implemented in the widget)
6. **Monitor analytics**: Regularly check analytics for unusual patterns or potential abuse

## Performance Optimization

The widget is optimized for performance in several ways:

1. **Lazy loading**: Widget assets load only when needed
2. **Caching**: Widget configuration is cached to reduce server load
3. **Selective loading**: Only necessary components are loaded based on configuration
4. **Optimized assets**: All assets are minified and compressed
5. **Rate limiting**: Prevents excessive requests to protect server resources

## Troubleshooting

### Common Issues

1. **Widget doesn't appear**: Check domain restrictions and console errors
2. **Styling issues**: Verify your custom CSS isn't conflicting with the widget
3. **Performance problems**: Check network tab for slow requests
4. **Embedding errors**: Verify the widget ID and embedding code
5. **AI response failures**: Check AI model configuration and fallback settings

### Browser Compatibility

The widget is tested and compatible with:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Chrome for Android 90+
- Safari for iOS 14+

## API Reference

For developers needing to interact with the widget programmatically, the widget exposes a JavaScript API:

```javascript
// Initialize the widget manually
window.AIChat.init({widgetId: 'your-widget-id'});

// Open the widget
window.AIChat.open();

// Close the widget
window.AIChat.close();

// Toggle the widget
window.AIChat.toggle();

// Send a message programmatically
window.AIChat.sendMessage('Hello from my website!');

// Listen for events
window.AIChat.on('ready', function() {
  console.log('Widget is ready');
});

window.AIChat.on('message', function(message) {
  console.log('New message:', message);
});
```

## Deployment Checklist

Before deploying to production, ensure:

1. ✅ All widget settings are properly configured
2. ✅ Domain restrictions are set correctly
3. ✅ Rate limiting is appropriate for expected traffic
4. ✅ Widget appearance matches your brand guidelines
5. ✅ Pre-chat forms and post-chat surveys are properly set up
6. ✅ AI model selection and fallback options are configured
7. ✅ Widget has been tested across all supported browsers
8. ✅ Mobile functionality has been verified
9. ✅ CORS settings allow the widget to work from all required domains
10. ✅ Analytics are properly tracking widget usage 