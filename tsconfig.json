{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowJs": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "Node", "target": "ES6", "skipLibCheck": true, "noImplicitAny": false, "noUnusedParameters": false, "noUnusedLocals": false, "strictNullChecks": false}}