import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { CornerDownLeft, CornerDownRight, CornerUpLeft, CornerUpRight, AlignCenter } from "lucide-react";

interface PositionSettings {
    type: 'fixed' | 'relative' | 'inline';
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'bottom-center' | 'top-center';
    horizontalOffset?: number;
    verticalOffset?: number;
    size?: {
        width: number;
        height: number;
    };
    responsiveMode?: 'maintain' | 'fullscreen' | 'collapse';
}

interface PositioningControlsProps {
    settings: PositionSettings;
    onChange: (settings: PositionSettings) => void;
}

export function PositioningControls({
    settings,
    onChange
}: PositioningControlsProps) {
    const [localSettings, setLocalSettings] = useState<PositionSettings>({
        type: settings.type || 'fixed',
        position: settings.position || 'bottom-right',
        horizontalOffset: settings.horizontalOffset || 20,
        verticalOffset: settings.verticalOffset || 20,
        size: settings.size || { width: 350, height: 500 },
        responsiveMode: settings.responsiveMode || 'maintain'
    });

    const handleChange = (key: string, value: any) => {
        const updated = { ...localSettings, [key]: value };
        setLocalSettings(updated);
        onChange(updated);
    };

    const handleSizeChange = (dimension: 'width' | 'height', value: number) => {
        const updated = {
            ...localSettings,
            size: {
                ...localSettings.size,
                [dimension]: value
            }
        };
        setLocalSettings(updated);
        onChange(updated);
    };

    return (
        <div className="space-y-6">
            <div>
                <Label className="text-base">Widget Position Type</Label>
                <p className="text-sm text-muted-foreground mb-2">
                    Choose how your widget will be positioned on the page
                </p>
                <RadioGroup
                    value={localSettings.type}
                    onValueChange={(value) => handleChange('type', value)}
                    className="grid grid-cols-3 gap-2 mt-2"
                >
                    <div>
                        <RadioGroupItem
                            value="fixed"
                            id="fixed-position"
                            className="peer sr-only"
                        />
                        <Label
                            htmlFor="fixed-position"
                            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                        >
                            <CornerDownRight className="mb-2 h-6 w-6" />
                            <span>Fixed</span>
                            <span className="text-xs text-muted-foreground font-normal">
                                Floating button
                            </span>
                        </Label>
                    </div>

                    <div>
                        <RadioGroupItem
                            value="relative"
                            id="relative-position"
                            className="peer sr-only"
                        />
                        <Label
                            htmlFor="relative-position"
                            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                        >
                            <AlignCenter className="mb-2 h-6 w-6" />
                            <span>Relative</span>
                            <span className="text-xs text-muted-foreground font-normal">
                                Within container
                            </span>
                        </Label>
                    </div>

                    <div>
                        <RadioGroupItem
                            value="inline"
                            id="inline-position"
                            className="peer sr-only"
                        />
                        <Label
                            htmlFor="inline-position"
                            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                        >
                            <AlignCenter className="mb-2 h-6 w-6 rotate-90" />
                            <span>Inline</span>
                            <span className="text-xs text-muted-foreground font-normal">
                                Embedded in page
                            </span>
                        </Label>
                    </div>
                </RadioGroup>
            </div>

            {localSettings.type === 'fixed' && (
                <div className="space-y-4">
                    <div>
                        <Label className="text-base">Widget Position</Label>
                        <div className="grid grid-cols-3 gap-2 mt-2">
                            <Button
                                type="button"
                                variant={localSettings.position === 'top-left' ? 'default' : 'outline'}
                                className={cn(
                                    "h-20 flex flex-col items-center justify-center gap-2",
                                    localSettings.position === 'top-left' && "border-primary"
                                )}
                                onClick={() => handleChange('position', 'top-left')}
                            >
                                <CornerUpLeft className="h-6 w-6" />
                                <span className="text-xs">Top Left</span>
                            </Button>

                            <Button
                                type="button"
                                variant={localSettings.position === 'top-center' ? 'default' : 'outline'}
                                className={cn(
                                    "h-20 flex flex-col items-center justify-center gap-2",
                                    localSettings.position === 'top-center' && "border-primary"
                                )}
                                onClick={() => handleChange('position', 'top-center')}
                            >
                                <AlignCenter className="h-6 w-6 rotate-180" />
                                <span className="text-xs">Top Center</span>
                            </Button>

                            <Button
                                type="button"
                                variant={localSettings.position === 'top-right' ? 'default' : 'outline'}
                                className={cn(
                                    "h-20 flex flex-col items-center justify-center gap-2",
                                    localSettings.position === 'top-right' && "border-primary"
                                )}
                                onClick={() => handleChange('position', 'top-right')}
                            >
                                <CornerUpRight className="h-6 w-6" />
                                <span className="text-xs">Top Right</span>
                            </Button>

                            <Button
                                type="button"
                                variant={localSettings.position === 'bottom-left' ? 'default' : 'outline'}
                                className={cn(
                                    "h-20 flex flex-col items-center justify-center gap-2",
                                    localSettings.position === 'bottom-left' && "border-primary"
                                )}
                                onClick={() => handleChange('position', 'bottom-left')}
                            >
                                <CornerDownLeft className="h-6 w-6" />
                                <span className="text-xs">Bottom Left</span>
                            </Button>

                            <Button
                                type="button"
                                variant={localSettings.position === 'bottom-center' ? 'default' : 'outline'}
                                className={cn(
                                    "h-20 flex flex-col items-center justify-center gap-2",
                                    localSettings.position === 'bottom-center' && "border-primary"
                                )}
                                onClick={() => handleChange('position', 'bottom-center')}
                            >
                                <AlignCenter className="h-6 w-6" />
                                <span className="text-xs">Bottom Center</span>
                            </Button>

                            <Button
                                type="button"
                                variant={localSettings.position === 'bottom-right' ? 'default' : 'outline'}
                                className={cn(
                                    "h-20 flex flex-col items-center justify-center gap-2",
                                    localSettings.position === 'bottom-right' && "border-primary"
                                )}
                                onClick={() => handleChange('position', 'bottom-right')}
                            >
                                <CornerDownRight className="h-6 w-6" />
                                <span className="text-xs">Bottom Right</span>
                            </Button>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <Label htmlFor="horizontalOffset">
                                Horizontal Offset: {localSettings.horizontalOffset}px
                            </Label>
                        </div>
                        <Slider
                            id="horizontalOffset"
                            min={0}
                            max={50}
                            step={1}
                            value={[localSettings.horizontalOffset || 20]}
                            onValueChange={(value) => handleChange('horizontalOffset', value[0])}
                        />
                    </div>

                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <Label htmlFor="verticalOffset">
                                Vertical Offset: {localSettings.verticalOffset}px
                            </Label>
                        </div>
                        <Slider
                            id="verticalOffset"
                            min={0}
                            max={50}
                            step={1}
                            value={[localSettings.verticalOffset || 20]}
                            onValueChange={(value) => handleChange('verticalOffset', value[0])}
                        />
                    </div>
                </div>
            )}

            <div className="space-y-4 pt-2 border-t">
                <Label className="text-base">Size Settings</Label>

                <Tabs defaultValue="desktop">
                    <TabsList className="grid grid-cols-3 mb-4">
                        <TabsTrigger value="desktop">Desktop</TabsTrigger>
                        <TabsTrigger value="tablet">Tablet</TabsTrigger>
                        <TabsTrigger value="mobile">Mobile</TabsTrigger>
                    </TabsList>

                    <TabsContent value="desktop" className="space-y-4">
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <Label htmlFor="width">
                                    Width: {localSettings.size?.width || 350}px
                                </Label>
                            </div>
                            <Slider
                                id="width"
                                min={250}
                                max={600}
                                step={10}
                                value={[localSettings.size?.width || 350]}
                                onValueChange={(value) => handleSizeChange('width', value[0])}
                            />
                        </div>

                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <Label htmlFor="height">
                                    Height: {localSettings.size?.height || 500}px
                                </Label>
                            </div>
                            <Slider
                                id="height"
                                min={300}
                                max={800}
                                step={10}
                                value={[localSettings.size?.height || 500]}
                                onValueChange={(value) => handleSizeChange('height', value[0])}
                            />
                        </div>
                    </TabsContent>

                    <TabsContent value="tablet" className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="tabletMode">Tablet Behavior</Label>
                            <Select
                                value={localSettings.responsiveMode}
                                onValueChange={(value) => handleChange('responsiveMode', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select tablet behavior" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="maintain">Maintain Size</SelectItem>
                                    <SelectItem value="fullscreen">Fullscreen</SelectItem>
                                    <SelectItem value="collapse">Auto-Collapse</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </TabsContent>

                    <TabsContent value="mobile" className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="mobileMode">Mobile Behavior</Label>
                            <Select
                                value={localSettings.responsiveMode}
                                onValueChange={(value) => handleChange('responsiveMode', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select mobile behavior" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="maintain">Maintain Size</SelectItem>
                                    <SelectItem value="fullscreen">Fullscreen</SelectItem>
                                    <SelectItem value="collapse">Auto-Collapse</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
} 