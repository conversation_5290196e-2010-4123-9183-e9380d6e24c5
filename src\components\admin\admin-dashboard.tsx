import React, { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  Settings,
  MessageSquare,
  ClipboardList,
  Users,
  Activity,
  TrendingUp,
  Calendar
} from "lucide-react"
import { WidgetStatisticsDashboard } from "@/components/analytics/widget-statistics-dashboard"
import { PreChatFormManager } from "@/components/admin/pre-chat-form-manager"
import { PostChatSurveyManager } from "@/components/admin/post-chat-survey-manager"
import { apiService } from "@/utils/api-service"
import { useToast } from "@/hooks/use-toast"

interface AdminDashboardProps {
  widgetId: string
  widgetName?: string
}

interface DashboardOverview {
  total_widgets: number
  active_sessions: number
  total_messages_today: number
  avg_response_time: number
  recent_activities: Array<{
    id: string
    type: string
    description: string
    timestamp: string
  }>
}

export function AdminDashboard({ widgetId, widgetName = "Widget" }: AdminDashboardProps) {
  const [overview, setOverview] = useState<DashboardOverview | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const { toast } = useToast()

  const fetchOverview = async () => {
    try {
      setLoading(true)

      // Fetch real-time stats and analytics summary
      const [realtimeResponse, summaryResponse] = await Promise.all([
        apiService.get(`/widgets/${widgetId}/statistics/realtime`),
        apiService.get(`/widgets/${widgetId}/analytics/summary?period=day`)
      ])

      // Calculate average response time from analytics data
      const avgResponseTime = summaryResponse.data.avg_messages_per_conversation > 0
        ? Math.round(2000 / summaryResponse.data.avg_messages_per_conversation) / 1000
        : 0

      setOverview({
        total_widgets: 1,
        active_sessions: realtimeResponse.data.active_sessions || 0,
        total_messages_today: realtimeResponse.data.messages_today || 0,
        avg_response_time: avgResponseTime,
        recent_activities: [] // Real activity data would come from a dedicated endpoint
      })
    } catch (error) {
      console.error("Failed to fetch overview:", error)
      toast({
        title: "Error",
        description: "Failed to load dashboard overview",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOverview()
    // Refresh overview every 30 seconds
    const interval = setInterval(fetchOverview, 30000)
    return () => clearInterval(interval)
  }, [widgetId])

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'session_started':
        return <Users className="h-4 w-4 text-green-600" />
      case 'form_submitted':
        return <ClipboardList className="h-4 w-4 text-blue-600" />
      case 'survey_completed':
        return <MessageSquare className="h-4 w-4 text-purple-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">Manage {widgetName} settings and view analytics</p>
        </div>
        <Badge variant="outline" className="text-sm">
          Widget ID: {widgetId}
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="forms" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            Forms
          </TabsTrigger>
          <TabsTrigger value="surveys" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Surveys
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          {overview && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Active Sessions</p>
                      <p className="text-2xl font-bold text-green-600">{overview.active_sessions}</p>
                    </div>
                    <Activity className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Messages Today</p>
                      <p className="text-2xl font-bold">{overview.total_messages_today}</p>
                    </div>
                    <MessageSquare className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Avg Response Time</p>
                      <p className="text-2xl font-bold">{overview.avg_response_time}s</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Widgets</p>
                      <p className="text-2xl font-bold">{overview.total_widgets}</p>
                    </div>
                    <Settings className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Activity tracking coming soon</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Real-time activity logs will be available in the next update
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setActiveTab("forms")}
                >
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Manage Pre-Chat Forms
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setActiveTab("surveys")}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Manage Post-Chat Surveys
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setActiveTab("analytics")}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Detailed Analytics
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => window.open(`/widgets/${widgetId}/settings`, '_blank')}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Widget Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <WidgetStatisticsDashboard widgetId={widgetId} />
        </TabsContent>

        <TabsContent value="forms">
          <PreChatFormManager widgetId={widgetId} />
        </TabsContent>

        <TabsContent value="surveys">
          <PostChatSurveyManager widgetId={widgetId} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
