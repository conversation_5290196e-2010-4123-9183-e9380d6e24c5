'use client'

import { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { AlertCircle, CheckCircle2, Copy, ExternalLink, Info, Settings, Trash } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/components/ui/use-toast'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ControllerRenderProps } from 'react-hook-form'
import axios from 'axios'
import { API_BASE_URL } from '@/utils/constants'

// Add type declaration for gtag
declare global {
    interface Window {
        gtag?: (command: string, action: string, params?: Record<string, any>) => void;
    }
}

// Simple analytics tracking
const trackEvent = (event: string, data?: Record<string, any>) => {
    // Replace with your actual analytics implementation
    if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', event, data);
    }
}

// Integration service for API calls
const integrationService = {
    testIntegration: async (integrationData: Partial<Integration>) => {
        try {
            // Format the payload based on integration type
            let payload = {};

            switch (integrationData.type) {
                case 'slack':
                    payload = {
                        text: "This is a test message from your chat widget",
                        username: "Test User",
                        icon_emoji: ":bell:",
                        blocks: [
                            {
                                "type": "section",
                                "text": {
                                    "type": "mrkdwn",
                                    "text": "*Test Message*\nThis is a test message from your chat widget."
                                }
                            },
                            {
                                "type": "context",
                                "elements": [
                                    {
                                        "type": "mrkdwn",
                                        "text": "Sent by: Test User"
                                    }
                                ]
                            }
                        ]
                    };
                    break;

                case 'ms-teams':
                    payload = {
                        "@type": "MessageCard",
                        "@context": "http://schema.org/extensions",
                        "themeColor": "0076D7",
                        "summary": "Test Message",
                        "sections": [{
                            "activityTitle": "Test Notification",
                            "activitySubtitle": "From Chat Widget",
                            "text": "This is a test message from your chat widget"
                        }]
                    };
                    break;

                case 'discord':
                    payload = {
                        content: "This is a test message from your chat widget",
                        username: "Chat Widget",
                        embeds: [{
                            title: "Test Notification",
                            description: "This is a test message from your chat widget",
                            color: 3447003
                        }]
                    };
                    break;

                default:
                    // Generic webhook format
                    payload = {
                        test: true,
                        timestamp: new Date().toISOString(),
                        integration_type: integrationData.type,
                        message: "This is a test message from your chat widget",
                        user_name: "Test User",
                        session_id: `test-session-${Math.floor(Date.now() / 1000)}`
                    };
            }

            const response = await axios.post(`${API_BASE_URL}/integrations/test`, {
                type: integrationData.type,
                url: integrationData.url,
                name: integrationData.name,
                events: integrationData.events,
                secret: integrationData.secret,
                // Add the properly formatted payload
                customPayload: payload
            });

            trackEvent('integration_test_success', { type: integrationData.type });
            return response.data;
        } catch (error) {
            console.error('Integration test failed:', error)
            trackEvent('integration_test_error', {
                type: integrationData.type,
                error: axios.isAxiosError(error) && error.response?.status ? error.response.status : 'unknown'
            });
            if (axios.isAxiosError(error) && error.response) {
                throw new Error(error.response.data.message || 'Integration test failed')
            }
            throw new Error('Failed to connect to the integration test endpoint')
        }
    }
}

interface Integration {
    id: string
    type: 'slack' | 'ms-teams' | 'discord' | 'zapier' | 'generic'
    name: string
    url: string
    active: boolean
    events: string[]
    secret?: string
    created_at: string
}

interface IntegrationsTabProps {
    field: ControllerRenderProps<any, any>
    webhookUrl?: string
}

export default function IntegrationsTab({ field, webhookUrl }: IntegrationsTabProps) {
    const { toast } = useToast()
    const [integrations, setIntegrations] = useState<Integration[]>([])
    const [currentIntegration, setCurrentIntegration] = useState<Partial<Integration> | null>(null)
    const [isEditing, setIsEditing] = useState(false)
    const [integrationTab, setIntegrationTab] = useState('slack')
    const [isTestingConnection, setIsTestingConnection] = useState(false)
    const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)

    // Create a ref for the onChange function to prevent it from causing re-renders
    const onChangeRef = useRef(field.onChange)

    // Update the ref if field.onChange changes
    useEffect(() => {
        onChangeRef.current = field.onChange
    }, [field.onChange])

    // Initialize with any existing webhookUrl
    useEffect(() => {
        const initialIntegrations = [];

        if (webhookUrl && !integrations.length) {
            // Convert legacy webhookUrl to generic integration
            initialIntegrations.push({
                id: 'legacy',
                type: 'generic',
                name: 'Legacy Webhook',
                url: webhookUrl,
                active: true,
                events: ['message.new', 'rating.submit', 'session.start', 'session.end'],
                created_at: new Date().toISOString()
            });
        }

        // Load integrations from field.value if it exists and is properly formatted
        if (field.value && Array.isArray(field.value) && field.value.length > 0) {
            setIntegrations(field.value);
            return;
        }

        // Only set state if we actually have initial integrations
        if (initialIntegrations.length > 0) {
            setIntegrations(initialIntegrations);
        }
    }, []) // Run only once on mount

    // Update form field when integrations change
    useEffect(() => {
        onChangeRef.current(integrations)
    }, [integrations])

    const addIntegration = (type: Integration['type']) => {
        setCurrentIntegration({
            id: `${type}-${Date.now()}`,
            type,
            name: getDefaultNameForType(type),
            url: '',
            active: true,
            events: ['message.new', 'rating.submit']
        })
        setIsEditing(true)
    }

    const getDefaultNameForType = (type: Integration['type']) => {
        switch (type) {
            case 'slack': return 'Slack Channel'
            case 'ms-teams': return 'Teams Channel'
            case 'discord': return 'Discord Channel'
            case 'zapier': return 'Zapier Integration'
            case 'generic': return 'Webhook Endpoint'
            default: return 'New Integration'
        }
    }

    const saveIntegration = () => {
        if (!currentIntegration || !currentIntegration.url) {
            toast({
                title: "Error",
                description: "URL is required for all integrations",
                variant: "destructive"
            })
            return
        }

        const integration = {
            ...currentIntegration,
            created_at: currentIntegration.created_at || new Date().toISOString()
        } as Integration

        // Update existing or add new
        const exists = integrations.some(i => i.id === integration.id)

        if (exists) {
            setIntegrations(prev => prev.map(i => i.id === integration.id ? integration : i))
            toast({
                title: "Integration Updated",
                description: `${integration.name} has been updated successfully.`
            })
            trackEvent('integration_updated', {
                type: integration.type,
                event_count: integration.events.length
            });
        } else {
            setIntegrations(prev => [...prev, integration])
            toast({
                title: "Integration Added",
                description: `${integration.name} has been added successfully.`
            })
            trackEvent('integration_added', {
                type: integration.type,
                event_count: integration.events.length
            });
        }

        setCurrentIntegration(null)
        setIsEditing(false)
        setTestResult(null)
    }

    const editIntegration = (integration: Integration) => {
        setCurrentIntegration(integration)
        setIntegrationTab(integration.type)
        setIsEditing(true)
    }

    const deleteIntegration = (id: string) => {
        const integration = integrations.find(i => i.id === id);
        setIntegrations(prev => prev.filter(i => i.id !== id))
        toast({
            title: "Integration Removed",
            description: "The integration has been removed successfully."
        })

        if (integration) {
            trackEvent('integration_deleted', {
                type: integration.type
            });
        }
    }

    const toggleActive = (id: string) => {
        setIntegrations(prev => {
            const newIntegrations = prev.map(i => {
                if (i.id === id) {
                    const newStatus = !i.active;
                    trackEvent(newStatus ? 'integration_enabled' : 'integration_disabled', {
                        type: i.type
                    });
                    return { ...i, active: newStatus };
                }
                return i;
            });
            return newIntegrations;
        })
    }

    const testConnection = async () => {
        if (!currentIntegration || !currentIntegration.url) {
            toast({
                title: "Error",
                description: "URL is required to test the connection",
                variant: "destructive"
            })
            return
        }

        setIsTestingConnection(true)
        setTestResult(null)

        try {
            const result = await integrationService.testIntegration(currentIntegration)

            setTestResult({
                success: result.success,
                message: result.message || (result.success
                    ? `Successfully sent test message to ${currentIntegration.name}`
                    : 'Failed to connect to integration endpoint')
            })
        } catch (error) {
            setTestResult({
                success: false,
                message: error instanceof Error
                    ? error.message
                    : 'Failed to test integration. Please check the URL and try again.'
            })
        } finally {
            setIsTestingConnection(false)
        }
    }

    const getIntegrationTypeDetails = (type: Integration['type']) => {
        switch (type) {
            case 'slack':
                return {
                    name: 'Slack',
                    description: 'Get chat messages and user feedback in your Slack channel',
                    icon: '/assets/integrations/slack-icon.svg',
                    placeholder: '*****************************************************************************',
                    helpUrl: 'https://api.slack.com/messaging/webhooks',
                }
            case 'ms-teams':
                return {
                    name: 'Microsoft Teams',
                    description: 'Send notifications to your Microsoft Teams channel',
                    icon: '/assets/integrations/teams-icon.svg',
                    placeholder: 'https://outlook.office.com/webhook/...',
                    helpUrl: 'https://learn.microsoft.com/en-us/microsoftteams/platform/webhooks-and-connectors/how-to/add-incoming-webhook',
                }
            case 'discord':
                return {
                    name: 'Discord',
                    description: 'Receive chat notifications in your Discord server',
                    icon: '/assets/integrations/discord-icon.svg',
                    placeholder: 'https://discord.com/api/webhooks/...',
                    helpUrl: 'https://support.discord.com/hc/en-us/articles/228383668-Intro-to-Webhooks',
                }
            case 'zapier':
                return {
                    name: 'Zapier',
                    description: 'Connect your chat widget to thousands of apps with Zapier',
                    icon: '/assets/integrations/zapier-icon.svg',
                    placeholder: 'https://hooks.zapier.com/hooks/catch/...',
                    helpUrl: 'https://zapier.com/apps/webhook/help',
                }
            default:
                return {
                    name: 'Custom Webhook',
                    description: 'Connect to any custom endpoint that accepts webhook data',
                    icon: '/assets/integrations/webhook-icon.svg',
                    placeholder: 'https://example.com/webhook',
                    helpUrl: 'https://en.wikipedia.org/wiki/Webhook',
                }
        }
    }

    // If we're editing, show the edit form
    if (isEditing && currentIntegration) {
        const typeDetails = getIntegrationTypeDetails(currentIntegration.type as Integration['type'])

        return (
            <Card className="w-full">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <img
                            src={typeDetails.icon}
                            alt={typeDetails.name}
                            className="w-5 h-5"
                            onError={(e) => {
                                // If image fails to load, replace with a default icon
                                e.currentTarget.src = '/assets/integrations/webhook-icon.svg'
                            }}
                        />
                        {isEditing ? 'Configure' : 'Add'} {typeDetails.name} Integration
                    </CardTitle>
                    <CardDescription>
                        {typeDetails.description}
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="integration-name">Integration Name</Label>
                        <Input
                            id="integration-name"
                            value={currentIntegration.name || ''}
                            onChange={(e) => setCurrentIntegration({ ...currentIntegration, name: e.target.value })}
                            placeholder="My Slack Channel"
                        />
                    </div>

                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <Label htmlFor="webhook-url">{typeDetails.name} Webhook URL</Label>
                            <a
                                href={typeDetails.helpUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-muted-foreground hover:text-primary flex items-center gap-1"
                            >
                                <Info className="w-3 h-3" /> How to get a webhook URL
                                <ExternalLink className="w-3 h-3" />
                            </a>
                        </div>
                        <Input
                            id="webhook-url"
                            value={currentIntegration.url || ''}
                            onChange={(e) => setCurrentIntegration({ ...currentIntegration, url: e.target.value })}
                            placeholder={typeDetails.placeholder}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label>Notification Events</Label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="event-message"
                                    checked={currentIntegration.events?.includes('message.new') || false}
                                    onCheckedChange={(checked) => {
                                        const events = [...(currentIntegration.events || [])]
                                        if (checked) {
                                            if (!events.includes('message.new')) events.push('message.new')
                                        } else {
                                            const index = events.indexOf('message.new')
                                            if (index > -1) events.splice(index, 1)
                                        }
                                        setCurrentIntegration({ ...currentIntegration, events })
                                    }}
                                />
                                <Label htmlFor="event-message">New messages</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="event-rating"
                                    checked={currentIntegration.events?.includes('rating.submit') || false}
                                    onCheckedChange={(checked) => {
                                        const events = [...(currentIntegration.events || [])]
                                        if (checked) {
                                            if (!events.includes('rating.submit')) events.push('rating.submit')
                                        } else {
                                            const index = events.indexOf('rating.submit')
                                            if (index > -1) events.splice(index, 1)
                                        }
                                        setCurrentIntegration({ ...currentIntegration, events })
                                    }}
                                />
                                <Label htmlFor="event-rating">Message ratings</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="event-session-start"
                                    checked={currentIntegration.events?.includes('session.start') || false}
                                    onCheckedChange={(checked) => {
                                        const events = [...(currentIntegration.events || [])]
                                        if (checked) {
                                            if (!events.includes('session.start')) events.push('session.start')
                                        } else {
                                            const index = events.indexOf('session.start')
                                            if (index > -1) events.splice(index, 1)
                                        }
                                        setCurrentIntegration({ ...currentIntegration, events })
                                    }}
                                />
                                <Label htmlFor="event-session-start">Chat sessions started</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="event-session-end"
                                    checked={currentIntegration.events?.includes('session.end') || false}
                                    onCheckedChange={(checked) => {
                                        const events = [...(currentIntegration.events || [])]
                                        if (checked) {
                                            if (!events.includes('session.end')) events.push('session.end')
                                        } else {
                                            const index = events.indexOf('session.end')
                                            if (index > -1) events.splice(index, 1)
                                        }
                                        setCurrentIntegration({ ...currentIntegration, events })
                                    }}
                                />
                                <Label htmlFor="event-session-end">Chat sessions ended</Label>
                            </div>
                        </div>
                    </div>

                    {currentIntegration.type === 'generic' && (
                        <div className="space-y-2">
                            <Label htmlFor="secret-key">Secret Key (optional)</Label>
                            <Input
                                id="secret-key"
                                type="password"
                                value={currentIntegration.secret || ''}
                                onChange={(e) => setCurrentIntegration({ ...currentIntegration, secret: e.target.value })}
                                placeholder="Secret key for signature verification"
                            />
                            <p className="text-xs text-muted-foreground">
                                If provided, a signature will be included in the X-Webhook-Signature header
                            </p>
                        </div>
                    )}

                    {testResult && (
                        <Alert variant={testResult.success ? "default" : "destructive"} className="mt-4">
                            {testResult.success ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                            <AlertTitle>{testResult.success ? "Success" : "Error"}</AlertTitle>
                            <AlertDescription>
                                {testResult.message}
                            </AlertDescription>
                        </Alert>
                    )}
                </CardContent>
                <CardFooter className="flex justify-between">
                    <Button variant="outline" onClick={() => {
                        setIsEditing(false)
                        setCurrentIntegration(null)
                        setTestResult(null)
                    }}>
                        Cancel
                    </Button>
                    <div className="flex gap-2">
                        <Button
                            variant="secondary"
                            onClick={testConnection}
                            disabled={isTestingConnection || !currentIntegration.url}
                        >
                            {isTestingConnection ? "Testing..." : "Test Connection"}
                        </Button>
                        <Button onClick={saveIntegration}>Save Integration</Button>
                    </div>
                </CardFooter>
            </Card>
        )
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Integrations</h3>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button>Add Integration</Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Integration Types</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => addIntegration('slack')}>
                            Slack
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => addIntegration('ms-teams')}>
                            Microsoft Teams
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => addIntegration('discord')}>
                            Discord
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => addIntegration('zapier')}>
                            Zapier
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => addIntegration('generic')}>
                            Custom Webhook
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {integrations.length === 0 ? (
                <Card className="border-dashed">
                    <CardContent className="pt-6 flex flex-col items-center justify-center text-center space-y-2 p-10">
                        <div className="rounded-full bg-muted h-10 w-10 flex items-center justify-center">
                            <Settings className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <CardTitle className="text-base">No integrations configured</CardTitle>
                        <CardDescription>
                            Add integrations to send chat notifications to your favorite platforms.
                        </CardDescription>
                        <Button
                            className="mt-4"
                            variant="outline"
                            onClick={() => addIntegration('slack')}
                        >
                            Add Your First Integration
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {integrations.map(integration => {
                        const typeDetails = getIntegrationTypeDetails(integration.type)

                        return (
                            <Card key={integration.id} className={!integration.active ? "opacity-70" : ""}>
                                <CardHeader className="pb-2">
                                    <div className="flex justify-between items-start">
                                        <div className="flex items-center gap-2">
                                            <img
                                                src={typeDetails.icon}
                                                alt={typeDetails.name}
                                                className="w-6 h-6"
                                                onError={(e) => {
                                                    e.currentTarget.src = '/assets/integrations/webhook-icon.svg'
                                                }}
                                            />
                                            <div>
                                                <CardTitle className="text-base">{integration.name}</CardTitle>
                                                <CardDescription className="text-xs">
                                                    {typeDetails.name}
                                                </CardDescription>
                                                <div className="flex items-center gap-1 mt-1">
                                                    {integration.active ? (
                                                        <Badge variant="outline" className="text-[10px] h-4 px-1 bg-green-50 text-green-700 hover:bg-green-50">
                                                            Active
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="outline" className="text-[10px] h-4 px-1 bg-muted hover:bg-muted">
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0" title="Options">
                                                        <span className="sr-only">Open menu</span>
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                                            <circle cx="12" cy="12" r="1" />
                                                            <circle cx="12" cy="5" r="1" />
                                                            <circle cx="12" cy="19" r="1" />
                                                        </svg>
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem onClick={() => editIntegration(integration)}>
                                                        Edit
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem onClick={() => toggleActive(integration.id)}>
                                                        {integration.active ? "Disable" : "Enable"}
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        className="text-destructive focus:text-destructive"
                                                        onClick={() => deleteIntegration(integration.id)}
                                                    >
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-xs text-muted-foreground truncate" title={integration.url}>
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <span className="flex items-center gap-1 cursor-default">
                                                        {integration.url.length > 40
                                                            ? integration.url.substring(0, 40) + "..."
                                                            : integration.url}
                                                        <Copy className="h-3 w-3 cursor-pointer hover:text-primary" onClick={() => {
                                                            navigator.clipboard.writeText(integration.url)
                                                            toast({
                                                                title: "URL Copied",
                                                                description: "Webhook URL copied to clipboard"
                                                            })
                                                        }} />
                                                    </span>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    {integration.url}
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                    <div className="flex flex-wrap gap-1 mt-2">
                                        {integration.events.includes('message.new') && (
                                            <Badge variant="secondary" className="text-xs">New messages</Badge>
                                        )}
                                        {integration.events.includes('rating.submit') && (
                                            <Badge variant="secondary" className="text-xs">Ratings</Badge>
                                        )}
                                        {integration.events.includes('session.start') && (
                                            <Badge variant="secondary" className="text-xs">Sessions start</Badge>
                                        )}
                                        {integration.events.includes('session.end') && (
                                            <Badge variant="secondary" className="text-xs">Sessions end</Badge>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        )
                    })}
                </div>
            )}

            <div className="text-sm text-muted-foreground">
                <p className="flex items-center gap-1 mb-1">
                    <Info className="h-4 w-4" />
                    Integrations allow you to receive chat notifications in your favorite platforms.
                </p>
                <p>Get notified when users send messages, provide ratings, or start/end chat sessions.</p>
            </div>
        </div>
    )
}