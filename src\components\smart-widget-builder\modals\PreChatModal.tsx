import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  FileText,
  Users,
  Mail,
  Phone,
  MessageSquare,
  Plus,
  Eye,
  Settings,
  CheckCircle2
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

// Import existing components
import { PreChatFormBuilder } from '@/components/pre-chat-form/PreChatFormBuilder';
import { EnhancedPreChatForm } from '@/components/pre-chat-form/enhanced-pre-chat-form';

interface PreChatModalProps {
  form: UseFormReturn<any>;
  onClose: () => void;
  widgetId?: string;
}

/**
 * IMPORTANT: Select field options MUST be simple strings, NOT objects with {value, label}
 * The SelectField component expects: options: string[]
 * NOT: options: {value: string, label: string}[]
 */
const quickTemplates = [
  {
    id: 'support',
    name: 'Customer Support',
    description: 'Collect basic contact info for support requests',
    icon: Users,
    fields: [
      { type: 'text', name: 'name', label: 'Name', isRequired: true },
      { type: 'email', name: 'email', label: 'Email', isRequired: true },
      {
        type: 'select', name: 'issue_type', label: 'Issue Type', isRequired: false, options: [
          'Technical Issue',
          'Billing Question',
          'General Inquiry'
        ]
      },
    ]
  },
  {
    id: 'sales',
    name: 'Sales Inquiry',
    description: 'Qualify leads with contact and company info',
    icon: Mail,
    fields: [
      { type: 'text', name: 'name', label: 'Name', isRequired: true },
      { type: 'email', name: 'email', label: 'Email', isRequired: true },
      { type: 'text', name: 'company', label: 'Company', isRequired: false },
      { type: 'phone', name: 'phone', label: 'Phone', isRequired: false },
    ]
  },
  {
    id: 'feedback',
    name: 'Feedback Collection',
    description: 'Simple form for collecting user feedback',
    icon: MessageSquare,
    fields: [
      { type: 'text', name: 'name', label: 'Name', isRequired: false },
      { type: 'email', name: 'email', label: 'Email', isRequired: false },
      {
        type: 'select', name: 'rating', label: 'Overall Experience', isRequired: true, options: [
          'Excellent',
          'Good',
          'Average',
          'Poor'
        ]
      },
    ]
  },
];

/**
 * Pre-Chat Modal Component
 *
 * Provides a user-friendly interface for setting up pre-chat forms
 * with quick templates and custom form builder integration.
 */
const PreChatModal = ({ form, onClose, widgetId }: PreChatModalProps) => {
  const [activeTab, setActiveTab] = useState('quick');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [showFormBuilder, setShowFormBuilder] = useState(false);
  const [formSettings, setFormSettings] = useState({
    enabled: true,
    required: false,
    title: "Welcome! Let's get to know you",
    description: "Please share a few details so we can provide you with better assistance.",
  });

  const handleTemplateSelect = (template: typeof quickTemplates[0]) => {
    setSelectedTemplate(template.id);
    setFormSettings({
      ...formSettings,
      title: `${template.name} Form`,
      description: template.description,
    });

    // Enable the feature in the main form
    form.setValue('features.preChat', true);
  };

  const handleCustomFormBuilder = () => {
    setShowFormBuilder(true);
  };

  const handleFormBuilderSave = (data: any) => {
    setShowFormBuilder(false);
    form.setValue('features.preChat', true);
    onClose();
  };

  const handleSaveAndClose = () => {
    if (selectedTemplate) {
      form.setValue('features.preChat', true);
    }
    onClose();
  };

  if (showFormBuilder) {
    return (
      <div className="space-y-6">
        <PreChatFormBuilder
          widgetId={widgetId ? Number(widgetId) : 0}
          onSave={handleFormBuilderSave}
          onCancel={() => setShowFormBuilder(false)}
          primaryColor={form.watch('primaryColor')}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Settings Toggle */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Pre-Chat Form Settings</CardTitle>
              <CardDescription>
                Collect visitor information before starting the conversation
              </CardDescription>
            </div>
            <Switch
              checked={formSettings.enabled}
              onCheckedChange={(checked) => {
                setFormSettings({ ...formSettings, enabled: checked });
                form.setValue('features.preChat', checked);
              }}
            />
          </div>
        </CardHeader>

        {formSettings.enabled && (
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={formSettings.required}
                onCheckedChange={(checked) =>
                  setFormSettings({ ...formSettings, required: checked })
                }
              />
              <Label htmlFor="required" className="text-sm">
                Make form mandatory (visitors must complete it to chat)
              </Label>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-700">
                💡 <strong>Tip:</strong> Required forms increase lead quality by 40% but may
                reduce total conversations by 15%. Optional forms provide a good balance.
              </p>
            </div>
          </CardContent>
        )}
      </Card>

      {formSettings.enabled && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="quick">Quick Templates</TabsTrigger>
            <TabsTrigger value="preview">Preview & Test</TabsTrigger>
          </TabsList>

          <TabsContent value="quick" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {quickTemplates.map((template) => {
                const Icon = template.icon;
                const isSelected = selectedTemplate === template.id;

                return (
                  <Card
                    key={template.id}
                    className={`
                      cursor-pointer transition-all hover:shadow-md
                      ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''}
                    `}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`
                          p-2 rounded-lg
                          ${isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}
                        `}>
                          <Icon className="w-5 h-5" />
                        </div>
                        <div>
                          <CardTitle className="text-base flex items-center space-x-2">
                            <span>{template.name}</span>
                            {isSelected && (
                              <CheckCircle2 className="w-4 h-4 text-green-600" />
                            )}
                          </CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-sm mb-3">
                        {template.description}
                      </CardDescription>
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-gray-700">Includes:</p>
                        {template.fields.slice(0, 3).map((field, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <div className="w-1 h-1 bg-gray-400 rounded-full" />
                            <span className="text-xs text-gray-600">
                              {field.label} {field.isRequired && '*'}
                            </span>
                          </div>
                        ))}
                        {template.fields.length > 3 && (
                          <p className="text-xs text-gray-500">
                            +{template.fields.length - 3} more fields
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            <div className="text-center pt-4">
              <Button
                variant="outline"
                onClick={handleCustomFormBuilder}
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Create Custom Form</span>
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                Need more control? Use our drag-and-drop form builder
              </p>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            {selectedTemplate ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Eye className="w-5 h-5" />
                    <span>Form Preview</span>
                  </CardTitle>
                  <CardDescription>
                    This is how your form will appear to visitors
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-4 bg-background max-w-md mx-auto">
                    <EnhancedPreChatForm
                      fields={quickTemplates.find(t => t.id === selectedTemplate)?.fields || []}
                      title={formSettings.title}
                      description={formSettings.description}
                      onSubmit={() => { }}
                      onCancel={() => { }}
                      primaryColor={form.watch('primaryColor')}
                      showCancelButton={!formSettings.required}
                      isRequired={formSettings.required}
                    />
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="border-dashed">
                <CardContent className="pt-6 flex flex-col items-center justify-center text-center space-y-2 p-10">
                  <div className="rounded-full bg-muted h-10 w-10 flex items-center justify-center">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <CardTitle className="text-base">No template selected</CardTitle>
                  <CardDescription>
                    Choose a template from the Quick Templates tab to see the preview
                  </CardDescription>
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab('quick')}
                  >
                    Choose Template
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button
          onClick={handleSaveAndClose}
          disabled={formSettings.enabled && !selectedTemplate}
        >
          Save & Continue
        </Button>
      </div>
    </div>
  );
};

export default PreChatModal;
