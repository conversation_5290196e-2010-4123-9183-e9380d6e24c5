import React, { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  GripVertical,
  Eye,
  Settings
} from "lucide-react"
import { apiService } from "@/utils/api-service"
import { useToast } from "@/hooks/use-toast"

interface PreChatFormManagerProps {
  widgetId: string
}

interface FormField {
  id?: number
  name: string
  type: 'text' | 'email' | 'phone' | 'textarea' | 'select' | 'checkbox' | 'radio'
  label: string
  placeholder?: string
  is_required: boolean
  options?: string[]
  order: number
}

interface FormTemplate {
  id: number
  title: string
  description?: string
  is_active: boolean
  fields: FormField[]
  created_at: string
}

export function PreChatFormManager({ widgetId }: PreChatFormManagerProps) {
  const [templates, setTemplates] = useState<FormTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [editingTemplate, setEditingTemplate] = useState<FormTemplate | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    fields: [] as FormField[]
  })
  const { toast } = useToast()

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const response = await apiService.get(`/widgets/${widgetId}/pre-chat-templates`)
      setTemplates(response.data)
    } catch (error) {
      console.error("Failed to fetch templates:", error)
      toast({
        title: "Error",
        description: "Failed to load pre-chat form templates",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    setEditingTemplate(null)
    setFormData({
      title: '',
      description: '',
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Full Name',
          placeholder: 'Enter your full name',
          is_required: true,
          order: 0
        },
        {
          name: 'email',
          type: 'email',
          label: 'Email Address',
          placeholder: 'Enter your email',
          is_required: true,
          order: 1
        }
      ]
    })
    setIsDialogOpen(true)
  }

  const handleEditTemplate = (template: FormTemplate) => {
    setEditingTemplate(template)
    setFormData({
      title: template.title,
      description: template.description || '',
      fields: [...template.fields]
    })
    setIsDialogOpen(true)
  }

  const handleSaveTemplate = async () => {
    try {
      if (editingTemplate) {
        await apiService.put(`/widgets/${widgetId}/pre-chat-templates/${editingTemplate.id}`, formData)
        toast({
          title: "Success",
          description: "Template updated successfully",
          variant: "default"
        })
      } else {
        await apiService.post(`/widgets/${widgetId}/pre-chat-templates`, formData)
        toast({
          title: "Success",
          description: "Template created successfully",
          variant: "default"
        })
      }
      setIsDialogOpen(false)
      fetchTemplates()
    } catch (error) {
      console.error("Failed to save template:", error)
      toast({
        title: "Error",
        description: "Failed to save template",
        variant: "destructive"
      })
    }
  }

  const handleDeleteTemplate = async (templateId: number) => {
    if (!confirm("Are you sure you want to delete this template?")) return

    try {
      await apiService.delete(`/widgets/${widgetId}/pre-chat-templates/${templateId}`)
      toast({
        title: "Success",
        description: "Template deleted successfully",
        variant: "default"
      })
      fetchTemplates()
    } catch (error) {
      console.error("Failed to delete template:", error)
      toast({
        title: "Error",
        description: "Failed to delete template",
        variant: "destructive"
      })
    }
  }

  const handleActivateTemplate = async (templateId: number) => {
    try {
      await apiService.post(`/widgets/${widgetId}/pre-chat-templates/${templateId}/activate`)
      toast({
        title: "Success",
        description: "Template activated successfully",
        variant: "default"
      })
      fetchTemplates()
    } catch (error) {
      console.error("Failed to activate template:", error)
      toast({
        title: "Error",
        description: "Failed to activate template",
        variant: "destructive"
      })
    }
  }

  const addField = () => {
    const newField: FormField = {
      name: `field_${formData.fields.length + 1}`,
      type: 'text',
      label: 'New Field',
      placeholder: '',
      is_required: false,
      order: formData.fields.length
    }
    setFormData({
      ...formData,
      fields: [...formData.fields, newField]
    })
  }

  const updateField = (index: number, field: Partial<FormField>) => {
    const updatedFields = [...formData.fields]
    updatedFields[index] = { ...updatedFields[index], ...field }
    setFormData({ ...formData, fields: updatedFields })
  }

  const removeField = (index: number) => {
    const updatedFields = formData.fields.filter((_, i) => i !== index)
    setFormData({ ...formData, fields: updatedFields })
  }

  const moveField = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1
    if (newIndex < 0 || newIndex >= formData.fields.length) return

    const updatedFields = [...formData.fields]
    const temp = updatedFields[index]
    updatedFields[index] = updatedFields[newIndex]
    updatedFields[newIndex] = temp

    // Update order values
    updatedFields[index].order = index
    updatedFields[newIndex].order = newIndex

    setFormData({ ...formData, fields: updatedFields })
  }

  useEffect(() => {
    fetchTemplates()
  }, [widgetId])

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Pre-Chat Form Templates</h3>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Pre-Chat Form Templates</h3>
        <Button onClick={handleCreateTemplate}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      <div className="grid gap-4">
        {templates.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground mb-4">No pre-chat form templates found</p>
              <Button onClick={handleCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Template
              </Button>
            </CardContent>
          </Card>
        ) : (
          templates.map((template) => (
            <Card key={template.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {template.title}
                      {template.is_active && (
                        <Badge variant="default">Active</Badge>
                      )}
                    </CardTitle>
                    {template.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {template.description}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    {!template.is_active && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleActivateTemplate(template.id)}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Activate
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditTemplate(template)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteTemplate(template.id)}
                      disabled={template.is_active}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {template.fields.length} field{template.fields.length !== 1 ? 's' : ''}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {template.fields.map((field) => (
                      <Badge key={field.name} variant="secondary">
                        {field.label} ({field.type})
                        {field.is_required && '*'}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Template Editor Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? 'Edit Template' : 'Create Template'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Template Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter template title"
                />
              </div>
              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter template description"
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-4">
                <Label>Form Fields</Label>
                <Button type="button" variant="outline" onClick={addField}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Field
                </Button>
              </div>

              <div className="space-y-4">
                {formData.fields.map((field, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <Label>Field Name</Label>
                          <Input
                            value={field.name}
                            onChange={(e) => updateField(index, { name: e.target.value })}
                            placeholder="field_name"
                          />
                        </div>
                        <div>
                          <Label>Field Type</Label>
                          <Select
                            value={field.type}
                            onValueChange={(value) => updateField(index, { type: value as FormField['type'] })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="text">Text</SelectItem>
                              <SelectItem value="email">Email</SelectItem>
                              <SelectItem value="phone">Phone</SelectItem>
                              <SelectItem value="textarea">Textarea</SelectItem>
                              <SelectItem value="select">Select</SelectItem>
                              <SelectItem value="checkbox">Checkbox</SelectItem>
                              <SelectItem value="radio">Radio</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label>Label</Label>
                          <Input
                            value={field.label}
                            onChange={(e) => updateField(index, { label: e.target.value })}
                            placeholder="Field Label"
                          />
                        </div>
                        <div>
                          <Label>Placeholder</Label>
                          <Input
                            value={field.placeholder || ''}
                            onChange={(e) => updateField(index, { placeholder: e.target.value })}
                            placeholder="Enter placeholder"
                          />
                        </div>
                      </div>

                      {(field.type === 'select' || field.type === 'radio') && (
                        <div className="mt-4">
                          <Label>Options (one per line)</Label>
                          <Textarea
                            value={field.options?.join('\n') || ''}
                            onChange={(e) => updateField(index, {
                              options: e.target.value.split('\n').filter(opt => opt.trim())
                            })}
                            placeholder="Option 1&#10;Option 2&#10;Option 3"
                            rows={3}
                          />
                        </div>
                      )}

                      <div className="flex justify-between items-center mt-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={field.is_required}
                            onCheckedChange={(checked) => updateField(index, { is_required: checked })}
                          />
                          <Label>Required</Label>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => moveField(index, 'up')}
                            disabled={index === 0}
                          >
                            ↑
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => moveField(index, 'down')}
                            disabled={index === formData.fields.length - 1}
                          >
                            ↓
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeField(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveTemplate}>
                {editingTemplate ? 'Update Template' : 'Create Template'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
