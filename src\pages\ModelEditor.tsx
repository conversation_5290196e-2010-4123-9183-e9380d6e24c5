import { useState, useEffect, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AdminLayout } from "@/components/admin-layout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Spinner } from "@/components/ui/spinner";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { AIModelData, aiModelService } from "@/utils/ai-model-service";
import { modelFormSchema, ModelFormValues } from "@/components/ai-configuration/model-management/model-form-schema";
import { ModelBasicInfoFields } from "@/components/ai-configuration/model-management/model-basic-info-fields";
import { ModelApiKeyField } from "@/components/ai-configuration/model-management/model-api-key-field";
import { ModelSettingsFields } from "@/components/ai-configuration/model-management/model-settings-fields";
import { ModelDefaultToggle } from "@/components/ai-configuration/model-management/model-default-toggle";
import { ModelActiveToggle } from "@/components/ai-configuration/model-management/model-active-toggle";
import { ModelQuickTest } from "@/components/ai-configuration/model-management/model-quick-test";
import { AlertCircle, ArrowLeft, Check, ChevronRight, Home, RefreshCw, Save } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { ModelTemplateSelector } from "@/components/templates/model-template-selector";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const ModelEditor = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isFetchingModels, setIsFetchingModels] = useState(false);
  const [model, setModel] = useState<AIModelData | null>(null);
  const [tempModel, setTempModel] = useState<AIModelData | null>(null);
  const [availableModels, setAvailableModels] = useState<Array<{ value: string, label: string, description?: string }>>([]);
  const isEditMode = !!id;
  const [activeTab, setActiveTab] = useState("general");

  // Initialize form with default values
  const form = useForm<ModelFormValues>({
    resolver: zodResolver(modelFormSchema),
    defaultValues: {
      name: "",
      provider: "",
      description: "",
      api_key: "",
      is_default: false,
      active: true,
      fallback_model_id: null,
      confidence_threshold: 0.7,
      settings: {
        model_name: "",
        temperature: 0.7,
        max_tokens: 2048,
      }
    },
  });

  // Watch for changes to provider and API key
  const provider = useWatch({
    control: form.control,
    name: "provider",
  });

  const apiKey = useWatch({
    control: form.control,
    name: "api_key",
  });

  // Function to fetch available models from the provider
  const fetchAvailableModels = useCallback(async (tempModel: AIModelData) => {
    if (!tempModel.provider || !tempModel.api_key) {
      return;
    }

    setIsFetchingModels(true);
    try {
      // Use the direct model discovery endpoint
      const result = await aiModelService.discoverModelsWithoutCreating(
        tempModel.provider,
        tempModel.api_key
      );

      if (result.success && result.models) {
        // Transform the models into the format needed for the dropdown
        const modelOptions = Object.entries(result.models).map(([key, value]: [string, any]) => ({
          value: key,
          label: value.display_name || key,
          description: value.description || '',
        }));

        setAvailableModels(modelOptions);

        // If we have models, automatically select the first one and set the model name
        if (modelOptions.length > 0) {
          form.setValue("settings.model_name", modelOptions[0].value);

          // Generate a name based on the provider and model
          const generatedName = `${modelOptions[0].label} (${tempModel.provider})`;
          form.setValue("name", generatedName);

          toast({
            title: "Models Retrieved",
            description: `Found ${modelOptions.length} models from ${tempModel.provider}`,
          });
        }
      } else {
        toast({
          title: "No Models Found",
          description: "Could not retrieve models from this provider. You may need to enter model details manually.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Failed to fetch available models:", error);
      toast({
        title: "Error Fetching Models",
        description: error.message || "Failed to retrieve models from the provider",
        variant: "destructive",
      });
    } finally {
      setIsFetchingModels(false);
    }
  }, [form, toast]);

  // Function to create a temporary model for testing
  const createTempModel = useCallback(() => {
    if (!provider || !apiKey) return;

    const modelName = form.getValues("settings.model_name");
    const temperature = form.getValues("settings.temperature");
    const maxTokens = form.getValues("settings.max_tokens");
    const name = form.getValues("name");

    // Only create a temp model if we have the minimum required fields
    if (!provider || !apiKey) {
      setTempModel(null);
      return;
    }

    const newTempModel: AIModelData = {
      name: name || `${provider} Model`,
      provider,
      api_key: apiKey,
      settings: {
        model_name: modelName,
        temperature,
        max_tokens: maxTokens
      }
    };

    console.log("Creating temp model:", newTempModel);
    setTempModel(newTempModel);
    return newTempModel;
  }, [form, provider, apiKey]);

  // Watch for changes to provider and API key to fetch models
  useEffect(() => {
    // Only fetch models if both provider and API key are provided
    if (provider && apiKey && apiKey.length > 10 && !isEditMode) {
      const tempModel: AIModelData = {
        name: form.getValues("name"),
        provider,
        api_key: apiKey,
        settings: {}
      };

      // Debounce the API call to avoid too many requests
      const timer = setTimeout(() => {
        fetchAvailableModels(tempModel);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [provider, apiKey, fetchAvailableModels, isEditMode]);

  // Update temp model when form values change
  useEffect(() => {
    if (!isEditMode) {
      // Use a short timeout to batch updates and avoid too many re-renders
      const timer = setTimeout(() => {
        createTempModel();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [createTempModel, isEditMode, provider, apiKey, form.watch("settings.model_name"), form.watch("settings.temperature"), form.watch("settings.max_tokens")]);

  // Fetch model data if in edit mode
  useEffect(() => {
    if (isEditMode) {
      const fetchModel = async () => {
        setIsLoading(true);
        try {
          const modelData = await aiModelService.getModel(parseInt(id));
          setModel(modelData);

          // Reset form with model data
          form.reset({
            name: modelData.name || "",
            provider: modelData.provider || "",
            description: modelData.description || "",
            api_key: modelData.api_key || "",
            is_default: modelData.is_default || false,
            active: modelData.active ?? true,
            fallback_model_id: modelData.fallback_model_id || null,
            confidence_threshold: modelData.confidence_threshold || 0.7,
            settings: {
              model_name: modelData.settings?.model_name || "",
              temperature: modelData.settings?.temperature || 0.7,
              max_tokens: modelData.settings?.max_tokens || 2048,
            }
          });

          // Also create a temp model for testing
          setTempModel(modelData);
        } catch (error: any) {
          console.error("Failed to fetch model:", error);
          toast({
            title: "Error",
            description: error.message || "Failed to load model details",
            variant: "destructive",
          });
          navigate("/model-management");
        } finally {
          setIsLoading(false);
        }
      };

      fetchModel();
    }
  }, [id, isEditMode, navigate, toast, form]);

  // Form submission handler
  const onSubmit = async (data: ModelFormValues) => {
    setIsSaving(true);
    try {
      if (isEditMode && id) {
        // Update existing model
        await aiModelService.updateModel(parseInt(id), data);
        toast({
          title: "Success",
          description: "Model updated successfully",
        });
      } else {
        // Create new model
        await aiModelService.createModel(data);
        toast({
          title: "Success",
          description: "New model created successfully",
        });
      }
      navigate("/model-management");
    } catch (error: any) {
      console.error("Failed to save model:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save model",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    navigate("/model-management");
  };

  // Function to test the connection to the model
  const handleTestConnection = async () => {
    setIsTesting(true);
    try {
      // Create a temp model if we don't have one
      const currentTemp = tempModel || createTempModel();
      if (!currentTemp) {
        toast({
          title: "Error",
          description: "Please fill in the required fields first",
          variant: "destructive",
        });
        return;
      }

      const result = await aiModelService.testConnection(currentTemp);
      if (result.success) {
        toast({
          title: "Connection Successful",
          description: result.message || "Connection to model API successful",
        });
      } else {
        toast({
          title: "Connection Failed",
          description: result.message || "Failed to connect to model API",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Test connection failed:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to test connection",
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  };

  // Function to handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard">
                  <Home className="h-4 w-4" />
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                <BreadcrumbLink href="/model-management">Model Management</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                <BreadcrumbLink>{isEditMode ? "Edit Model" : "Add Model"}</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">{isEditMode ? "Edit AI Model" : "Add AI Model"}</h1>
            <p className="text-gray-500">
              {isEditMode ? "Update the settings for this AI model" : "Configure a new AI model for your application"}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            {isEditMode && model && (
              <Button variant="default" onClick={form.handleSubmit(onSubmit)} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Spinner className="h-4 w-4 mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Spinner className="h-8 w-8" />
            <span className="ml-2">Loading model data...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="general">General Settings</TabsTrigger>
                  <TabsTrigger value="advanced" disabled={!isEditMode}>Advanced Settings</TabsTrigger>
                  <TabsTrigger value="templates" disabled={!isEditMode}>Templates</TabsTrigger>
                </TabsList>

                <TabsContent value="general">
                  <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Basic Information</CardTitle>
                        <CardDescription>Configure the basic details for your AI model</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ModelBasicInfoFields control={form.control} errors={form.formState.errors} />
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>API Configuration</CardTitle>
                        <CardDescription>Configure API access for this model</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ModelApiKeyField control={form.control} errors={form.formState.errors} />
                      </CardContent>
                      <CardFooter>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleTestConnection}
                          disabled={isTesting || !provider || !apiKey}
                          className="w-full"
                        >
                          {isTesting ? (
                            <>
                              <Spinner className="h-4 w-4 mr-2" />
                              Testing Connection...
                            </>
                          ) : (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Test Connection
                            </>
                          )}
                        </Button>
                      </CardFooter>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Model Configuration</CardTitle>
                        <CardDescription>Configure the model settings</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ModelSettingsFields
                          control={form.control}
                          errors={form.formState.errors}
                          provider={provider}
                          availableModels={availableModels}
                          isFetchingModels={isFetchingModels}
                        />
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Status Configuration</CardTitle>
                        <CardDescription>Configure the status settings for this model</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <ModelDefaultToggle control={form.control} />
                        <ModelActiveToggle control={form.control} />
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="advanced">
                  <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Advanced Settings</CardTitle>
                        <CardDescription>Configure advanced settings for this model</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              Advanced settings are only available for saved models.
                            </AlertDescription>
                          </Alert>

                          {/* Additional advanced settings can be added here */}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Test the Model</CardTitle>
                        <CardDescription>Send a test message to check model functionality</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {isEditMode && model ? (
                          <ModelQuickTest model={model} />
                        ) : (
                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              Test functionality is available after saving the model.
                            </AlertDescription>
                          </Alert>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="templates">
                  <div className="grid gap-6 md:grid-cols-1">
                    {isEditMode && id ? (
                      <ModelTemplateSelector modelId={parseInt(id)} />
                    ) : (
                      <Card>
                        <CardHeader>
                          <CardTitle>Template Assignment</CardTitle>
                          <CardDescription>Assign prompt templates to this model</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              Template assignment is available after saving the model.
                            </AlertDescription>
                          </Alert>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="mt-6 flex justify-end gap-3">
                <Button variant="outline" type="button" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Spinner className="h-4 w-4 mr-2" />
                      {isEditMode ? "Saving..." : "Creating..."}
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      {isEditMode ? "Save Changes" : "Create Model"}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </div>
    </AdminLayout>
  );
};

export default ModelEditor;
