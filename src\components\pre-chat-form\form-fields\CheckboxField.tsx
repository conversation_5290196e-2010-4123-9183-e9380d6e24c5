"use client"

import React from "react"
import { UseFormReturn } from "react-hook-form"
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"

interface CheckboxFieldProps {
  form: UseFormReturn<any>
  name: string
  label: string
  description?: string
  required?: boolean
  className?: string
  disabled?: boolean
}

/**
 * Checkbox Field Component
 * 
 * A form field for boolean checkbox input.
 */
export function CheckboxField({
  form,
  name,
  label,
  description,
  required = false,
  className,
  disabled = false
}: CheckboxFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <div className="flex items-start space-x-3 space-y-0 py-1">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
                disabled={disabled}
                className="mt-1"
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel className="text-sm font-medium">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
              </FormLabel>
              {description && (
                <FormDescription className="text-xs">{description}</FormDescription>
              )}
              <FormMessage className="text-xs font-medium" />
            </div>
          </div>
        </FormItem>
      )}
    />
  )
}
