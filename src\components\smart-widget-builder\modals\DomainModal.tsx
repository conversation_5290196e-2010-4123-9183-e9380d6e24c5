import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Shield,
  Globe,
  Plus,
  Trash2,
  CheckCircle2,
  AlertTriangle,
  Lock,
  Unlock
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface DomainModalProps {
  form: UseFormReturn<any>;
  onClose: () => void;
  widgetId?: string;
}

/**
 * Domain Modal Component
 *
 * Provides a user-friendly interface for managing domain restrictions
 * with clear security benefits and easy domain management.
 */
const DomainModal = ({ form, onClose, widgetId }: DomainModalProps) => {
  const [restrictionEnabled, setRestrictionEnabled] = useState(false);
  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const [newDomain, setNewDomain] = useState('');
  const [domainError, setDomainError] = useState('');

  // Initialize modal state from form data when it opens
  useEffect(() => {
    const currentFeatureState = form.getValues('features.domainRestriction');
    const currentDomains = form.getValues('advanced.allowedDomains') || [];

    setRestrictionEnabled(currentFeatureState || false);
    setAllowedDomains(currentDomains);
  }, [form]);

  const validateDomain = (domain: string): boolean => {
    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
    return domainRegex.test(domain);
  };

  const addDomain = () => {
    const domain = newDomain.trim().toLowerCase();

    if (!domain) {
      setDomainError('Please enter a domain');
      return;
    }

    if (!validateDomain(domain)) {
      setDomainError('Please enter a valid domain (e.g., example.com)');
      return;
    }

    if (allowedDomains.includes(domain)) {
      setDomainError('This domain is already in the list');
      return;
    }

    setAllowedDomains([...allowedDomains, domain]);
    setNewDomain('');
    setDomainError('');
  };

  const removeDomain = (domain: string) => {
    setAllowedDomains(allowedDomains.filter(d => d !== domain));
  };

  const handleSaveAndClose = () => {
    // Set the feature toggle based on restriction state
    form.setValue('features.domainRestriction', restrictionEnabled);

    // Save the allowed domains to the form
    if (restrictionEnabled) {
      form.setValue('advanced.allowedDomains', allowedDomains);
    } else {
      // If not restricting, clear the domains array
      form.setValue('advanced.allowedDomains', []);
    }

    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addDomain();
    }
  };

  return (
    <div className="space-y-6">
      {/* Security Level Selection */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>Widget Access Control</span>
          </CardTitle>
          <CardDescription>
            Control which websites can display your chat widget
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Allow Everywhere Option */}
            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${!restrictionEnabled ? 'ring-2 ring-green-500 bg-green-50 dark:bg-green-950/30' : ''
                }`}
              onClick={() => setRestrictionEnabled(false)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${!restrictionEnabled ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400' : 'bg-muted text-muted-foreground'
                    }`}>
                    <Globe className="w-5 h-5" />
                  </div>
                  <div>
                    <CardTitle className="text-base flex items-center space-x-2">
                      <span>Allow Everywhere</span>
                      {!restrictionEnabled && (
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                      )}
                    </CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm mb-3">
                  Widget can be embedded on any website
                </CardDescription>
                <div className="flex items-center space-x-2">
                  <Unlock className="w-4 h-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm text-green-700 dark:text-green-300">Maximum flexibility</span>
                </div>
              </CardContent>
            </Card>

            {/* Restrict to Specific Domains Option */}
            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${restrictionEnabled ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/30' : ''
                }`}
              onClick={() => setRestrictionEnabled(true)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${restrictionEnabled ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' : 'bg-muted text-muted-foreground'
                    }`}>
                    <Lock className="w-5 h-5" />
                  </div>
                  <div>
                    <CardTitle className="text-base flex items-center space-x-2">
                      <span>Restrict to My Sites</span>
                      {restrictionEnabled && (
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                      )}
                    </CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm mb-3">
                  Only allow specific domains to use your widget
                </CardDescription>
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Enhanced security</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Security Benefits */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription className="text-sm">
              <strong>Why restrict domains?</strong><br />
              • Prevents unauthorized use of your widget<br />
              • Protects your API quota and resources<br />
              • Ensures brand consistency and control<br />
              • Complies with security best practices
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Domain Management */}
      {restrictionEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Allowed Domains</CardTitle>
            <CardDescription>
              Add the domains where your widget should be allowed to appear
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Add Domain Input */}
            <div className="flex space-x-2">
              <div className="flex-1">
                <Label htmlFor="new-domain" className="sr-only">
                  Add domain
                </Label>
                <Input
                  id="new-domain"
                  placeholder="example.com"
                  value={newDomain}
                  onChange={(e) => {
                    setNewDomain(e.target.value);
                    setDomainError('');
                  }}
                  onKeyPress={handleKeyPress}
                  className={domainError ? 'border-red-500' : ''}
                />
                {domainError && (
                  <p className="text-sm text-red-600 mt-1">{domainError}</p>
                )}
              </div>
              <Button onClick={addDomain}>
                <Plus className="w-4 h-4 mr-2" />
                Add
              </Button>
            </div>

            {/* Domain List */}
            {allowedDomains.length > 0 ? (
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Allowed Domains ({allowedDomains.length})
                </Label>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {allowedDomains.map((domain, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-muted rounded-lg border"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                        <span className="text-sm font-medium">{domain}</span>
                        <Badge variant="outline" className="text-xs">
                          Active
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDomain(domain)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 border-2 border-dashed border-border rounded-lg">
                <Globe className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-2">No domains added yet</p>
                <p className="text-xs text-muted-foreground">
                  Add your website domains to enable widget access
                </p>
              </div>
            )}

            {/* Domain Examples */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 dark:bg-blue-950/50 dark:border-blue-800">
              <p className="text-sm text-blue-700 mb-2 dark:text-blue-300">
                <strong>Examples of valid domains:</strong>
              </p>
              <div className="text-xs text-blue-600 space-y-1 dark:text-blue-400">
                <div>• <code>example.com</code> - Main domain</div>
                <div>• <code>www.example.com</code> - With www subdomain</div>
                <div>• <code>shop.example.com</code> - Subdomain</div>
                <div>• <code>staging.example.com</code> - Staging environment</div>
              </div>
            </div>

            {/* Warning for empty domains */}
            {restrictionEnabled && allowedDomains.length === 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <strong>Warning:</strong> No domains are currently allowed.
                  Your widget will not be accessible on any website until you add at least one domain.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button
          onClick={handleSaveAndClose}
          disabled={restrictionEnabled && allowedDomains.length === 0}
        >
          Save & Continue
        </Button>
      </div>
    </div>
  );
};

export default DomainModal;
