import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { knowledgeBaseService } from "@/utils/knowledge-base-service";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import { useNavigate } from "react-router-dom";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Import the actual tab components
import DocumentsTab from "@/components/knowledge-base/tabs/documents-tab";
import WebScrapingTab from "@/components/knowledge-base/tabs/web-scraping-tab";
import ScheduledScrapingTab from "@/components/knowledge-base/tabs/scheduled-scraping-tab";
import DatabaseTab from "@/components/knowledge-base/tabs/database-tab";
import ContextTab from "@/components/knowledge-base/tabs/context-tab";
import { ProjectCreateDialog } from "@/components/knowledge-base/project-create-dialog";
import { ProjectSelector } from "@/components/knowledge-base/project-selector";
import {
  KnowledgeBaseGuide,
  TabTooltip,
} from "@/components/knowledge-base/knowledge-base-guide";
import { FeatureExplainer } from "@/components/knowledge-base/feature-explainer";
import { QuickActionPanel } from "@/components/knowledge-base/quick-action-panel";
import { VisualWorkflow } from "@/components/knowledge-base/visual-workflow";
import { SetupWizard } from "@/components/knowledge-base/setup-wizard";

// Import icons
import {
  Database,
  FileText,
  Globe,
  Network,
  RefreshCw,
  Clock,
  Plus,
  BookOpen,
  HelpCircle,
  Lightbulb,
  Info,
} from "lucide-react";

// Define FileMeta type for use in other components
export interface FileMeta {
  id: number;
  file_name: string;
  file_type: string;
  file_size: number;
  category?: string;
  created_at: string;
  has_embeddings: boolean;
  embeddings_count?: number;
  embeddings_provider?: string;
  embeddings_model?: string;
  embeddings_generated_at?: string;
  is_active_source: boolean;
}

export function KnowledgeBaseResources() {
  // State for basic module functionality
  const [activeTab, setActiveTab] = useState("documents");
  const [projects, setProjects] = useState([]);
  const [selectedProjectId, setSelectedProjectId] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showProjectDialog, setShowProjectDialog] = useState(false);
  const [authError, setAuthError] = useState(false);
  const [showGuide, setShowGuide] = useState(true);
  const [showFeatureExplainer, setShowFeatureExplainer] = useState<
    string | null
  >(null);
  const [showSetupWizard, setShowSetupWizard] = useState<
    "document" | "website" | "database" | null
  >(null);
  const [showWorkflow, setShowWorkflow] = useState(true);

  // Auth hooks
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch projects on mount
  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      setAuthError(true);
      setIsLoading(false);
      return;
    }

    const fetchProjects = async () => {
      try {
        setIsLoading(true);
        const response = await knowledgeBaseService.getProjects();

        if (response?.data?.success && response.data.data?.length > 0) {
          setProjects(response.data.data);
          // Auto-select the first project
          setSelectedProjectId(response.data.data[0].id);
        } else {
          setProjects([]);
        }
      } catch (error) {
        console.error("Failed to load knowledge bases:", error);

        // Check if it's an authentication error
        if (error.response?.status === 401) {
          setAuthError(true);
          toast.error("Authentication required. Please log in.");
          setTimeout(() => navigate("/login"), 2000);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [user, navigate, refreshTrigger]);

  // Handler for refresh
  const handleRefresh = () => setRefreshTrigger((prev) => prev + 1);

  // Handler for project creation
  const handleProjectCreated = (project) => {
    if (project) {
      setProjects((prev) => [...prev, project]);
      setSelectedProjectId(project.id);
      toast.success(`Knowledge base "${project.name}" created successfully`);
    }
  };

  // Show authentication error if needed
  if (authError) {
    return (
      <Card className="border-0 shadow-md bg-gradient-to-b from-card to-background">
        <CardHeader className="pb-4 border-b">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-2 rounded-full">
              <Database className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle>Knowledge Base</CardTitle>
              <CardDescription className="mt-1.5">
                Authentication required
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <p className="mb-4">
              You need to be logged in to access the Knowledge Base.
            </p>
            <Button onClick={() => navigate("/login")}>Go to Login</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show empty state if no projects
  if (projects.length === 0 && !isLoading) {
    return (
      <Card className="border-0 shadow-md bg-gradient-to-b from-card to-background">
        <CardHeader className="pb-4 border-b">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-2 rounded-full">
              <BookOpen className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle>AI Knowledge Base</CardTitle>
              <CardDescription className="mt-1.5">
                Teach your AI assistant using your own content
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="max-w-3xl mx-auto">
            <Alert className="mb-6">
              <Lightbulb className="h-4 w-4" />
              <AlertTitle>Why create a knowledge base?</AlertTitle>
              <AlertDescription>
                A knowledge base helps your AI assistant provide accurate,
                factual answers based on your specific content rather than
                generic information. This makes responses more relevant to your
                users.
              </AlertDescription>
            </Alert>

            <div className="grid gap-8 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                  <FileText className="h-8 w-8 text-primary mb-2" />
                  <h3 className="text-lg font-semibold mb-1">
                    Upload Documents
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Add PDFs, Word docs, and text files containing your company
                    information.
                  </p>
                </Card>

                <Card className="p-4">
                  <Globe className="h-8 w-8 text-primary mb-2" />
                  <h3 className="text-lg font-semibold mb-1">
                    Scrape Websites
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Import content from your website pages automatically.
                  </p>
                </Card>

                <Card className="p-4">
                  <Database className="h-8 w-8 text-primary mb-2" />
                  <h3 className="text-lg font-semibold mb-1">
                    Connect Databases
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Link to databases containing product details or customer
                    information.
                  </p>
                </Card>
              </div>
            </div>

            <div className="flex justify-center">
              <ProjectSelector
                projects={projects}
                selectedProjectId={selectedProjectId}
                setSelectedProjectId={setSelectedProjectId}
                onRefreshProjects={handleRefresh}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-md bg-gradient-to-b from-card to-background">
      <CardHeader className="pb-4 border-b">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-2 rounded-full">
              <Database className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle>AI Knowledge Base</CardTitle>
              <CardDescription className="mt-1.5">
                Teach your AI with your content
              </CardDescription>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <ProjectSelector
              projects={projects}
              selectedProjectId={selectedProjectId}
              setSelectedProjectId={setSelectedProjectId}
              onRefreshProjects={handleRefresh}
            />

            <Button
              size="sm"
              variant="outline"
              onClick={handleRefresh}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-6">
        <div className="flex justify-between mb-2">
          <div className="flex items-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={() => setShowSetupWizard("document")}
              className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4" />
              Quick Start
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowGuide(!showGuide)}
            className="flex items-center gap-1"
          >
            <HelpCircle className="h-4 w-4" />
            {showGuide ? "Hide Guide" : "Show Guide"}
          </Button>
        </div>

        {showGuide && (
          <KnowledgeBaseGuide
            activeTab={activeTab}
            onClose={() => setShowGuide(false)}
          />
        )}

        {showFeatureExplainer && (
          <FeatureExplainer
            feature={showFeatureExplainer}
            onClose={() => setShowFeatureExplainer(null)}
          />
        )}

        {showSetupWizard && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <SetupWizard
              setupType={showSetupWizard}
              onComplete={(data) => {
                console.log("Setup completed with data:", data);
                setShowSetupWizard(null);
                toast.success("Setup completed successfully!");
              }}
              onCancel={() => setShowSetupWizard(null)}
            />
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-5 max-w-3xl mx-auto mb-6">
            <TabTooltip content="Upload and manage documents like PDFs, Word files, and text documents to teach your AI assistant.">
              <TabsTrigger
                value="documents"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                <span className="hidden md:inline">Documents</span>
                <span className="md:hidden">Docs</span>
              </TabsTrigger>
            </TabTooltip>
            <TabTooltip content="Import content directly from your website pages to keep your AI assistant up to date with your online information.">
              <TabsTrigger
                value="web-scraping"
                className="flex items-center gap-2"
              >
                <Globe className="h-4 w-4" />
                <span className="hidden md:inline">Web Scraping</span>
                <span className="md:hidden">Web</span>
              </TabsTrigger>
            </TabTooltip>
            <TabTooltip content="Set up automatic, recurring web scraping to keep your knowledge base updated without manual intervention.">
              <TabsTrigger
                value="scheduled-scraping"
                className="flex items-center gap-2"
              >
                <Clock className="h-4 w-4" />
                <span className="hidden md:inline">Scheduled</span>
                <span className="md:hidden">Auto</span>
              </TabsTrigger>
            </TabTooltip>
            <TabTooltip content="Connect to databases to use structured data as knowledge sources for your AI assistant.">
              <TabsTrigger value="database" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                <span>Database</span>
              </TabsTrigger>
            </TabTooltip>
            <TabTooltip content="Define rules for how your AI assistant prioritizes different knowledge sources based on the context of questions.">
              <TabsTrigger value="context" className="flex items-center gap-2">
                <Network className="h-4 w-4" />
                <span>Context</span>
              </TabsTrigger>
            </TabTooltip>
          </TabsList>

          <QuickActionPanel
            activeTab={activeTab}
            selectedProjectId={selectedProjectId}
            onAction={(action) => {
              console.log("Quick action:", action);
              switch (action) {
                case "upload":
                  setShowSetupWizard("document");
                  break;
                case "new-scrape":
                  setShowSetupWizard("website");
                  break;
                case "connect-db":
                  setShowSetupWizard("database");
                  break;
                // Handle other actions
              }
            }}
          />

          <div className="flex flex-wrap gap-2 mb-4 justify-center">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs flex items-center gap-1"
              onClick={() => setShowFeatureExplainer("embeddings")}
            >
              <Info className="h-3 w-3" /> What are vector embeddings?
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs flex items-center gap-1"
              onClick={() => setShowFeatureExplainer("chunks")}
            >
              <Info className="h-3 w-3" /> Understanding chunk size & overlap
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs flex items-center gap-1"
              onClick={() => setShowFeatureExplainer("context")}
            >
              <Info className="h-3 w-3" /> How context rules work
            </Button>
          </div>

          {showWorkflow && activeTab === "documents" && selectedProjectId && (
            <VisualWorkflow
              activeTab={activeTab}
              steps={[
                {
                  id: "upload",
                  title: "Upload Documents",
                  description:
                    "Add PDF, Word, or text files to your knowledge base",
                  icon: <FileText className="h-6 w-6" />,
                  status: "active",
                  action: () => setShowSetupWizard("document"),
                },
                {
                  id: "process",
                  title: "Process Documents",
                  description:
                    "Generate embeddings to make your documents searchable",
                  icon: <Database className="h-6 w-6" />,
                  status: "pending",
                },
                {
                  id: "activate",
                  title: "Activate Knowledge",
                  description:
                    "Make your documents available to your AI assistant",
                  icon: <Globe className="h-6 w-6" />,
                  status: "pending",
                },
              ]}
            />
          )}

          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              {/* Documents Tab */}
              <TabsContent value="documents">
                <DocumentsTab
                  projects={projects}
                  selectedProjectId={selectedProjectId}
                  setSelectedProjectId={setSelectedProjectId}
                  refreshTrigger={refreshTrigger}
                  onRefresh={handleRefresh}
                />
              </TabsContent>

              {/* Web Scraping Tab */}
              <TabsContent value="web-scraping">
                <WebScrapingTab
                  projects={projects}
                  selectedProjectId={selectedProjectId}
                  setSelectedProjectId={setSelectedProjectId}
                  refreshTrigger={refreshTrigger}
                  onRefresh={handleRefresh}
                />
              </TabsContent>

              {/* Scheduled Scraping Tab */}
              <TabsContent value="scheduled-scraping">
                <ScheduledScrapingTab
                  projects={projects}
                  selectedProjectId={selectedProjectId}
                  setSelectedProjectId={setSelectedProjectId}
                  refreshTrigger={refreshTrigger}
                  onRefresh={handleRefresh}
                />
              </TabsContent>

              {/* Database Tab */}
              <TabsContent value="database">
                <DatabaseTab
                  projects={projects}
                  selectedProjectId={selectedProjectId}
                  setSelectedProjectId={setSelectedProjectId}
                  refreshTrigger={refreshTrigger}
                  onRefresh={handleRefresh}
                />
              </TabsContent>

              {/* Context Tab */}
              <TabsContent value="context">
                <ContextTab
                  projects={projects}
                  selectedProjectId={selectedProjectId}
                  setSelectedProjectId={setSelectedProjectId}
                  refreshTrigger={refreshTrigger}
                  onRefresh={handleRefresh}
                />
              </TabsContent>
            </>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
