<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('knowledge_embeddings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->constrained('knowledge_documents')->onDelete('cascade');
            $table->integer('chunk_index');
            $table->text('chunk_text');
            $table->json('embedding');
            $table->string('provider', 50);
            $table->string('model', 100);
            $table->integer('dimensions')->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Add index for faster lookups
            $table->index(['document_id', 'chunk_index']);
        });
        
        // Add embedding-related columns to knowledge_documents table
        Schema::table('knowledge_documents', function (Blueprint $table) {
            if (!Schema::hasColumn('knowledge_documents', 'has_embeddings')) {
                $table->boolean('has_embeddings')->default(false);
            }
            
            if (!Schema::hasColumn('knowledge_documents', 'embeddings_count')) {
                $table->integer('embeddings_count')->default(0);
            }
            
            if (!Schema::hasColumn('knowledge_documents', 'embeddings_provider')) {
                $table->string('embeddings_provider', 50)->nullable();
            }
            
            if (!Schema::hasColumn('knowledge_documents', 'embeddings_model')) {
                $table->string('embeddings_model', 100)->nullable();
            }
            
            if (!Schema::hasColumn('knowledge_documents', 'embeddings_generated_at')) {
                $table->timestamp('embeddings_generated_at')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove embedding-related columns from knowledge_documents table
        Schema::table('knowledge_documents', function (Blueprint $table) {
            $table->dropColumn([
                'has_embeddings',
                'embeddings_count',
                'embeddings_provider',
                'embeddings_model',
                'embeddings_generated_at',
            ]);
        });
        
        Schema::dropIfExists('knowledge_embeddings');
    }
};
