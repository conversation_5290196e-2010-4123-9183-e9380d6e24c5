import { AdminLayout } from "@/components/admin-layout";
import { WidgetList } from "@/components/widget-listing/WidgetList";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Sparkles } from "lucide-react";
import { Link } from "react-router-dom";

const WidgetsListPage = () => {
    return (
        <AdminLayout>
            <div className="flex flex-col space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Widget Management</h1>
                        <p className="text-muted-foreground">
                            Create, manage, and customize your chat widgets for different websites and applications.
                        </p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <Button asChild className="bg-blue-600 hover:bg-blue-700">
                            <Link to="/dashboard/widget-builder/smart">
                                <Sparkles className="h-4 w-4 mr-2" />
                                Smart Builder
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link to="/dashboard/widget-builder">
                                <Plus className="h-4 w-4 mr-2" />
                                Classic Builder
                            </Link>
                        </Button>
                    </div>
                </div>

                <WidgetList showCreate={false} />
            </div>
        </AdminLayout>
    );
};

export default WidgetsListPage;