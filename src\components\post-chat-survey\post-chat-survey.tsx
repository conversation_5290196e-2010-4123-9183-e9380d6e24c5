"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle2, ChevronLeft, ChevronRight, Star, ThumbsDown, ThumbsUp, MessageSquareText, ClipboardCheck } from "lucide-react"
import { cn } from "@/lib/utils"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

interface PostChatSurveyProps {
    onSubmit: (data: Record<string, any>) => void
    onCancel: () => void
    questions?: SurveyQuestion[]
    title?: string
    description?: string
    thankYouMessage?: string
}

interface SurveyQuestion {
    id: number
    text: string
    type: 'rating' | 'text' | 'select' | 'boolean'
    options?: string[]
    isRequired: boolean
    order: number
}

export function PostChatSurvey({
    onSubmit,
    onCancel,
    questions = [],
    title = "How was your experience?",
    description = "Please take a moment to provide feedback on your chat experience",
    thankYouMessage = "Thank you for your feedback!"
}: PostChatSurveyProps) {
    const [formData, setFormData] = useState<Record<string, any>>({
        rating: "5",
        resolved: true,
        improvements: [],
        feedback: ""
    })
    const [currentStep, setCurrentStep] = useState(0)
    const [submitted, setSubmitted] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleSubmit = () => {
        setIsSubmitting(true)
        // Short delay to show loading state
        setTimeout(() => {
            setSubmitted(true)
            setIsSubmitting(false)
            onSubmit(formData)
        }, 500)
    }

    const steps = [
        {
            title: "Rate your experience",
            icon: <Star className="h-5 w-5" />,
            component: (
                <div className="space-y-6">
                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <Star className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
                            <Label className="text-base dark:text-gray-200">How would you rate your overall experience?</Label>
                        </div>
                        <div className="flex justify-center py-3">
                            <div className="flex gap-2">
                                {[1, 2, 3, 4, 5].map((value) => (
                                    <button
                                        key={value}
                                        type="button"
                                        className={cn(
                                            "h-10 w-10 rounded-full flex items-center justify-center transition-all",
                                            parseInt(formData.rating) >= value
                                                ? "text-yellow-500 hover:text-yellow-600 dark:text-yellow-400 dark:hover:text-yellow-300"
                                                : "text-gray-300 hover:text-yellow-400 dark:text-gray-500 dark:hover:text-yellow-400"
                                        )}
                                        onClick={() => setFormData({ ...formData, rating: value.toString() })}
                                    >
                                        <Star className="h-8 w-8 fill-current" />
                                    </button>
                                ))}
                            </div>
                        </div>
                        <div className="flex justify-between text-xs text-muted-foreground dark:text-gray-400 pt-1 px-1">
                            <span>Poor</span>
                            <span>Excellent</span>
                        </div>
                    </div>

                    <Separator className="dark:bg-gray-700" />

                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <ClipboardCheck className="h-5 w-5 text-green-500 dark:text-green-400" />
                            <Label className="text-base dark:text-gray-200">Was your question or issue resolved?</Label>
                        </div>
                        <div className="flex justify-center gap-4 py-2">
                            <Button
                                type="button"
                                variant={formData.resolved ? "default" : "outline"}
                                className={cn(
                                    "flex items-center gap-2 w-32 h-11",
                                    formData.resolved
                                        ? "bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
                                        : "dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
                                )}
                                onClick={() => setFormData({ ...formData, resolved: true })}
                            >
                                <ThumbsUp className="h-4 w-4" />
                                <span>Yes</span>
                            </Button>
                            <Button
                                type="button"
                                variant={!formData.resolved ? "default" : "outline"}
                                className={cn(
                                    "flex items-center gap-2 w-32 h-11",
                                    !formData.resolved
                                        ? "bg-amber-600 hover:bg-amber-700 dark:bg-amber-700 dark:hover:bg-amber-800"
                                        : "dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
                                )}
                                onClick={() => setFormData({ ...formData, resolved: false })}
                            >
                                <ThumbsDown className="h-4 w-4" />
                                <span>No</span>
                            </Button>
                        </div>
                    </div>
                </div>
            )
        },
        {
            title: "Additional feedback",
            icon: <MessageSquareText className="h-5 w-5" />,
            component: (
                <div className="space-y-6">
                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs py-0 h-5 px-2 border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                Select all that apply
                            </Badge>
                            <Label className="text-base dark:text-gray-200">What could we improve?</Label>
                        </div>
                        <div className="grid grid-cols-2 gap-3 pt-2">
                            {["Response time", "Answer quality", "Ease of use", "Clarity"].map((item) => (
                                <div
                                    key={item}
                                    className={cn(
                                        "flex items-center space-x-2 rounded-md border p-3 cursor-pointer transition-colors",
                                        formData.improvements?.includes(item)
                                            ? "bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
                                            : "border-gray-200 dark:border-gray-700"
                                    )}
                                    onClick={() => {
                                        const current = formData.improvements || []
                                        if (current.includes(item)) {
                                            setFormData({
                                                ...formData,
                                                improvements: current.filter((i: string) => i !== item)
                                            })
                                        } else {
                                            setFormData({
                                                ...formData,
                                                improvements: [...current, item]
                                            })
                                        }
                                    }}
                                >
                                    <Checkbox
                                        id={item}
                                        checked={formData.improvements?.includes(item)}
                                        className="dark:border-gray-500 dark:data-[state=checked]:bg-blue-600 dark:data-[state=checked]:border-blue-600"
                                        onCheckedChange={(checked) => {
                                            const current = formData.improvements || []
                                            if (checked) {
                                                setFormData({
                                                    ...formData,
                                                    improvements: [...current, item]
                                                })
                                            } else {
                                                setFormData({
                                                    ...formData,
                                                    improvements: current.filter((i: string) => i !== item)
                                                })
                                            }
                                        }}
                                    />
                                    <Label
                                        htmlFor={item}
                                        className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 dark:text-gray-300"
                                    >
                                        {item}
                                    </Label>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <MessageSquareText className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                            <Label htmlFor="feedback" className="dark:text-gray-200">Additional comments</Label>
                        </div>
                        <Textarea
                            id="feedback"
                            placeholder="Please share any additional feedback"
                            rows={4}
                            value={formData.feedback || ""}
                            onChange={(e) => setFormData({ ...formData, feedback: e.target.value })}
                            className="resize-none dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 dark:placeholder:text-gray-400"
                        />
                    </div>
                </div>
            )
        }
    ]

    return (
        <Card className="border-0 shadow-none w-full mx-auto dark:bg-gray-800 dark:text-gray-100">
            <CardHeader className="pb-4 border-b dark:border-gray-700">
                <div className="flex items-center gap-2 mb-1">
                    {submitted ? (
                        <div className="h-9 w-9 rounded-full bg-green-100 flex items-center justify-center dark:bg-green-900/30">
                            <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                    ) : (
                        <div className="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center dark:bg-blue-900/30">
                            {steps[currentStep].icon || <Star className="h-5 w-5 text-blue-600 dark:text-blue-400" />}
                        </div>
                    )}
                    <CardTitle className="text-lg dark:text-gray-100">
                        {submitted ? "Thank You" : steps[currentStep].title}
                    </CardTitle>
                </div>
                <CardDescription className="dark:text-gray-300">
                    {submitted ? thankYouMessage : description}
                </CardDescription>
            </CardHeader>

            {!submitted ? (
                <>
                    <CardContent className="pt-5">
                        {/* Progress indicator */}
                        <div className="mb-6">
                            <div className="flex justify-between mb-2">
                                {steps.map((step, index) => (
                                    <div
                                        key={index}
                                        className="flex flex-col items-center"
                                        style={{ width: `${100 / steps.length}%` }}
                                    >
                                        <div
                                            className={`h-2 w-full ${index === 0 ? 'rounded-l-full' : ''
                                                } ${index === steps.length - 1 ? 'rounded-r-full' : ''
                                                } ${index <= currentStep
                                                    ? 'bg-blue-600 dark:bg-blue-600'
                                                    : 'bg-gray-200 dark:bg-gray-700'
                                                }`}
                                        />
                                        <span className={`text-xs mt-1 ${index === currentStep
                                            ? 'text-blue-600 font-medium dark:text-blue-400'
                                            : 'text-gray-500 dark:text-gray-400'
                                            }`}>
                                            {index + 1}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {steps[currentStep].component}
                    </CardContent>

                    <CardFooter className="flex justify-between pt-4 border-t dark:border-gray-700">
                        {currentStep > 0 ? (
                            <Button
                                variant="outline"
                                className="flex items-center gap-1 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                                onClick={() => setCurrentStep(currentStep - 1)}
                            >
                                <ChevronLeft className="h-4 w-4" />
                                Back
                            </Button>
                        ) : (
                            <Button
                                variant="outline"
                                onClick={onCancel}
                                className="dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                            >
                                Skip
                            </Button>
                        )}

                        {currentStep < steps.length - 1 ? (
                            <Button
                                className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700"
                                onClick={() => setCurrentStep(currentStep + 1)}
                            >
                                Next
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                        ) : (
                            <Button
                                className="relative bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 flex items-center gap-2"
                                disabled={isSubmitting}
                                onClick={handleSubmit}
                            >
                                {isSubmitting ? (
                                    <>
                                        <span className="mr-2 h-4 w-4 rounded-full border-2 border-b-transparent border-white animate-spin inline-block"></span>
                                        Submitting...
                                    </>
                                ) : (
                                    <>
                                        Submit Feedback
                                        <CheckCircle2 className="h-4 w-4" />
                                    </>
                                )}
                            </Button>
                        )}
                    </CardFooter>
                </>
            ) : (
                <CardFooter className="flex flex-col items-center gap-3 py-6">
                    <div className="rounded-full bg-green-100 p-4 text-green-600 dark:bg-green-900/30 dark:text-green-400">
                        <CheckCircle2 className="h-8 w-8" />
                    </div>
                    <p className="text-center text-sm text-muted-foreground dark:text-gray-300 px-6 mt-2">
                        Your feedback helps us improve our service. Thank you for taking the time to share your thoughts.
                    </p>
                    <Button
                        variant="outline"
                        onClick={onCancel}
                        className="mt-4 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                    >
                        Close
                    </Button>
                </CardFooter>
            )}
        </Card>
    )
}

export default PostChatSurvey;