import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { widgetService, Widget } from "@/utils/widgetService";
import {
    Edit,
    Copy,
    Trash2,
    MoreVertical,
    CheckCircle,
    XCircle,
    ExternalLink,
    EyeIcon,
    Code,
    BarChart3
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface WidgetListProps {
    showCreate?: boolean;
}

export function WidgetList({ showCreate = true }: WidgetListProps) {
    const [widgets, setWidgets] = useState<Widget[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [widgetToDelete, setWidgetToDelete] = useState<Widget | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const { toast } = useToast();

    const fetchWidgets = async () => {
        setIsLoading(true);
        try {
            const response = await widgetService.getAllWidgets();
            setWidgets(response.data);
        } catch (error) {
            console.error("Failed to fetch widgets:", error);
            toast({
                title: "Error",
                description: "Failed to load widgets. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchWidgets();
    }, []);

    const handleWidgetActivation = async (widget: Widget, isActive: boolean) => {
        try {
            if (widget.id) {
                await widgetService.setWidgetActive(widget.id, isActive);

                // Update local state
                setWidgets(widgets.map(w => {
                    if (w.id === widget.id) {
                        return { ...w, is_active: isActive };
                    }
                    return w;
                }));

                toast({
                    title: isActive ? "Widget Activated" : "Widget Deactivated",
                    description: `${widget.name} has been ${isActive ? "activated" : "deactivated"} successfully.`,
                });
            }
        } catch (error) {
            console.error(`Failed to ${isActive ? "activate" : "deactivate"} widget:`, error);
            toast({
                title: "Error",
                description: `Failed to ${isActive ? "activate" : "deactivate"} widget. Please try again.`,
                variant: "destructive",
            });
        }
    };

    const handleDeleteWidget = async () => {
        if (!widgetToDelete || !widgetToDelete.id) return;

        setIsDeleting(true);
        try {
            await widgetService.deleteWidget(widgetToDelete.id);

            // Update local state
            setWidgets(widgets.filter(w => w.id !== widgetToDelete.id));

            toast({
                title: "Widget Deleted",
                description: `${widgetToDelete.name} has been deleted successfully.`,
            });

            setWidgetToDelete(null);
            setDeleteDialogOpen(false);
        } catch (error) {
            console.error("Failed to delete widget:", error);
            toast({
                title: "Error",
                description: "Failed to delete widget. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsDeleting(false);
        }
    };

    const duplicateWidget = async (widget: Widget) => {
        if (!widget.id) return;

        try {
            // Create a new widget with the same settings but a different name
            const newWidget: Widget = {
                name: `${widget.name} (Copy)`,
                settings: widget.settings,
                is_active: false, // Start as inactive by default
                ai_model_id: widget.ai_model_id
            };

            const response = await widgetService.createWidget(newWidget);

            // Add the new widget to the list
            setWidgets([...widgets, response.data]);

            toast({
                title: "Widget Duplicated",
                description: `${widget.name} has been duplicated successfully.`,
            });
        } catch (error) {
            console.error("Failed to duplicate widget:", error);
            toast({
                title: "Error",
                description: "Failed to duplicate widget. Please try again.",
                variant: "destructive",
            });
        }
    };

    const formatDate = (dateString?: string) => {
        if (!dateString) return "N/A";
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        }).format(date);
    };

    return (
        <div className="space-y-6">
            {showCreate && (
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold tracking-tight">Your Widgets</h2>
                    <Button asChild>
                        <Link to="/dashboard/widget-builder">
                            Create New Widget
                        </Link>
                    </Button>
                </div>
            )}

            {isLoading ? (
                // Loading skeletons
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                        <Card key={i} className="overflow-hidden">
                            <CardHeader className="p-6">
                                <Skeleton className="h-6 w-3/4 mb-2" />
                                <Skeleton className="h-4 w-1/2" />
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-full" />
                                    <Skeleton className="h-4 w-2/3" />
                                    <Skeleton className="h-4 w-1/2" />
                                </div>
                            </CardContent>
                            <CardFooter className="flex justify-between border-t p-6">
                                <Skeleton className="h-9 w-24" />
                                <Skeleton className="h-9 w-9 rounded-full" />
                            </CardFooter>
                        </Card>
                    ))}
                </div>
            ) : widgets.length === 0 ? (
                // Empty state
                <Card className="flex flex-col items-center justify-center py-12">
                    <CardContent className="text-center space-y-4">
                        <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                            <Code className="h-10 w-10 text-primary" />
                        </div>
                        <CardTitle>No Widgets Found</CardTitle>
                        <CardDescription>
                            You haven't created any widgets yet. Get started by creating your first widget.
                        </CardDescription>
                        <Button asChild className="mt-4">
                            <Link to="/dashboard/widget-builder">Create New Widget</Link>
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                // Widget list grid
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {widgets.map((widget) => (
                        <Card key={widget.id} className="overflow-hidden">
                            <CardHeader className="pb-3">
                                <div className="flex justify-between items-start">
                                    <div>
                                        <CardTitle className="truncate">{widget.name}</CardTitle>
                                        <CardDescription className="truncate mt-1">
                                            ID: {widget.widget_id || widget.id}
                                        </CardDescription>
                                    </div>
                                    <Badge variant={widget.is_active ? "default" : "outline"}>
                                        {widget.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <div className="w-6 h-6 rounded-full"
                                            style={{ backgroundColor: widget.settings?.primaryColor || '#4f46e5' }}
                                        />
                                        <span className="text-sm text-muted-foreground">Primary Color</span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Position:</span>
                                        <span className="font-medium capitalize">
                                            {(widget.settings?.position || widget.position_settings?.position || 'bottom-right').replace('-', ' ')}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Bot Name:</span>
                                        <span className="font-medium">
                                            {widget.settings?.headerTitle || widget.settings?.botName || 'AI Assistant'}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Pre-Chat:</span>
                                        <span className="font-medium">
                                            {widget.settings?.preChat ? 'Enabled' : 'Disabled'}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Created:</span>
                                        <span className="font-medium">{formatDate(widget.created_at)}</span>
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter className="flex justify-between border-t p-4">
                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        asChild
                                    >
                                        <Link to={`/dashboard/widget-builder?id=${widget.id}`}>
                                            <Edit className="h-4 w-4 mr-1" />
                                            Edit
                                        </Link>
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        asChild
                                    >
                                        <Link to={`/dashboard/widget-preview?id=${widget.id}`}>
                                            <EyeIcon className="h-4 w-4 mr-1" />
                                            Preview
                                        </Link>
                                    </Button>
                                </div>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="icon">
                                            <MoreVertical className="h-4 w-4" />
                                            <span className="sr-only">Open menu</span>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem asChild>
                                            <Link to={`/admin/${widget.widget_id || widget.id}`} className="w-full flex items-center">
                                                <BarChart3 className="h-4 w-4 mr-2" />
                                                Admin Dashboard
                                            </Link>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem asChild>
                                            <Link to={`/dashboard/embed-code?id=${widget.id}`} className="w-full flex items-center">
                                                <Code className="h-4 w-4 mr-2" />
                                                Get Embed Code
                                            </Link>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem asChild>
                                            <Link to={`/dashboard/widgets/${widget.id}/ratings`} className="w-full flex items-center">
                                                <BarChart3 className="h-4 w-4 mr-2" />
                                                View Ratings
                                            </Link>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            onClick={() => duplicateWidget(widget)}
                                        >
                                            <Copy className="h-4 w-4 mr-2" />
                                            Duplicate
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            onClick={() => handleWidgetActivation(widget, !widget.is_active)}
                                        >
                                            {widget.is_active ? (
                                                <>
                                                    <XCircle className="h-4 w-4 mr-2" />
                                                    Deactivate
                                                </>
                                            ) : (
                                                <>
                                                    <CheckCircle className="h-4 w-4 mr-2" />
                                                    Activate
                                                </>
                                            )}
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            className="text-destructive focus:text-destructive"
                                            onClick={() => {
                                                setWidgetToDelete(widget);
                                                setDeleteDialogOpen(true);
                                            }}
                                        >
                                            <Trash2 className="h-4 w-4 mr-2" />
                                            Delete
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </CardFooter>
                        </Card>
                    ))}
                </div>
            )}

            {/* Delete Confirmation Dialog */}
            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Widget</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete the widget "{widgetToDelete?.name}"? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setDeleteDialogOpen(false)}
                            disabled={isDeleting}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteWidget}
                            disabled={isDeleting}
                        >
                            {isDeleting ? (
                                <>
                                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                                    Deleting...
                                </>
                            ) : (
                                "Delete"
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}