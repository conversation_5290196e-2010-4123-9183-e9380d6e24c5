import { useState, useEffect } from "react";
import { AIModelData, aiModelService } from "@/utils/ai-model-service";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ArrowRight, Bot, User, Database, FileText, Globe } from "lucide-react";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import api from "@/utils/api";

interface ModelTestChatDialogProps {
  model: AIModelData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  metadata?: any;
}

interface Project {
  id: number;
  name: string;
}

export function ModelTestChatDialog({
  model,
  open,
  onOpenChange
}: ModelTestChatDialogProps) {
  const { toast } = useToast();
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [useKnowledgeBase, setUseKnowledgeBase] = useState(true);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<number | null>(
    model.settings?.knowledge_base?.project_id || null
  );
  const [loadingProjects, setLoadingProjects] = useState(false);

  // Load projects for knowledge base testing
  useEffect(() => {
    if (!open) return;

    const fetchProjects = async () => {
      try {
        setLoadingProjects(true);
        const response = await api.get('/projects');
        if (response.data) {
          setProjects(response.data);

          // If model has a default project, use it
          if (model.settings?.knowledge_base?.project_id && !selectedProject) {
            setSelectedProject(model.settings.knowledge_base.project_id);
          } else if (response.data.length > 0 && !selectedProject) {
            // Otherwise use the first project
            setSelectedProject(response.data[0].id);
          }
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        toast({
          title: 'Error',
          description: 'Failed to load knowledge base projects',
          variant: 'destructive'
        });
      } finally {
        setLoadingProjects(false);
      }
    };

    fetchProjects();
  }, [open, model, toast, selectedProject]);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      role: "user",
      content: message,
    };

    const currentMessage = message;
    setMessages([...messages, userMessage]);
    setMessage("");
    setIsLoading(true);

    try {
      // Prepare test options
      const options = {
        temperature: model.settings?.temperature,
        max_tokens: model.settings?.max_tokens,
        use_knowledge_base: useKnowledgeBase,
        project_id: useKnowledgeBase ? selectedProject : null
      };

      // Call the API to get a response from the model
      const response = await aiModelService.testChat(
        model.id!,
        currentMessage,
        options
      );

      // Add AI response
      const aiMessage: ChatMessage = {
        role: "assistant",
        content: response.response || "No response received",
        metadata: response.metadata
      };

      setMessages(prev => [...prev, aiMessage]);

      // Show metadata in toast if available
      if (response.metadata) {
        toast({
          title: "Model Response",
          description: `Response time: ${response.metadata.response_time}ms | Tokens: ${response.metadata.tokens_input} in, ${response.metadata.tokens_output} out`
        });
      }
    } catch (error: any) {
      // Add error message as AI response
      const errorMessage: ChatMessage = {
        role: "assistant",
        content: `Error: ${error.message || "Failed to get response from model"}`,
      };

      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Error",
        description: error.message || "Failed to get response from model",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Test Chat: {model.name}</DialogTitle>
          <DialogDescription>
            Test how the AI model responds to different prompts
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Knowledge Base Options */}
          <div className="flex flex-col space-y-4 p-4 border rounded-lg bg-muted/40">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                <Label htmlFor="use-kb">Use Knowledge Base</Label>
              </div>
              <Switch
                id="use-kb"
                checked={useKnowledgeBase}
                onCheckedChange={setUseKnowledgeBase}
              />
            </div>

            {useKnowledgeBase && (
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="project">Knowledge Base Project</Label>
                {loadingProjects ? (
                  <div className="flex items-center gap-2">
                    <Spinner size="sm" />
                    <span className="text-sm text-muted-foreground">Loading projects...</span>
                  </div>
                ) : (
                  <Select
                    value={selectedProject?.toString() || ''}
                    onValueChange={(value) => setSelectedProject(parseInt(value, 10))}
                    disabled={loadingProjects || projects.length === 0}
                  >
                    <SelectTrigger id="project">
                      <SelectValue placeholder="Select a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id.toString()}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                {projects.length === 0 && !loadingProjects && (
                  <p className="text-sm text-amber-600">
                    No projects available. Please create one in the Knowledge Base section.
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Chat Messages */}
          <div className="flex flex-col h-80 border rounded-md p-4 overflow-hidden">
            <div className="flex-1 overflow-y-auto space-y-4 mb-4">
              {messages.map((msg, i) => (
                <div
                  key={i}
                  className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"
                    }`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${msg.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted"
                      }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {msg.role === "user" ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                      <span className="text-xs font-medium">
                        {msg.role === "user" ? "You" : model.name}
                      </span>
                    </div>
                    <div className="whitespace-pre-wrap break-words">
                      {msg.content}
                    </div>

                    {/* Show knowledge sources if available */}
                    {msg.role === "assistant" && msg.metadata?.knowledge_sources_used && (
                      <div className="mt-3 pt-2 border-t border-border/30">
                        <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                          <Database className="h-3 w-3" />
                          <span>Knowledge sources used:</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {msg.metadata.knowledge_sources_used.map((source, idx) => (
                            <div
                              key={`${source.type}-${source.id}-${idx}`}
                              className="text-[10px] px-1.5 py-0.5 rounded-full bg-primary/10 text-primary-foreground/80 flex items-center gap-1"
                              title={`Relevance: ${Math.round(source.relevance * 100)}%`}
                            >
                              {source.type === 'document' && <FileText className="h-2.5 w-2.5" />}
                              {source.type === 'database' && <Database className="h-2.5 w-2.5" />}
                              {source.type === 'web' && <Globe className="h-2.5 w-2.5" />}
                              <span className="truncate max-w-[120px]">{source.name}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="max-w-[80%] rounded-lg p-3 bg-muted">
                    <div className="flex items-center gap-2">
                      <Bot className="h-4 w-4" />
                      <span className="text-xs font-medium">{model.name}</span>
                    </div>
                    <div className="animate-pulse flex items-center gap-1 mt-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      <div className="h-2 w-2 rounded-full bg-primary/70"></div>
                      <div className="h-2 w-2 rounded-full bg-primary/40"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex gap-2">
              <Textarea
                placeholder="Type your message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              <Button
                onClick={handleSendMessage}
                disabled={isLoading || !message.trim()}
                className="self-end"
              >
                <ArrowRight className="h-4 w-4" />
                <span className="sr-only">Send</span>
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
