/**
 * Widget Utilities
 * 
 * This file contains utility functions for widget-related operations.
 * These functions are used throughout the application to ensure
 * consistent behavior and reduce code duplication.
 */

import { Widget, WidgetSettings } from '@/types/widget';

/**
 * Validate a domain format
 * @param domain Domain to validate
 * @returns Whether the domain is valid
 */
export function isValidDomain(domain: string): boolean {
  // Allow wildcard domains (*.example.com)
  if (domain === '*') {
    return true;
  }

  if (domain.startsWith('*.')) {
    domain = domain.substring(2);
  }

  // Regular domain validation regex
  const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;
  return domainRegex.test(domain);
}

/**
 * Check if a domain is allowed for a widget
 * @param allowedDomains List of allowed domains
 * @param domain Domain to check
 * @returns Whether the domain is allowed
 */
export function isDomainAllowed(allowedDomains: string[], domain: string): boolean {
  // Clean input
  domain = domain.trim().toLowerCase();

  // If no allowed domains are specified, allow all domains
  if (!allowedDomains || allowedDomains.length === 0) {
    return true;
  }

  // If wildcard is in the allowed list, allow all domains
  if (allowedDomains.includes('*')) {
    return true;
  }

  // Check if exact domain is in the allowed list
  if (allowedDomains.includes(domain)) {
    return true;
  }

  // Check for wildcard domains (*.example.com)
  for (const allowedDomain of allowedDomains) {
    if (allowedDomain.startsWith('*')) {
      // Extract the domain part after the wildcard
      const wildcardDomain = allowedDomain.substring(1); // Remove the * character

      // Check if domain ends with the wildcard domain part
      if (wildcardDomain === '.' + domain ||
          domain.endsWith(wildcardDomain)) {
        return true;
      }
    }
  }

  return false;
}

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html HTML content to sanitize
 * @returns Sanitized HTML
 */
export function sanitizeHtml(html: string): string {
  // Create a temporary element
  const tempElement = document.createElement('div');
  tempElement.textContent = html;
  return tempElement.innerHTML;
}

/**
 * Generate a placeholder embed code for a widget
 * @param type Embed code type
 * @param widgetId Widget ID
 * @param settings Widget settings
 * @returns Placeholder embed code
 */
export function generatePlaceholderEmbedCode(
  type: 'standard' | 'iframe' | 'web-component',
  widgetId: string,
  settings: Partial<WidgetSettings> = {}
): string {
  const baseUrl = typeof window !== 'undefined'
    ? window.location.origin
    : 'http://localhost:9090';

  const primaryColor = settings.primaryColor || '#4f46e5';
  const borderRadius = settings.borderRadius || 8;
  const position = settings.position || 'bottom-right';
  const initialMessage = settings.welcomeMessage || 'How can I help you today?';
  const persistConversation = settings.persistConversation || false;

  switch (type) {
    case 'standard':
      return `<script src="${baseUrl}/widget/v1/script.js"
  data-widget-id="${widgetId}"
  data-primary-color="${primaryColor}"
  data-border-radius="${borderRadius}"
  data-position="${position}"
  data-initial-message="${initialMessage}"
  data-persist-conversation="${persistConversation}"
  async>
</script>`;
    case 'iframe':
      return `<iframe
  src="${baseUrl}/widget/embed/${widgetId}"
  width="100%"
  height="500px"
  style="border: 1px solid #eee; border-radius: 8px;">
</iframe>`;
    case 'web-component':
      return `<script type="module" src="${baseUrl}/widget/v1/web-component.js"></script>
<ai-chat-widget
  widget-id="${widgetId}"
  primary-color="${primaryColor}"
  border-radius="${borderRadius}"
  position="${position}"
  initial-message="${initialMessage}"
  persist-conversation="${persistConversation}"
  api-base-url="${baseUrl}">
</ai-chat-widget>`;
    default:
      return '<!-- Select an embed type to generate code -->';
  }
}

/**
 * Generate a visitor ID for anonymous users
 * @returns Unique visitor ID
 */
export function generateVisitorId(): string {
  // Check if visitor ID already exists in localStorage
  const existingId = localStorage.getItem('ai_chat_visitor_id');
  if (existingId) {
    return existingId;
  }

  // Generate a new visitor ID
  const visitorId = 'visitor_' + Math.random().toString(36).substring(2, 15);
  
  // Store in localStorage for future use
  localStorage.setItem('ai_chat_visitor_id', visitorId);
  
  return visitorId;
}

/**
 * Get default widget settings
 * @returns Default widget settings
 */
export function getDefaultWidgetSettings(): WidgetSettings {
  return {
    primaryColor: '#7E69AB',
    secondaryColor: '#f3f4f6',
    borderRadius: 8,
    position: 'bottom-right',
    welcomeMessage: 'Hello! How can I help you today?',
    headerTitle: 'AI Assistant',
    inputPlaceholder: 'Type your message...',
    sendButtonText: 'Send',
    startMinimized: false,
    autoOpen: false,
    autoOpenDelay: 5,
    showTypingIndicator: true,
    enableUserRatings: true,
    requireGuestInfo: false,
    persistConversation: false,
    preChat: false,
    postChat: false,
    closeAfterInactivity: false,
    inactivityTimeout: 5,
    width: 350,
    height: 600,
    contextRetention: 'session',
    maxMessagesStored: 100,
    customCSS: '',
    securitySettings: {
      dataSanitization: true,
      preventDataCollection: false,
      cspEnabled: false,
      enableSRI: false
    }
  };
}
