import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageSquare, Send, X, ChevronDown, ChevronUp, Image, Paperclip, AlertCircle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { WidgetSettings } from "@/utils/widgetService";
import { useToast } from "@/components/ui/use-toast";
import api from "@/utils/api";

interface Message {
  id: number;
  text: string;
  sender: "user" | "assistant" | "error";
  timestamp: Date;
  metadata?: {
    model?: string;
    response_time?: number;
    total_tokens?: number;
    error?: string;
  };
}

interface WidgetPreviewSectionProps {
  settings: WidgetSettings;
}

export function WidgetPreviewSection({ settings }: WidgetPreviewSectionProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: settings.welcomeMessage || "Hello! How can I help you today?",
      sender: "assistant",
      timestamp: new Date()
    }
  ]);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Extract settings with defaults
  const primaryColor = settings.primaryColor || "#4f46e5";
  const secondaryColor = settings.secondaryColor || primaryColor;
  const borderRadius = settings.borderRadius || 8;
  const fontFamily = settings.fontFamily || "Inter";
  const chatIconSize = settings.chatIconSize || 50;
  const logoUrl = settings.logoUrl || "";
  const headerTitle = settings.headerTitle || "Chat Support";
  const inputPlaceholder = settings.inputPlaceholder || "Type your message...";
  const sendButtonText = settings.sendButtonText || "Send";

  // Calculate position based on settings
  const getPositionStyle = () => {
    if (!isOpen) return {};

    const baseStyles = {
      width: settings.width ? `${settings.width}px` : "350px",
      height: settings.height ? `${settings.height}px` : "500px",
    };

    // Apply position type
    if (settings.positionType === 'inline') {
      return {
        ...baseStyles,
        position: 'relative' as const,
      };
    }

    // For fixed and relative, calculate position
    const position = settings.position || 'bottom-right';
    let positionStyles = {};

    const horizontalOffset = settings.horizontalOffset || 20;
    const verticalOffset = settings.verticalOffset || 20;

    if (position.includes('bottom')) {
      positionStyles = {
        ...positionStyles,
        bottom: `${verticalOffset}px`
      };
    } else {
      positionStyles = {
        ...positionStyles,
        top: `${verticalOffset}px`
      };
    }

    if (position.includes('right')) {
      positionStyles = {
        ...positionStyles,
        right: `${horizontalOffset}px`
      };
    } else if (position.includes('left')) {
      positionStyles = {
        ...positionStyles,
        left: `${horizontalOffset}px`
      };
    } else {
      // Center position
      positionStyles = {
        ...positionStyles,
        left: '50%',
        transform: 'translateX(-50%)'
      };
    }

    return {
      ...baseStyles,
      position: 'absolute' as const,
      ...positionStyles
    };
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    if (isLoading) return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      text: newMessage,
      sender: "user",
      timestamp: new Date()
    };

    setMessages([...messages, userMessage]);
    setNewMessage("");
    setIsLoading(true);

    try {
      // Make API call to get response
      const response = await api.post('/ai/chat/process', {
        message: newMessage,
        widget_id: settings.id,
        options: {
          temperature: settings.temperature || 0.7,
          max_tokens: settings.maxTokens || 2048
        }
      });

      // Add assistant response
      const assistantMessage: Message = {
        id: messages.length + 2,
        text: response.data.message,
        sender: "assistant",
        timestamp: new Date(),
        metadata: {
          model: response.data.metadata?.model,
          response_time: response.data.metadata?.response_time,
          total_tokens: response.data.metadata?.total_tokens
        }
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: Message = {
        id: messages.length + 2,
        text: error.response?.data?.message || 'Failed to get a response. Please try again.',
        sender: "error",
        timestamp: new Date(),
        metadata: {
          error: error.message
        }
      };

      setMessages(prev => [...prev, errorMessage]);

      // Show error toast
      toast({
        title: "Error",
        description: errorMessage.text,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderButtonContent = () => {
    if (isOpen) {
      return <X size={24} />;
    }

    if (settings.buttonCustomization?.iconName === 'message') {
      return <MessageSquare size={24} />;
    }

    return <MessageSquare size={24} />;
  };

  const avatarContent = () => {
    if (logoUrl) {
      return (
        <Avatar className="h-8 w-8">
          <AvatarImage src={logoUrl} alt="Logo" />
          <AvatarFallback>{settings.avatar?.fallbackInitial || 'A'}</AvatarFallback>
        </Avatar>
      );
    }

    if (settings.avatar?.enabled && settings.avatar.imageUrl) {
      return (
        <Avatar className="h-8 w-8">
          <AvatarImage src={settings.avatar.imageUrl} alt="Avatar" />
          <AvatarFallback>{settings.avatar.fallbackInitial || 'A'}</AvatarFallback>
        </Avatar>
      );
    }

    return null;
  };

  // Custom styles
  const customStyles = settings.customCSS ?
    <style dangerouslySetInnerHTML={{ __html: settings.customCSS }} /> : null;

  return (
    <div
      id="widget-preview"
      className="ai-chat-widget relative h-full border border-gray-200 p-4 flex items-center justify-center rounded-lg bg-slate-50"
    >
      {customStyles}

      <div className="relative w-full h-96">
        {/* Chat button */}
        <button
          onClick={toggleChat}
          className="absolute z-10 flex items-center justify-center rounded-full focus:outline-none shadow-lg"
          style={{
            backgroundColor: primaryColor,
            borderRadius: '50%',
            width: `${chatIconSize}px`,
            height: `${chatIconSize}px`,
            right: isOpen ? 'calc(100% + 20px)' : '20px',
            bottom: '20px',
            transform: isOpen ? 'scale(0.8)' : 'scale(1)',
            transition: 'all 0.3s ease',
            color: '#ffffff'
          }}
        >
          {renderButtonContent()}
        </button>

        {/* Chat window */}
        {isOpen && (
          <div
            className="ai-chat-widget flex flex-col bg-white rounded-lg shadow-xl overflow-hidden border"
            style={{
              ...getPositionStyle(),
              fontFamily,
              borderRadius: `${borderRadius}px`,
              maxHeight: settings.height ? `${settings.height}px` : "500px",
            }}
          >
            {/* Header */}
            <div
              className="ai-chat-header flex items-center justify-between p-4"
              style={{ backgroundColor: primaryColor, color: '#ffffff' }}
            >
              <div className="flex items-center gap-2">
                {avatarContent()}
                <h3 className="font-medium">{headerTitle}</h3>
              </div>
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white opacity-80 hover:opacity-100 hover:bg-white/10"
                  onClick={() => setIsOpen(false)}
                >
                  <ChevronDown size={18} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white opacity-80 hover:opacity-100 hover:bg-white/10"
                  onClick={() => setIsOpen(false)}
                >
                  <X size={18} />
                </Button>
              </div>
            </div>

            {/* Messages */}
            <div className="ai-chat-messages flex-1 p-4 overflow-y-auto space-y-4">
              {messages.map((message) => {
                if (message.sender === "error") {
                  return (
                    <div
                      key={message.id}
                      className="flex max-w-[80%] rounded-lg p-3 bg-red-50 border border-red-200 text-red-600"
                    >
                      <div>
                        <div className="flex items-center gap-1 mb-1">
                          <AlertCircle size={14} />
                          <span className="font-medium text-sm">Error</span>
                        </div>
                        <p className="text-sm">{message.text}</p>
                      </div>
                    </div>
                  );
                }

                return (
                  <div
                    key={message.id}
                    className={cn(
                      "flex max-w-[80%] rounded-lg p-3",
                      message.sender === "user"
                        ? "ml-auto bg-primary text-primary-foreground"
                        : "bg-muted"
                    )}
                    style={{
                      backgroundColor: message.sender === "user" ? primaryColor : '#f1f5f9',
                      color: message.sender === "user" ? '#ffffff' : '#1e293b',
                    }}
                  >
                    <div>
                      <p className="text-sm">{message.text}</p>
                      {message.sender === "assistant" && message.metadata && (
                        <div className="mt-2 text-xs opacity-70">
                          {message.metadata.model && <div>Model: {message.metadata.model}</div>}
                          {message.metadata.response_time && (
                            <div>Response time: {(message.metadata.response_time / 1000).toFixed(2)}s</div>
                          )}
                          {message.metadata.total_tokens && (
                            <div>Tokens: {message.metadata.total_tokens}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}

              {isLoading && (
                <div className="flex max-w-[80%] rounded-lg p-3 bg-muted">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300 border-t-gray-600 animate-spin"></div>
                    <p className="text-sm text-gray-500">Generating response...</p>
                  </div>
                </div>
              )}
            </div>

            {/* Input area */}
            <div className="ai-chat-input border-t p-3 flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="text-muted-foreground"
                disabled={isLoading}
              >
                <Paperclip size={18} />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-muted-foreground"
                disabled={isLoading}
              >
                <Image size={18} />
              </Button>

              <Input
                placeholder={isLoading ? "Waiting for response..." : inputPlaceholder}
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isLoading) {
                    handleSendMessage();
                  }
                }}
                disabled={isLoading}
              />

              <Button
                onClick={handleSendMessage}
                style={{ backgroundColor: primaryColor }}
                className={`px-3 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                size="sm"
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <>
                    <Send size={16} className="mr-1" />
                    <span className="sr-only">{sendButtonText}</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
