import { useState, useEffect } from 'react'
import { aiModelService, AIModelData } from '@/utils/ai-model-service'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Props interface
interface AIModelSelectorProps {
    selectedModelId: number | null;
    onModelChange: (id: number | null) => void;
    placeholder?: string;
    isOptional?: boolean;
    className?: string;
    id?: string;
}

export function AIModelSelector({
    selectedModelId,
    onModelChange,
    placeholder = 'Select AI model',
    isOptional = false,
    className = '',
    id
}: AIModelSelectorProps) {
    // Local state
    const [models, setModels] = useState<AIModelData[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Fetch models on component mount
    useEffect(() => {
        fetchModels();
    }, []);

    // Function to fetch models from API
    const fetchModels = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const modelsList = await aiModelService.getModels();
            setModels(modelsList);
        } catch (err: any) {
            console.error('Error fetching AI models:', err);
            // Display the specific error message
            setError(err.message || 'Failed to fetch AI models');
        } finally {
            setIsLoading(false);
        }
    };

    // Get active models - check active property
    const activeModels = models.filter(m => m.active !== false);

    // Safe value conversion
    const selectValue = selectedModelId !== null ? String(selectedModelId) : 'none';

    return (
        <div className={`${className}`}>
            <Select
                value={selectValue}
                onValueChange={(value) => {
                    // Handle special values
                    if (value === 'none' || value === 'no-models-available' || value === '') {
                        onModelChange(null);
                    } else {
                        // Convert numeric string to number
                        onModelChange(Number(value));
                    }
                }}
                disabled={isLoading}
            >
                <SelectTrigger id={id}>
                    <SelectValue placeholder={
                        isLoading ? 'Loading models...' :
                            error ? 'Error loading models' :
                                placeholder
                    } />
                </SelectTrigger>
                <SelectContent>
                    {isOptional && (
                        <SelectItem value="none">None (Use default)</SelectItem>
                    )}
                    {activeModels.length > 0 ? (
                        activeModels.map((model) => (
                            <SelectItem key={model.id} value={String(model.id)}>
                                {model.name}
                            </SelectItem>
                        ))
                    ) : !isLoading && !error && (
                        <SelectItem value="no-models-available" disabled>No models available</SelectItem>
                    )}
                </SelectContent>
            </Select>
            {error && (
                <p className="text-sm text-red-500 mt-1">{error}</p>
            )}
            {!isLoading && !error && activeModels.length === 0 && (
                <p className="text-sm text-amber-500 mt-1">No AI models found. Please add models in the AI Model Management section.</p>
            )}
        </div>
    );
}