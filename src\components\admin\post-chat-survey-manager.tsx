import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Plus,
  Edit,
  Trash2,
  Check,
  Star,
  MessageSquare,
  CheckCircle,
  HelpCircle
} from "lucide-react"
import { apiService } from "@/utils/api-service"
import { useToast } from "@/hooks/use-toast"

interface PostChatSurveyManagerProps {
  widgetId: string
}

interface SurveyQuestion {
  id?: number
  question: string
  type: 'rating' | 'text' | 'multiple_choice' | 'yes_no'
  options?: string[]
  is_required: boolean
  order: number
}

interface Survey {
  id: number
  title: string
  description?: string
  is_active: boolean
  questions: SurveyQuestion[]
  created_at: string
}

export function PostChatSurveyManager({ widgetId }: PostChatSurveyManagerProps) {
  const [surveys, setSurveys] = useState<Survey[]>([])
  const [loading, setLoading] = useState(true)
  const [editingSurvey, setEditingSurvey] = useState<Survey | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    questions: [] as SurveyQuestion[]
  })
  const { toast } = useToast()

  const fetchSurveys = async () => {
    try {
      setLoading(true)
      const response = await apiService.get(`/widgets/${widgetId}/post-chat-surveys`)
      setSurveys(response.data)
    } catch (error) {
      console.error("Failed to fetch surveys:", error)
      toast({
        title: "Error",
        description: "Failed to load post-chat surveys",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateSurvey = () => {
    setEditingSurvey(null)
    setFormData({
      title: '',
      description: '',
      questions: [
        {
          question: 'How would you rate your overall experience?',
          type: 'rating',
          is_required: true,
          order: 0
        },
        {
          question: 'Was your issue resolved?',
          type: 'yes_no',
          is_required: true,
          order: 1
        },
        {
          question: 'Any additional feedback?',
          type: 'text',
          is_required: false,
          order: 2
        }
      ]
    })
    setIsDialogOpen(true)
  }

  const handleEditSurvey = (survey: Survey) => {
    setEditingSurvey(survey)
    setFormData({
      title: survey.title,
      description: survey.description || '',
      questions: [...survey.questions]
    })
    setIsDialogOpen(true)
  }

  const handleSaveSurvey = async () => {
    try {
      if (editingSurvey) {
        await apiService.put(`/widgets/${widgetId}/post-chat-surveys/${editingSurvey.id}`, formData)
        toast({
          title: "Success",
          description: "Survey updated successfully",
          variant: "default"
        })
      } else {
        await apiService.post(`/widgets/${widgetId}/post-chat-surveys`, formData)
        toast({
          title: "Success",
          description: "Survey created successfully",
          variant: "default"
        })
      }
      setIsDialogOpen(false)
      fetchSurveys()
    } catch (error) {
      console.error("Failed to save survey:", error)
      toast({
        title: "Error",
        description: "Failed to save survey",
        variant: "destructive"
      })
    }
  }

  const handleDeleteSurvey = async (surveyId: number) => {
    if (!confirm("Are you sure you want to delete this survey?")) return

    try {
      await apiService.delete(`/widgets/${widgetId}/post-chat-surveys/${surveyId}`)
      toast({
        title: "Success",
        description: "Survey deleted successfully",
        variant: "default"
      })
      fetchSurveys()
    } catch (error) {
      console.error("Failed to delete survey:", error)
      toast({
        title: "Error",
        description: "Failed to delete survey",
        variant: "destructive"
      })
    }
  }

  const handleActivateSurvey = async (surveyId: number) => {
    try {
      await apiService.post(`/widgets/${widgetId}/post-chat-surveys/${surveyId}/activate`)
      toast({
        title: "Success",
        description: "Survey activated successfully",
        variant: "default"
      })
      fetchSurveys()
    } catch (error) {
      console.error("Failed to activate survey:", error)
      toast({
        title: "Error",
        description: "Failed to activate survey",
        variant: "destructive"
      })
    }
  }

  const addQuestion = () => {
    const newQuestion: SurveyQuestion = {
      question: 'New Question',
      type: 'text',
      is_required: false,
      order: formData.questions.length
    }
    setFormData({
      ...formData,
      questions: [...formData.questions, newQuestion]
    })
  }

  const updateQuestion = (index: number, question: Partial<SurveyQuestion>) => {
    const updatedQuestions = [...formData.questions]
    updatedQuestions[index] = { ...updatedQuestions[index], ...question }
    setFormData({ ...formData, questions: updatedQuestions })
  }

  const removeQuestion = (index: number) => {
    const updatedQuestions = formData.questions.filter((_, i) => i !== index)
    setFormData({ ...formData, questions: updatedQuestions })
  }

  const moveQuestion = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1
    if (newIndex < 0 || newIndex >= formData.questions.length) return

    const updatedQuestions = [...formData.questions]
    const temp = updatedQuestions[index]
    updatedQuestions[index] = updatedQuestions[newIndex]
    updatedQuestions[newIndex] = temp

    // Update order values
    updatedQuestions[index].order = index
    updatedQuestions[newIndex].order = newIndex

    setFormData({ ...formData, questions: updatedQuestions })
  }

  const getQuestionIcon = (type: SurveyQuestion['type']) => {
    switch (type) {
      case 'rating':
        return <Star className="h-4 w-4" />
      case 'text':
        return <MessageSquare className="h-4 w-4" />
      case 'yes_no':
        return <CheckCircle className="h-4 w-4" />
      case 'multiple_choice':
        return <HelpCircle className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }

  useEffect(() => {
    fetchSurveys()
  }, [widgetId])

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Post-Chat Surveys</h3>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Post-Chat Surveys</h3>
        <Button onClick={handleCreateSurvey}>
          <Plus className="h-4 w-4 mr-2" />
          Create Survey
        </Button>
      </div>

      <div className="grid gap-4">
        {surveys.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground mb-4">No post-chat surveys found</p>
              <Button onClick={handleCreateSurvey}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Survey
              </Button>
            </CardContent>
          </Card>
        ) : (
          surveys.map((survey) => (
            <Card key={survey.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {survey.title}
                      {survey.is_active && (
                        <Badge variant="default">Active</Badge>
                      )}
                    </CardTitle>
                    {survey.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {survey.description}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    {!survey.is_active && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleActivateSurvey(survey.id)}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Activate
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditSurvey(survey)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteSurvey(survey.id)}
                      disabled={survey.is_active}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {survey.questions.length} question{survey.questions.length !== 1 ? 's' : ''}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {survey.questions.map((question, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {getQuestionIcon(question.type)}
                        {question.type}
                        {question.is_required && '*'}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Survey Editor Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingSurvey ? 'Edit Survey' : 'Create Survey'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Survey Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter survey title"
                />
              </div>
              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter survey description"
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-4">
                <Label>Survey Questions</Label>
                <Button type="button" variant="outline" onClick={addQuestion}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Question
                </Button>
              </div>

              <div className="space-y-4">
                {formData.questions.map((question, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>Question</Label>
                            <Textarea
                              value={question.question}
                              onChange={(e) => updateQuestion(index, { question: e.target.value })}
                              placeholder="Enter your question"
                              rows={2}
                            />
                          </div>
                          <div>
                            <Label>Question Type</Label>
                            <Select
                              value={question.type}
                              onValueChange={(value) => updateQuestion(index, { type: value as SurveyQuestion['type'] })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="rating">Rating (1-5 stars)</SelectItem>
                                <SelectItem value="text">Text Response</SelectItem>
                                <SelectItem value="yes_no">Yes/No</SelectItem>
                                <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {question.type === 'multiple_choice' && (
                          <div>
                            <Label>Options (one per line)</Label>
                            <Textarea
                              value={question.options?.join('\n') || ''}
                              onChange={(e) => updateQuestion(index, {
                                options: e.target.value.split('\n').filter(opt => opt.trim())
                              })}
                              placeholder="Option 1&#10;Option 2&#10;Option 3"
                              rows={3}
                            />
                          </div>
                        )}

                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={question.is_required}
                              onCheckedChange={(checked) => updateQuestion(index, { is_required: checked })}
                            />
                            <Label>Required</Label>
                          </div>

                          <div className="flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => moveQuestion(index, 'up')}
                              disabled={index === 0}
                            >
                              ↑
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => moveQuestion(index, 'down')}
                              disabled={index === formData.questions.length - 1}
                            >
                              ↓
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeQuestion(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveSurvey}>
                {editingSurvey ? 'Update Survey' : 'Create Survey'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
