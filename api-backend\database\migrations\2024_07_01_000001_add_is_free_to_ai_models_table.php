<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_models', function (Blueprint $table) {
            // Add is_free column if it doesn't exist
            if (!Schema::hasColumn('ai_models', 'is_free')) {
                $table->boolean('is_free')->default(false);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ai_models', function (Blueprint $table) {
            // Drop is_free column if it exists
            if (Schema::hasColumn('ai_models', 'is_free')) {
                $table->dropColumn('is_free');
            }
        });
    }
};
