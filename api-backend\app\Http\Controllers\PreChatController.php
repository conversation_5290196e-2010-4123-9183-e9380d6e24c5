<?php

namespace App\Http\Controllers;

use App\Models\PreChatFormTemplate;
use App\Models\PreChatFormField;
use App\Models\PreChatFormSubmission;
use App\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PreChatController extends Controller
{
    /**
     * Get the pre-chat form templates for a widget
     *
     * @param string $widgetId
     * @return JsonResponse
     */
    public function getTemplates(string $widgetId): JsonResponse
    {
        try {
            $widget = Widget::where('widget_id', $widgetId)->firstOrFail();

            $template = PreChatFormTemplate::where('widget_id', $widget->id)
                ->where('is_active', true)
                ->with(['fields' => function ($query) {
                    $query->orderBy('order', 'asc');
                }])
                ->first();

            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active pre-chat form found for this widget'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'template' => $template
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get pre-chat templates: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get pre-chat form'
            ], 500);
        }
    }

    /**
     * Submit a pre-chat form
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function submitForm(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|integer|exists:pre_chat_form_templates,id',
            'session_id' => 'required|string',
            'data' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get the template
            $template = PreChatFormTemplate::with('fields')->findOrFail($request->template_id);

            // Validate required fields
            $requiredFields = $template->fields->where('is_required', true)->pluck('name')->toArray();
            foreach ($requiredFields as $field) {
                if (!isset($request->data[$field]) || empty($request->data[$field])) {
                    return response()->json([
                        'success' => false,
                        'message' => "Field '{$field}' is required"
                    ], 422);
                }
            }

            // Create submission
            $submission = PreChatFormSubmission::create([
                'template_id' => $request->template_id,
                'session_id' => $request->session_id,
                'data' => $request->data
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Form submitted successfully',
                'submission_id' => $submission->id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to submit pre-chat form: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit form'
            ], 500);
        }
    }
}
