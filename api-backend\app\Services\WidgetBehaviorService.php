<?php

namespace App\Services;

use App\Models\WidgetBehavior;

class WidgetBehaviorService
{
    /**
     * Get behavior configuration for a widget.
     */
    public function getBehavior(int $widgetId): ?WidgetBehavior
    {
        return WidgetBehavior::where('widget_id', $widgetId)->first();
    }

    /**
     * Create or update behavior configuration for a widget.
     */
    public function updateBehavior(int $widgetId, array $behaviorData): WidgetBehavior
    {
        return WidgetBehavior::createFromSettings($widgetId, $behaviorData);
    }

    /**
     * Delete behavior configuration for a widget.
     */
    public function deleteBehavior(int $widgetId): bool
    {
        return WidgetBehavior::where('widget_id', $widgetId)->delete() > 0;
    }

    /**
     * Get default behavior settings.
     */
    public function getDefaultBehavior(): array
    {
        return [
            'autoOpen' => false,
            'autoOpenDelay' => 5,
            'startMinimized' => false,
            'showTypingIndicator' => true,
            'enableUserRatings' => false,
            'collectUserData' => false,
            'persistConversation' => false,
            'preChat' => false,
            'postChat' => false,
            'closeAfterInactivity' => false,
            'inactivityTimeout' => 5,
            'showLogo' => true,
            'showCloseButton' => true,
        ];
    }

    /**
     * Validate behavior settings.
     */
    public function validateBehaviorSettings(array $settings): array
    {
        $errors = [];

        // Validate auto open delay
        if (isset($settings['autoOpenDelay'])) {
            $delay = $settings['autoOpenDelay'];
            if (!is_numeric($delay) || $delay < 0 || $delay > 60) {
                $errors['autoOpenDelay'] = 'Auto open delay must be between 0 and 60 seconds.';
            }
        }

        // Validate inactivity timeout
        if (isset($settings['inactivityTimeout'])) {
            $timeout = $settings['inactivityTimeout'];
            if (!is_numeric($timeout) || $timeout < 1 || $timeout > 60) {
                $errors['inactivityTimeout'] = 'Inactivity timeout must be between 1 and 60 minutes.';
            }
        }

        // Validate boolean fields
        $booleanFields = [
            'autoOpen', 'startMinimized', 'showTypingIndicator', 'enableUserRatings',
            'collectUserData', 'persistConversation', 'preChat', 'postChat',
            'closeAfterInactivity', 'showLogo', 'showCloseButton'
        ];

        foreach ($booleanFields as $field) {
            if (isset($settings[$field]) && !is_bool($settings[$field])) {
                $errors[$field] = "The {$field} field must be a boolean value.";
            }
        }

        return $errors;
    }

    /**
     * Merge behavior settings with defaults.
     */
    public function mergeBehaviorSettings(array $settings): array
    {
        return array_merge($this->getDefaultBehavior(), $settings);
    }

    /**
     * Get behavior settings optimized for public widget display.
     */
    public function getPublicBehaviorSettings(int $widgetId): array
    {
        $behavior = $this->getBehavior($widgetId);
        
        if (!$behavior) {
            return $this->getDefaultBehavior();
        }

        $settings = $behavior->toSettingsArray();

        // Remove sensitive or internal settings for public use
        unset($settings['advancedSettings']);

        return $settings;
    }

    /**
     * Check if pre-chat is enabled for a widget.
     */
    public function isPreChatEnabled(int $widgetId): bool
    {
        $behavior = $this->getBehavior($widgetId);
        return $behavior ? $behavior->pre_chat_enabled : false;
    }

    /**
     * Check if post-chat is enabled for a widget.
     */
    public function isPostChatEnabled(int $widgetId): bool
    {
        $behavior = $this->getBehavior($widgetId);
        return $behavior ? $behavior->post_chat_enabled : false;
    }

    /**
     * Check if user data collection is enabled for a widget.
     */
    public function isUserDataCollectionEnabled(int $widgetId): bool
    {
        $behavior = $this->getBehavior($widgetId);
        return $behavior ? $behavior->collect_user_data : false;
    }

    /**
     * Get auto-open settings for a widget.
     */
    public function getAutoOpenSettings(int $widgetId): array
    {
        $behavior = $this->getBehavior($widgetId);
        
        if (!$behavior) {
            return [
                'enabled' => false,
                'delay' => 5,
            ];
        }

        return [
            'enabled' => $behavior->auto_open,
            'delay' => $behavior->auto_open_delay,
        ];
    }

    /**
     * Get inactivity settings for a widget.
     */
    public function getInactivitySettings(int $widgetId): array
    {
        $behavior = $this->getBehavior($widgetId);
        
        if (!$behavior) {
            return [
                'enabled' => false,
                'timeout' => 5,
            ];
        }

        return [
            'enabled' => $behavior->close_after_inactivity,
            'timeout' => $behavior->inactivity_timeout,
        ];
    }
}
