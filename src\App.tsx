import { BrowserRouter, Routes, Route, useRoutes } from "react-router-dom";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/react-query";
import routes from "tempo-routes";

import Templates from "./pages/Templates";
import ModelManagement from "./pages/ModelManagement";
import ModelEditor from "./pages/ModelEditor";
import AIConfiguration from "./pages/AIConfiguration";
import { PromptTemplatesModule } from "./modules/prompt-templates";
import ContextRules from "./pages/ContextRules";
import UserManagement from "./pages/UserManagement";
import ApiTester from "./pages/ApiTester";
import EmbedCode from "./pages/EmbedCode";
import SmartWidgetBuilderPage from "./pages/SmartWidgetBuilderPage";
import WidgetPreviewPage from "./pages/WidgetPreviewPage";
import WidgetsListPage from "./pages/WidgetsListPage";
import KnowledgeBase from "./pages/KnowledgeBase";
import ResponseFormatter from "./pages/ResponseFormatter";
import Branding from "./pages/Branding";
import FollowUp from "./pages/FollowUp";
import Analytics from "./pages/Analytics";
import DirectChat from "./pages/DirectChat";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import { AuthProvider } from "./hooks/use-auth";
import { ProtectedRoute } from "./components/protected-route";
// Import template pages
import NewTemplatePage from "./pages/templates/new";
import EditTemplatePage from "./pages/templates/[id]";
// Import admin pages
import WidgetAdminPage from "./pages/admin/WidgetAdminPage";
import Dashboard from "./pages/Dashboard";

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ThemeProvider defaultTheme="system" storageKey="ui-theme">
          <BrowserRouter>
            <Routes>
              {/* Tempo routes */}
              {import.meta.env.VITE_TEMPO &&
                routes.map((route) => (
                  <Route
                    key={route.path}
                    path={route.path}
                    element={route.element}
                  />
                ))}
              {/* Public routes */}
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Protected dashboard routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/templates"
                element={
                  <ProtectedRoute>
                    <Templates />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/templates"
                element={
                  <ProtectedRoute>
                    <Templates />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/templates/new"
                element={
                  <ProtectedRoute>
                    <NewTemplatePage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/templates/:id"
                element={
                  <ProtectedRoute>
                    <EditTemplatePage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/prompts"
                element={
                  <ProtectedRoute>
                    <PromptTemplatesModule />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/model-management"
                element={
                  <ProtectedRoute>
                    <ModelManagement />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/model-management/add"
                element={
                  <ProtectedRoute>
                    <ModelEditor />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/model-management/edit/:id"
                element={
                  <ProtectedRoute>
                    <ModelEditor />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/ai-configuration"
                element={
                  <ProtectedRoute>
                    <AIConfiguration />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/context-rules"
                element={
                  <ProtectedRoute>
                    <ContextRules />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/user-management"
                element={
                  <ProtectedRoute>
                    <UserManagement />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/api-tester"
                element={
                  <ProtectedRoute>
                    <ApiTester />
                  </ProtectedRoute>
                }
              />
              <Route path="/api-tester" element={<ApiTester />} />
              <Route
                path="/dashboard/widgets"
                element={
                  <ProtectedRoute>
                    <WidgetsListPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/widget-builder"
                element={
                  <ProtectedRoute>
                    <SmartWidgetBuilderPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/widget-builder/smart"
                element={
                  <ProtectedRoute>
                    <SmartWidgetBuilderPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/widget-builder/smart/:widgetId"
                element={
                  <ProtectedRoute>
                    <SmartWidgetBuilderPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/widget-builder/:widgetId"
                element={
                  <ProtectedRoute>
                    <SmartWidgetBuilderPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/widget-preview"
                element={
                  <ProtectedRoute>
                    <WidgetPreviewPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/embed-code"
                element={
                  <ProtectedRoute>
                    <EmbedCode />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/dashboard/knowledge-base"
                element={
                  <ProtectedRoute>
                    <KnowledgeBase />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/response-formatter"
                element={
                  <ProtectedRoute>
                    <ResponseFormatter />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/branding"
                element={
                  <ProtectedRoute>
                    <Branding />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/follow-up"
                element={
                  <ProtectedRoute>
                    <FollowUp />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/analytics"
                element={
                  <ProtectedRoute>
                    <Analytics />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/direct-chat"
                element={
                  <ProtectedRoute>
                    <DirectChat />
                  </ProtectedRoute>
                }
              />

              {/* Widget Admin Dashboard */}
              <Route
                path="/admin/:widgetId"
                element={
                  <ProtectedRoute>
                    <WidgetAdminPage />
                  </ProtectedRoute>
                }
              />

              {/* Allow Tempo routes before fallback */}
              {import.meta.env.VITE_TEMPO && <Route path="/tempobook/*" />}

              {/* Fallback route */}
              <Route path="*" element={<Index />} />
            </Routes>
          </BrowserRouter>
          <Toaster />
        </ThemeProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
