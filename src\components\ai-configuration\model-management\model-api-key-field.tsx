import React from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { AlertCircle } from "lucide-react";

export const ModelApiKeyField: React.FC = () => {
  const form = useFormContext();
  const provider = form.watch("provider");

  return (
    <FormField
      control={form.control}
      name="api_key"
      render={({ field }) => (
        <FormItem>
          <FormLabel>API Key</FormLabel>
          <FormControl>
            <Input
              type="password"
              placeholder="Enter API key"
              {...field}
            />
          </FormControl>
          <FormDescription className="flex items-center text-amber-500 dark:text-amber-400">
            <AlertCircle className="h-3 w-3 mr-1" />
            The API key must be valid for the selected {provider || "model"}
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
