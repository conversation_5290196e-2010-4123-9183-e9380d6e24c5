import { useAIModelManagement } from "@/hooks/use-ai-model-management";
import { useModelActions } from "./model-management/model-actions";
import { AIModelData, aiModelService } from "@/utils/ai-model-service";
import { AIModelDialog } from "./model-management/ai-model-dialog";
import { ModelSelectionCard } from "./model-management/model-selection-card";
import { ApiKeyCard } from "./model-management/api-key-card";
import { ConfigParametersCard } from "./model-management/config-parameters-card";
import { ModelTestChatDialog } from "./model-management/model-test-chat-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { MessageSquare, Settings, History, BarChart } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface AIModelManagerProps {
  initialTab?: string;
  onTabChange?: (tab: string) => void;
}

export function AIModelManager({ initialTab = "basic", onTabChange }: AIModelManagerProps = {}) {
  const {
    selectedModelId,
    setSelectedModelId,
    temperature,
    setTemperature,
    maxTokens,
    setMaxTokens,
    apiKey,
    setApiKey,
    isAPIKeyValid,
    setIsAPIKeyValid,
    isLoading,
    isSaving,
    setIsSaving,
    isTesting,
    setIsTesting,
    models,
    setModels,
    selectedModel,
    setSelectedModel,
    isDialogOpen,
    setIsDialogOpen,
    isTestChatOpen,
    setIsTestChatOpen,
    editingModel,
    setEditingModel,
    fetchModels
  } = useAIModelManagement();

  const { toast } = useToast();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [modelToDelete, setModelToDelete] = useState<AIModelData | null>(null);

  const {
    handleModelSelect,
    handleAPIKeySave,
    handleSaveConfiguration,
    handleTestConnection,
    handleModelDialogSubmit,
    handleOpenTestChat
  } = useModelActions();

  // Handle model selection
  const onModelSelect = (modelId: number) => {
    handleModelSelect(
      String(modelId),
      models,
      setSelectedModelId,
      setSelectedModel,
      setTemperature,
      setMaxTokens,
      setApiKey,
      setIsAPIKeyValid
    );
  };

  // Handle API key save
  const onApiKeySave = async () => {
    await handleAPIKeySave(
      selectedModel,
      selectedModelId,
      apiKey,
      setIsAPIKeyValid,
      setIsSaving
    );
  };

  // Handle configuration save
  const onSaveConfiguration = async () => {
    await handleSaveConfiguration(
      selectedModel,
      selectedModelId,
      temperature,
      maxTokens,
      setModels,
      models,
      setIsSaving
    );
  };

  // Handle model name change
  const onModelNameChange = (modelName: string) => {
    if (!selectedModel) return;

    // Update the model settings with the new model name
    const updatedSettings = {
      ...selectedModel.settings,
      model_name: modelName
    };

    // Update the selected model
    const updatedModel = {
      ...selectedModel,
      settings: updatedSettings
    };

    setSelectedModel(updatedModel);
  };

  // Handle connection test
  const onTestConnection = async () => {
    try {
      await handleTestConnection(
        selectedModel,
        selectedModelId,
        setIsTesting
      );

      // Return the expected format for ApiKeyCard
      return {
        data: {
          message: "Connection successful"
        }
      };
    } catch (error: any) {
      // Re-throw the error so ApiKeyCard can handle it
      throw error;
    }
  };

  // Handle opening test chat dialog
  const onOpenTestChat = () => {
    handleOpenTestChat(
      selectedModel,
      selectedModelId,
      setIsTestChatOpen
    );
  };

  // Handle adding new model
  const onAddNewModel = () => {
    setEditingModel(null);
    setIsDialogOpen(true);
  };

  // Handle editing model
  const onEditModel = (model: AIModelData) => {
    setEditingModel(model);
    setIsDialogOpen(true);
  };

  // Handle model dialog submit
  const onModelDialogSubmit = async (formData: AIModelData) => {
    await handleModelDialogSubmit(
      formData,
      editingModel,
      fetchModels,
      setIsDialogOpen,
      setIsSaving
    );
  };

  // Handle model update
  const onUpdateModel = (updatedModel: AIModelData) => {
    setSelectedModel(updatedModel);
    // Also update in the models array if it exists and is an array
    if (models && Array.isArray(models)) {
      setModels(models.map(model =>
        model.id === updatedModel.id ? updatedModel : model
      ));
    }
  };

  // Handle toggle model activation
  const onToggleActive = async (model: AIModelData, active: boolean) => {
    if (!model.id) return;

    setIsSaving(true);
    try {
      await aiModelService.toggleModelActivation(model.id, active);

      // Update models list
      const updatedModels = models.map(m =>
        m.id === model.id ? { ...m, active } : m
      );
      setModels(updatedModels);

      // If this is the currently selected model, update it too
      if (selectedModelId === model.id) {
        setSelectedModel({ ...selectedModel!, active });
      }

      toast({
        title: active ? "Model Activated" : "Model Deactivated",
        description: `${model.name} has been ${active ? "activated" : "deactivated"}`
      });
    } catch (error) {
      console.error("Failed to toggle model activation:", error);
      toast({
        title: "Operation Failed",
        description: `Could not ${active ? "activate" : "deactivate"} the model`,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle setting default model
  const onSetDefault = async (model: AIModelData) => {
    if (!model.id) return;

    setIsSaving(true);
    try {
      await aiModelService.setDefaultModel(model.id);

      // Update all models in the list
      const updatedModels = models.map(m => ({
        ...m,
        is_default: m.id === model.id
      }));
      setModels(updatedModels);

      // If this is the currently selected model, update it too
      if (selectedModelId === model.id) {
        setSelectedModel({ ...selectedModel!, is_default: true });
      }

      toast({
        title: "Default Model Updated",
        description: `${model.name} is now the default AI model`
      });
    } catch (error) {
      console.error("Failed to set default model:", error);
      toast({
        title: "Operation Failed",
        description: "Could not set the default model",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle deleting model
  const onDeleteModel = (model: AIModelData) => {
    setModelToDelete(model);
    setIsDeleteDialogOpen(true);
  };

  // Confirm model deletion
  const confirmDeleteModel = async () => {
    if (!modelToDelete || !modelToDelete.id) return;

    setIsSaving(true);
    try {
      await aiModelService.deleteModel(modelToDelete.id);

      // Remove model from list
      const updatedModels = models.filter(m => m.id !== modelToDelete.id);
      setModels(updatedModels);

      // If this was the selected model, clear selection
      if (selectedModelId === modelToDelete.id) {
        setSelectedModelId(null);
        setSelectedModel(null);
      }

      toast({
        title: "Model Deleted",
        description: `${modelToDelete.name} has been deleted`
      });
    } catch (error) {
      console.error("Failed to delete model:", error);
      toast({
        title: "Deletion Failed",
        description: "Could not delete the model",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
      setIsDeleteDialogOpen(false);
      setModelToDelete(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Model Selection Card */}
      <div className="flex flex-col md:flex-row md:items-start gap-4">
        <div className="flex-1">
          <ModelSelectionCard
            models={models}
            selectedModelId={selectedModelId}
            onModelSelect={onModelSelect}
            onAddNewModel={onAddNewModel}
            onEditModel={onEditModel}
            onToggleActive={onToggleActive}
            onSetDefault={onSetDefault}
            onDeleteModel={onDeleteModel}
            isLoading={isLoading}
          />
        </div>
        {selectedModel && (
          <Button
            variant="outline"
            className="flex items-center gap-2 md:self-start"
            onClick={onOpenTestChat}
          >
            <MessageSquare className="h-4 w-4" />
            Test Chat
          </Button>
        )}
      </div>

      {/* Only show configuration cards if a model is selected */}
      {selectedModel && (
        <Tabs
          defaultValue="basic"
          value={initialTab}
          onValueChange={(value) => {
            onTabChange?.(value);
          }}
          className="w-full"
        >
          <TabsList className="mb-4">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Basic Settings
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Advanced Settings
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart className="h-4 w-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <div className="grid gap-6 md:grid-cols-2">
              <ApiKeyCard
                selectedModel={selectedModel}
                apiKey={apiKey}
                isAPIKeyValid={isAPIKeyValid}
                isSaving={isSaving}
                isTesting={isTesting}
                onApiKeyChange={setApiKey}
                onApiKeySave={onApiKeySave}
                onTestConnection={onTestConnection}
              />

              <ConfigParametersCard
                selectedModel={selectedModel}
                temperature={temperature}
                maxTokens={maxTokens}
                onTemperatureChange={setTemperature}
                onMaxTokensChange={setMaxTokens}
                onModelNameChange={onModelNameChange}
                onSaveConfig={onSaveConfiguration}
                isSaving={isSaving}
              />
            </div>
          </TabsContent>

          <TabsContent value="advanced">
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">Advanced settings are available in the full implementation</p>
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">Analytics are available in the full implementation</p>
            </div>
          </TabsContent>
        </Tabs>
      )}

      {/* Add/Edit Model Dialog */}
      {isDialogOpen && (
        <AIModelDialog
          model={editingModel}
          open={isDialogOpen}
          onOpenChange={(open) => setIsDialogOpen(open)}
          onSubmit={onModelDialogSubmit}
        />
      )}

      {/* Test Chat Dialog */}
      {isTestChatOpen && selectedModel && (
        <ModelTestChatDialog
          model={selectedModel}
          open={isTestChatOpen}
          onOpenChange={(open) => setIsTestChatOpen(open)}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete AI Model</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {modelToDelete?.name}? This action cannot be undone.
              {modelToDelete?.is_default && (
                <span className="block mt-2 font-semibold text-destructive">
                  Warning: This is your default model. Deleting it will require setting a new default.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteModel}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
