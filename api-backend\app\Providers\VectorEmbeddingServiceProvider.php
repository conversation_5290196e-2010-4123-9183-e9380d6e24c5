<?php

namespace App\Providers;

use App\Services\VectorEmbeddingService;
use App\Services\DocumentProcessingService;
use Illuminate\Support\ServiceProvider;

class VectorEmbeddingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(VectorEmbeddingService::class, function ($app) {
            return new VectorEmbeddingService(
                $app->make(DocumentProcessingService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
